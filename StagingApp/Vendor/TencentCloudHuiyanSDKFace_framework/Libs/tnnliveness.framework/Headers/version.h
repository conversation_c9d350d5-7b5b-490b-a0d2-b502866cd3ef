// <PERSON><PERSON> is pleased to support the open source community by making TNN available.
//
// Copyright (C) 2020 THL A29 Limited, a Tencent company. All rights reserved.
//
// Licensed under the BSD 3-Clause License (the "License"); you may not use this file except
// in compliance with the License. You may obtain a copy of the License at
//
// https://opensource.org/licenses/BSD-3-Clause
//
// Unless required by applicable law or agreed to in writing, software distributed
// under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
// CONDITIONS OF ANY KIND, either express or implied. See the License for the 
// specific language governing permissions and limitations under the License.
#ifndef TNN_INCLUDE_TNN_VERSION_H_
#define TNN_INCLUDE_TNN_VERSION_H_

static char *branch_name_tnn = "HEAD";
static char *commit_date_tnn = "2022-09-07";
static char *commit_hash_tnn = "51cc596d";

#endif //TNN_INCLUDE_TNN_VERSION_H_
