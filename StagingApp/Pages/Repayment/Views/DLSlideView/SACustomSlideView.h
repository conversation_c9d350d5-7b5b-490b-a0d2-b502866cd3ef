
#import <UIKit/UIKit.h>

#import "DLSlideTabbarProtocol.h"
#import "SASlideView.h"
#import "SATabedSlideView.h"
#import "DLCacheProtocol.h"


@class SACustomSlideView;

@protocol DLCustomSlideViewDelegate <NSObject>
- (NSInteger)numberOfTabsInDLCustomSlideView:(SACustomSlideView *)sender;
- (UIViewController *)SACustomSlideView:(SACustomSlideView *)sender controllerAt:(NSInteger)index;
@optional
- (void)SACustomSlideView:(SACustomSlideView *)sender didSelectedAt:(NSInteger)index;
@end



@interface SACustomSlideView : UIView<DLSlideTabbarDelegate, DLSlideViewDelegate, DLSlideViewDataSource>
@property(nonatomic, weak) UIViewController *baseViewController;
@property(nonatomic, assign) NSInteger selectedIndex;

@property(nonatomic, strong) UIView<DLSlideTabbarProtocol> *tabbar;
@property(nonatomic, assign) float tabbarBottomSpacing;

@property(nonatomic, strong) id<DLCacheProtocol> cache;

@property(nonatomic, weak)IBOutlet id<DLCustomSlideViewDelegate>delegate;

- (void)setup;

@end
