
#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface SAAdvanceManager : NSObject

+ (SAAdvanceManager *)sharedInstance;

- (void)startUp;
- (void)setAdvanceInfo: (NSDictionary *)dict;

@property (nonatomic, strong) NSString *accessKey;
@property (nonatomic, strong) NSString *secretKey;
@property (nonatomic, strong) NSString *accessToken;

@property (nonatomic, strong) NSString *liveApi;
@property (nonatomic, strong) NSString *ocrApi;
@property (nonatomic, strong) NSString *faceApi;

@property (nonatomic, strong) NSDictionary *afDict;

@property (nonatomic, strong) NSArray *citiesArr;


@property (nonatomic, strong) NSString *appId;
@property (nonatomic, strong) NSString *appKey;

@property (nonatomic, strong) NSString *customerServiceUrl;


@end

NS_ASSUME_NONNULL_END

