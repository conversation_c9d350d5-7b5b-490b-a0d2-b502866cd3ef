

#import "SALoginViewController.h"
#import "SAImage+Extension.h"
#import "SAGradientView.h"
#import "SAVerifyCodeApiManager.h"
#import "SAGetRegManager.h"
#import "SAMobileLoginApiManager.h"
#import "SADeviceInfoSaveApiManager.h"
#import "SAAliyunOSSManager.h"
#import "SAMainManager.h"
#import "SASmsCodeViewController.h"
#import "SANavigationViewController.h"
#import "SACommonWebViewController.h"

#import "SAGetProtocolApiManager.h"


@interface SALoginViewController ()<QLAPIManagerCallBackDelegate, QLAPIManagerParamSource, UITextFieldDelegate, UITextViewDelegate, NPRequestMethodProtocol>

@property (nonatomic, strong) UIView *frontView;
@property (nonatomic, strong) UILabel *frontLabel;

@property (nonatomic, strong) UIImageView *appIconView;
@property (nonatomic, strong) UILabel *titleLab;
@property (nonatomic, strong) UILabel *tipsLab;


@property (nonatomic, strong) UILabel *phoneTipLab;
@property (nonatomic, strong) UITextField *phoneTextFie;
@property (nonatomic, strong) UIView *lineV;

@property (nonatomic, strong) SAGradientButton *codeBtn;

@property (nonatomic, assign) BOOL isAgree;
@property (nonatomic, strong) UIView *bottomView;
@property (nonatomic, strong) UIImageView *agreeImg;

@property (nonatomic, strong) SAGetRegManager *regManager;
@property (nonatomic, strong) NSString *mobileReg;
@property (nonatomic, strong) NSString *mobileMsg;

@property (nonatomic, strong) SAVerifyCodeApiManager *codeManager;

@property (nonatomic, strong) SAMainManager *mainMgr;

@property (nonatomic, strong) UILabel *versionLab;

@property (nonatomic, strong) UIButton *closeBtn;

@property (nonatomic, strong) UIButton *testBtn;


@property (nonatomic, strong) SAGetProtocolApiManager *protocolManager;
@property (nonatomic, strong) NSString *protocolKey;


@end

@implementation SALoginViewController


- (UIStatusBarStyle)preferredStatusBarStyle{
    if (@available(iOS 13.0, *)) {
        return UIStatusBarStyleDarkContent;
    }
    return UIStatusBarStyleDefault;
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    
    [self.navigationController setNavigationBarHidden:YES animated:YES];
}

- (void)viewDidAppear:(BOOL)animated
{
    [super viewDidAppear:animated];
    
    self.navigationController.interactivePopGestureRecognizer.enabled = NO;
}

- (void)viewDidDisappear:(BOOL)animated
{
    [super viewDidDisappear:animated];
    
    self.navigationController.interactivePopGestureRecognizer.enabled = YES;
}

- (void)viewDidLoad {
    [super viewDidLoad];
        
    self.isAgree = YES;
    [self.regManager loadData];
        
    [self addCustomViews];
    [self createFrames];
}

- (void)addCustomViews
{
    [self.view addSubview:self.frontView];
    [self.view addSubview:self.titleLab];
    [self.view addSubview:self.phoneTextFie];
    [self.view addSubview:self.phoneTipLab];

    [self.view addSubview:self.bottomView];
    [self.view addSubview:self.lineV];
    [self.view addSubview:self.codeBtn];
    [self.view addSubview:self.versionLab];
    
    [self.view addSubview:self.closeBtn];

    self.titleLab.text = [NSString stringWithFormat:@"欢迎使用%@", [[NSBundle mainBundle] objectForInfoDictionaryKey:@"CFBundleDisplayName"]];
    self.phoneTipLab.text = @"手机号码";
    self.phoneTextFie.placeholder = @"请输入您的手机号码";
}

- (void)createFrames
{
    [self.frontView mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.top.right.equalTo(self.view);
        make.height.mas_equalTo(94);
    }];
   
    [self.titleLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.equalTo(self.phoneTextFie);
        make.top.equalTo(self.view).offset(125);
    }];
            
    [self.phoneTextFie mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.equalTo(self.view).offset(XX_6(30));
        make.top.equalTo(self.titleLab.mas_bottom).offset(XX_6(80));
        make.height.mas_equalTo(XX_6(52));
        make.centerX.equalTo(self.view);
    }];
    [self.lineV mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.right.equalTo(self.phoneTextFie);
        make.height.mas_equalTo(1);
        make.top.equalTo(self.phoneTextFie.mas_bottom);
    }];
    [self.phoneTipLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.equalTo(self.phoneTextFie);
        make.bottom.equalTo(self.phoneTextFie.mas_top);
    }];

    [self.codeBtn mas_makeConstraints:^(SAConstraintMaker *make) {
        make.height.mas_equalTo(XX_6(48));
        make.left.right.equalTo(self.phoneTextFie);
        make.top.equalTo(self.phoneTextFie.mas_bottom).offset(30);
    }];
    self.codeBtn.layer.masksToBounds = YES;

    [self.bottomView mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.right.equalTo(self.phoneTextFie);
        make.height.mas_equalTo(54);
        make.top.equalTo(self.codeBtn.mas_bottom).offset(16);
    }];
    
    [self.versionLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.right.equalTo(self.view);
        make.bottom.equalTo(self.view).offset(-20);
    }];
    self.versionLab.text = [NSString stringWithFormat:@"V %@",  [[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleShortVersionString"]];

    
    [self.closeBtn mas_makeConstraints:^(SAConstraintMaker *make) {
        make.top.equalTo(self.view).offset(48);
        make.left.equalTo(self.view).offset(24);
        make.width.height.mas_equalTo(18);
    }];
}

#pragma mark - UITextFieldDelegate

- (void)textFieldDidBeginEditing:(UITextField *)textField
{
    self.lineV.backgroundColor =  kPrimaryColor;
}

- (void)textFieldDidEndEditing:(UITextField *)textField
{
    self.lineV.backgroundColor = [UIColor colorWithHex:0xE3E3E3];
}

- (BOOL)textField:(UITextField *)textField shouldChangeCharactersInRange:(NSRange)range replacementString:(NSString *)string
{
    if (textField == self.phoneTextFie) {
           
       if (range.length == 1 && string.length == 0) {
         return YES;
       }
       
       else if (self.phoneTextFie.text.length >= 11) {
         self.phoneTextFie.text = [textField.text substringToIndex: 11];
         return NO;
       }
   }
   
   return YES;
}
#pragma mark - TextView

-(BOOL)textView:(UITextView *)textView shouldInteractWithURL:(nonnull NSURL *)URL inRange:(NSRange)characterRange interaction:(UITextItemInteraction)interaction{

    if ([URL.scheme isEqualToString:@"privacy"]) 
    {
        NSLog(@"点击了隐私协议");
        self.protocolKey = PRIVACY_POLICY_YSXY;
        [self showActivityHUD:nil];
        [self.protocolManager loadData];

    }
    else if ([URL.scheme isEqualToString:@"service"])
    {
        NSLog(@"点击了服务协议");
        self.protocolKey = PRIVACY_POLICY_ZCXY;
        [self showActivityHUD:nil];
        [self.protocolManager loadData];
    }

    return YES;
}

#pragma mark QLAPIManagerCallBackDelegate
- (void)managerCallAPIDidSuccess:(SABaseManager *)manager
{
    [self hideHUDView];

    if(manager == self.regManager)
    {
        NSDictionary *dataDict = [manager fetchDataWithReformer:self.regManager];
        self.mobileMsg = [NSString judgeStringExist:dataDict[@"mobileMsg"]] ? dataDict[@"mobileMsg"] : NSLocalizedString(@"screenBind", nil);

        self.mobileReg = [NSString judgeStringExist:dataDict[@"mobileRegExp"]] ? dataDict[@"mobileRegExp"] : @"";
    }
    
    if (manager == self.codeManager)
    {
        
        NSDictionary *dataDict = [manager fetchDataWithReformer:self.codeManager];
        NSString *time = [NSString stringWithFormat:@"%@", dataDict[@"sendVerifyCodeSuccessVo"][@"effectiveSeconds"]];
        NSString *verifyCodeLength = [NSString stringWithFormat:@"%@", dataDict[@"sendVerifyCodeSuccessVo"][@"verifyCodeLength"]];

        SASmsCodeViewController *vc = [SASmsCodeViewController new];
        vc.effectiveTime = @(time.integerValue);
        vc.verifyCodeLength = [verifyCodeLength integerValue];
        vc.mobile = self.phoneTextFie.text;
        [self.navigationController pushViewController:vc animated:YES];
    }
    
    if(manager == self.protocolManager)
    {
        NSDictionary *dataDict = [manager fetchDataWithReformer:self.protocolManager];
        SAProtocolViewController *vc = [SAProtocolViewController new];
        vc.htmlStr = dataDict[@"text"];
        vc.navTitle = dataDict[@"title"];
        vc.companyName = dataDict[@"companyName"];
        vc.appName = dataDict[@"appName"];
        [self presentViewController:[[UINavigationController alloc] initWithRootViewController:vc] animated:YES completion:nil];
    }
}

- (void)managerCallAPIDidFailed:(SABaseManager *)manager {
    [self hideHUDView];
    NSString *failMsg  = [SAResponseHandle netWorkErrorMsg:[manager fetchDataWithReformer:nil] andErrorType:manager.errorType];
    [self textStateHUD:failMsg];
}

#pragma mark QLAPIManagerParamSource
- (NSDictionary *)paramsForApi:(SABaseManager *)manager
{
    if (manager == self.codeManager)
    {
        return @{@"mobile": self.phoneTextFie.text};
    }
    return [NSDictionary dictionary];
}

- (NSString *)methodForApi:(SABaseManager *)manager
{
    if (manager == self.protocolManager) {
        return [NSString stringWithFormat:@"1a99fb9ad8534512af0ede2979dfd68f/%@", self.protocolKey];
    }
    return @"";
}

#pragma mark - Action

- (void)closeVC
{
    [self.navigationController dismissViewControllerAnimated:YES completion:nil];
}

- (void)sendCode:(id)sender
{
    if (![NSString judgeStringExist:self.phoneTextFie.text]) {
        [self textStateHUD:NSLocalizedString(@"screenBind", nil)];
        return;
    }
    if([NSString judgeStringExist:self.mobileReg]){
       NSPredicate *predicate = [NSPredicate predicateWithFormat:@"SELF MATCHES %@", self.mobileReg];
       if(![predicate evaluateWithObject:self.phoneTextFie.text]) {
           [self textStateHUD:self.mobileMsg];
           return;
       }
    }
    
    if(!self.isAgree){
        [self textStateHUD:@"阅读并同意协议"];
        return;
    }
    
    [self showActivityHUD:nil];
    [self.codeManager loadData];
}

- (void)changeAgree{
    self.isAgree = !self.isAgree;
    if(!self.isAgree){
        self.agreeImg.image = [UIImage imageNamed:@"verify_check_nor"];
    } else {
        self.agreeImg.image = [UIImage customImageNamed:@"verify_check_sel"];
    }
}

#pragma mark - timer


- (void)beginUpdateUI {
    

}


#pragma mark - getters

- (UILabel *)phoneTipLab{
    FF_Fetch_UILable_CenterX(_phoneTipLab, [UIColor colorWithHex:0x666666], Font_XX6(11))
}

- (UITextField *)phoneTextFie {
    if (_phoneTextFie == nil) {
        _phoneTextFie = [[UITextField alloc] init];
        _phoneTextFie.font = BoldFont_XX6(18);
        _phoneTextFie.textAlignment = NSTextAlignmentLeft;
        _phoneTextFie.textColor = [UIColor colorWithHex:0x333333];
        _phoneTextFie.clearButtonMode = UITextFieldViewModeWhileEditing;
        _phoneTextFie.keyboardType = UIKeyboardTypeNumberPad;
        
        _phoneTextFie.delegate = self;
        
        UIView *leftV = [[UIView alloc] initWithFrame:CGRectMake(0, 0, 6, XX_6(52))];
        _phoneTextFie.leftViewMode = UITextFieldViewModeAlways;
        _phoneTextFie.leftView = leftV;
    }
    return _phoneTextFie;
}

- (SAGradientButton *)codeBtn {
    if (_codeBtn == nil) {
        _codeBtn = [SAGradientButton buttonWithType:UIButtonTypeCustom];
        [_codeBtn gradientBackgroundColors:@[kButtonStartColor, kButtonEndColor] locations:nil startPoint:CGPointMake(0, 0.5) endPoint:CGPointMake(1, 0.5)];
        [_codeBtn setTitle:@"获取短信验证码" forState:UIControlStateNormal];
        [_codeBtn setTitleColor:kHomeTextColor forState:UIControlStateNormal];
        _codeBtn.titleLabel.font = Font_XX6(15);
        [_codeBtn addTarget:self action:@selector(sendCode:) forControlEvents:UIControlEventTouchUpInside];
        _codeBtn.layer.cornerRadius = 7;
        _codeBtn.layer.masksToBounds = YES;

    }
    return _codeBtn;
}

- (UIView *)bottomView{
    if (_bottomView == nil) {
        _bottomView = [UIView new];
        _bottomView.userInteractionEnabled = YES;
        
        UIImageView *agreeImg = [[UIImageView alloc] initWithImage:[UIImage customImageNamed:@"verify_check_sel"]];
        [_bottomView addSubview:agreeImg];
        self.agreeImg = agreeImg;
        [agreeImg mas_makeConstraints:^(SAConstraintMaker *make) {
            make.width.height.mas_equalTo(14);
            make.left.equalTo(_bottomView);
            make.top.equalTo(_bottomView).offset(2);
        }];
        UITapGestureRecognizer *tapIcon = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(changeAgree)];
        agreeImg.userInteractionEnabled = YES;
        [agreeImg addGestureRecognizer:tapIcon];

        
        SATextView *protocolTV = [[SATextView alloc] init];
        protocolTV.editable = NO;
        protocolTV.delegate = self;
        protocolTV.textContainer.lineFragmentPadding = 0.0;
        protocolTV.textContainerInset = UIEdgeInsetsMake(0, 0, 0,  0);
        protocolTV.linkTextAttributes = @{NSForegroundColorAttributeName:[UIColor blueColor]};
        [_bottomView addSubview:protocolTV];
        [protocolTV mas_makeConstraints:^(SAConstraintMaker *make) {
            make.left.equalTo(agreeImg.mas_right).offset(5);
            make.right.top.height.equalTo(_bottomView);
        }];

        NSString *serviceStr= [NSString stringWithFormat:@"《用户注册协议》"];
        NSString *privacyStr = [NSString stringWithFormat:@"《隐私保护政策》"];
        NSString *protocolStr = [NSString stringWithFormat:@"温馨提示:未注册%@的手机号，登录时将自动注册，且代表您已同意%@、%@", [[NSBundle mainBundle] objectForInfoDictionaryKey:@"CFBundleDisplayName"], serviceStr, privacyStr];

        NSMutableAttributedString *privacy = [[NSMutableAttributedString alloc]initWithString:[NSString stringWithFormat:@"%@",protocolStr] attributes:@{NSFontAttributeName:[UIFont systemFontOfSize:13],NSForegroundColorAttributeName:[UIColor grayColor]}];
        
        NSRange privacyRange = [protocolStr rangeOfString:privacyStr];
        [privacy addAttribute:NSLinkAttributeName value:@"privacy://" range:privacyRange];

           
        NSRange serviceRange = [protocolStr rangeOfString:serviceStr];
        [privacy addAttribute:NSLinkAttributeName value:@"service://" range:serviceRange];

        protocolTV.linkTextAttributes = @{NSForegroundColorAttributeName:kPrimaryColor};
        protocolTV.attributedText = privacy;
    }
    return _bottomView;
}

- (SAGetRegManager *)regManager{
    if (_regManager == nil) {
        _regManager = [[SAGetRegManager alloc] init];
        _regManager.delegate = self;
        _regManager.paramSource = self;
    }
    return _regManager;
}

- (SAVerifyCodeApiManager *)codeManager {
    if (_codeManager == nil) {
        _codeManager = [[SAVerifyCodeApiManager alloc] init];
        _codeManager.delegate = self;
        _codeManager.paramSource = self;
    }
    return _codeManager;
}


- (SAMainManager *)mainMgr {
    if (_mainMgr == nil) {
        _mainMgr = [[SAMainManager alloc] init];
    }
    return _mainMgr;
}

- (UIView *)frontView{
    FF_Fetch_UIView(_frontView, [UIColor clearColor])
}


- (UILabel *)titleLab{
    FF_Fetch_UILable(_titleLab, [UIColor blackColor], BoldFont_XX6(28))
}

- (UIView *)lineV{
    FF_Fetch_UIView(_lineV, [UIColor colorWithHex:0xE3E3E3])
}

- (UILabel *)versionLab{
    FF_Fetch_UILable_CenterX(_versionLab , [UIColor colorWithHex:0x777777], Font_XX6(11))
}

-  (UIButton *)closeBtn{
    if(_closeBtn == nil){
        _closeBtn=[UIButton buttonWithType:UIButtonTypeCustom];
        [_closeBtn addTarget:self action:@selector(closeVC) forControlEvents:UIControlEventTouchUpInside];
        [_closeBtn setImage:[UIImage imageNamed:@"close"] forState:UIControlStateNormal];
    }
    return _closeBtn;
}


- (SAGetProtocolApiManager *)protocolManager {
    if (_protocolManager == nil) {
        _protocolManager = [[SAGetProtocolApiManager alloc] init];
        _protocolManager.delegate = self;
        _protocolManager.paramSource = self;
        _protocolManager.methodSource = self;
    }
    return _protocolManager;
}
@end

