
#import "SASmsCodeViewController.h"
#import "SAImage+Extension.h"
#import "SAGradientView.h"
#import "SAVerifyCodeApiManager.h"
#import "SAGetRegManager.h"
#import "SAMobileLoginApiManager.h"
#import "SADeviceInfoSaveApiManager.h"
#import "SAAliyunOSSManager.h"
#import "SAMainManager.h"
#import "SACommonWebViewController.h"
#import "SAGetProtocolApiManager.h"
#import <CRBoxInputView/CRBoxInputView.h>

#define kInputCodeHeight       48

@interface SASmsCodeViewController ()<QLAPIManagerCallBackDelegate, QLAPIManagerParamSource, UITextFieldDelegate, UITextViewDelegate, NPRequestMethodProtocol>

@property (nonatomic, strong) UIView *frontView;
@property (nonatomic, strong) UILabel *frontLabel;
@property (nonatomic, strong) UIView *backBg;

@property (nonatomic, strong) UIImageView *backImg;
@property (nonatomic, strong) UILabel *titleLab;
@property (nonatomic, strong) UILabel *tipsLab;
@property (nonatomic, strong) CRBoxInputView *inputView;
@property (nonatomic, strong) UIButton *otpBtn;
@property (nonatomic, strong) SAGradientButton *signinControl;

@property (nonatomic, assign) BOOL isAgree;
@property (nonatomic, strong) UIView *bottomView;
@property (nonatomic, strong) UIImageView *agreeImg;

@property (nonatomic, strong) SAVerifyCodeApiManager *codeManager;
@property (nonatomic, strong) SAMobileLoginApiManager *loginManager;
@property (nonatomic, strong) SADeviceInfoSaveApiManager *deviceInfoApiMgr;

@property (nonatomic, weak) NSTimer *timer;
@property (nonatomic, assign)int times;

@property (nonatomic, strong) SAMainManager *mainMgr;

@property (nonatomic, strong) SAGetProtocolApiManager *protocolManager;
@property (nonatomic, strong) NSString *protocolKey;

@end

@implementation SASmsCodeViewController

- (void)dealloc
{
    [self removeTimer];
}

- (UIStatusBarStyle)preferredStatusBarStyle{
    if (@available(iOS 13.0, *)) {
        return UIStatusBarStyleDarkContent;
    }
    return UIStatusBarStyleDefault;
}

- (void)viewWillAppear:(BOOL)animated 
{
    [super viewWillAppear:animated];
    [self.navigationController setNavigationBarHidden:YES animated:YES];
}

- (void)viewDidAppear:(BOOL)animated
{
    [super viewDidAppear:animated];
    
    self.navigationController.interactivePopGestureRecognizer.enabled = NO;
}

- (void)viewDidDisappear:(BOOL)animated
{
    [super viewDidDisappear:animated];
    
    self.navigationController.interactivePopGestureRecognizer.enabled = YES;
}

- (void)viewDidLoad
{
    [super viewDidLoad];

    [self addCustomViews];
    [self createFrames];
    self.isAgree = YES;
    
    NSString *str = [NSString stringWithFormat:@"%02lds", [self.effectiveTime integerValue]];
    [self.otpBtn setTitle:str forState:UIControlStateNormal];
    
    [self addTimer];
    
    @weakify(self)
    [self.inputView resetCodeLength:self.verifyCodeLength beginEdit:YES];
    self.inputView.textDidChangeblock = ^(NSString * _Nullable text, BOOL isFinished) {
        if(isFinished){
            @strongify(self)
            [self.inputView resignFirstResponder];
            [self showActivityHUD:nil];
            [self.loginManager loadData];
        }
    };
}

- (void)addCustomViews
{
    [self.view addSubview:self.backBg];
    [self.view addSubview:self.backImg];
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(tapBack)];
    self.backBg.userInteractionEnabled = YES;
    [self.backBg addGestureRecognizer:tap];

    [self.view addSubview:self.titleLab];
    [self.view addSubview:self.tipsLab];
    
    CRBoxInputCellProperty *cellProperty = [CRBoxInputCellProperty new];
    cellProperty.cellBgColorNormal = [UIColor whiteColor];
    cellProperty.cellBgColorSelected = [UIColor whiteColor];
    cellProperty.cellCursorColor = kPrimaryColor;
    cellProperty.cellCursorWidth = 2;
    cellProperty.cellCursorHeight = 30;
    cellProperty.cornerRadius = 4;
    cellProperty.borderWidth = 1;
    cellProperty.cellBorderColorFilled = kPrimaryColor;
    cellProperty.cellFont = [UIFont boldSystemFontOfSize:24];
    cellProperty.cellTextColor = [UIColor blackColor];
    cellProperty.configCellShadowBlock = ^(CALayer * _Nonnull layer) {
        layer.shadowColor = [kPrimaryColor colorWithAlphaComponent:0.2].CGColor;
        layer.shadowOpacity = 1;
        layer.shadowOffset = CGSizeMake(0, 2);
        layer.shadowRadius = 4;
    };
    
    CRBoxInputView *boxInputView = [[CRBoxInputView alloc] initWithFrame:CGRectMake(30, 240, UIScreenWidth-30*2, kInputCodeHeight)];
    boxInputView.customCellProperty = cellProperty;
    boxInputView.keyBoardType = UIKeyboardTypeNumberPad;
    [boxInputView loadAndPrepareViewWithBeginEdit:YES]; 
    self.inputView = boxInputView;

    
    
    [self.view addSubview:self.inputView];
    
    [self.view addSubview:self.otpBtn];
    [self.view addSubview:self.signinControl];
    
    self.titleLab.text = @"输入验证码";
    self.tipsLab.text = [NSString stringWithFormat:@"验证码已发送至 %@", self.mobile];
}

- (void)createFrames
{
    [self.backBg mas_makeConstraints:^(SAConstraintMaker *make) {
        make.top.equalTo(self.view).offset(60);
        make.left.equalTo(self.view).offset(14);
        make.width.height.mas_equalTo(34);
    }];
    
    [self.backImg mas_makeConstraints:^(SAConstraintMaker *make) {
        make.center.equalTo(self.backBg);
        make.width.height.mas_equalTo(22);
    }];
    self.backImg.contentMode = UIViewContentModeScaleAspectFit;

    
    [self.titleLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.equalTo(self.view).offset(30);
        make.top.equalTo(self.view).offset(125);
    }];
    [self.tipsLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.equalTo(self.titleLab);
        make.top.equalTo(self.titleLab.mas_bottom).offset(8);
    }];
            
    
    [self.otpBtn mas_makeConstraints:^(SAConstraintMaker *make) {
        make.centerX.equalTo(self.view);
        make.top.equalTo(self.inputView.mas_bottom).offset(20);
    }];
        
    [self.signinControl mas_makeConstraints:^(SAConstraintMaker *make) {
        make.height.equalTo(self.inputView);
        make.left.right.equalTo(self.inputView);
        make.top.equalTo(self.otpBtn.mas_bottom).offset(30);
    }];
    self.signinControl.layer.masksToBounds = YES;

}

- (void)tapBack
{
    [self removeTimer];
    [self.navigationController popViewControllerAnimated:YES];
}

#pragma mark - TextView

- (BOOL)textView:(UITextView *)textView shouldInteractWithURL:(nonnull NSURL *)URL inRange:(NSRange)characterRange interaction:(UITextItemInteraction)interaction{

    if ([URL.scheme isEqualToString:@"privacy"])
    {
        NSLog(@"点击了隐私协议");
        self.protocolKey = PRIVACY_POLICY_YSXY;
        [self showActivityHUD:nil];
        [self.protocolManager loadData];

    }
    else if ([URL.scheme isEqualToString:@"service"])
    {
        NSLog(@"点击了服务协议");
        self.protocolKey = PRIVACY_POLICY_ZCXY;
        [self showActivityHUD:nil];
        [self.protocolManager loadData];
    }

    return YES;
}

#pragma mark QLAPIManagerCallBackDelegate
- (void)managerCallAPIDidSuccess:(SABaseManager *)manager
{
    [self hideHUDView];

    if (manager == self.codeManager) {
        
        NSDictionary *dataDict = [manager fetchDataWithReformer:self.codeManager];
        [self.inputView becomeFirstResponder];
        NSString *time = [NSString stringWithFormat:@"%@", dataDict[@"sendVerifyCodeSuccessVo"][@"effectiveSeconds"]];
        self.effectiveTime = @(time.integerValue);
        NSString *str = [NSString stringWithFormat:@"%02lds后重新获取验证码", [time integerValue]];
        [self.otpBtn setTitle:str forState:UIControlStateNormal];
        
        [self addTimer];
        
    }
    else if(manager == self.loginManager){
        NSDictionary *dataDict = [manager fetchDataWithReformer:self.loginManager];
        NSString *tokenStr = dataDict[@"token"];
        
        if ([NSString judgeStringExist:tokenStr]) {
            [SAUserManager saveToken:tokenStr];
        }
               
        [self showActivityHUD:nil];
        [self.mainMgr uploadGps];
        [self.deviceInfoApiMgr loadData];
    } 
    else if (manager == self.deviceInfoApiMgr) {
                
        [[SAAdvanceManager sharedInstance] startUp];
        [[SANLUploadManager sharedInstance] setup];

        [self.navigationController dismissViewControllerAnimated:YES completion:nil];
    }
    
    if(manager == self.protocolManager)
    {
        NSDictionary *dataDict = [manager fetchDataWithReformer:self.protocolManager];
        SAProtocolViewController *vc = [SAProtocolViewController new];
        vc.htmlStr = dataDict[@"text"];
        vc.navTitle = dataDict[@"title"];
        vc.companyName = dataDict[@"companyName"];
        vc.appName = dataDict[@"appName"];
        [self presentViewController:[[UINavigationController alloc] initWithRootViewController:vc] animated:YES completion:nil];
    }
}

- (void)managerCallAPIDidFailed:(SABaseManager *)manager 
{
    [self hideHUDView];
    NSString *failMsg  = [SAResponseHandle netWorkErrorMsg:[manager fetchDataWithReformer:nil] andErrorType:manager.errorType];
    [self textStateHUD:failMsg];
    
    if(manager == self.loginManager)
    {
        [self.inputView clearAllWithBeginEdit:YES];
    }
}

#pragma mark QLAPIManagerParamSource

- (NSDictionary *)paramsForApi:(SABaseManager *)manager {
    if (manager == self.codeManager) {
        return @{@"mobile": self.mobile};
    } else if (manager == self.loginManager){
        return @{
            @"mobile": self.mobile,
            @"code": self.inputView.textValue
        };
    }else if (manager == self.deviceInfoApiMgr) {
        return @{
            @"deviceBrand": [SADeviceTool deviceModelName],
            @"hasRoot": [NSNumber numberWithBool:NO],
            @"phoneVersion": [SADeviceTool deviceSystemVersion],
            @"googleAdId": [SADeviceTool deviceUUID],
            @"simState": @([SADeviceTool getSIMState]),
            @"guestId": [SADeviceTool deviceGuestID]
        };
    }
    return [NSDictionary dictionary];
}

- (NSString *)methodForApi:(SABaseManager *)manager
{
    if (manager == self.protocolManager) {
        return [NSString stringWithFormat:@"1a99fb9ad8534512af0ede2979dfd68f/%@", self.protocolKey];
    }
    return @"";
}

#pragma mark - Action

- (void)sendCode:(id)sender
{
    [self showActivityHUD:nil];
    [self.codeManager loadData];
}

- (void)changeAgree{
    self.isAgree = !self.isAgree;
    if(!self.isAgree){
        self.agreeImg.image = [UIImage imageNamed:@"verify_check_nor"];
    } else {
        self.agreeImg.image = [UIImage customImageNamed:@"verify_check_sel"];
    }
}


- (void)jumpPrivacy{
    if ([NSString judgeStringExist:self.privacyUrl]) {
        SACommonWebViewController *vc = [SACommonWebViewController new];
        vc.title = self.privacyTit;
        vc.url = self.privacyUrl;
        [self.navigationController pushViewController:vc animated:NO];
    }
}

- (void)clicksigninControl:(UIButton *)btn
{
    if (![NSString judgeStringExist:self.inputView.textValue]) {
        [self textStateHUD:@"请输入短信验证码"];
        return;
    }

    [self.view endEditing:YES];
    
    [self showActivityHUD:nil];
    [self.loginManager loadData];
}

#pragma mark - timer

- (void)addTimer 
{
    [self removeTimer];
    
    self.times = self.effectiveTime.intValue;
    NSTimer *timer = [NSTimer timerWithTimeInterval:1.0 target:self selector:@selector(beginUpdateUI) userInfo:nil repeats:YES];
    self.timer = timer;
    [[NSRunLoop mainRunLoop] addTimer:timer forMode:NSRunLoopCommonModes];
}

- (void)removeTimer {
    [_timer invalidate];
    self.timer = nil;
}


- (void)beginUpdateUI {
    
    if (self.times > 0) {
        self.times--;
        NSString *str = [NSString stringWithFormat:@"%02ds后重新获取验证码", self.times];
        [self.otpBtn setTitle:str forState:UIControlStateNormal];
        [self.otpBtn setTitleColor:[UIColor colorWithHex:0x777777] forState:UIControlStateNormal];
        self.otpBtn.enabled = NO;
    } else {
        [self.otpBtn setTitle:@"重新发送验证码" forState:UIControlStateNormal];
        [self.otpBtn setTitleColor:kPrimaryColor forState:UIControlStateNormal];
        self.otpBtn.enabled = YES;
    }
}


#pragma mark - getters
- (UILabel *)tipsLab{
    FF_Fetch_UILable_Alignment(_tipsLab, [UIColor colorWithHex:0x444444], Font_XX6(12), NSTextAlignmentLeft)
}


- (UIButton *)otpBtn{
    if(_otpBtn == nil){
        _otpBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_otpBtn addTarget:self action:@selector(sendCode:) forControlEvents:UIControlEventTouchUpInside];
        [_otpBtn setTitleColor:kPrimaryColor forState:UIControlStateNormal];
        _otpBtn.titleLabel.font = Font_XX6(15);
        [_otpBtn setTitle:@"发送验证码" forState:UIControlStateNormal];
    }
    return _otpBtn;
}

- (SAGradientButton *)signinControl {
    if (_signinControl == nil) {
        _signinControl = [SAGradientButton buttonWithType:UIButtonTypeCustom];
        [_signinControl gradientBackgroundColors:@[kButtonStartColor, kButtonEndColor] locations:nil startPoint:CGPointMake(0, 0.5) endPoint:CGPointMake(1, 0.5)];
        [_signinControl setTitle:@"确认" forState:UIControlStateNormal];
        _signinControl.backgroundColor = kPrimaryColor;
        [_signinControl setTitleColor:[UIColor blackColor] forState:UIControlStateNormal];
        _signinControl.titleLabel.font = Font_XX6(15);
        _signinControl.layer.masksToBounds = YES;
        [_signinControl addTarget:self action:@selector(clicksigninControl:) forControlEvents:UIControlEventTouchUpInside];
        _signinControl.layer.cornerRadius = 7;
        _signinControl.layer.masksToBounds = YES;

    }
    return _signinControl;
}

- (SAVerifyCodeApiManager *)codeManager {
    if (_codeManager == nil) {
        _codeManager = [[SAVerifyCodeApiManager alloc] init];
        _codeManager.delegate = self;
        _codeManager.paramSource = self;
    }
    return _codeManager;
}

- (SAMobileLoginApiManager *)loginManager{
    if (_loginManager == nil) {
        _loginManager = [[SAMobileLoginApiManager alloc] init];
        _loginManager.delegate = self;
        _loginManager.paramSource = self;
    }
    return _loginManager;
}

- (SADeviceInfoSaveApiManager *)deviceInfoApiMgr {
    if (_deviceInfoApiMgr == nil) {
        _deviceInfoApiMgr = [[SADeviceInfoSaveApiManager alloc] init];
        _deviceInfoApiMgr.delegate = self;
        _deviceInfoApiMgr.paramSource = self;
    }
    return _deviceInfoApiMgr;
}

- (SAMainManager *)mainMgr {
    if (_mainMgr == nil) {
        _mainMgr = [[SAMainManager alloc] init];
    }
    return _mainMgr;
}


- (UILabel *)titleLab{
    FF_Fetch_UILable(_titleLab, [UIColor blackColor], BoldFont_XX6(28))
}

- (UIImageView *)backImg{
    FF_Fetch_UIImageViewWithImage(_backImg, [UIImage imageNamed:@"arrow_main_start"])
}

- (UIView *)backBg{
    FF_Fetch_UIView(_backBg, [UIColor clearColor])
}


- (UIView *)bottomView{
    if (_bottomView == nil) {
        _bottomView = [UIView new];
        _bottomView.userInteractionEnabled = YES;
        
        UIImageView *agreeImg = [[UIImageView alloc] initWithImage:[UIImage customImageNamed:@"verify_check_sel"]];
        [_bottomView addSubview:agreeImg];
        self.agreeImg = agreeImg;
        [agreeImg mas_makeConstraints:^(SAConstraintMaker *make) {
            make.width.height.mas_equalTo(14);
            make.left.equalTo(_bottomView);
            make.top.equalTo(_bottomView).offset(2);
        }];
        UITapGestureRecognizer *tapIcon = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(changeAgree)];
        agreeImg.userInteractionEnabled = YES;
        [agreeImg addGestureRecognizer:tapIcon];

        
        SATextView *protocolTV = [[SATextView alloc] init];
        protocolTV.editable = NO;
        protocolTV.delegate = self;
        protocolTV.textContainer.lineFragmentPadding = 0.0;
        protocolTV.textContainerInset = UIEdgeInsetsMake(0, 0, 0,  0);
        protocolTV.linkTextAttributes = @{NSForegroundColorAttributeName:[UIColor blueColor]};
        [_bottomView addSubview:protocolTV];
        [protocolTV mas_makeConstraints:^(SAConstraintMaker *make) {
            make.left.equalTo(agreeImg.mas_right).offset(5);
            make.right.top.height.equalTo(_bottomView);
        }];

        NSString *serviceStr= [NSString stringWithFormat:@"《用户注册协议》"];
        NSString *privacyStr = [NSString stringWithFormat:@"《隐私协议》"];
        NSString *protocolStr = [NSString stringWithFormat:@"我已阅读并同意%@ %@", serviceStr, privacyStr];

        NSMutableAttributedString *privacy = [[NSMutableAttributedString alloc]initWithString:[NSString stringWithFormat:@"%@",protocolStr] attributes:@{NSFontAttributeName:[UIFont systemFontOfSize:13],NSForegroundColorAttributeName:[UIColor grayColor]}];
        
        NSRange privacyRange = [protocolStr rangeOfString:privacyStr];
        [privacy addAttribute:NSLinkAttributeName value:@"privacy://" range:privacyRange];

           
        NSRange serviceRange = [protocolStr rangeOfString:serviceStr];
        [privacy addAttribute:NSLinkAttributeName value:@"service://" range:serviceRange];

        protocolTV.linkTextAttributes = @{NSForegroundColorAttributeName:kPrimaryColor};
        protocolTV.attributedText = privacy;
    }
    return _bottomView;
}

- (SAGetProtocolApiManager *)protocolManager {
    if (_protocolManager == nil) {
        _protocolManager = [[SAGetProtocolApiManager alloc] init];
        _protocolManager.delegate = self;
        _protocolManager.paramSource = self;
        _protocolManager.methodSource = self;
    }
    return _protocolManager;
}

@end

