

#import <Foundation/Foundation.h>
#import <MJExtension.h>

NS_ASSUME_NONNULL_BEGIN

@interface SAProductModel : NSObject

@property (nonatomic, strong) NSString *productCode;
@property (nonatomic, strong) NSString *productName;
@property (nonatomic, strong) NSString *productStar;
@property (nonatomic, strong) NSString *productIconUrl;
@property (nonatomic, strong) NSString *productJumpUrl;
@property (nonatomic, strong) NSString *loanAmount;
@property (nonatomic, strong) NSString *loanTerm;
@property (nonatomic, strong) NSString *apr;

@property (nonatomic, strong) NSString *buttonDesc;
@property (nonatomic, assign) NSInteger applyStatus;
@property (nonatomic, strong) NSString *orderStatusDesc;



+ (instancetype)objectWithDict:(NSDictionary *)dict;

@end

NS_ASSUME_NONNULL_END
