
#import "SAString+Size.h"

@implementation NSString (Size)

- (CGFloat)widthWithFont:(UIFont *)font
{
    return [self widthWithFont:font maxWidth:0];
}

- (CGFloat)widthWithFont:(UIFont *)font maxWidth:(CGFloat)maxWidth
{
    if (self.length == 0) {
        return 0.0;
    }
    
    CGSize textSize;
    
        if (maxWidth == 0) {
            textSize = [self textSizeWithFont:font constrainedToSize:CGSizeMake(0, 21)];
        } else {
            textSize = [self textSizeWithFont:font constrainedToSize:CGSizeMake(maxWidth, 21)];
        }
    
    return textSize.width;
}

- (CGFloat)heightWithFixWidth:(CGFloat)width font:(UIFont *)font
{
    if (self.length == 0) {
        return 0.0;
    }
    
    CGSize textSize;
        textSize = [self textSizeWithFont:font constrainedToSize:CGSizeMake(width, 0)];
    
    return textSize.height;
}

- (CGSize)textSizeWithFont:(UIFont *)font
{
    return [self textSizeWithFont:font constrainedToSize:CGSizeZero];
}

- (CGSize)textSizeWithFont:(UIFont *)font constrainedToSize:(CGSize)size
{
    CGSize textSize;
    
    if (CGSizeEqualToSize(size, CGSizeZero)) {
        
        NSDictionary *attributes = [NSDictionary dictionaryWithObject:font
                                                               forKey:NSFontAttributeName];
        textSize = [self sizeWithAttributes:attributes];
        
    } else {
        
        
        if (!font) {
            font = [UIFont systemFontOfSize:17];
        }
        
        NSDictionary *attribute = @{NSFontAttributeName:font};
        textSize = [self boundingRectWithSize:size
                                      options:NSStringDrawingTruncatesLastVisibleLine | NSStringDrawingUsesLineFragmentOrigin | NSStringDrawingUsesFontLeading
                                   attributes:attribute
                                      context:nil].size;
        textSize.height = ceil(textSize.height);
        textSize.width = ceil(textSize.width);
    }
    
    return textSize;
}

- (CGSize)textDrawAtPoint:(CGPoint)point withFont:(UIFont *)font
{
    NSDictionary *attribute = @{NSFontAttributeName:font};
    return [self sizeWithAttributes:attribute];
}

- (CGSize)textDrawInRect:(CGRect)rect withFont:(UIFont *)font
{
    NSDictionary *attribute = @{NSFontAttributeName:font};
    
    return [self sizeWithAttributes:attribute];
}

- (CGSize)textDrawInRect:(CGRect)rect withFont:(UIFont *)font lineBreakMode:(NSLineBreakMode)lineBreakMode
{
    NSDictionary *attribute = @{NSFontAttributeName:font};
    
    return [self sizeWithAttributes:attribute];
}

- (CGSize)textDrawInRect:(CGRect)rect withFont:(UIFont *)font lineBreakMode:(NSLineBreakMode)lineBreakMode alignment:(NSTextAlignment)textAlignment
{
    NSDictionary *attribute = @{NSFontAttributeName:font};
    
    return [self sizeWithAttributes:attribute];
}

@end
