
#import "SAMainTopBgView.h"
#import "SAGradientView.h"
#import "SATabBarViewController.h"
#import "SACommonWebViewController.h"
#import "SAServiceView.h"

@interface SAMainTopBgView ()

@property (nonatomic, strong) SAGradientView *bgView;
@property (nonatomic, strong) UIImageView *avatarImg;
@property (nonatomic, strong) UILabel *mobileLab;
@property (nonatomic, strong) UILabel *titleLab;
@property (nonatomic, strong) UIImageView *serviceIcon;


@property (nonatomic, strong) SAServiceView *serviceView;

@end

@implementation SAMainTopBgView

- (instancetype)init
{
    if (self = [super init])
    {
        [self addCustomViews];
        
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(jumpService)];
        self.serviceIcon.userInteractionEnabled = YES;
        [self.serviceIcon addGestureRecognizer:tap];
    }
    return self;
}

- (void)addCustomViews
{
    [self addSubview:self.bgView];
    [self.bgView addSubview:self.titleLab];
    [self.bgView addSubview:self.avatarImg];
    [self.bgView addSubview:self.mobileLab];
    [self.bgView addSubview:self.serviceIcon];
    
}

- (void)layoutSubviews
{
    [super layoutSubviews];
    self.bgView.layer.masksToBounds = YES;
    [self.bgView mas_makeConstraints:^(SAConstraintMaker *make) {
        make.top.left.right.bottom.equalTo(self);
    }];
    
    
    [self.titleLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.centerX.equalTo(self);
        make.top.equalTo(self).offset(kNavBarHeight+4);
    }];
    
    [self.avatarImg mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.equalTo(self).offset(20);
        make.top.equalTo(self.titleLab.mas_bottom).offset(18);
        make.width.height.mas_equalTo(66);
    }];
    self.avatarImg.layer.masksToBounds = YES;
    self.avatarImg.layer.cornerRadius = 33;
    self.avatarImg.backgroundColor = kMinorColor;
        
    [self.mobileLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.equalTo(self.avatarImg.mas_right).offset(15);
        make.centerY.equalTo(self.avatarImg);
    }];
    
    
    [self.serviceIcon mas_makeConstraints:^(SAConstraintMaker *make) {
        make.centerY.equalTo(self.avatarImg);
        make.right.equalTo(self.bgView).offset(-20);
        make.width.height.mas_equalTo(36);
    }];
}

- (void)setModel:(SAUserCenterModel *)model 
{
    _model = model;
    self.titleLab.text = @"我的";
    self.mobileLab.text = [NSString judgeStringExist:model.mobile] ? model.mobile : @"";
}

- (void)jumpService
{
    if(![NSString judgeStringExist:[SAAdvanceManager sharedInstance].customerServiceUrl]) return;
    
    SACommonWebViewController *webVC = [[SACommonWebViewController alloc] init];
    webVC.url = [SAAdvanceManager sharedInstance].customerServiceUrl;
    webVC.navTitle = @"在线客服";
    webVC.hidesBottomBarWhenPushed = YES;
    [[SACommonTool currentViewController].navigationController pushViewController:webVC animated:NO];
}

#pragma mark - getters

- (SAGradientView *)bgView{
    if(_bgView == nil){
        _bgView = [SAGradientView new];
    }
    return _bgView;
}

- (UIImageView *)avatarImg {
    if (_avatarImg == nil) {
        _avatarImg = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"me_default_icon"]];
        _avatarImg.contentMode = UIViewContentModeScaleAspectFill;
    }
    return _avatarImg;
}

- (UILabel *)mobileLab{
    FF_Fetch_UILable_Alignment(_mobileLab, [UIColor blackColor], BoldFont_XX6(18), NSTextAlignmentLeft)
}

- (SAServiceView *)serviceView{
    if(_serviceView == nil){
        _serviceView = [[SAServiceView alloc] init];
    }
    return _serviceView;
}

- (UILabel *)titleLab{
    FF_Fetch_UILable_CenterX(_titleLab, [UIColor blackColor], BoldFont_XX6(18))
}

- (UIImageView *)serviceIcon{
    FF_Fetch_UIImageViewWithImage(_serviceIcon, [UIImage imageNamed:@"customer_service_mine"])
}

@end
