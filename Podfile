# Uncomment the next line to define a global platform for your project
platform :ios, '12.0'
ENV['SWIFT_VERSION'] = '4.0'

# Uncomment this line if you're using Swift
use_frameworks!
#use_frameworks! :linkage => :static

# use_modular_headers!

source 'https://github.com/CocoaPods/Specs.git'

workspace 'StagingApp'

project 'StagingApp.xcodeproj'


def shared_pod
    pod 'HandyFrame',               '1.1.2'
    pod 'MBProgressHUD'
    pod 'IQKeyboardManager'
    pod 'AFNetworking'
    pod 'WebViewJavascriptBridge',  '~> 6.0'
    pod 'MJExtension'
    pod 'AliyunOSSiOS'
    pod 'Reachability'
    pod 'SDWebImage'
    pod 'AWSMobileClient', :modular_headers => true
    pod 'AWSPinpoint', :modular_headers => true
    pod 'AWSS3', :modular_headers => true
    pod 'JhtMarquee'
    pod 'CRBoxInputView', '1.2.1'
    pod 'YYModel', '~> 1.0.4'
    pod 'YYCache', '~> 1.0.4'
    pod 'TencentCloudHuiyanSDKFace_framework', :path => './StagingApp/Vendor/TencentCloudHuiyanSDKFace_framework'
    pod 'TYCyclePagerView'

end


targetNameArray = [
  "StagingApp",
]

post_install do |installer|
    installer.generated_projects.each do |project|
        project.targets.each do |target|
            target.build_configurations.each do |config|
                config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '13.0'
#                config.build_settings['CODE_SIGNING_ALLOWED'] = 'NO'
            end
        end
    end
end

targetNameArray.each do |targetName|
    
    puts targetName + " Pod Installing..."
    
    target :"#{targetName}" do
        shared_pod
        project 'StagingApp.xcodeproj'
    end
    
end

