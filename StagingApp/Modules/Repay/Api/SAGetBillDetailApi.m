
#import "SAGetBillDetailApi.h"

@implementation SAGetBillDetailApi
#pragma mark - life cycle

- (instancetype)init
{
    if (self = [super init])
    {
        self.validator = self;
    }
    return self;
}

#pragma mark - CTAPIManager

- (NSString *)methodName
{
    return @"9616bd02c882499eb8b561c19d1ff602";
}

- (NSString *)serviceType
{
    return kNPBasicServiceKey;
}

- (QLAPIManagerRequestType)requestType
{
    return QLAPIManagerRequestTypeGet;
}

- (BOOL)shouldCache
{
    return NO;
}

#pragma mark - QLAPIManagerDataReformer

- (id)manager:(SABaseManager *)manager reformData:(NSDictionary *)data
{
    if ([manager isKindOfClass:[self class]])
    {
        return [data objectForKey:@"data"];
    }
    return nil;
}

#pragma mark - QLAPIManagerValidator

- (BOOL)manager:(SABaseManager *)manager isCorrectWithParamsData:(NSDictionary *)data
{
    return YES;
}

- (BOOL)manager:(SABaseManager *)manager isCorrectWithCallBackData:(id)data
{
    return [SAResponseHandle processRespDictCodeZero:data];
}
@end
