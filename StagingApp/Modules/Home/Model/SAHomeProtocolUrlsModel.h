
#import <Foundation/Foundation.h>
#import "SAHomeProtocolModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface SAHomeProtocolUrlsModel : NSObject

@property (nonatomic, strong)SAHomeProtocolModel *helpCenter;
@property (nonatomic, strong)SAHomeProtocolModel *aboutUs;
@property (nonatomic, strong)SAHomeProtocolModel *mobileBank;
@property (nonatomic, strong)SAHomeProtocolModel *loanAgreement;
@property (nonatomic, strong)SAHomeProtocolModel *internetBanking;
@property (nonatomic, strong)SAHomeProtocolModel *privacyAgreement;
@property (nonatomic, strong)SAHomeProtocolModel *alfamart;
@property (nonatomic, strong)SAHomeProtocolModel *termsService;
@property (nonatomic, strong)SAHomeProtocolModel *ATM;

@end

NS_ASSUME_NONNULL_END
