
#import "SAPickerTextField.h"

@implementation SAPickerTextField

- (BOOL)canPerformAction:(SEL)action withSender:(id)sender {
    if (action == @selector(paste:))
        return self.isPicker;
    if (action == @selector(select:))
        return self.isPicker;
    if (action == @selector(selectAll:))
        return self.isPicker;
    return [super canPerformAction:action withSender:sender];
}

@end
