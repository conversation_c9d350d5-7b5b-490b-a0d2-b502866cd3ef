
#import <Foundation/Foundation.h>
#import "SAResponse.h"
#import "QLNetworkEnum.h"
#import "QLAPIManagerInterceptor.h"
#import "QLAPIManagerValidator.h"
#import "QLAPIManager.h"
#import "QLAPIManagerDataReformer.h"
#import "SANetworkConst.h"
#import "SAResponseHandle.h"

static NSString * const kNPAPIBaseManagerRequestID = @"kNPAPIBaseManagerRequestID";

@class SABaseManager;
#pragma mark - api回调
@protocol QLAPIManagerCallBackDelegate <NSObject>

@required
- (void)managerCallAPIDidSuccess:(SABaseManager *)manager;
- (void)managerCallAPIDidFailed:(SABaseManager *)manager;

@end

#pragma mark - 让manager能够获取调用API所需要的请求参数
@protocol QLAPIManagerParamSource <NSObject>

@required
- (NSDictionary *)paramsForApi:(SABaseManager *)manager;

@optional
- (id)dataForUploadApi:(SABaseManager *)manager;

@end


@interface SABaseManager : NSObject

@property (nonatomic, weak) id<QLAPIManagerCallBackDelegate> delegate;
@property (nonatomic, weak) id<QLAPIManagerParamSource>     paramSource;
@property (nonatomic, weak) id<QLAPIManagerValidator>       validator;
@property (nonatomic, weak) NSObject<QLAPIManager>          *child; 
@property (nonatomic, weak) id<QLAPIManagerInterceptor>     interceptor;

 
@property (nonatomic, copy, readonly)   NSString *errorMessage;
@property (nonatomic, readonly)         QLAPIManagerErrorType errorType;
@property (nonatomic, strong)           SAResponse *response;

@property (nonatomic, assign, readonly) BOOL isReachable;
@property (nonatomic, assign, readonly) BOOL isLoading;

- (id)fetchDataWithReformer:(id<QLAPIManagerDataReformer>)reformer;

- (id)fetchFailedRequstMsg:(id<QLAPIManagerDataReformer>)reformer;

- (NSInteger)loadData;

- (void)cancelAllRequests;
- (void)cancelRequestWithRequestId:(NSInteger)requestID;

- (BOOL)beforePerformSuccessWithResponse:(SAResponse *)response;
- (void)afterPerformSuccessWithResponse:(SAResponse *)response;

- (BOOL)beforePerformFailWithResponse:(SAResponse *)response;
- (void)afterPerformFailWithResponse:(SAResponse *)response;

- (BOOL)shouldCallAPIWithParams:(NSDictionary *)params;
- (void)afterCallingAPIWithParams:(NSDictionary *)params;

 
- (NSDictionary *)reformParams:(NSDictionary *)params;
- (void)cleanData;
- (BOOL)shouldCache;

- (void)successedOnCallingAPI:(SAResponse *)response;
- (void)failedOnCallingAPI:(SAResponse *)response withErrorType:(QLAPIManagerErrorType)errorType;

@end
