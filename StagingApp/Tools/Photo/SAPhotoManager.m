
#import "SAPhotoManager.h"

#import <Photos/PHPhotoLibrary.h>
#import <AVFoundation/AVCaptureDevice.h>
#import <AVFoundation/AVMediaFormat.h>

@implementation SAPhotoManager

+ (BOOL)AlbumAuthorization {
    
    if(![UIImagePickerController availableMediaTypesForSourceType:UIImagePickerControllerSourceTypePhotoLibrary]) {
        UIAlertController *alertVc = [UIAlertController alertControllerWithTitle:NSLocalizedString(@"ixedFoot", nil)
                                                                         message:NSLocalizedString(@"const_hLabelReal", nil) preferredStyle:UIAlertControllerStyleAlert];
        UIAlertAction *sure = [UIAlertAction actionWithTitle:NSLocalizedString(@"asiceAbedPload", nil) style:UIAlertActionStyleDefault handler:nil];
        [alertVc addAction:sure];
        
        [[SACommonTool currentViewController] presentViewController:alertVc animated:YES completion:nil];
    }
    
    PHAuthorizationStatus status = [PHPhotoLibrary authorizationStatus];
    if (status == PHAuthorizationStatusRestricted || status == PHAuthorizationStatusDenied) {
        
        
        UIAlertController *alertVc = [UIAlertController alertControllerWithTitle:NSLocalizedString(@"ixedFoot", nil)
                                                                         message:NSLocalizedString(@"protocol_mPanel", nil) preferredStyle:UIAlertControllerStyleAlert];
        UIAlertAction *cancel = [UIAlertAction actionWithTitle:NSLocalizedString(@"serviceCtionDebug", nil) style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
            
        }];
        
        UIAlertAction *sure = [UIAlertAction actionWithTitle:NSLocalizedString(@"handleRotocolMasonry", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            
            if (@available(iOS 10.0, *)) {
                NSURL *url = [NSURL URLWithString:UIApplicationOpenSettingsURLString];
                [[UIApplication sharedApplication] openURL:url options:@{}
                                         completionHandler:^(BOOL success) {
                                         }];
            } else {
                
                
                NSString *schemesStr = [NSString stringWithFormat:@"prefs:root=Privacy&path=PHOTOS/%@", [SADeviceTool bundleId]];
                NSURL *url = [NSURL URLWithString:schemesStr];
                if ([[UIApplication sharedApplication] canOpenURL:url]) {
                    [[UIApplication sharedApplication] openURL: url];
                }
            }
            
        }];
        [alertVc addAction:cancel];
        [alertVc addAction:sure];
        
        [[SACommonTool currentViewController] presentViewController:alertVc animated:YES completion:nil];
        
        return NO;
    }
    
    return YES;
}

+ (BOOL)CameraAuthorization {
    
    
    if (![UIImagePickerController isSourceTypeAvailable:UIImagePickerControllerSourceTypeCamera]) {
        UIAlertController *alertVc = [UIAlertController alertControllerWithTitle:NSLocalizedString(@"ixedFoot", nil) message:NSLocalizedString(@"tringOntactCarrier", nil) preferredStyle:UIAlertControllerStyleAlert];
        UIAlertAction *sure = [UIAlertAction actionWithTitle:NSLocalizedString(@"asiceAbedPload", nil) style:UIAlertActionStyleDefault handler:nil];
        [alertVc addAction:sure];
        
        [[SACommonTool currentViewController] presentViewController:alertVc animated:YES completion:nil];
    }
    
    AVAuthorizationStatus status = [AVCaptureDevice authorizationStatusForMediaType:AVMediaTypeVideo];
    if (status == AVAuthorizationStatusRestricted || status == AVAuthorizationStatusDenied)
    {
        
        
        UIAlertController *alertVc = [UIAlertController alertControllerWithTitle:NSLocalizedString(@"ixedFoot", nil)
                                                                         message:NSLocalizedString(@"recordInput", nil) preferredStyle:UIAlertControllerStyleAlert];
        UIAlertAction *cancel = [UIAlertAction actionWithTitle:NSLocalizedString(@"serviceCtionDebug", nil) style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
            
        }];
        
        UIAlertAction *sure = [UIAlertAction actionWithTitle:NSLocalizedString(@"handleRotocolMasonry", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            
            if (@available(iOS 10.0, *)) {
                NSURL *url = [NSURL URLWithString:UIApplicationOpenSettingsURLString];
                [[UIApplication sharedApplication] openURL:url options:@{}
                                         completionHandler:^(BOOL success) {
                                         }];
            } else {
                
                NSString *schemesStr = [NSString stringWithFormat:@"prefs:root=Privacy&path=CAMERA/%@", [SADeviceTool bundleId]];
                NSURL *url = [NSURL URLWithString:schemesStr];
                if ([[UIApplication sharedApplication] canOpenURL:url]) {
                    [[UIApplication sharedApplication] openURL: url];
                }
            }
            
        }];
        [alertVc addAction:cancel];
        [alertVc addAction:sure];
        
        [[SACommonTool currentViewController] presentViewController:alertVc animated:YES completion:nil];
        
        return NO;
    }
    
    return YES;
}


+ (NSData *)compressOriginalImage:(UIImage *)image toMaxDataSizeKBytes:(NSUInteger)maxLength {
    
    CGFloat compression = 1;
    NSData *data = UIImageJPEGRepresentation(image, compression);
    if (data.length < maxLength) return data;
    
    CGFloat max = 1;
    CGFloat min = 0;
    for (int i = 0; i < 6; ++i) {
        compression = (max + min) / 2;
        data = UIImageJPEGRepresentation(image, compression);
        if (data.length < maxLength * 0.9) {
            min = compression;
        } else if (data.length > maxLength) {
            max = compression;
        } else {
            break;
        }
    }
    UIImage *resultImage = [UIImage imageWithData:data];
    if (data.length < maxLength) return data;
    
    
    NSUInteger lastDataLength = 0;
    while (data.length > maxLength && data.length != lastDataLength) {
        lastDataLength = data.length;
        CGFloat ratio = (CGFloat)maxLength / data.length;
        CGSize size = CGSizeMake((NSUInteger)(resultImage.size.width * sqrtf(ratio)),
                                 (NSUInteger)(resultImage.size.height * sqrtf(ratio))); 
        UIGraphicsBeginImageContext(size);
        [resultImage drawInRect:CGRectMake(0, 0, size.width, size.height)];
        resultImage = UIGraphicsGetImageFromCurrentImageContext();
        UIGraphicsEndImageContext();
        data = UIImageJPEGRepresentation(resultImage, compression);
    }
    
    return data;
}

@end
