
#import "MASUtilities.h"
#import "SAConstraintMaker.h"
#import "SAViewAttribute.h"

 
@interface MAS_VIEW (MASAdditions)

 
@property (nonatomic, strong, readonly) SAViewAttribute *mas_left;
@property (nonatomic, strong, readonly) SAViewAttribute *mas_top;
@property (nonatomic, strong, readonly) SAViewAttribute *mas_right;
@property (nonatomic, strong, readonly) SAViewAttribute *mas_bottom;
@property (nonatomic, strong, readonly) SAViewAttribute *mas_leading;
@property (nonatomic, strong, readonly) SAViewAttribute *mas_trailing;
@property (nonatomic, strong, readonly) SAViewAttribute *mas_width;
@property (nonatomic, strong, readonly) SAViewAttribute *mas_height;
@property (nonatomic, strong, readonly) SAViewAttribute *mas_centerX;
@property (nonatomic, strong, readonly) SAViewAttribute *mas_centerY;
@property (nonatomic, strong, readonly) SAViewAttribute *mas_baseline;
@property (nonatomic, strong, readonly) SAViewAttribute *(^mas_attribute)(NSLayoutAttribute attr);

#if (__IPHONE_OS_VERSION_MIN_REQUIRED >= 80000) || (__TV_OS_VERSION_MIN_REQUIRED >= 9000) || (__MAC_OS_X_VERSION_MIN_REQUIRED >= 101100)

@property (nonatomic, strong, readonly) SAViewAttribute *mas_firstBaseline;
@property (nonatomic, strong, readonly) SAViewAttribute *mas_lastBaseline;

#endif

#if (__IPHONE_OS_VERSION_MIN_REQUIRED >= 80000) || (__TV_OS_VERSION_MIN_REQUIRED >= 9000)

@property (nonatomic, strong, readonly) SAViewAttribute *mas_leftMargin;
@property (nonatomic, strong, readonly) SAViewAttribute *mas_rightMargin;
@property (nonatomic, strong, readonly) SAViewAttribute *mas_topMargin;
@property (nonatomic, strong, readonly) SAViewAttribute *mas_bottomMargin;
@property (nonatomic, strong, readonly) SAViewAttribute *mas_leadingMargin;
@property (nonatomic, strong, readonly) SAViewAttribute *mas_trailingMargin;
@property (nonatomic, strong, readonly) SAViewAttribute *mas_centerXWithinMargins;
@property (nonatomic, strong, readonly) SAViewAttribute *mas_centerYWithinMargins;

#endif

#if (__IPHONE_OS_VERSION_MAX_ALLOWED >= 110000) || (__TV_OS_VERSION_MAX_ALLOWED >= 110000)

@property (nonatomic, strong, readonly) SAViewAttribute *mas_safeAreaLayoutGuide API_AVAILABLE(ios(11.0),tvos(11.0));
@property (nonatomic, strong, readonly) SAViewAttribute *mas_safeAreaLayoutGuideTop API_AVAILABLE(ios(11.0),tvos(11.0));
@property (nonatomic, strong, readonly) SAViewAttribute *mas_safeAreaLayoutGuideBottom API_AVAILABLE(ios(11.0),tvos(11.0));
@property (nonatomic, strong, readonly) SAViewAttribute *mas_safeAreaLayoutGuideLeft API_AVAILABLE(ios(11.0),tvos(11.0));
@property (nonatomic, strong, readonly) SAViewAttribute *mas_safeAreaLayoutGuideRight API_AVAILABLE(ios(11.0),tvos(11.0));

#endif

 
@property (nonatomic, strong) id mas_key;

 
- (instancetype)mas_closestCommonSuperview:(MAS_VIEW *)view;

 
- (NSArray *)mas_makeConstraints:(void(NS_NOESCAPE ^)(SAConstraintMaker *make))block;

 
- (NSArray *)mas_updateConstraints:(void(NS_NOESCAPE ^)(SAConstraintMaker *make))block;

 
- (NSArray *)mas_remakeConstraints:(void(NS_NOESCAPE ^)(SAConstraintMaker *make))block;

@end
