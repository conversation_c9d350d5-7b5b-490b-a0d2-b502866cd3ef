
#if __has_include(<MJRefresh/SARefreshBackStateFooter.h>)
#import <MJRefresh/SARefreshBackStateFooter.h>
#else
#import "SARefreshBackStateFooter.h"
#endif

NS_ASSUME_NONNULL_BEGIN

@interface SARefreshBackGifFooter : SARefreshBackStateFooter
@property (weak, nonatomic, readonly) UIImageView *gifView;

 
- (instancetype)setImages:(NSArray *)images duration:(NSTimeInterval)duration forState:(MJRefreshState)state;
- (instancetype)setImages:(NSArray *)images forState:(MJRefreshState)state;
@end

NS_ASSUME_NONNULL_END
