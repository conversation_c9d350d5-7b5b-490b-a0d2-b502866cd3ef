
#import <Foundation/Foundation.h>
#import<CommonCrypto/CommonDigest.h>

@interface SAObjC : NSObject


 
+ (NSString *)encryptString:(NSString *)str publicKey:(NSString *)pubKey;


 
+ (NSData *)encryptData:(NSData *)data publicKey:(NSString *)pubKey;



 
+ (NSString *)decryptString:(NSString *)str privateKey:(NSString *)privKey;


 
+ (NSData *)decryptData:(NSData *)data privateKey:(NSString *)privKey;

 
+ (BOOL)rsaVerifySignature:(NSString *)signature plainString:(NSString *)plainString WithPublicKey:(NSString *)publickey;

 
+ (NSString *)rsaSignString:(NSString *)plainString WithPrivateKey:(NSString *)privateKey;


+ (NSString *)encodeString:(NSString*)unencodedString;

@end










