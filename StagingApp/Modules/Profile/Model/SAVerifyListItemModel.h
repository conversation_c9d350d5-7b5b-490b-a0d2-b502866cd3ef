
#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface SAVerifyListItemModel : NSObject

@property (nonatomic, copy)NSString *code;
@property (nonatomic, copy)NSString *desc;
@property (nonatomic, copy) NSString *subDescription;
@property (nonatomic, copy)NSString *productCategoryCode;
@property (nonatomic, copy)NSString *jumpUrl;
@property (nonatomic, copy)NSString *nextJumpUrl;
@property (nonatomic, copy)NSString *authLogoUrl;
@property (nonatomic, assign)NSInteger sort;
@property (nonatomic, assign)NSInteger status;       
@property (nonatomic, copy)NSString *statusText;
@property (nonatomic, assign)BOOL enable;
@property (nonatomic, copy)NSString *disableText;
@property (nonatomic, copy)NSString *tipText;
@property (nonatomic, copy)NSString *logoUrl;
@property (nonatomic, assign)BOOL auth;
@property (nonatomic, strong) NSString *headerTip;

@property (nonatomic, strong) NSString *serviceType;

@end

NS_ASSUME_NONNULL_END
