// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		0805C28622B1F54600021D25 /* SAVerifyDataListApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 0805C28522B1F54600021D25 /* SAVerifyDataListApiManager.m */; };
		0805C28922B1F6DD00021D25 /* SAVerifyListModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 0805C28822B1F6DD00021D25 /* SAVerifyListModel.m */; };
		0805C28C22B1F6F900021D25 /* SAVerifyListItemModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 0805C28B22B1F6F900021D25 /* SAVerifyListItemModel.m */; };
		0805C2A922B24A7800021D25 /* SAVerifyListManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 0805C2A822B24A7800021D25 /* SAVerifyListManager.m */; };
		0805C2D922B24DF900021D25 /* SAVerifyBasicInfoViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0805C2D822B24DF900021D25 /* SAVerifyBasicInfoViewController.m */; };
		0805C2F122B2570800021D25 /* SAGetPersonalInfoApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 0805C2F022B2570800021D25 /* SAGetPersonalInfoApiManager.m */; };
		0805C2F422B25B2000021D25 /* SAPersonalInfoModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 0805C2F322B25B2000021D25 /* SAPersonalInfoModel.m */; };
		0805C2FA22B25C5B00021D25 /* SAPersonalInfoCheckModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 0805C2F922B25C5B00021D25 /* SAPersonalInfoCheckModel.m */; };
		0811F47222CB41D000CDBBD1 /* SAErrorView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0811F47122CB41D000CDBBD1 /* SAErrorView.m */; };
		082B160822BB871600A4C7C7 /* SAPayClientInfoApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 082B160722BB871600A4C7C7 /* SAPayClientInfoApiManager.m */; };
		082B160B22BB8CCA00A4C7C7 /* SAPayWayModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 082B160A22BB8CCA00A4C7C7 /* SAPayWayModel.m */; };
		082B162422BBAECA00A4C7C7 /* SAPayClientChargesApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 082B162322BBAECA00A4C7C7 /* SAPayClientChargesApiManager.m */; };
		083AB35422B36DEA003B4BB4 /* SAPersonalVerifyModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 083AB35322B36DEA003B4BB4 /* SAPersonalVerifyModel.m */; };
		083D64DA2313E2E400810B26 /* SASubmitProductInfoModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 083D64D92313E2E400810B26 /* SASubmitProductInfoModel.m */; };
		0845D8CD22AE6ACF0044AB4D /* SABasicViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D8C622AE6ACF0044AB4D /* SABasicViewController.m */; };
		0845D8CE22AE6ACF0044AB4D /* SATabBarViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D8C722AE6ACF0044AB4D /* SATabBarViewController.m */; };
		0845D8CF22AE6ACF0044AB4D /* SANavigationViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D8CB22AE6ACF0044AB4D /* SANavigationViewController.m */; };
		0845D91522AE6BB40044AB4D /* SANoNetView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D91322AE6BB40044AB4D /* SANoNetView.m */; };
		0845D94A22AE6C880044AB4D /* SAHomePageViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D93F22AE6C880044AB4D /* SAHomePageViewController.m */; };
		0845D94C22AE6C880044AB4D /* SAMineViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D94522AE6C880044AB4D /* SAMineViewController.m */; };
		0845D95422AE6D1D0044AB4D /* SALoginViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D95022AE6D1D0044AB4D /* SALoginViewController.m */; };
		0845D98322AE6D680044AB4D /* SATool.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D95822AE6D670044AB4D /* SATool.m */; };
		0845D98422AE6D680044AB4D /* SACommonTool.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D95922AE6D670044AB4D /* SACommonTool.m */; };
		0845D98522AE6D680044AB4D /* SANormalRefresh.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D95C22AE6D670044AB4D /* SANormalRefresh.m */; };
		0845D98622AE6D680044AB4D /* SAMutableDictionary+Extension.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D96022AE6D670044AB4D /* SAMutableDictionary+Extension.m */; };
		0845D98722AE6D680044AB4D /* SADictionary+Common.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D96322AE6D680044AB4D /* SADictionary+Common.m */; };
		0845D98822AE6D680044AB4D /* SAColor+Extension.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D96622AE6D680044AB4D /* SAColor+Extension.m */; };
		0845D98922AE6D680044AB4D /* SAAlertController+Orientation.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D96822AE6D680044AB4D /* SAAlertController+Orientation.m */; };
		0845D98A22AE6D680044AB4D /* SAView+Extension.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D96C22AE6D680044AB4D /* SAView+Extension.m */; };
		0845D98B22AE6D680044AB4D /* SAFont+Extension.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D96F22AE6D680044AB4D /* SAFont+Extension.m */; };
		0845D98C22AE6D680044AB4D /* SAString+Extension.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D97222AE6D680044AB4D /* SAString+Extension.m */; };
		0845D98D22AE6D680044AB4D /* SADate+Extension.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D97522AE6D680044AB4D /* SADate+Extension.m */; };
		0845D98E22AE6D680044AB4D /* SALocationManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D97822AE6D680044AB4D /* SALocationManager.m */; };
		0845D99022AE6D680044AB4D /* SADeviceTool.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D97D22AE6D680044AB4D /* SADeviceTool.m */; };
		0845D99122AE6D680044AB4D /* SABCTool.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D97E22AE6D680044AB4D /* SABCTool.m */; };
		0845D99222AE6D680044AB4D /* SAKeyChainTool.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D98022AE6D680044AB4D /* SAKeyChainTool.m */; };
		0856E5C322B2793C00D5C183 /* SACenterYTextView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0856E5C222B2793C00D5C183 /* SACenterYTextView.m */; };
		0856E5C622B279CB00D5C183 /* SAVerifyInfoCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 0856E5C522B279CB00D5C183 /* SAVerifyInfoCell.m */; };
		08675B5C22C1F7EC007CCC9F /* CoreMotion.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 08675B5B22C1F7EB007CCC9F /* CoreMotion.framework */; };
		0878030122B0967D000A7A4F /* SAGradientView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0878030022B0967D000A7A4F /* SAGradientView.m */; };
		0878033622B0E898000A7A4F /* SAUserCenterApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 0878033522B0E898000A7A4F /* SAUserCenterApiManager.m */; };
		0878034E22B10C35000A7A4F /* SAVerifyCodeApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 0878034D22B10C35000A7A4F /* SAVerifyCodeApiManager.m */; };
		0878035222B110B8000A7A4F /* SAVerifyCodeInputView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0878035122B110B8000A7A4F /* SAVerifyCodeInputView.m */; };
		0878035522B138E9000A7A4F /* SAUserCenterModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 0878035422B138E9000A7A4F /* SAUserCenterModel.m */; };
		0880D0C222BB2357001C979F /* SAAuditProgressItemModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 0880D0C122BB2357001C979F /* SAAuditProgressItemModel.m */; };
		089A56BA22E9B3FC005BEBD5 /* SASaveDeviceChannelApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 089A56B922E9B3FC005BEBD5 /* SASaveDeviceChannelApiManager.m */; };
		08A6BAED22B1E67F001CA577 /* SADeviceInfoSaveApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 08A6BAEC22B1E67F001CA577 /* SADeviceInfoSaveApiManager.m */; };
		08AB288D22BF606900CAE59C /* SAWifiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 08AB288C22BF606900CAE59C /* SAWifiManager.m */; };
		08B1EC4922B8815A001367BB /* SAShapeLayer+Extension.m in Sources */ = {isa = PBXBuildFile; fileRef = 08B1EC4822B8815A001367BB /* SAShapeLayer+Extension.m */; };
		08B1EC4C22B88754001367BB /* SACommonWebViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 08B1EC4B22B88754001367BB /* SACommonWebViewController.m */; };
		08B1EC6322B8B46C001367BB /* SAActionSheet.m in Sources */ = {isa = PBXBuildFile; fileRef = 08B1EC6222B8B46C001367BB /* SAActionSheet.m */; };
		08B1ECC122B8CEC5001367BB /* SAPhotoManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 08B1ECC022B8CEC5001367BB /* SAPhotoManager.m */; };
		08B1ED1522B8DC14001367BB /* SATokenApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 08B1ED1422B8DC14001367BB /* SATokenApiManager.m */; };
		08B1ED2422B8DDCE001367BB /* SAAliyunOSSManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 08B1ED2322B8DDCE001367BB /* SAAliyunOSSManager.m */; };
		08B4A93E22B79A0D006B35D4 /* SAContactsManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 08B4A93D22B79A0D006B35D4 /* SAContactsManager.m */; };
		08CE109A22A8AA4100E30241 /* SANetworkConst.m in Sources */ = {isa = PBXBuildFile; fileRef = 08CE109922A8AA4100E30241 /* SANetworkConst.m */; };
		08CE10F922A8C2FB00E30241 /* Localizable.strings in Resources */ = {isa = PBXBuildFile; fileRef = 08CE10FB22A8C2FB00E30241 /* Localizable.strings */; };
		08CE115922A8E7A300E30241 /* SAUserManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 08CE115822A8E7A300E30241 /* SAUserManager.m */; };
		08CE116322A8F73F00E30241 /* SAAppCodeManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 08CE116222A8F73F00E30241 /* SAAppCodeManager.m */; };
		08CE116622A8F79400E30241 /* SAAppConfigApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 08CE116522A8F79400E30241 /* SAAppConfigApiManager.m */; };
		08D0A8A122C31B9700D68951 /* SAVersionUpdateViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 08D0A8A022C31B9700D68951 /* SAVersionUpdateViewController.m */; };
		08D0A96B22C37F4B00D68951 /* libc++.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 08D0A96A22C37F4B00D68951 /* libc++.tbd */; };
		08D0A96D22C37F6C00D68951 /* CoreTelephony.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 08D0A96C22C37F6C00D68951 /* CoreTelephony.framework */; };
		08D14F1022BC7E1E0094582F /* SAClientChargesModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 08D14F0F22BC7E1E0094582F /* SAClientChargesModel.m */; };
		08D14F3B22BC7E970094582F /* SARepaymentModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 08D14F3A22BC7E970094582F /* SARepaymentModel.m */; };
		08D14F5B22BCCB4C0094582F /* SAAlertSureTipsViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 08D14F5A22BCCB4C0094582F /* SAAlertSureTipsViewController.m */; };
		08E3B51622A0DB6D00C6BA22 /* JavaScriptCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 08E3B51522A0DB6D00C6BA22 /* JavaScriptCore.framework */; };
		08E66BD022B4F7C7005E62D9 /* SABankListModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 08E66BCF22B4F7C7005E62D9 /* SABankListModel.m */; };
		08EC05D32315245200D3C93C /* SAHistoryOrderModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 08EC05D22315245200D3C93C /* SAHistoryOrderModel.m */; };
		08F4C12322C063380000B366 /* SASaveGPSApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 08F4C12222C063380000B366 /* SASaveGPSApiManager.m */; };
		08F4C18E22C078330000B366 /* SAManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 08F4C18D22C078330000B366 /* SAManager.m */; };
		08F4C1C822C0A63A0000B366 /* AVFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 08F4C1C722C0A63A0000B366 /* AVFoundation.framework */; };
		08F4C1CA22C0A6440000B366 /* CoreMedia.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 08F4C1C922C0A6440000B366 /* CoreMedia.framework */; };
		08F5A3F7231517BF00D75C55 /* SAOrderConfirmApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 08F5A3F6231517BF00D75C55 /* SAOrderConfirmApiManager.m */; };
		08FF04B722AF3EC500DF17E6 /* SAMainHomeApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF04B622AF3EC500DF17E6 /* SAMainHomeApiManager.m */; };
		08FF04EF22AF424E00DF17E6 /* SAMobileLoginApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF04EE22AF424E00DF17E6 /* SAMobileLoginApiManager.m */; };
		08FF04F222AF432800DF17E6 /* SALogoutApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF04F122AF432800DF17E6 /* SALogoutApiManager.m */; };
		08FF04FB22AF455E00DF17E6 /* SAHomeModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF04FA22AF455E00DF17E6 /* SAHomeModel.m */; };
		08FF051122AF539700DF17E6 /* SAHomeProtocolUrlsModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF051022AF539700DF17E6 /* SAHomeProtocolUrlsModel.m */; };
		08FF051422AF53CF00DF17E6 /* SAHomeProtocolModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF051322AF53CF00DF17E6 /* SAHomeProtocolModel.m */; };
		08FF051722AF5A2F00DF17E6 /* SAOrderInfoModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF051622AF5A2F00DF17E6 /* SAOrderInfoModel.m */; };
		08FF051A22AF5BE500DF17E6 /* SABorrowPurposeTypeModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF051922AF5BE500DF17E6 /* SABorrowPurposeTypeModel.m */; };
		08FF051D22AF5C7700DF17E6 /* SABorrowCashModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF051C22AF5C7600DF17E6 /* SABorrowCashModel.m */; };
		08FF052022AF5CCF00DF17E6 /* SABorrowCashPeriodModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF051F22AF5CCF00DF17E6 /* SABorrowCashPeriodModel.m */; };
		08FF054222AF777900DF17E6 /* SAImage+Extension.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF054122AF777900DF17E6 /* SAImage+Extension.m */; };
		08FF056022AF923600DF17E6 /* SAHomeRejectCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF055F22AF923600DF17E6 /* SAHomeRejectCell.m */; };
		08FF056F22AF9B4400DF17E6 /* SAHomeBasicCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF056E22AF9B4400DF17E6 /* SAHomeBasicCell.m */; };
		08FF05B322AFAAE300DF17E6 /* SAMainTopBgView.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF05B222AFAAE300DF17E6 /* SAMainTopBgView.m */; };
		08FF4C2122B604390095863C /* SAPickerTextField.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF4C2022B604390095863C /* SAPickerTextField.m */; };
		08FF4C5922B61C010095863C /* SASavePersonalInfoApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF4C5822B61C010095863C /* SASavePersonalInfoApiManager.m */; };
		08FF4C5F22B61CF20095863C /* SAGetContactInfoApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF4C5E22B61CF20095863C /* SAGetContactInfoApiManager.m */; };
		08FF4C6222B61D040095863C /* SASaveContactInfoApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF4C6122B61D040095863C /* SASaveContactInfoApiManager.m */; };
		08FF4C6C22B623870095863C /* SAContactSelectModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF4C6B22B623870095863C /* SAContactSelectModel.m */; };
		08FF4C7222B626420095863C /* SAContactSectionModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF4C7122B626420095863C /* SAContactSectionModel.m */; };
		08FF4C7522B626580095863C /* SAContactRowModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF4C7422B626580095863C /* SAContactRowModel.m */; };
		22BDEAA5C0D817416B0B2077 /* Pods_StagingApp.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = F80DFFF8ED02B4B42D2E74DE /* Pods_StagingApp.framework */; };
		745683A290BCBD35D46535D2 /* (null) in Frameworks */ = {isa = PBXBuildFile; };
		9A01F53E2ABC2FC10024CC0A /* SAObjC.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F53D2ABC2FC10024CC0A /* SAObjC.m */; };
		9A01F5402ABC2FD30024CC0A /* Security.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 9A01F53F2ABC2FD20024CC0A /* Security.framework */; };
		9A01F57B2ABD33170024CC0A /* SAScrollView+MJRefresh.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F5422ABD33170024CC0A /* SAScrollView+MJRefresh.m */; };
		9A01F57C2ABD33170024CC0A /* SACollectionViewLayout+MJRefresh.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F5432ABD33170024CC0A /* SACollectionViewLayout+MJRefresh.m */; };
		9A01F57D2ABD33170024CC0A /* SARefreshConst.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F5442ABD33170024CC0A /* SARefreshConst.m */; };
		9A01F57E2ABD33170024CC0A /* SARefreshConfig.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F5452ABD33170024CC0A /* SARefreshConfig.m */; };
		9A01F57F2ABD33170024CC0A /* Qdraw.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 9A01F5492ABD33170024CC0A /* Qdraw.bundle */; };
		9A01F5802ABD33170024CC0A /* SAScrollView+MJExtension.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F54B2ABD33170024CC0A /* SAScrollView+MJExtension.m */; };
		9A01F5812ABD33170024CC0A /* SABundle+MJRefresh.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F5502ABD33170024CC0A /* SABundle+MJRefresh.m */; };
		9A01F5822ABD33170024CC0A /* SAView+MJExtension.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F5512ABD33170024CC0A /* SAView+MJExtension.m */; };
		9A01F5832ABD33170024CC0A /* SARefreshBackGifFooter.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F5582ABD33170024CC0A /* SARefreshBackGifFooter.m */; };
		9A01F5842ABD33170024CC0A /* SARefreshBackStateFooter.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F5592ABD33170024CC0A /* SARefreshBackStateFooter.m */; };
		9A01F5852ABD33170024CC0A /* SARefreshBackNormalFooter.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F55A2ABD33170024CC0A /* SARefreshBackNormalFooter.m */; };
		9A01F5862ABD33170024CC0A /* SARefreshAutoStateFooter.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F55F2ABD33170024CC0A /* SARefreshAutoStateFooter.m */; };
		9A01F5872ABD33170024CC0A /* SARefreshAutoGifFooter.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F5602ABD33170024CC0A /* SARefreshAutoGifFooter.m */; };
		9A01F5882ABD33170024CC0A /* SARefreshAutoNormalFooter.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F5612ABD33170024CC0A /* SARefreshAutoNormalFooter.m */; };
		9A01F5892ABD33170024CC0A /* SARefreshStateTrailer.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F5642ABD33170024CC0A /* SARefreshStateTrailer.m */; };
		9A01F58A2ABD33170024CC0A /* SARefreshNormalTrailer.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F5652ABD33170024CC0A /* SARefreshNormalTrailer.m */; };
		9A01F58B2ABD33170024CC0A /* SARefreshNormalHeader.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F5682ABD33170024CC0A /* SARefreshNormalHeader.m */; };
		9A01F58C2ABD33170024CC0A /* SARefreshStateHeader.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F56C2ABD33170024CC0A /* SARefreshStateHeader.m */; };
		9A01F58D2ABD33170024CC0A /* SARefreshGifHeader.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F56D2ABD33170024CC0A /* SARefreshGifHeader.m */; };
		9A01F58E2ABD33170024CC0A /* SARefreshFooter.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F56F2ABD33170024CC0A /* SARefreshFooter.m */; };
		9A01F58F2ABD33170024CC0A /* SARefreshHeader.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F5712ABD33170024CC0A /* SARefreshHeader.m */; };
		9A01F5902ABD33170024CC0A /* SARefreshBackFooter.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F5742ABD33170024CC0A /* SARefreshBackFooter.m */; };
		9A01F5912ABD33170024CC0A /* SARefreshAutoFooter.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F5752ABD33170024CC0A /* SARefreshAutoFooter.m */; };
		9A01F5922ABD33170024CC0A /* SARefreshComponent.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F5772ABD33170024CC0A /* SARefreshComponent.m */; };
		9A01F5932ABD33170024CC0A /* SARefreshTrailer.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F57A2ABD33170024CC0A /* SARefreshTrailer.m */; };
		9A021B1E2AB8622F007EC584 /* SAGetCitiesListApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A021B1D2AB8622F007EC584 /* SAGetCitiesListApiManager.m */; };
		9A02DF552CC656C000431F1B /* SABannerModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A02DF542CC656C000431F1B /* SABannerModel.m */; };
		9A02DF582CC6593400431F1B /* SABannerView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A02DF572CC6593400431F1B /* SABannerView.m */; };
		9A02DF5B2CC65D2600431F1B /* SABannerCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A02DF5A2CC65D2600431F1B /* SABannerCell.m */; };
		9A02DF5E2CC66F0100431F1B /* SAPaidCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A02DF5D2CC66F0100431F1B /* SAPaidCell.m */; };
		9A0951302CBE076C0053D586 /* SAFaceBackAlert.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A09512E2CBE076C0053D586 /* SAFaceBackAlert.m */; };
		9A0A39642A7A02EC0095AB86 /* SASmsCodeViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A0A39632A7A02EC0095AB86 /* SASmsCodeViewController.m */; };
		9A0F351E2AD3CE1F0080FD21 /* VideoToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 9A0F351D2AD3CE1F0080FD21 /* VideoToolbox.framework */; };
		9A0F35232AD3CE8A0080FD21 /* libiconv.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 9A0F35222AD3CE750080FD21 /* libiconv.tbd */; };
		9A0F35242AD3CE950080FD21 /* libz.1.2.5.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 9A0F35212AD3CE540080FD21 /* libz.1.2.5.tbd */; };
		9A0F35252AD3CE9F0080FD21 /* libz.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 9A0F35202AD3CE430080FD21 /* libz.tbd */; };
		9A0F35262AD3CEA90080FD21 /* libbz2.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 9A0F351F2AD3CE370080FD21 /* libbz2.tbd */; };
		9A150B162214092B00840F54 /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A150AA32214092A00840F54 /* AppDelegate.m */; };
		9A150B182214092B00840F54 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A150AA72214092B00840F54 /* main.m */; };
		9A150B192214092B00840F54 /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 9A150AA82214092B00840F54 /* Images.xcassets */; };
		9A150B2D2214092B00840F54 /* SALoggerConfiguration.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A150AE22214092B00840F54 /* SALoggerConfiguration.m */; };
		9A150B2E2214092B00840F54 /* SANetworkingConfigurationManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A150AE32214092B00840F54 /* SANetworkingConfigurationManager.m */; };
		9A150B2F2214092B00840F54 /* SALogger.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A150AE82214092B00840F54 /* SALogger.m */; };
		9A150B302214092B00840F54 /* SAResponse.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A150AED2214092B00840F54 /* SAResponse.m */; };
		9A150B312214092B00840F54 /* SABaseManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A150AF42214092B00840F54 /* SABaseManager.m */; };
		9A150B322214092B00840F54 /* SAApiProxy.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A150AF72214092B00840F54 /* SAApiProxy.m */; };
		9A150B332214092B00840F54 /* SACache.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A150AFC2214092B00840F54 /* SACache.m */; };
		9A150B342214092B00840F54 /* SACachedObject.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A150AFD2214092B00840F54 /* SACachedObject.m */; };
		9A150B352214092B00840F54 /* SARequestGenerator.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A150B002214092B00840F54 /* SARequestGenerator.m */; };
		9A150B362214092B00840F54 /* SARequest+QLNetworkingMethods.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A150B032214092B00840F54 /* SARequest+QLNetworkingMethods.m */; };
		9A150B372214092B00840F54 /* SAObject+QLNetworkingMethods.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A150B042214092B00840F54 /* SAObject+QLNetworkingMethods.m */; };
		9A150B382214092B00840F54 /* SAArray+QLNetworkingMethods.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A150B052214092B00840F54 /* SAArray+QLNetworkingMethods.m */; };
		9A150B392214092B00840F54 /* SAString+QLNetworkingMethods.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A150B062214092B00840F54 /* SAString+QLNetworkingMethods.m */; };
		9A150B3A2214092B00840F54 /* SADictionary+QLNetworkingMethods.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A150B072214092B00840F54 /* SADictionary+QLNetworkingMethods.m */; };
		9A150B3B2214092B00840F54 /* SAMutableString+QLNetworkingMethods.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A150B082214092B00840F54 /* SAMutableString+QLNetworkingMethods.m */; };
		9A150B3C2214092B00840F54 /* SAService.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A150B102214092B00840F54 /* SAService.m */; };
		9A150B3D2214092B00840F54 /* SAServiceFactory.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A150B122214092B00840F54 /* SAServiceFactory.m */; };
		9A15F9772840B5EA00D79C39 /* SAHomeReviewingCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A15F9762840B5E900D79C39 /* SAHomeReviewingCell.m */; };
		9A15F9A52840E22D00D79C39 /* SAHomeBankModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A15F9A42840E22D00D79C39 /* SAHomeBankModel.m */; };
		9A16888F2C8ED0E800A98C9B /* SATextView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A16888E2C8ED0E800A98C9B /* SATextView.m */; };
		9A1688922C8EFA3000A98C9B /* SAGetBankRealNameApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A1688912C8EFA3000A98C9B /* SAGetBankRealNameApi.m */; };
		9A1702542C8866CB00FD811E /* SAProtocolViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A1702532C8866CB00FD811E /* SAProtocolViewController.m */; };
		9A1702572C8964D100FD811E /* SAGetProtocolApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A1702562C8964D100FD811E /* SAGetProtocolApiManager.m */; };
		9A2245A929418F5F00DA1F9D /* SAProductModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A2245A829418F5F00DA1F9D /* SAProductModel.m */; };
		9A2245AC2941BAC400DA1F9D /* SAOrderDetailViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A2245AB2941BAC400DA1F9D /* SAOrderDetailViewController.m */; };
		9A2245AF2941D07A00DA1F9D /* SALoanRecordModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A2245AE2941D07A00DA1F9D /* SALoanRecordModel.m */; };
		9A2245D3294AED5400DA1F9D /* SACommonNoDataView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A2245D2294AED5400DA1F9D /* SACommonNoDataView.m */; };
		9A2506DD2AD41375008C1068 /* SAGetOcrConfigApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A2506DC2AD41375008C1068 /* SAGetOcrConfigApiManager.m */; };
		9A2506E02AD4F318008C1068 /* SAFaceSubmitApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A2506DF2AD4F318008C1068 /* SAFaceSubmitApiManager.m */; };
		9A2506E32AD506B4008C1068 /* SAGetCarrierUrlApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A2506E22AD506B4008C1068 /* SAGetCarrierUrlApiManager.m */; };
		9A2506E92AD53FA3008C1068 /* SAOfflineRepayViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A2506E82AD53FA3008C1068 /* SAOfflineRepayViewController.m */; };
		9A2506EC2AD53FD6008C1068 /* SAOfflineRepayInfoApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A2506EB2AD53FD6008C1068 /* SAOfflineRepayInfoApiManager.m */; };
		9A2506EF2AD54A97008C1068 /* SAUploadModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A2506EE2AD54A97008C1068 /* SAUploadModel.m */; };
		9A2506F22AD54AA5008C1068 /* SANLUploadManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A2506F12AD54AA5008C1068 /* SANLUploadManager.m */; };
		9A2506F52AD54D43008C1068 /* SAGetFileTokenApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A2506F42AD54D43008C1068 /* SAGetFileTokenApiManager.m */; };
		9A2506F82AD54F08008C1068 /* SAFileUploadApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A2506F72AD54F08008C1068 /* SAFileUploadApiManager.m */; };
		9A2506FB2AD656E4008C1068 /* SAS3Manager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A2506FA2AD656E4008C1068 /* SAS3Manager.m */; };
		9A2533072C902CA90093728D /* SARepayH5ViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A2533062C902CA90093728D /* SARepayH5ViewController.m */; };
		9A3387682CCA562D0056585A /* NPTradeAlertViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A3387672CCA562D0056585A /* NPTradeAlertViewController.m */; };
		9A33876B2CCA5AE90056585A /* SAHomeLoaningCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A33876A2CCA5AE90056585A /* SAHomeLoaningCell.m */; };
		9A33876E2CCA64740056585A /* NPHomeStepModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A33876D2CCA64740056585A /* NPHomeStepModel.m */; };
		9A344B552833698A004AEE1D /* SystemConfiguration.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 08675B5D22C1F7F8007CCC9F /* SystemConfiguration.framework */; };
		9A35DE08283E03C400C387B7 /* SAToast+UIView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A35DE06283E03C300C387B7 /* SAToast+UIView.m */; };
		9A35DE0B283E046D00C387B7 /* SAString+Size.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A35DE0A283E046D00C387B7 /* SAString+Size.m */; };
		9A35DE4B283E1F8800C387B7 /* SAGetLiveConfigManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A35DE4A283E1F8800C387B7 /* SAGetLiveConfigManager.m */; };
		9A35DE6C283E38C400C387B7 /* SAAdvanceManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A35DE6B283E38C400C387B7 /* SAAdvanceManager.m */; };
		9A35DE6F283F1AFA00C387B7 /* SAVerifyIdentityViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A35DE6E283F1AFA00C387B7 /* SAVerifyIdentityViewController.m */; };
		9A35DE87283F46EF00C387B7 /* SAUploadManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A35DE86283F46EF00C387B7 /* SAUploadManager.m */; };
		9A35DE8A283F84D700C387B7 /* SASubmitManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A35DE89283F84D700C387B7 /* SASubmitManager.m */; };
		9A35DE90283F95BA00C387B7 /* SACompareUploadManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A35DE8F283F95BA00C387B7 /* SACompareUploadManager.m */; };
		9A35DE93283FA9D400C387B7 /* SAHomeNormalCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A35DE92283FA9D400C387B7 /* SAHomeNormalCell.m */; };
		9A3BA6D5285ACE1C0041A741 /* SAOrderConfigApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A3BA6D4285ACE1C0041A741 /* SAOrderConfigApiManager.m */; };
		9A4288292844A4E9000FE25F /* SAUploadInfoManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A4288282844A4E9000FE25F /* SAUploadInfoManager.m */; };
		9A42882D2844B83F000FE25F /* SAData+Compress.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A42882C2844B83F000FE25F /* SAData+Compress.m */; };
		9A48D7122CCBA59500A3B04F /* SAPayStatusViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A48D7112CCBA59500A3B04F /* SAPayStatusViewController.m */; };
		9A48D7182CCBB5F000A3B04F /* SARepayCashierModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A48D7172CCBB5F000A3B04F /* SARepayCashierModel.m */; };
		9A48D71B2CCBB80500A3B04F /* NPCashierCardCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A48D71A2CCBB80500A3B04F /* NPCashierCardCell.m */; };
		9A48D71D2CCBC26400A3B04F /* pay_loading.gif in Resources */ = {isa = PBXBuildFile; fileRef = 9A48D71C2CCBC26400A3B04F /* pay_loading.gif */; };
		9A4BC7122CBD18230051D311 /* EsignSDK.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 9A4BC7102CBD18230051D311 /* EsignSDK.framework */; };
		9A4BC7132CBD18230051D311 /* EsignSDK.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 9A4BC7112CBD18230051D311 /* EsignSDK.bundle */; };
		9A4C51D32CD8DBAC004BC9CE /* SAService.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A150B102214092B00840F54 /* SAService.m */; };
		9A4C51D42CD8DBAC004BC9CE /* SAFileUploadApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A2506F72AD54F08008C1068 /* SAFileUploadApiManager.m */; };
		9A4C51D52CD8DBAC004BC9CE /* SAMainHomeApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF04B622AF3EC500DF17E6 /* SAMainHomeApiManager.m */; };
		9A4C51D62CD8DBAC004BC9CE /* SAResponse.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A150AED2214092B00840F54 /* SAResponse.m */; };
		9A4C51D72CD8DBAC004BC9CE /* SADictionary+Common.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D96322AE6D680044AB4D /* SADictionary+Common.m */; };
		9A4C51D82CD8DBAC004BC9CE /* SAView+MASAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ACA665E2AC1345E00D6250B /* SAView+MASAdditions.m */; };
		9A4C51D92CD8DBAC004BC9CE /* SACommonTool.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D95922AE6D670044AB4D /* SACommonTool.m */; };
		9A4C51DA2CD8DBAC004BC9CE /* NPInviteAlertViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AE3D2072CCA488C008C0D58 /* NPInviteAlertViewController.m */; };
		9A4C51DB2CD8DBAC004BC9CE /* SAVersionUpdateViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 08D0A8A022C31B9700D68951 /* SAVersionUpdateViewController.m */; };
		9A4C51DC2CD8DBAC004BC9CE /* SAMutableDictionary+Extension.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D96022AE6D670044AB4D /* SAMutableDictionary+Extension.m */; };
		9A4C51DD2CD8DBAC004BC9CE /* SAMyCardsListViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ACF14E72C8DB6200057A6D1 /* SAMyCardsListViewController.m */; };
		9A4C51DE2CD8DBAC004BC9CE /* SADate+Extension.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D97522AE6D680044AB4D /* SADate+Extension.m */; };
		9A4C51DF2CD8DBAC004BC9CE /* SARequestHandle.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AFB9919222005B400F294D4 /* SARequestHandle.m */; };
		9A4C51E02CD8DBAC004BC9CE /* SAHomeProtocolUrlsModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF051022AF539700DF17E6 /* SAHomeProtocolUrlsModel.m */; };
		9A4C51E12CD8DBAC004BC9CE /* SAFixedTabbarView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AF5C40A2C8B0B77000EF027 /* SAFixedTabbarView.m */; };
		9A4C51E22CD8DBAC004BC9CE /* SAFaceSubmitApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A2506DF2AD4F318008C1068 /* SAFaceSubmitApiManager.m */; };
		9A4C51E32CD8DBAC004BC9CE /* SAGetPersonalInfoApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 0805C2F022B2570800021D25 /* SAGetPersonalInfoApiManager.m */; };
		9A4C51E42CD8DBAC004BC9CE /* SARefreshStateTrailer.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F5642ABD33170024CC0A /* SARefreshStateTrailer.m */; };
		9A4C51E52CD8DBAC004BC9CE /* SAViewConstraint.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ACA66632AC1345E00D6250B /* SAViewConstraint.m */; };
		9A4C51E62CD8DBAC004BC9CE /* SAMainTopBgView.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF05B222AFAAE300DF17E6 /* SAMainTopBgView.m */; };
		9A4C51E72CD8DBAC004BC9CE /* SAVerifyInfoCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 0856E5C522B279CB00D5C183 /* SAVerifyInfoCell.m */; };
		9A4C51E82CD8DBAC004BC9CE /* SARefreshTrailer.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F57A2ABD33170024CC0A /* SARefreshTrailer.m */; };
		9A4C51E92CD8DBAC004BC9CE /* SARepayListVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AF5C4162C8B113B000EF027 /* SARepayListVC.m */; };
		9A4C51EA2CD8DBAC004BC9CE /* SATextView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A16888E2C8ED0E800A98C9B /* SATextView.m */; };
		9A4C51EB2CD8DBAC004BC9CE /* SAPersonalInfoModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 0805C2F322B25B2000021D25 /* SAPersonalInfoModel.m */; };
		9A4C51EC2CD8DBAC004BC9CE /* SAPhotoManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 08B1ECC022B8CEC5001367BB /* SAPhotoManager.m */; };
		9A4C51ED2CD8DBAC004BC9CE /* SANetworkConst.m in Sources */ = {isa = PBXBuildFile; fileRef = 08CE109922A8AA4100E30241 /* SANetworkConst.m */; };
		9A4C51EE2CD8DBAC004BC9CE /* SAGetOcrConfigApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A2506DC2AD41375008C1068 /* SAGetOcrConfigApiManager.m */; };
		9A4C51EF2CD8DBAC004BC9CE /* SATextPickerView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AA557882C8D839D00F902A5 /* SATextPickerView.m */; };
		9A4C51F02CD8DBAC004BC9CE /* SARefreshConst.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F5442ABD33170024CC0A /* SARefreshConst.m */; };
		9A4C51F12CD8DBAC004BC9CE /* SANLUploadManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A2506F12AD54AA5008C1068 /* SANLUploadManager.m */; };
		9A4C51F22CD8DBAC004BC9CE /* SARefreshBackGifFooter.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F5582ABD33170024CC0A /* SARefreshBackGifFooter.m */; };
		9A4C51F32CD8DBAC004BC9CE /* SARefreshBackStateFooter.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F5592ABD33170024CC0A /* SARefreshBackStateFooter.m */; };
		9A4C51F42CD8DBAC004BC9CE /* SASmsCodeViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A0A39632A7A02EC0095AB86 /* SASmsCodeViewController.m */; };
		9A4C51F52CD8DBAC004BC9CE /* SAVerifyIdentityViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A35DE6E283F1AFA00C387B7 /* SAVerifyIdentityViewController.m */; };
		9A4C51F62CD8DBAC004BC9CE /* SATextModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AA557872C8D839D00F902A5 /* SATextModel.m */; };
		9A4C51F72CD8DBAC004BC9CE /* SAPaidListVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AF5C4192C8B1155000EF027 /* SAPaidListVC.m */; };
		9A4C51F82CD8DBAC004BC9CE /* SAPickerAlertView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AA557732C8D839D00F902A5 /* SAPickerAlertView.m */; };
		9A4C51F92CD8DBAC004BC9CE /* SAObject+QLNetworkingMethods.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A150B042214092B00840F54 /* SAObject+QLNetworkingMethods.m */; };
		9A4C51FA2CD8DBAC004BC9CE /* SABorrowCashPeriodModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF051F22AF5CCF00DF17E6 /* SABorrowCashPeriodModel.m */; };
		9A4C51FB2CD8DBAC004BC9CE /* SAPersonalInfoCheckModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 0805C2F922B25C5B00021D25 /* SAPersonalInfoCheckModel.m */; };
		9A4C51FC2CD8DBAC004BC9CE /* SAGetEsignUrlApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AA5576C2C8D4D5C00F902A5 /* SAGetEsignUrlApi.m */; };
		9A4C51FD2CD8DBAC004BC9CE /* NPOnlineRepayViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A9FFDA02CCB4CB400DF3EE9 /* NPOnlineRepayViewController.m */; };
		9A4C51FE2CD8DBAC004BC9CE /* SABasicViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D8C622AE6ACF0044AB4D /* SABasicViewController.m */; };
		9A4C51FF2CD8DBAC004BC9CE /* SAApiProxy.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A150AF72214092B00840F54 /* SAApiProxy.m */; };
		9A4C52002CD8DBAC004BC9CE /* SAObjC.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F53D2ABC2FC10024CC0A /* SAObjC.m */; };
		9A4C52012CD8DBAC004BC9CE /* SAImage+Extension.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF054122AF777900DF17E6 /* SAImage+Extension.m */; };
		9A4C52022CD8DBAC004BC9CE /* SAPayClientInfoApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 082B160722BB871600A4C7C7 /* SAPayClientInfoApiManager.m */; };
		9A4C52032CD8DBAC004BC9CE /* SAString+Size.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A35DE0A283E046D00C387B7 /* SAString+Size.m */; };
		9A4C52042CD8DBAC004BC9CE /* SACustomSlideView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AF5C40C2C8B0B77000EF027 /* SACustomSlideView.m */; };
		9A4C52052CD8DBAC004BC9CE /* SAHomeNormalCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A35DE92283FA9D400C387B7 /* SAHomeNormalCell.m */; };
		9A4C52062CD8DBAC004BC9CE /* SABCTool.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D97E22AE6D680044AB4D /* SABCTool.m */; };
		9A4C52072CD8DBAC004BC9CE /* SAView+Extension.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D96C22AE6D680044AB4D /* SAView+Extension.m */; };
		9A4C52082CD8DBAC004BC9CE /* SAShapeLayer+Extension.m in Sources */ = {isa = PBXBuildFile; fileRef = 08B1EC4822B8815A001367BB /* SAShapeLayer+Extension.m */; };
		9A4C52092CD8DBAC004BC9CE /* SAString+QLNetworkingMethods.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A150B062214092B00840F54 /* SAString+QLNetworkingMethods.m */; };
		9A4C520A2CD8DBAC004BC9CE /* SAPayWayModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 082B160A22BB8CCA00A4C7C7 /* SAPayWayModel.m */; };
		9A4C520B2CD8DBAC004BC9CE /* SASaveBindCardInfoApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AEC08822C89A597008475BD /* SASaveBindCardInfoApi.m */; };
		9A4C520C2CD8DBAC004BC9CE /* SAConfrimTradeViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AA557602C8D4B7F00F902A5 /* SAConfrimTradeViewController.m */; };
		9A4C520D2CD8DBAC004BC9CE /* SAConfirmSmsAlertView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ACF14E12C8D93DD0057A6D1 /* SAConfirmSmsAlertView.m */; };
		9A4C520E2CD8DBAC004BC9CE /* SAContactsManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 08B4A93D22B79A0D006B35D4 /* SAContactsManager.m */; };
		9A4C520F2CD8DBAC004BC9CE /* SARefreshGifHeader.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F56D2ABD33170024CC0A /* SARefreshGifHeader.m */; };
		9A4C52102CD8DBAC004BC9CE /* SABaseManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A150AF42214092B00840F54 /* SABaseManager.m */; };
		9A4C52112CD8DBAC004BC9CE /* SAPickerTextField.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF4C2022B604390095863C /* SAPickerTextField.m */; };
		9A4C52122CD8DBAC004BC9CE /* SANetworkingConfigurationManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A150AE32214092B00840F54 /* SANetworkingConfigurationManager.m */; };
		9A4C52132CD8DBAC004BC9CE /* SARefreshFooter.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F56F2ABD33170024CC0A /* SARefreshFooter.m */; };
		9A4C52142CD8DBAC004BC9CE /* SAGetCashierInfoAPi.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A9FFDA32CCB4ED300DF3EE9 /* SAGetCashierInfoAPi.m */; };
		9A4C52152CD8DBAC004BC9CE /* SACustomHudImg.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A64804B2CC6492900C20F02 /* SACustomHudImg.m */; };
		9A4C52162CD8DBAC004BC9CE /* SASettingViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AEC08912C89DAA0008475BD /* SASettingViewController.m */; };
		9A4C52172CD8DBAC004BC9CE /* SAGetContactInfoApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF4C5E22B61CF20095863C /* SAGetContactInfoApiManager.m */; };
		9A4C52182CD8DBAC004BC9CE /* SASubmitTradeApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AA557692C8D4D0F00F902A5 /* SASubmitTradeApi.m */; };
		9A4C52192CD8DBAC004BC9CE /* SAProtocolViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A1702532C8866CB00FD811E /* SAProtocolViewController.m */; };
		9A4C521A2CD8DBAC004BC9CE /* SAServiceView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AF5C3E12C8AE656000EF027 /* SAServiceView.m */; };
		9A4C521B2CD8DBAC004BC9CE /* SATabBarViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D8C722AE6ACF0044AB4D /* SATabBarViewController.m */; };
		9A4C521C2CD8DBAC004BC9CE /* SACycleVerticalView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A818DD42BF5A721008DF462 /* SACycleVerticalView.m */; };
		9A4C521D2CD8DBAC004BC9CE /* SABorrowCashModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF051C22AF5C7600DF17E6 /* SABorrowCashModel.m */; };
		9A4C521E2CD8DBAC004BC9CE /* SAHomePageViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D93F22AE6C880044AB4D /* SAHomePageViewController.m */; };
		9A4C521F2CD8DBAC004BC9CE /* SAOfflineRepayViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A2506E82AD53FA3008C1068 /* SAOfflineRepayViewController.m */; };
		9A4C52202CD8DBAC004BC9CE /* SAGetCitiesListApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A021B1D2AB8622F007EC584 /* SAGetCitiesListApiManager.m */; };
		9A4C52212CD8DBAC004BC9CE /* SAMarqueeView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A6E92752BF5BDDD003D679C /* SAMarqueeView.m */; };
		9A4C52222CD8DBAC004BC9CE /* SABannerModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A02DF542CC656C000431F1B /* SABannerModel.m */; };
		9A4C52232CD8DBAC004BC9CE /* SABankItemView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AEC08762C899B1E008475BD /* SABankItemView.m */; };
		9A4C52242CD8DBAC004BC9CE /* SAHomeBasicCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF056E22AF9B4400DF17E6 /* SAHomeBasicCell.m */; };
		9A4C52252CD8DBAC004BC9CE /* SAFont+Extension.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D96F22AE6D680044AB4D /* SAFont+Extension.m */; };
		9A4C52262CD8DBAC004BC9CE /* SAVerifyBankCardViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AEC08732C898F4D008475BD /* SAVerifyBankCardViewController.m */; };
		9A4C52272CD8DBAC004BC9CE /* Pointer.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AA557972C8D839D00F902A5 /* Pointer.m */; };
		9A4C52282CD8DBAC004BC9CE /* SAViewAttribute.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ACA66642AC1345E00D6250B /* SAViewAttribute.m */; };
		9A4C52292CD8DBAC004BC9CE /* SAView+MJExtension.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F5512ABD33170024CC0A /* SAView+MJExtension.m */; };
		9A4C522A2CD8DBAC004BC9CE /* SAAlertSureTipsViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 08D14F5A22BCCB4C0094582F /* SAAlertSureTipsViewController.m */; };
		9A4C522B2CD8DBAC004BC9CE /* SAAlertController+Orientation.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D96822AE6D680044AB4D /* SAAlertController+Orientation.m */; };
		9A4C522C2CD8DBAC004BC9CE /* SAResponseHandle.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AFB9916222005B400F294D4 /* SAResponseHandle.m */; };
		9A4C522D2CD8DBAC004BC9CE /* SAAppCodeManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 08CE116222A8F73F00E30241 /* SAAppCodeManager.m */; };
		9A4C522E2CD8DBAC004BC9CE /* SAServiceFactory.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A150B122214092B00840F54 /* SAServiceFactory.m */; };
		9A4C522F2CD8DBAC004BC9CE /* SAScrollView+MJExtension.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F54B2ABD33170024CC0A /* SAScrollView+MJExtension.m */; };
		9A4C52302CD8DBAC004BC9CE /* SABankListModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 08E66BCF22B4F7C7005E62D9 /* SABankListModel.m */; };
		9A4C52312CD8DBAC004BC9CE /* SABankBinListViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AEC088B2C89BD26008475BD /* SABankBinListViewController.m */; };
		9A4C52322CD8DBAC004BC9CE /* SABannerCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A02DF5A2CC65D2600431F1B /* SABannerCell.m */; };
		9A4C52332CD8DBAC004BC9CE /* SAPaidCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A02DF5D2CC66F0100431F1B /* SAPaidCell.m */; };
		9A4C52342CD8DBAC004BC9CE /* SAPickerStyle.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AA557772C8D839D00F902A5 /* SAPickerStyle.m */; };
		9A4C52352CD8DBAC004BC9CE /* SARepaymentViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AF5C3EA2C8B0193000EF027 /* SARepaymentViewController.m */; };
		9A4C52362CD8DBAC004BC9CE /* SAAppConfigApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 08CE116522A8F79400E30241 /* SAAppConfigApiManager.m */; };
		9A4C52372CD8DBAC004BC9CE /* SACompareUploadManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A35DE8F283F95BA00C387B7 /* SACompareUploadManager.m */; };
		9A4C52382CD8DBAC004BC9CE /* SARefreshAutoFooter.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F5752ABD33170024CC0A /* SARefreshAutoFooter.m */; };
		9A4C52392CD8DBAC004BC9CE /* SACompositeConstraint.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ACA665A2AC1345E00D6250B /* SACompositeConstraint.m */; };
		9A4C523A2CD8DBAC004BC9CE /* SAResultModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AA5578B2C8D839D00F902A5 /* SAResultModel.m */; };
		9A4C523B2CD8DBAC004BC9CE /* SARequest+QLNetworkingMethods.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A150B032214092B00840F54 /* SARequest+QLNetworkingMethods.m */; };
		9A4C523C2CD8DBAC004BC9CE /* SAHomeConfirmCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AA5575D2C8D4AE700F902A5 /* SAHomeConfirmCell.m */; };
		9A4C523D2CD8DBAC004BC9CE /* SALocationManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D97822AE6D680044AB4D /* SALocationManager.m */; };
		9A4C523E2CD8DBAC004BC9CE /* SALoginViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D95022AE6D1D0044AB4D /* SALoginViewController.m */; };
		9A4C523F2CD8DBAC004BC9CE /* SAStringPickerView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AA5578E2C8D839D00F902A5 /* SAStringPickerView.m */; };
		9A4C52402CD8DBAC004BC9CE /* SAHomeProtocolModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF051322AF53CF00DF17E6 /* SAHomeProtocolModel.m */; };
		9A4C52412CD8DBAC004BC9CE /* SAMutableString+QLNetworkingMethods.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A150B082214092B00840F54 /* SAMutableString+QLNetworkingMethods.m */; };
		9A4C52422CD8DBAC004BC9CE /* SARequestGenerator.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A150B002214092B00840F54 /* SARequestGenerator.m */; };
		9A4C52432CD8DBAC004BC9CE /* SAVerifyListItemModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 0805C28B22B1F6F900021D25 /* SAVerifyListItemModel.m */; };
		9A4C52442CD8DBAC004BC9CE /* SAGetProtocolApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A1702562C8964D100FD811E /* SAGetProtocolApiManager.m */; };
		9A4C52452CD8DBAC004BC9CE /* SAData+Compress.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A42882C2844B83F000FE25F /* SAData+Compress.m */; };
		9A4C52462CD8DBAC004BC9CE /* SAGetTradeDetailApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AA557662C8D4CC200F902A5 /* SAGetTradeDetailApi.m */; };
		9A4C52472CD8DBAC004BC9CE /* SACycleView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A818DD52BF5A721008DF462 /* SACycleView.m */; };
		9A4C52482CD8DBAC004BC9CE /* SAConstraintMaker.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ACA665B2AC1345E00D6250B /* SAConstraintMaker.m */; };
		9A4C52492CD8DBAC004BC9CE /* SADate+BRPickerView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AA5577E2C8D839D00F902A5 /* SADate+BRPickerView.m */; };
		9A4C524A2CD8DBAC004BC9CE /* SAUploadInfoManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A4288282844A4E9000FE25F /* SAUploadInfoManager.m */; };
		9A4C524B2CD8DBAC004BC9CE /* SAManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 08F4C18D22C078330000B366 /* SAManager.m */; };
		9A4C524C2CD8DBAC004BC9CE /* SALogger.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A150AE82214092B00840F54 /* SALogger.m */; };
		9A4C524D2CD8DBAC004BC9CE /* SADeviceTool.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D97D22AE6D680044AB4D /* SADeviceTool.m */; };
		9A4C524E2CD8DBAC004BC9CE /* SALoggerConfiguration.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A150AE22214092B00840F54 /* SALoggerConfiguration.m */; };
		9A4C524F2CD8DBAC004BC9CE /* SAHistoryOrderModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 08EC05D22315245200D3C93C /* SAHistoryOrderModel.m */; };
		9A4C52502CD8DBAC004BC9CE /* SAVerifyCodeInputView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0878035122B110B8000A7A4F /* SAVerifyCodeInputView.m */; };
		9A4C52512CD8DBAC004BC9CE /* SAScope.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AFB99352220082200F294D4 /* SAScope.m */; };
		9A4C52522CD8DBAC004BC9CE /* SAMobileLoginApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF04EE22AF424E00DF17E6 /* SAMobileLoginApiManager.m */; };
		9A4C52532CD8DBAC004BC9CE /* SABorrowPurposeTypeModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF051922AF5BE500DF17E6 /* SABorrowPurposeTypeModel.m */; };
		9A4C52542CD8DBAC004BC9CE /* SAUploadManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A35DE86283F46EF00C387B7 /* SAUploadManager.m */; };
		9A4C52552CD8DBAC004BC9CE /* SAGetCarrierUrlApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A2506E22AD506B4008C1068 /* SAGetCarrierUrlApiManager.m */; };
		9A4C52562CD8DBAC004BC9CE /* SARefreshStateHeader.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F56C2ABD33170024CC0A /* SARefreshStateHeader.m */; };
		9A4C52572CD8DBAC004BC9CE /* SANoNetView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D91322AE6BB40044AB4D /* SANoNetView.m */; };
		9A4C52582CD8DBAC004BC9CE /* SAAdvanceManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A35DE6B283E38C400C387B7 /* SAAdvanceManager.m */; };
		9A4C52592CD8DBAC004BC9CE /* SAToast+UIView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A35DE06283E03C300C387B7 /* SAToast+UIView.m */; };
		9A4C525A2CD8DBAC004BC9CE /* SAUserCenterApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 0878033522B0E898000A7A4F /* SAUserCenterApiManager.m */; };
		9A4C525B2CD8DBAC004BC9CE /* SAMyBankItemModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ACF14F32C8DBC5F0057A6D1 /* SAMyBankItemModel.m */; };
		9A4C525C2CD8DBAC004BC9CE /* SASavePersonalInfoApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF4C5822B61C010095863C /* SASavePersonalInfoApiManager.m */; };
		9A4C525D2CD8DBAC004BC9CE /* SADeviceInfoSaveApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 08A6BAEC22B1E67F001CA577 /* SADeviceInfoSaveApiManager.m */; };
		9A4C525E2CD8DBAC004BC9CE /* SAAddressModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AA557932C8D839D00F902A5 /* SAAddressModel.m */; };
		9A4C525F2CD8DBAC004BC9CE /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A150AA72214092B00840F54 /* main.m */; };
		9A4C52602CD8DBAC004BC9CE /* SAVerifyContactsViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ABE6D222CBF9F3A003B5100 /* SAVerifyContactsViewController.m */; };
		9A4C52612CD8DBAC004BC9CE /* SAAuditProgressItemModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 0880D0C122BB2357001C979F /* SAAuditProgressItemModel.m */; };
		9A4C52622CD8DBAC004BC9CE /* SAUploadModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A2506EE2AD54A97008C1068 /* SAUploadModel.m */; };
		9A4C52632CD8DBAC004BC9CE /* SAHomeReviewingCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A15F9762840B5E900D79C39 /* SAHomeReviewingCell.m */; };
		9A4C52642CD8DBAC004BC9CE /* SABaseView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AA557902C8D839D00F902A5 /* SABaseView.m */; };
		9A4C52652CD8DBAC004BC9CE /* SABindCardResendSmsApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AEC087C2C89A136008475BD /* SABindCardResendSmsApi.m */; };
		9A4C52662CD8DBAC004BC9CE /* SAPersonalVerifyModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 083AB35322B36DEA003B4BB4 /* SAPersonalVerifyModel.m */; };
		9A4C52672CD8DBAC004BC9CE /* SATabedSlideView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AF5C4042C8B0B77000EF027 /* SATabedSlideView.m */; };
		9A4C52682CD8DBAC004BC9CE /* SARepayH5ViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A2533062C902CA90093728D /* SARepayH5ViewController.m */; };
		9A4C52692CD8DBAC004BC9CE /* SACommonNoDataView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A2245D2294AED5400DA1F9D /* SACommonNoDataView.m */; };
		9A4C526A2CD8DBAC004BC9CE /* SAOrderInfoModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF051622AF5A2F00DF17E6 /* SAOrderInfoModel.m */; };
		9A4C526B2CD8DBAC004BC9CE /* SASaveGPSApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 08F4C12222C063380000B366 /* SASaveGPSApiManager.m */; };
		9A4C526C2CD8DBAC004BC9CE /* SATradeDetailCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AA5576F2C8D52D300F902A5 /* SATradeDetailCell.m */; };
		9A4C526D2CD8DBAC004BC9CE /* SAClientChargesModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 08D14F0F22BC7E1E0094582F /* SAClientChargesModel.m */; };
		9A4C526E2CD8DBAC004BC9CE /* SAGetRegManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AB09A72284276F7005C55AB /* SAGetRegManager.m */; };
		9A4C526F2CD8DBAC004BC9CE /* NPCashierCardCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A48D71A2CCBB80500A3B04F /* NPCashierCardCell.m */; };
		9A4C52702CD8DBAC004BC9CE /* SARefreshConfig.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F5452ABD33170024CC0A /* SARefreshConfig.m */; };
		9A4C52712CD8DBAC004BC9CE /* SAScrollTabbarView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AF5C4092C8B0B77000EF027 /* SAScrollTabbarView.m */; };
		9A4C52722CD8DBAC004BC9CE /* SAErrorView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0811F47122CB41D000CDBBD1 /* SAErrorView.m */; };
		9A4C52732CD8DBAC004BC9CE /* SARefreshAutoStateFooter.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F55F2ABD33170024CC0A /* SARefreshAutoStateFooter.m */; };
		9A4C52742CD8DBAC004BC9CE /* SALayoutConstraint+MASDebugAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ACA664D2AC1345E00D6250B /* SALayoutConstraint+MASDebugAdditions.m */; };
		9A4C52752CD8DBAC004BC9CE /* SARefreshNormalHeader.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F5682ABD33170024CC0A /* SARefreshNormalHeader.m */; };
		9A4C52762CD8DBAC004BC9CE /* SAContactSelectModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF4C6B22B623870095863C /* SAContactSelectModel.m */; };
		9A4C52772CD8DBAC004BC9CE /* SAContactRowModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF4C7422B626580095863C /* SAContactRowModel.m */; };
		9A4C52782CD8DBAC004BC9CE /* SALoanRecordModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A2245AE2941D07A00DA1F9D /* SALoanRecordModel.m */; };
		9A4C52792CD8DBAC004BC9CE /* SARepayListModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AF5C3F32C8B0253000EF027 /* SARepayListModel.m */; };
		9A4C527A2CD8DBAC004BC9CE /* SAConfirmmBankViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ACF14EA2C8DB6350057A6D1 /* SAConfirmmBankViewController.m */; };
		9A4C527B2CD8DBAC004BC9CE /* SAUserCenterModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 0878035422B138E9000A7A4F /* SAUserCenterModel.m */; };
		9A4C527C2CD8DBAC004BC9CE /* SARefreshBackNormalFooter.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F55A2ABD33170024CC0A /* SARefreshBackNormalFooter.m */; };
		9A4C527D2CD8DBAC004BC9CE /* SAQueryCardBinApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AEC088E2C89D150008475BD /* SAQueryCardBinApi.m */; };
		9A4C527E2CD8DBAC004BC9CE /* SAVerifyListManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 0805C2A822B24A7800021D25 /* SAVerifyListManager.m */; };
		9A4C527F2CD8DBAC004BC9CE /* SAWifiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 08AB288C22BF606900CAE59C /* SAWifiManager.m */; };
		9A4C52802CD8DBAC004BC9CE /* SAMainManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ABB8E1422141F1B00FBF613 /* SAMainManager.m */; };
		9A4C52812CD8DBAC004BC9CE /* SAGetBankRealNameApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A1688912C8EFA3000A98C9B /* SAGetBankRealNameApi.m */; };
		9A4C52822CD8DBAC004BC9CE /* SAGetBorrowAgainApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ACF14F62C8DC04C0057A6D1 /* SAGetBorrowAgainApi.m */; };
		9A4C52832CD8DBAC004BC9CE /* SAFaceBackAlert.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A09512E2CBE076C0053D586 /* SAFaceBackAlert.m */; };
		9A4C52842CD8DBAC004BC9CE /* SAContactItemCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ABE6D1F2CBF9EB8003B5100 /* SAContactItemCell.m */; };
		9A4C52852CD8DBAC004BC9CE /* SACollectionViewLayout+MJRefresh.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F5432ABD33170024CC0A /* SACollectionViewLayout+MJRefresh.m */; };
		9A4C52862CD8DBAC004BC9CE /* NPHomeStepModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A33876D2CCA64740056585A /* NPHomeStepModel.m */; };
		9A4C52872CD8DBAC004BC9CE /* SADictionary+QLNetworkingMethods.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A150B072214092B00840F54 /* SADictionary+QLNetworkingMethods.m */; };
		9A4C52882CD8DBAC004BC9CE /* SARuntimeExtensions.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AFB99362220082200F294D4 /* SARuntimeExtensions.m */; };
		9A4C52892CD8DBAC004BC9CE /* SASubmitProductInfoModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 083D64D92313E2E400810B26 /* SASubmitProductInfoModel.m */; };
		9A4C528A2CD8DBAC004BC9CE /* SARepaymentCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AF5C3ED2C8B0212000EF027 /* SARepaymentCell.m */; };
		9A4C528B2CD8DBAC004BC9CE /* SAArray+MASAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ACA665D2AC1345E00D6250B /* SAArray+MASAdditions.m */; };
		9A4C528C2CD8DBAC004BC9CE /* SABasicService.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AFB991B222005B400F294D4 /* SABasicService.m */; };
		9A4C528D2CD8DBAC004BC9CE /* SAAliyunOSSManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 08B1ED2322B8DDCE001367BB /* SAAliyunOSSManager.m */; };
		9A4C528E2CD8DBAC004BC9CE /* SAGradientView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0878030022B0967D000A7A4F /* SAGradientView.m */; };
		9A4C528F2CD8DBAC004BC9CE /* SABundle+BRPickerView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AA557782C8D839D00F902A5 /* SABundle+BRPickerView.m */; };
		9A4C52902CD8DBAC004BC9CE /* SAProductModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A2245A829418F5F00DA1F9D /* SAProductModel.m */; };
		9A4C52912CD8DBAC004BC9CE /* SABindCardSendCodeApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AEC08792C89A11D008475BD /* SABindCardSendCodeApi.m */; };
		9A4C52922CD8DBAC004BC9CE /* SATool.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D95822AE6D670044AB4D /* SATool.m */; };
		9A4C52932CD8DBAC004BC9CE /* SAHomeRejectCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF055F22AF923600DF17E6 /* SAHomeRejectCell.m */; };
		9A4C52942CD8DBAC004BC9CE /* SABankNameModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AEC08882C89BCCF008475BD /* SABankNameModel.m */; };
		9A4C52952CD8DBAC004BC9CE /* SAUtility.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AF5C40B2C8B0B77000EF027 /* SAUtility.m */; };
		9A4C52962CD8DBAC004BC9CE /* SAGetBillDetailApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AA5575A2C8B309300F902A5 /* SAGetBillDetailApi.m */; };
		9A4C52972CD8DBAC004BC9CE /* SAString+Extension.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D97222AE6D680044AB4D /* SAString+Extension.m */; };
		9A4C52982CD8DBAC004BC9CE /* SAJQCache.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AF5C3FE2C8B0B77000EF027 /* SAJQCache.m */; };
		9A4C52992CD8DBAC004BC9CE /* SACardListCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ACF14F02C8DB71E0057A6D1 /* SACardListCell.m */; };
		9A4C529A2CD8DBAC004BC9CE /* SAS3Manager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A2506FA2AD656E4008C1068 /* SAS3Manager.m */; };
		9A4C529B2CD8DBAC004BC9CE /* SABannerView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A02DF572CC6593400431F1B /* SABannerView.m */; };
		9A4C529C2CD8DBAC004BC9CE /* SASubmitManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A35DE89283F84D700C387B7 /* SASubmitManager.m */; };
		9A4C529D2CD8DBAC004BC9CE /* SAContactSectionModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF4C7122B626420095863C /* SAContactSectionModel.m */; };
		9A4C529E2CD8DBAC004BC9CE /* SACenterYTextView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0856E5C222B2793C00D5C183 /* SACenterYTextView.m */; };
		9A4C529F2CD8DBAC004BC9CE /* SADatePickerView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AA5577F2C8D839D00F902A5 /* SADatePickerView.m */; };
		9A4C52A02CD8DBAC004BC9CE /* SAMineViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D94522AE6C880044AB4D /* SAMineViewController.m */; };
		9A4C52A12CD8DBAC004BC9CE /* SAPayClientChargesApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 082B162322BBAECA00A4C7C7 /* SAPayClientChargesApiManager.m */; };
		9A4C52A22CD8DBAC004BC9CE /* SALayoutConstraint.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ACA665C2AC1345E00D6250B /* SALayoutConstraint.m */; };
		9A4C52A32CD8DBAC004BC9CE /* SARefreshAutoGifFooter.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F5602ABD33170024CC0A /* SARefreshAutoGifFooter.m */; };
		9A4C52A42CD8DBAC004BC9CE /* SAGetLiveConfigManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A35DE4A283E1F8800C387B7 /* SAGetLiveConfigManager.m */; };
		9A4C52A52CD8DBAC004BC9CE /* SACashierPayStatusApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A9FFDA92CCB513A00DF3EE9 /* SACashierPayStatusApi.m */; };
		9A4C52A62CD8DBAC004BC9CE /* SAHomeBankModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A15F9A42840E22D00D79C39 /* SAHomeBankModel.m */; };
		9A4C52A72CD8DBAC004BC9CE /* SAArray+QLNetworkingMethods.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A150B052214092B00840F54 /* SAArray+QLNetworkingMethods.m */; };
		9A4C52A82CD8DBAC004BC9CE /* SAImage+ChangeColor.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AD150A32C8AA0080033A9D9 /* SAImage+ChangeColor.m */; };
		9A4C52A92CD8DBAC004BC9CE /* SARefreshAutoNormalFooter.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F5612ABD33170024CC0A /* SARefreshAutoNormalFooter.m */; };
		9A4C52AA2CD8DBAC004BC9CE /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A150AA32214092A00840F54 /* AppDelegate.m */; };
		9A4C52AB2CD8DBAC004BC9CE /* SAGetPaidBillListApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AF5C3F92C8B05CA000EF027 /* SAGetPaidBillListApi.m */; };
		9A4C52AC2CD8DBAC004BC9CE /* SAVerifyCodeApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 0878034D22B10C35000A7A4F /* SAVerifyCodeApiManager.m */; };
		9A4C52AD2CD8DBAC004BC9CE /* SAHomeLoaningCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A33876A2CCA5AE90056585A /* SAHomeLoaningCell.m */; };
		9A4C52AE2CD8DBAC004BC9CE /* SALogoutApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF04F122AF432800DF17E6 /* SALogoutApiManager.m */; };
		9A4C52AF2CD8DBAC004BC9CE /* SAGetSupportBankListApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ACF14E42C8DB42A0057A6D1 /* SAGetSupportBankListApi.m */; };
		9A4C52B02CD8DBAC004BC9CE /* SARefreshComponent.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F5772ABD33170024CC0A /* SARefreshComponent.m */; };
		9A4C52B12CD8DBAC004BC9CE /* SAKeyChainTool.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D98022AE6D680044AB4D /* SAKeyChainTool.m */; };
		9A4C52B22CD8DBAC004BC9CE /* SAOrderConfirmApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 08F5A3F6231517BF00D75C55 /* SAOrderConfirmApiManager.m */; };
		9A4C52B32CD8DBAC004BC9CE /* SARefreshBackFooter.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F5742ABD33170024CC0A /* SARefreshBackFooter.m */; };
		9A4C52B42CD8DBAC004BC9CE /* SAGetFileTokenApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A2506F42AD54D43008C1068 /* SAGetFileTokenApiManager.m */; };
		9A4C52B52CD8DBAC004BC9CE /* SASlideView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AF5C40D2C8B0B77000EF027 /* SASlideView.m */; };
		9A4C52B62CD8DBAC004BC9CE /* SABindCardInfoModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AEC08852C89A679008475BD /* SABindCardInfoModel.m */; };
		9A4C52B72CD8DBAC004BC9CE /* SASaveContactInfoApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF4C6122B61D040095863C /* SASaveContactInfoApiManager.m */; };
		9A4C52B82CD8DBAC004BC9CE /* SAVerifyDataListApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 0805C28522B1F54600021D25 /* SAVerifyDataListApiManager.m */; };
		9A4C52B92CD8DBAC004BC9CE /* SARefreshNormalTrailer.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F5652ABD33170024CC0A /* SARefreshNormalTrailer.m */; };
		9A4C52BA2CD8DBAC004BC9CE /* SATokenApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 08B1ED1422B8DC14001367BB /* SATokenApiManager.m */; };
		9A4C52BB2CD8DBAC004BC9CE /* SAGetMyCardsApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ACF14ED2C8DB6550057A6D1 /* SAGetMyCardsApi.m */; };
		9A4C52BC2CD8DBAC004BC9CE /* SABundle+MJRefresh.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F5502ABD33170024CC0A /* SABundle+MJRefresh.m */; };
		9A4C52BD2CD8DBAC004BC9CE /* SAGetCustomerUrlApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A6E927F2BF5E86C003D679C /* SAGetCustomerUrlApiManager.m */; };
		9A4C52BE2CD8DBAC004BC9CE /* SAColor+Extension.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D96622AE6D680044AB4D /* SAColor+Extension.m */; };
		9A4C52BF2CD8DBAC004BC9CE /* SAUserManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 08CE115822A8E7A300E30241 /* SAUserManager.m */; };
		9A4C52C02CD8DBAC004BC9CE /* SACashierDoPayApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A9FFDA62CCB4F8100DF3EE9 /* SACashierDoPayApi.m */; };
		9A4C52C12CD8DBAC004BC9CE /* SAActionSheet.m in Sources */ = {isa = PBXBuildFile; fileRef = 08B1EC6222B8B46C001367BB /* SAActionSheet.m */; };
		9A4C52C22CD8DBAC004BC9CE /* SAOfflineRepayInfoApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A2506EB2AD53FD6008C1068 /* SAOfflineRepayInfoApiManager.m */; };
		9A4C52C32CD8DBAC004BC9CE /* SACache.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A150AFC2214092B00840F54 /* SACache.m */; };
		9A4C52C42CD8DBAC004BC9CE /* SAOrderDetailViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A2245AB2941BAC400DA1F9D /* SAOrderDetailViewController.m */; };
		9A4C52C52CD8DBAC004BC9CE /* SAGetRepayBillListApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AF5C3F62C8B053B000EF027 /* SAGetRepayBillListApi.m */; };
		9A4C52C62CD8DBAC004BC9CE /* SAViewController+MASAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ACA66562AC1345E00D6250B /* SAViewController+MASAdditions.m */; };
		9A4C52C72CD8DBAC004BC9CE /* SAVerifyBasicInfoViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0805C2D822B24DF900021D25 /* SAVerifyBasicInfoViewController.m */; };
		9A4C52C82CD8DBAC004BC9CE /* SANormalRefresh.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D95C22AE6D670044AB4D /* SANormalRefresh.m */; };
		9A4C52C92CD8DBAC004BC9CE /* SACommonWebViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 08B1EC4B22B88754001367BB /* SACommonWebViewController.m */; };
		9A4C52CA2CD8DBAC004BC9CE /* SAHomeModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF04FA22AF455E00DF17E6 /* SAHomeModel.m */; };
		9A4C52CB2CD8DBAC004BC9CE /* SAScrollView+MJRefresh.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F5422ABD33170024CC0A /* SAScrollView+MJRefresh.m */; };
		9A4C52CC2CD8DBAC004BC9CE /* SACachedObject.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A150AFD2214092B00840F54 /* SACachedObject.m */; };
		9A4C52CD2CD8DBAC004BC9CE /* SAConstraint.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ACA66612AC1345E00D6250B /* SAConstraint.m */; };
		9A4C52CE2CD8DBAC004BC9CE /* SARepaymentModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 08D14F3A22BC7E970094582F /* SARepaymentModel.m */; };
		9A4C52CF2CD8DBAC004BC9CE /* SARefreshHeader.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F5712ABD33170024CC0A /* SARefreshHeader.m */; };
		9A4C52D02CD8DBAC004BC9CE /* SADatePickerView+BR.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AA5577C2C8D839D00F902A5 /* SADatePickerView+BR.m */; };
		9A4C52D12CD8DBAC004BC9CE /* SARepayCashierModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A48D7172CCBB5F000A3B04F /* SARepayCashierModel.m */; };
		9A4C52D22CD8DBAC004BC9CE /* SASaveDeviceChannelApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 089A56B922E9B3FC005BEBD5 /* SASaveDeviceChannelApiManager.m */; };
		9A4C52D32CD8DBAC004BC9CE /* SAPayStatusViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A48D7112CCBA59500A3B04F /* SAPayStatusViewController.m */; };
		9A4C52D42CD8DBAC004BC9CE /* NPTradeAlertViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A3387672CCA562D0056585A /* NPTradeAlertViewController.m */; };
		9A4C52D52CD8DBAC004BC9CE /* SANavigationViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D8CB22AE6ACF0044AB4D /* SANavigationViewController.m */; };
		9A4C52D62CD8DBAC004BC9CE /* SAVerifyListModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 0805C28822B1F6DD00021D25 /* SAVerifyListModel.m */; };
		9A4C52D72CD8DBAC004BC9CE /* SAOrderConfigApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A3BA6D4285ACE1C0041A741 /* SAOrderConfigApiManager.m */; };
		9A4C52D92CD8DBAC004BC9CE /* libbz2.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 9A0F351F2AD3CE370080FD21 /* libbz2.tbd */; };
		9A4C52DA2CD8DBAC004BC9CE /* libz.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 9A0F35202AD3CE430080FD21 /* libz.tbd */; };
		9A4C52DB2CD8DBAC004BC9CE /* libz.1.2.5.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 9A0F35212AD3CE540080FD21 /* libz.1.2.5.tbd */; };
		9A4C52DC2CD8DBAC004BC9CE /* EsignSDK.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 9A4BC7102CBD18230051D311 /* EsignSDK.framework */; };
		9A4C52DD2CD8DBAC004BC9CE /* libiconv.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 9A0F35222AD3CE750080FD21 /* libiconv.tbd */; };
		9A4C52DE2CD8DBAC004BC9CE /* CoreTelephony.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 08D0A96C22C37F6C00D68951 /* CoreTelephony.framework */; };
		9A4C52DF2CD8DBAC004BC9CE /* libc++.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 08D0A96A22C37F4B00D68951 /* libc++.tbd */; };
		9A4C52E02CD8DBAC004BC9CE /* CoreMotion.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 08675B5B22C1F7EB007CCC9F /* CoreMotion.framework */; };
		9A4C52E12CD8DBAC004BC9CE /* VideoToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 9A0F351D2AD3CE1F0080FD21 /* VideoToolbox.framework */; };
		9A4C52E22CD8DBAC004BC9CE /* CoreMedia.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 08F4C1C922C0A6440000B366 /* CoreMedia.framework */; };
		9A4C52E32CD8DBAC004BC9CE /* AVFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 08F4C1C722C0A63A0000B366 /* AVFoundation.framework */; };
		9A4C52E42CD8DBAC004BC9CE /* JavaScriptCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 08E3B51522A0DB6D00C6BA22 /* JavaScriptCore.framework */; };
		9A4C52E52CD8DBAC004BC9CE /* SystemConfiguration.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 08675B5D22C1F7F8007CCC9F /* SystemConfiguration.framework */; };
		9A4C52E62CD8DBAC004BC9CE /* Security.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 9A01F53F2ABC2FD20024CC0A /* Security.framework */; };
		9A4C52E72CD8DBAC004BC9CE /* (null) in Frameworks */ = {isa = PBXBuildFile; };
		9A4C52E82CD8DBAC004BC9CE /* Pods_StagingApp.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 49EAE65573E009493E201410 /* Pods_StagingApp.framework */; };
		9A4C52E92CD8DBAC004BC9CE /* Pods_StagingApp.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = F80DFFF8ED02B4B42D2E74DE /* Pods_StagingApp.framework */; };
		9A4C52EB2CD8DBAC004BC9CE /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 9AFDCD832846553000850B41 /* LaunchScreen.storyboard */; };
		9A4C52EC2CD8DBAC004BC9CE /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 9A150AA82214092B00840F54 /* Images.xcassets */; };
		9A4C52ED2CD8DBAC004BC9CE /* Qdraw.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 9A01F5492ABD33170024CC0A /* Qdraw.bundle */; };
		9A4C52EE2CD8DBAC004BC9CE /* EsignSDK.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 9A4BC7112CBD18230051D311 /* EsignSDK.bundle */; };
		9A4C52EF2CD8DBAC004BC9CE /* Panding.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 9AA557762C8D839D00F902A5 /* Panding.bundle */; };
		9A4C52F02CD8DBAC004BC9CE /* InfoPlist.strings in Resources */ = {isa = PBXBuildFile; fileRef = 9AA0904F2856EEAE00E835CB /* InfoPlist.strings */; };
		9A4C52F12CD8DBAC004BC9CE /* Localizable.strings in Resources */ = {isa = PBXBuildFile; fileRef = 08CE10FB22A8C2FB00E30241 /* Localizable.strings */; };
		9A4C52F22CD8DBAC004BC9CE /* pay_loading.gif in Resources */ = {isa = PBXBuildFile; fileRef = 9A48D71C2CCBC26400A3B04F /* pay_loading.gif */; };
		9A4C52F32CD8DBAC004BC9CE /* loading.gif in Resources */ = {isa = PBXBuildFile; fileRef = 9A6480482CC6452900C20F02 /* loading.gif */; };
		9A4C52F42CD8DBAC004BC9CE /* Pointer.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 9AA557962C8D839D00F902A5 /* Pointer.bundle */; };
		9A4C52F52CD8DBAC004BC9CE /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 9AA557832C8D839D00F902A5 /* PrivacyInfo.xcprivacy */; };
		9A4C52FF2CD8DBF0004BC9CE /* Xinruihua-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = 9A4C52FE2CD8DBF0004BC9CE /* Xinruihua-Info.plist */; };
		9A4C53002CD8DBF0004BC9CE /* Xinruihua-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = 9A4C52FE2CD8DBF0004BC9CE /* Xinruihua-Info.plist */; };
		9A6480492CC6452900C20F02 /* loading.gif in Resources */ = {isa = PBXBuildFile; fileRef = 9A6480482CC6452900C20F02 /* loading.gif */; };
		9A64804C2CC6492900C20F02 /* SACustomHudImg.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A64804B2CC6492900C20F02 /* SACustomHudImg.m */; };
		9A6E92762BF5BDDD003D679C /* SAMarqueeView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A6E92752BF5BDDD003D679C /* SAMarqueeView.m */; };
		9A6E92802BF5E86C003D679C /* SAGetCustomerUrlApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A6E927F2BF5E86C003D679C /* SAGetCustomerUrlApiManager.m */; };
		9A818DD82BF5A721008DF462 /* SACycleVerticalView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A818DD42BF5A721008DF462 /* SACycleVerticalView.m */; };
		9A818DDB2BF5A721008DF462 /* SACycleView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A818DD52BF5A721008DF462 /* SACycleView.m */; };
		9A9FFDA12CCB4CB400DF3EE9 /* NPOnlineRepayViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A9FFDA02CCB4CB400DF3EE9 /* NPOnlineRepayViewController.m */; };
		9A9FFDA42CCB4ED300DF3EE9 /* SAGetCashierInfoAPi.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A9FFDA32CCB4ED300DF3EE9 /* SAGetCashierInfoAPi.m */; };
		9A9FFDA72CCB4F8100DF3EE9 /* SACashierDoPayApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A9FFDA62CCB4F8100DF3EE9 /* SACashierDoPayApi.m */; };
		9A9FFDAA2CCB513A00DF3EE9 /* SACashierPayStatusApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A9FFDA92CCB513A00DF3EE9 /* SACashierPayStatusApi.m */; };
		9AA0904D2856EEAE00E835CB /* InfoPlist.strings in Resources */ = {isa = PBXBuildFile; fileRef = 9AA0904F2856EEAE00E835CB /* InfoPlist.strings */; };
		9AA5575B2C8B309300F902A5 /* SAGetBillDetailApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AA5575A2C8B309300F902A5 /* SAGetBillDetailApi.m */; };
		9AA5575E2C8D4AE700F902A5 /* SAHomeConfirmCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AA5575D2C8D4AE700F902A5 /* SAHomeConfirmCell.m */; };
		9AA557612C8D4B7F00F902A5 /* SAConfrimTradeViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AA557602C8D4B7F00F902A5 /* SAConfrimTradeViewController.m */; };
		9AA557672C8D4CC200F902A5 /* SAGetTradeDetailApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AA557662C8D4CC200F902A5 /* SAGetTradeDetailApi.m */; };
		9AA5576A2C8D4D0F00F902A5 /* SASubmitTradeApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AA557692C8D4D0F00F902A5 /* SASubmitTradeApi.m */; };
		9AA5576D2C8D4D5C00F902A5 /* SAGetEsignUrlApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AA5576C2C8D4D5C00F902A5 /* SAGetEsignUrlApi.m */; };
		9AA557702C8D52D300F902A5 /* SATradeDetailCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AA5576F2C8D52D300F902A5 /* SATradeDetailCell.m */; };
		9AA557982C8D839D00F902A5 /* SAPickerAlertView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AA557732C8D839D00F902A5 /* SAPickerAlertView.m */; };
		9AA557992C8D839D00F902A5 /* Panding.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 9AA557762C8D839D00F902A5 /* Panding.bundle */; };
		9AA5579A2C8D839D00F902A5 /* SAPickerStyle.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AA557772C8D839D00F902A5 /* SAPickerStyle.m */; };
		9AA5579B2C8D839D00F902A5 /* SABundle+BRPickerView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AA557782C8D839D00F902A5 /* SABundle+BRPickerView.m */; };
		9AA5579C2C8D839D00F902A5 /* SADatePickerView+BR.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AA5577C2C8D839D00F902A5 /* SADatePickerView+BR.m */; };
		9AA5579D2C8D839D00F902A5 /* SADate+BRPickerView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AA5577E2C8D839D00F902A5 /* SADate+BRPickerView.m */; };
		9AA5579E2C8D839D00F902A5 /* SADatePickerView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AA5577F2C8D839D00F902A5 /* SADatePickerView.m */; };
		9AA5579F2C8D839D00F902A5 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 9AA557832C8D839D00F902A5 /* PrivacyInfo.xcprivacy */; };
		9AA557A02C8D839D00F902A5 /* SATextModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AA557872C8D839D00F902A5 /* SATextModel.m */; };
		9AA557A12C8D839D00F902A5 /* SATextPickerView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AA557882C8D839D00F902A5 /* SATextPickerView.m */; };
		9AA557A22C8D839D00F902A5 /* SAResultModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AA5578B2C8D839D00F902A5 /* SAResultModel.m */; };
		9AA557A32C8D839D00F902A5 /* SAStringPickerView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AA5578E2C8D839D00F902A5 /* SAStringPickerView.m */; };
		9AA557A42C8D839D00F902A5 /* SABaseView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AA557902C8D839D00F902A5 /* SABaseView.m */; };
		9AA557A52C8D839D00F902A5 /* SAAddressModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AA557932C8D839D00F902A5 /* SAAddressModel.m */; };
		9AA557A62C8D839D00F902A5 /* Pointer.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 9AA557962C8D839D00F902A5 /* Pointer.bundle */; };
		9AA557A72C8D839D00F902A5 /* Pointer.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AA557972C8D839D00F902A5 /* Pointer.m */; };
		9AB09A73284276F7005C55AB /* SAGetRegManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AB09A72284276F7005C55AB /* SAGetRegManager.m */; };
		9ABB8E1522141F1B00FBF613 /* SAMainManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ABB8E1422141F1B00FBF613 /* SAMainManager.m */; };
		9ABE6D202CBF9EB8003B5100 /* SAContactItemCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ABE6D1F2CBF9EB8003B5100 /* SAContactItemCell.m */; };
		9ABE6D232CBF9F3A003B5100 /* SAVerifyContactsViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ABE6D222CBF9F3A003B5100 /* SAVerifyContactsViewController.m */; };
		9AC7DFE52CE595D000944F3B /* SAService.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A150B102214092B00840F54 /* SAService.m */; };
		9AC7DFE62CE595D000944F3B /* SAFileUploadApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A2506F72AD54F08008C1068 /* SAFileUploadApiManager.m */; };
		9AC7DFE72CE595D000944F3B /* SAMainHomeApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF04B622AF3EC500DF17E6 /* SAMainHomeApiManager.m */; };
		9AC7DFE82CE595D000944F3B /* SAResponse.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A150AED2214092B00840F54 /* SAResponse.m */; };
		9AC7DFE92CE595D000944F3B /* SADictionary+Common.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D96322AE6D680044AB4D /* SADictionary+Common.m */; };
		9AC7DFEA2CE595D000944F3B /* SAView+MASAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ACA665E2AC1345E00D6250B /* SAView+MASAdditions.m */; };
		9AC7DFEB2CE595D000944F3B /* SACommonTool.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D95922AE6D670044AB4D /* SACommonTool.m */; };
		9AC7DFEC2CE595D000944F3B /* NPInviteAlertViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AE3D2072CCA488C008C0D58 /* NPInviteAlertViewController.m */; };
		9AC7DFED2CE595D000944F3B /* SAVersionUpdateViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 08D0A8A022C31B9700D68951 /* SAVersionUpdateViewController.m */; };
		9AC7DFEE2CE595D000944F3B /* SAMutableDictionary+Extension.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D96022AE6D670044AB4D /* SAMutableDictionary+Extension.m */; };
		9AC7DFEF2CE595D000944F3B /* SAMyCardsListViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ACF14E72C8DB6200057A6D1 /* SAMyCardsListViewController.m */; };
		9AC7DFF02CE595D000944F3B /* SADate+Extension.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D97522AE6D680044AB4D /* SADate+Extension.m */; };
		9AC7DFF12CE595D000944F3B /* SARequestHandle.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AFB9919222005B400F294D4 /* SARequestHandle.m */; };
		9AC7DFF22CE595D000944F3B /* SAHomeProtocolUrlsModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF051022AF539700DF17E6 /* SAHomeProtocolUrlsModel.m */; };
		9AC7DFF32CE595D000944F3B /* SAFixedTabbarView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AF5C40A2C8B0B77000EF027 /* SAFixedTabbarView.m */; };
		9AC7DFF42CE595D000944F3B /* SAFaceSubmitApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A2506DF2AD4F318008C1068 /* SAFaceSubmitApiManager.m */; };
		9AC7DFF52CE595D000944F3B /* SAGetPersonalInfoApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 0805C2F022B2570800021D25 /* SAGetPersonalInfoApiManager.m */; };
		9AC7DFF62CE595D000944F3B /* SARefreshStateTrailer.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F5642ABD33170024CC0A /* SARefreshStateTrailer.m */; };
		9AC7DFF72CE595D000944F3B /* SAViewConstraint.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ACA66632AC1345E00D6250B /* SAViewConstraint.m */; };
		9AC7DFF82CE595D000944F3B /* SAMainTopBgView.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF05B222AFAAE300DF17E6 /* SAMainTopBgView.m */; };
		9AC7DFF92CE595D000944F3B /* SAVerifyInfoCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 0856E5C522B279CB00D5C183 /* SAVerifyInfoCell.m */; };
		9AC7DFFA2CE595D000944F3B /* SARefreshTrailer.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F57A2ABD33170024CC0A /* SARefreshTrailer.m */; };
		9AC7DFFB2CE595D000944F3B /* SARepayListVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AF5C4162C8B113B000EF027 /* SARepayListVC.m */; };
		9AC7DFFC2CE595D000944F3B /* SATextView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A16888E2C8ED0E800A98C9B /* SATextView.m */; };
		9AC7DFFD2CE595D000944F3B /* SAPersonalInfoModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 0805C2F322B25B2000021D25 /* SAPersonalInfoModel.m */; };
		9AC7DFFE2CE595D000944F3B /* SAPhotoManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 08B1ECC022B8CEC5001367BB /* SAPhotoManager.m */; };
		9AC7DFFF2CE595D000944F3B /* SANetworkConst.m in Sources */ = {isa = PBXBuildFile; fileRef = 08CE109922A8AA4100E30241 /* SANetworkConst.m */; };
		9AC7E0002CE595D000944F3B /* SAGetOcrConfigApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A2506DC2AD41375008C1068 /* SAGetOcrConfigApiManager.m */; };
		9AC7E0012CE595D000944F3B /* SATextPickerView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AA557882C8D839D00F902A5 /* SATextPickerView.m */; };
		9AC7E0022CE595D000944F3B /* SARefreshConst.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F5442ABD33170024CC0A /* SARefreshConst.m */; };
		9AC7E0032CE595D000944F3B /* SANLUploadManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A2506F12AD54AA5008C1068 /* SANLUploadManager.m */; };
		9AC7E0042CE595D000944F3B /* SARefreshBackGifFooter.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F5582ABD33170024CC0A /* SARefreshBackGifFooter.m */; };
		9AC7E0052CE595D000944F3B /* SARefreshBackStateFooter.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F5592ABD33170024CC0A /* SARefreshBackStateFooter.m */; };
		9AC7E0062CE595D000944F3B /* SASmsCodeViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A0A39632A7A02EC0095AB86 /* SASmsCodeViewController.m */; };
		9AC7E0072CE595D000944F3B /* SAVerifyIdentityViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A35DE6E283F1AFA00C387B7 /* SAVerifyIdentityViewController.m */; };
		9AC7E0082CE595D000944F3B /* SATextModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AA557872C8D839D00F902A5 /* SATextModel.m */; };
		9AC7E0092CE595D000944F3B /* SAPaidListVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AF5C4192C8B1155000EF027 /* SAPaidListVC.m */; };
		9AC7E00A2CE595D000944F3B /* SAPickerAlertView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AA557732C8D839D00F902A5 /* SAPickerAlertView.m */; };
		9AC7E00B2CE595D000944F3B /* SAObject+QLNetworkingMethods.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A150B042214092B00840F54 /* SAObject+QLNetworkingMethods.m */; };
		9AC7E00C2CE595D000944F3B /* SABorrowCashPeriodModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF051F22AF5CCF00DF17E6 /* SABorrowCashPeriodModel.m */; };
		9AC7E00D2CE595D000944F3B /* SAPersonalInfoCheckModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 0805C2F922B25C5B00021D25 /* SAPersonalInfoCheckModel.m */; };
		9AC7E00E2CE595D000944F3B /* SAGetEsignUrlApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AA5576C2C8D4D5C00F902A5 /* SAGetEsignUrlApi.m */; };
		9AC7E00F2CE595D000944F3B /* NPOnlineRepayViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A9FFDA02CCB4CB400DF3EE9 /* NPOnlineRepayViewController.m */; };
		9AC7E0102CE595D000944F3B /* SABasicViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D8C622AE6ACF0044AB4D /* SABasicViewController.m */; };
		9AC7E0112CE595D000944F3B /* SAApiProxy.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A150AF72214092B00840F54 /* SAApiProxy.m */; };
		9AC7E0122CE595D000944F3B /* SAObjC.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F53D2ABC2FC10024CC0A /* SAObjC.m */; };
		9AC7E0132CE595D000944F3B /* SAImage+Extension.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF054122AF777900DF17E6 /* SAImage+Extension.m */; };
		9AC7E0142CE595D000944F3B /* SAPayClientInfoApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 082B160722BB871600A4C7C7 /* SAPayClientInfoApiManager.m */; };
		9AC7E0152CE595D000944F3B /* SAString+Size.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A35DE0A283E046D00C387B7 /* SAString+Size.m */; };
		9AC7E0162CE595D000944F3B /* SACustomSlideView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AF5C40C2C8B0B77000EF027 /* SACustomSlideView.m */; };
		9AC7E0172CE595D000944F3B /* SAHomeNormalCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A35DE92283FA9D400C387B7 /* SAHomeNormalCell.m */; };
		9AC7E0182CE595D000944F3B /* SABCTool.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D97E22AE6D680044AB4D /* SABCTool.m */; };
		9AC7E0192CE595D000944F3B /* SAView+Extension.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D96C22AE6D680044AB4D /* SAView+Extension.m */; };
		9AC7E01A2CE595D000944F3B /* SAShapeLayer+Extension.m in Sources */ = {isa = PBXBuildFile; fileRef = 08B1EC4822B8815A001367BB /* SAShapeLayer+Extension.m */; };
		9AC7E01B2CE595D000944F3B /* SAString+QLNetworkingMethods.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A150B062214092B00840F54 /* SAString+QLNetworkingMethods.m */; };
		9AC7E01C2CE595D000944F3B /* SAPayWayModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 082B160A22BB8CCA00A4C7C7 /* SAPayWayModel.m */; };
		9AC7E01D2CE595D000944F3B /* SASaveBindCardInfoApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AEC08822C89A597008475BD /* SASaveBindCardInfoApi.m */; };
		9AC7E01E2CE595D000944F3B /* SAConfrimTradeViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AA557602C8D4B7F00F902A5 /* SAConfrimTradeViewController.m */; };
		9AC7E01F2CE595D000944F3B /* SAConfirmSmsAlertView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ACF14E12C8D93DD0057A6D1 /* SAConfirmSmsAlertView.m */; };
		9AC7E0202CE595D000944F3B /* SAContactsManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 08B4A93D22B79A0D006B35D4 /* SAContactsManager.m */; };
		9AC7E0212CE595D000944F3B /* SARefreshGifHeader.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F56D2ABD33170024CC0A /* SARefreshGifHeader.m */; };
		9AC7E0222CE595D000944F3B /* SABaseManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A150AF42214092B00840F54 /* SABaseManager.m */; };
		9AC7E0232CE595D000944F3B /* SAPickerTextField.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF4C2022B604390095863C /* SAPickerTextField.m */; };
		9AC7E0242CE595D000944F3B /* SANetworkingConfigurationManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A150AE32214092B00840F54 /* SANetworkingConfigurationManager.m */; };
		9AC7E0252CE595D000944F3B /* SARefreshFooter.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F56F2ABD33170024CC0A /* SARefreshFooter.m */; };
		9AC7E0262CE595D000944F3B /* SAGetCashierInfoAPi.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A9FFDA32CCB4ED300DF3EE9 /* SAGetCashierInfoAPi.m */; };
		9AC7E0272CE595D000944F3B /* SACustomHudImg.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A64804B2CC6492900C20F02 /* SACustomHudImg.m */; };
		9AC7E0282CE595D000944F3B /* SASettingViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AEC08912C89DAA0008475BD /* SASettingViewController.m */; };
		9AC7E0292CE595D000944F3B /* SAGetContactInfoApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF4C5E22B61CF20095863C /* SAGetContactInfoApiManager.m */; };
		9AC7E02A2CE595D000944F3B /* SASubmitTradeApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AA557692C8D4D0F00F902A5 /* SASubmitTradeApi.m */; };
		9AC7E02B2CE595D000944F3B /* SAProtocolViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A1702532C8866CB00FD811E /* SAProtocolViewController.m */; };
		9AC7E02C2CE595D000944F3B /* SAServiceView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AF5C3E12C8AE656000EF027 /* SAServiceView.m */; };
		9AC7E02D2CE595D000944F3B /* SATabBarViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D8C722AE6ACF0044AB4D /* SATabBarViewController.m */; };
		9AC7E02E2CE595D000944F3B /* SACycleVerticalView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A818DD42BF5A721008DF462 /* SACycleVerticalView.m */; };
		9AC7E02F2CE595D000944F3B /* SABorrowCashModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF051C22AF5C7600DF17E6 /* SABorrowCashModel.m */; };
		9AC7E0302CE595D000944F3B /* SAHomePageViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D93F22AE6C880044AB4D /* SAHomePageViewController.m */; };
		9AC7E0312CE595D000944F3B /* SAOfflineRepayViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A2506E82AD53FA3008C1068 /* SAOfflineRepayViewController.m */; };
		9AC7E0322CE595D000944F3B /* SAGetCitiesListApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A021B1D2AB8622F007EC584 /* SAGetCitiesListApiManager.m */; };
		9AC7E0332CE595D000944F3B /* SAMarqueeView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A6E92752BF5BDDD003D679C /* SAMarqueeView.m */; };
		9AC7E0342CE595D000944F3B /* SABannerModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A02DF542CC656C000431F1B /* SABannerModel.m */; };
		9AC7E0352CE595D000944F3B /* SABankItemView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AEC08762C899B1E008475BD /* SABankItemView.m */; };
		9AC7E0362CE595D000944F3B /* SAHomeBasicCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF056E22AF9B4400DF17E6 /* SAHomeBasicCell.m */; };
		9AC7E0372CE595D000944F3B /* SAFont+Extension.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D96F22AE6D680044AB4D /* SAFont+Extension.m */; };
		9AC7E0382CE595D000944F3B /* SAVerifyBankCardViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AEC08732C898F4D008475BD /* SAVerifyBankCardViewController.m */; };
		9AC7E0392CE595D000944F3B /* Pointer.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AA557972C8D839D00F902A5 /* Pointer.m */; };
		9AC7E03A2CE595D000944F3B /* SAViewAttribute.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ACA66642AC1345E00D6250B /* SAViewAttribute.m */; };
		9AC7E03B2CE595D000944F3B /* SAView+MJExtension.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F5512ABD33170024CC0A /* SAView+MJExtension.m */; };
		9AC7E03C2CE595D000944F3B /* SAAlertSureTipsViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 08D14F5A22BCCB4C0094582F /* SAAlertSureTipsViewController.m */; };
		9AC7E03D2CE595D000944F3B /* SAAlertController+Orientation.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D96822AE6D680044AB4D /* SAAlertController+Orientation.m */; };
		9AC7E03E2CE595D000944F3B /* SAResponseHandle.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AFB9916222005B400F294D4 /* SAResponseHandle.m */; };
		9AC7E03F2CE595D000944F3B /* SAAppCodeManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 08CE116222A8F73F00E30241 /* SAAppCodeManager.m */; };
		9AC7E0402CE595D000944F3B /* SAServiceFactory.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A150B122214092B00840F54 /* SAServiceFactory.m */; };
		9AC7E0412CE595D000944F3B /* SAScrollView+MJExtension.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F54B2ABD33170024CC0A /* SAScrollView+MJExtension.m */; };
		9AC7E0422CE595D000944F3B /* SABankListModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 08E66BCF22B4F7C7005E62D9 /* SABankListModel.m */; };
		9AC7E0432CE595D000944F3B /* SABankBinListViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AEC088B2C89BD26008475BD /* SABankBinListViewController.m */; };
		9AC7E0442CE595D000944F3B /* SABannerCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A02DF5A2CC65D2600431F1B /* SABannerCell.m */; };
		9AC7E0452CE595D000944F3B /* SAPaidCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A02DF5D2CC66F0100431F1B /* SAPaidCell.m */; };
		9AC7E0462CE595D000944F3B /* SAPickerStyle.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AA557772C8D839D00F902A5 /* SAPickerStyle.m */; };
		9AC7E0472CE595D000944F3B /* SARepaymentViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AF5C3EA2C8B0193000EF027 /* SARepaymentViewController.m */; };
		9AC7E0482CE595D000944F3B /* SAAppConfigApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 08CE116522A8F79400E30241 /* SAAppConfigApiManager.m */; };
		9AC7E0492CE595D000944F3B /* SACompareUploadManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A35DE8F283F95BA00C387B7 /* SACompareUploadManager.m */; };
		9AC7E04A2CE595D000944F3B /* SARefreshAutoFooter.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F5752ABD33170024CC0A /* SARefreshAutoFooter.m */; };
		9AC7E04B2CE595D000944F3B /* SACompositeConstraint.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ACA665A2AC1345E00D6250B /* SACompositeConstraint.m */; };
		9AC7E04C2CE595D000944F3B /* SAResultModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AA5578B2C8D839D00F902A5 /* SAResultModel.m */; };
		9AC7E04D2CE595D000944F3B /* SARequest+QLNetworkingMethods.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A150B032214092B00840F54 /* SARequest+QLNetworkingMethods.m */; };
		9AC7E04E2CE595D000944F3B /* SAHomeConfirmCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AA5575D2C8D4AE700F902A5 /* SAHomeConfirmCell.m */; };
		9AC7E04F2CE595D000944F3B /* SALocationManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D97822AE6D680044AB4D /* SALocationManager.m */; };
		9AC7E0502CE595D000944F3B /* SALoginViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D95022AE6D1D0044AB4D /* SALoginViewController.m */; };
		9AC7E0512CE595D000944F3B /* SAStringPickerView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AA5578E2C8D839D00F902A5 /* SAStringPickerView.m */; };
		9AC7E0522CE595D000944F3B /* SAHomeProtocolModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF051322AF53CF00DF17E6 /* SAHomeProtocolModel.m */; };
		9AC7E0532CE595D000944F3B /* SAMutableString+QLNetworkingMethods.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A150B082214092B00840F54 /* SAMutableString+QLNetworkingMethods.m */; };
		9AC7E0542CE595D000944F3B /* SARequestGenerator.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A150B002214092B00840F54 /* SARequestGenerator.m */; };
		9AC7E0552CE595D000944F3B /* SAVerifyListItemModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 0805C28B22B1F6F900021D25 /* SAVerifyListItemModel.m */; };
		9AC7E0562CE595D000944F3B /* SAGetProtocolApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A1702562C8964D100FD811E /* SAGetProtocolApiManager.m */; };
		9AC7E0572CE595D000944F3B /* SAData+Compress.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A42882C2844B83F000FE25F /* SAData+Compress.m */; };
		9AC7E0582CE595D000944F3B /* SAGetTradeDetailApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AA557662C8D4CC200F902A5 /* SAGetTradeDetailApi.m */; };
		9AC7E0592CE595D000944F3B /* SACycleView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A818DD52BF5A721008DF462 /* SACycleView.m */; };
		9AC7E05A2CE595D000944F3B /* SAConstraintMaker.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ACA665B2AC1345E00D6250B /* SAConstraintMaker.m */; };
		9AC7E05B2CE595D000944F3B /* SADate+BRPickerView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AA5577E2C8D839D00F902A5 /* SADate+BRPickerView.m */; };
		9AC7E05C2CE595D000944F3B /* SAUploadInfoManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A4288282844A4E9000FE25F /* SAUploadInfoManager.m */; };
		9AC7E05D2CE595D000944F3B /* SAManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 08F4C18D22C078330000B366 /* SAManager.m */; };
		9AC7E05E2CE595D000944F3B /* SALogger.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A150AE82214092B00840F54 /* SALogger.m */; };
		9AC7E05F2CE595D000944F3B /* SADeviceTool.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D97D22AE6D680044AB4D /* SADeviceTool.m */; };
		9AC7E0602CE595D000944F3B /* SALoggerConfiguration.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A150AE22214092B00840F54 /* SALoggerConfiguration.m */; };
		9AC7E0612CE595D000944F3B /* SAHistoryOrderModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 08EC05D22315245200D3C93C /* SAHistoryOrderModel.m */; };
		9AC7E0622CE595D000944F3B /* SAVerifyCodeInputView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0878035122B110B8000A7A4F /* SAVerifyCodeInputView.m */; };
		9AC7E0632CE595D000944F3B /* SAScope.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AFB99352220082200F294D4 /* SAScope.m */; };
		9AC7E0642CE595D000944F3B /* SAMobileLoginApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF04EE22AF424E00DF17E6 /* SAMobileLoginApiManager.m */; };
		9AC7E0652CE595D000944F3B /* SABorrowPurposeTypeModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF051922AF5BE500DF17E6 /* SABorrowPurposeTypeModel.m */; };
		9AC7E0662CE595D000944F3B /* SAUploadManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A35DE86283F46EF00C387B7 /* SAUploadManager.m */; };
		9AC7E0672CE595D000944F3B /* SAGetCarrierUrlApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A2506E22AD506B4008C1068 /* SAGetCarrierUrlApiManager.m */; };
		9AC7E0682CE595D000944F3B /* SARefreshStateHeader.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F56C2ABD33170024CC0A /* SARefreshStateHeader.m */; };
		9AC7E0692CE595D000944F3B /* SANoNetView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D91322AE6BB40044AB4D /* SANoNetView.m */; };
		9AC7E06A2CE595D000944F3B /* SAAdvanceManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A35DE6B283E38C400C387B7 /* SAAdvanceManager.m */; };
		9AC7E06B2CE595D000944F3B /* SAToast+UIView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A35DE06283E03C300C387B7 /* SAToast+UIView.m */; };
		9AC7E06C2CE595D000944F3B /* SAUserCenterApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 0878033522B0E898000A7A4F /* SAUserCenterApiManager.m */; };
		9AC7E06D2CE595D000944F3B /* SAMyBankItemModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ACF14F32C8DBC5F0057A6D1 /* SAMyBankItemModel.m */; };
		9AC7E06E2CE595D000944F3B /* SASavePersonalInfoApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF4C5822B61C010095863C /* SASavePersonalInfoApiManager.m */; };
		9AC7E06F2CE595D000944F3B /* SADeviceInfoSaveApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 08A6BAEC22B1E67F001CA577 /* SADeviceInfoSaveApiManager.m */; };
		9AC7E0702CE595D000944F3B /* SAAddressModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AA557932C8D839D00F902A5 /* SAAddressModel.m */; };
		9AC7E0712CE595D000944F3B /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A150AA72214092B00840F54 /* main.m */; };
		9AC7E0722CE595D000944F3B /* SAVerifyContactsViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ABE6D222CBF9F3A003B5100 /* SAVerifyContactsViewController.m */; };
		9AC7E0732CE595D000944F3B /* SAAuditProgressItemModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 0880D0C122BB2357001C979F /* SAAuditProgressItemModel.m */; };
		9AC7E0742CE595D000944F3B /* SAUploadModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A2506EE2AD54A97008C1068 /* SAUploadModel.m */; };
		9AC7E0752CE595D000944F3B /* SAHomeReviewingCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A15F9762840B5E900D79C39 /* SAHomeReviewingCell.m */; };
		9AC7E0762CE595D000944F3B /* SABaseView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AA557902C8D839D00F902A5 /* SABaseView.m */; };
		9AC7E0772CE595D000944F3B /* SABindCardResendSmsApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AEC087C2C89A136008475BD /* SABindCardResendSmsApi.m */; };
		9AC7E0782CE595D000944F3B /* SAPersonalVerifyModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 083AB35322B36DEA003B4BB4 /* SAPersonalVerifyModel.m */; };
		9AC7E0792CE595D000944F3B /* SATabedSlideView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AF5C4042C8B0B77000EF027 /* SATabedSlideView.m */; };
		9AC7E07A2CE595D000944F3B /* SARepayH5ViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A2533062C902CA90093728D /* SARepayH5ViewController.m */; };
		9AC7E07B2CE595D000944F3B /* SACommonNoDataView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A2245D2294AED5400DA1F9D /* SACommonNoDataView.m */; };
		9AC7E07C2CE595D000944F3B /* SAOrderInfoModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF051622AF5A2F00DF17E6 /* SAOrderInfoModel.m */; };
		9AC7E07D2CE595D000944F3B /* SASaveGPSApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 08F4C12222C063380000B366 /* SASaveGPSApiManager.m */; };
		9AC7E07E2CE595D000944F3B /* SATradeDetailCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AA5576F2C8D52D300F902A5 /* SATradeDetailCell.m */; };
		9AC7E07F2CE595D000944F3B /* SAClientChargesModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 08D14F0F22BC7E1E0094582F /* SAClientChargesModel.m */; };
		9AC7E0802CE595D000944F3B /* SAGetRegManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AB09A72284276F7005C55AB /* SAGetRegManager.m */; };
		9AC7E0812CE595D000944F3B /* NPCashierCardCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A48D71A2CCBB80500A3B04F /* NPCashierCardCell.m */; };
		9AC7E0822CE595D000944F3B /* SARefreshConfig.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F5452ABD33170024CC0A /* SARefreshConfig.m */; };
		9AC7E0832CE595D000944F3B /* SAScrollTabbarView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AF5C4092C8B0B77000EF027 /* SAScrollTabbarView.m */; };
		9AC7E0842CE595D000944F3B /* SAErrorView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0811F47122CB41D000CDBBD1 /* SAErrorView.m */; };
		9AC7E0852CE595D000944F3B /* SARefreshAutoStateFooter.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F55F2ABD33170024CC0A /* SARefreshAutoStateFooter.m */; };
		9AC7E0862CE595D000944F3B /* SALayoutConstraint+MASDebugAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ACA664D2AC1345E00D6250B /* SALayoutConstraint+MASDebugAdditions.m */; };
		9AC7E0872CE595D000944F3B /* SARefreshNormalHeader.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F5682ABD33170024CC0A /* SARefreshNormalHeader.m */; };
		9AC7E0882CE595D000944F3B /* SAContactSelectModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF4C6B22B623870095863C /* SAContactSelectModel.m */; };
		9AC7E0892CE595D000944F3B /* SAContactRowModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF4C7422B626580095863C /* SAContactRowModel.m */; };
		9AC7E08A2CE595D000944F3B /* SALoanRecordModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A2245AE2941D07A00DA1F9D /* SALoanRecordModel.m */; };
		9AC7E08B2CE595D000944F3B /* SARepayListModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AF5C3F32C8B0253000EF027 /* SARepayListModel.m */; };
		9AC7E08C2CE595D000944F3B /* SAConfirmmBankViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ACF14EA2C8DB6350057A6D1 /* SAConfirmmBankViewController.m */; };
		9AC7E08D2CE595D000944F3B /* SAUserCenterModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 0878035422B138E9000A7A4F /* SAUserCenterModel.m */; };
		9AC7E08E2CE595D000944F3B /* SARefreshBackNormalFooter.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F55A2ABD33170024CC0A /* SARefreshBackNormalFooter.m */; };
		9AC7E08F2CE595D000944F3B /* SAQueryCardBinApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AEC088E2C89D150008475BD /* SAQueryCardBinApi.m */; };
		9AC7E0902CE595D000944F3B /* SAVerifyListManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 0805C2A822B24A7800021D25 /* SAVerifyListManager.m */; };
		9AC7E0912CE595D000944F3B /* SAWifiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 08AB288C22BF606900CAE59C /* SAWifiManager.m */; };
		9AC7E0922CE595D000944F3B /* SAMainManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ABB8E1422141F1B00FBF613 /* SAMainManager.m */; };
		9AC7E0932CE595D000944F3B /* SAGetBankRealNameApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A1688912C8EFA3000A98C9B /* SAGetBankRealNameApi.m */; };
		9AC7E0942CE595D000944F3B /* SAGetBorrowAgainApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ACF14F62C8DC04C0057A6D1 /* SAGetBorrowAgainApi.m */; };
		9AC7E0952CE595D000944F3B /* SAFaceBackAlert.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A09512E2CBE076C0053D586 /* SAFaceBackAlert.m */; };
		9AC7E0962CE595D000944F3B /* SAContactItemCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ABE6D1F2CBF9EB8003B5100 /* SAContactItemCell.m */; };
		9AC7E0972CE595D000944F3B /* SACollectionViewLayout+MJRefresh.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F5432ABD33170024CC0A /* SACollectionViewLayout+MJRefresh.m */; };
		9AC7E0982CE595D000944F3B /* NPHomeStepModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A33876D2CCA64740056585A /* NPHomeStepModel.m */; };
		9AC7E0992CE595D000944F3B /* SADictionary+QLNetworkingMethods.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A150B072214092B00840F54 /* SADictionary+QLNetworkingMethods.m */; };
		9AC7E09A2CE595D000944F3B /* SARuntimeExtensions.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AFB99362220082200F294D4 /* SARuntimeExtensions.m */; };
		9AC7E09B2CE595D000944F3B /* SASubmitProductInfoModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 083D64D92313E2E400810B26 /* SASubmitProductInfoModel.m */; };
		9AC7E09C2CE595D000944F3B /* SARepaymentCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AF5C3ED2C8B0212000EF027 /* SARepaymentCell.m */; };
		9AC7E09D2CE595D000944F3B /* SAArray+MASAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ACA665D2AC1345E00D6250B /* SAArray+MASAdditions.m */; };
		9AC7E09E2CE595D000944F3B /* SABasicService.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AFB991B222005B400F294D4 /* SABasicService.m */; };
		9AC7E09F2CE595D000944F3B /* SAAliyunOSSManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 08B1ED2322B8DDCE001367BB /* SAAliyunOSSManager.m */; };
		9AC7E0A02CE595D000944F3B /* SAGradientView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0878030022B0967D000A7A4F /* SAGradientView.m */; };
		9AC7E0A12CE595D000944F3B /* SABundle+BRPickerView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AA557782C8D839D00F902A5 /* SABundle+BRPickerView.m */; };
		9AC7E0A22CE595D000944F3B /* SAProductModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A2245A829418F5F00DA1F9D /* SAProductModel.m */; };
		9AC7E0A32CE595D000944F3B /* SABindCardSendCodeApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AEC08792C89A11D008475BD /* SABindCardSendCodeApi.m */; };
		9AC7E0A42CE595D000944F3B /* SATool.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D95822AE6D670044AB4D /* SATool.m */; };
		9AC7E0A52CE595D000944F3B /* SAHomeRejectCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF055F22AF923600DF17E6 /* SAHomeRejectCell.m */; };
		9AC7E0A62CE595D000944F3B /* SABankNameModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AEC08882C89BCCF008475BD /* SABankNameModel.m */; };
		9AC7E0A72CE595D000944F3B /* SAUtility.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AF5C40B2C8B0B77000EF027 /* SAUtility.m */; };
		9AC7E0A82CE595D000944F3B /* SAGetBillDetailApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AA5575A2C8B309300F902A5 /* SAGetBillDetailApi.m */; };
		9AC7E0A92CE595D000944F3B /* SAString+Extension.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D97222AE6D680044AB4D /* SAString+Extension.m */; };
		9AC7E0AA2CE595D000944F3B /* SAJQCache.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AF5C3FE2C8B0B77000EF027 /* SAJQCache.m */; };
		9AC7E0AB2CE595D000944F3B /* SACardListCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ACF14F02C8DB71E0057A6D1 /* SACardListCell.m */; };
		9AC7E0AC2CE595D000944F3B /* SAS3Manager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A2506FA2AD656E4008C1068 /* SAS3Manager.m */; };
		9AC7E0AD2CE595D000944F3B /* SABannerView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A02DF572CC6593400431F1B /* SABannerView.m */; };
		9AC7E0AE2CE595D000944F3B /* SASubmitManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A35DE89283F84D700C387B7 /* SASubmitManager.m */; };
		9AC7E0AF2CE595D000944F3B /* SAContactSectionModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF4C7122B626420095863C /* SAContactSectionModel.m */; };
		9AC7E0B02CE595D000944F3B /* SACenterYTextView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0856E5C222B2793C00D5C183 /* SACenterYTextView.m */; };
		9AC7E0B12CE595D000944F3B /* SADatePickerView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AA5577F2C8D839D00F902A5 /* SADatePickerView.m */; };
		9AC7E0B22CE595D000944F3B /* SAMineViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D94522AE6C880044AB4D /* SAMineViewController.m */; };
		9AC7E0B32CE595D000944F3B /* SAPayClientChargesApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 082B162322BBAECA00A4C7C7 /* SAPayClientChargesApiManager.m */; };
		9AC7E0B42CE595D000944F3B /* SALayoutConstraint.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ACA665C2AC1345E00D6250B /* SALayoutConstraint.m */; };
		9AC7E0B52CE595D000944F3B /* SARefreshAutoGifFooter.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F5602ABD33170024CC0A /* SARefreshAutoGifFooter.m */; };
		9AC7E0B62CE595D000944F3B /* SAGetLiveConfigManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A35DE4A283E1F8800C387B7 /* SAGetLiveConfigManager.m */; };
		9AC7E0B72CE595D000944F3B /* SACashierPayStatusApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A9FFDA92CCB513A00DF3EE9 /* SACashierPayStatusApi.m */; };
		9AC7E0B82CE595D000944F3B /* SAHomeBankModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A15F9A42840E22D00D79C39 /* SAHomeBankModel.m */; };
		9AC7E0B92CE595D000944F3B /* SAArray+QLNetworkingMethods.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A150B052214092B00840F54 /* SAArray+QLNetworkingMethods.m */; };
		9AC7E0BA2CE595D000944F3B /* SAImage+ChangeColor.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AD150A32C8AA0080033A9D9 /* SAImage+ChangeColor.m */; };
		9AC7E0BB2CE595D000944F3B /* SARefreshAutoNormalFooter.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F5612ABD33170024CC0A /* SARefreshAutoNormalFooter.m */; };
		9AC7E0BC2CE595D000944F3B /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A150AA32214092A00840F54 /* AppDelegate.m */; };
		9AC7E0BD2CE595D000944F3B /* SAGetPaidBillListApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AF5C3F92C8B05CA000EF027 /* SAGetPaidBillListApi.m */; };
		9AC7E0BE2CE595D000944F3B /* SAVerifyCodeApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 0878034D22B10C35000A7A4F /* SAVerifyCodeApiManager.m */; };
		9AC7E0BF2CE595D000944F3B /* SAHomeLoaningCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A33876A2CCA5AE90056585A /* SAHomeLoaningCell.m */; };
		9AC7E0C02CE595D000944F3B /* SALogoutApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF04F122AF432800DF17E6 /* SALogoutApiManager.m */; };
		9AC7E0C12CE595D000944F3B /* SAGetSupportBankListApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ACF14E42C8DB42A0057A6D1 /* SAGetSupportBankListApi.m */; };
		9AC7E0C22CE595D000944F3B /* SARefreshComponent.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F5772ABD33170024CC0A /* SARefreshComponent.m */; };
		9AC7E0C32CE595D000944F3B /* SAKeyChainTool.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D98022AE6D680044AB4D /* SAKeyChainTool.m */; };
		9AC7E0C42CE595D000944F3B /* SAOrderConfirmApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 08F5A3F6231517BF00D75C55 /* SAOrderConfirmApiManager.m */; };
		9AC7E0C52CE595D000944F3B /* SARefreshBackFooter.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F5742ABD33170024CC0A /* SARefreshBackFooter.m */; };
		9AC7E0C62CE595D000944F3B /* SAGetFileTokenApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A2506F42AD54D43008C1068 /* SAGetFileTokenApiManager.m */; };
		9AC7E0C72CE595D000944F3B /* SASlideView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AF5C40D2C8B0B77000EF027 /* SASlideView.m */; };
		9AC7E0C82CE595D000944F3B /* SABindCardInfoModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AEC08852C89A679008475BD /* SABindCardInfoModel.m */; };
		9AC7E0C92CE595D000944F3B /* SASaveContactInfoApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF4C6122B61D040095863C /* SASaveContactInfoApiManager.m */; };
		9AC7E0CA2CE595D000944F3B /* SAVerifyDataListApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 0805C28522B1F54600021D25 /* SAVerifyDataListApiManager.m */; };
		9AC7E0CB2CE595D000944F3B /* SARefreshNormalTrailer.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F5652ABD33170024CC0A /* SARefreshNormalTrailer.m */; };
		9AC7E0CC2CE595D000944F3B /* SATokenApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 08B1ED1422B8DC14001367BB /* SATokenApiManager.m */; };
		9AC7E0CD2CE595D000944F3B /* SAGetMyCardsApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ACF14ED2C8DB6550057A6D1 /* SAGetMyCardsApi.m */; };
		9AC7E0CE2CE595D000944F3B /* SABundle+MJRefresh.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F5502ABD33170024CC0A /* SABundle+MJRefresh.m */; };
		9AC7E0CF2CE595D000944F3B /* SAGetCustomerUrlApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A6E927F2BF5E86C003D679C /* SAGetCustomerUrlApiManager.m */; };
		9AC7E0D02CE595D000944F3B /* SAColor+Extension.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D96622AE6D680044AB4D /* SAColor+Extension.m */; };
		9AC7E0D12CE595D000944F3B /* SAUserManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 08CE115822A8E7A300E30241 /* SAUserManager.m */; };
		9AC7E0D22CE595D000944F3B /* SACashierDoPayApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A9FFDA62CCB4F8100DF3EE9 /* SACashierDoPayApi.m */; };
		9AC7E0D32CE595D000944F3B /* SAActionSheet.m in Sources */ = {isa = PBXBuildFile; fileRef = 08B1EC6222B8B46C001367BB /* SAActionSheet.m */; };
		9AC7E0D42CE595D000944F3B /* SAOfflineRepayInfoApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A2506EB2AD53FD6008C1068 /* SAOfflineRepayInfoApiManager.m */; };
		9AC7E0D52CE595D000944F3B /* SACache.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A150AFC2214092B00840F54 /* SACache.m */; };
		9AC7E0D62CE595D000944F3B /* SAOrderDetailViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A2245AB2941BAC400DA1F9D /* SAOrderDetailViewController.m */; };
		9AC7E0D72CE595D000944F3B /* SAGetRepayBillListApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AF5C3F62C8B053B000EF027 /* SAGetRepayBillListApi.m */; };
		9AC7E0D82CE595D000944F3B /* SAViewController+MASAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ACA66562AC1345E00D6250B /* SAViewController+MASAdditions.m */; };
		9AC7E0D92CE595D000944F3B /* SAVerifyBasicInfoViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0805C2D822B24DF900021D25 /* SAVerifyBasicInfoViewController.m */; };
		9AC7E0DA2CE595D000944F3B /* SANormalRefresh.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D95C22AE6D670044AB4D /* SANormalRefresh.m */; };
		9AC7E0DB2CE595D000944F3B /* SACommonWebViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 08B1EC4B22B88754001367BB /* SACommonWebViewController.m */; };
		9AC7E0DC2CE595D000944F3B /* SAHomeModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FF04FA22AF455E00DF17E6 /* SAHomeModel.m */; };
		9AC7E0DD2CE595D000944F3B /* SAScrollView+MJRefresh.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F5422ABD33170024CC0A /* SAScrollView+MJRefresh.m */; };
		9AC7E0DE2CE595D000944F3B /* SACachedObject.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A150AFD2214092B00840F54 /* SACachedObject.m */; };
		9AC7E0DF2CE595D000944F3B /* SAConstraint.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ACA66612AC1345E00D6250B /* SAConstraint.m */; };
		9AC7E0E02CE595D000944F3B /* SARepaymentModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 08D14F3A22BC7E970094582F /* SARepaymentModel.m */; };
		9AC7E0E12CE595D000944F3B /* SARefreshHeader.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A01F5712ABD33170024CC0A /* SARefreshHeader.m */; };
		9AC7E0E22CE595D000944F3B /* SADatePickerView+BR.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AA5577C2C8D839D00F902A5 /* SADatePickerView+BR.m */; };
		9AC7E0E32CE595D000944F3B /* SARepayCashierModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A48D7172CCBB5F000A3B04F /* SARepayCashierModel.m */; };
		9AC7E0E42CE595D000944F3B /* SASaveDeviceChannelApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 089A56B922E9B3FC005BEBD5 /* SASaveDeviceChannelApiManager.m */; };
		9AC7E0E52CE595D000944F3B /* SAPayStatusViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A48D7112CCBA59500A3B04F /* SAPayStatusViewController.m */; };
		9AC7E0E62CE595D000944F3B /* NPTradeAlertViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A3387672CCA562D0056585A /* NPTradeAlertViewController.m */; };
		9AC7E0E72CE595D000944F3B /* SANavigationViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0845D8CB22AE6ACF0044AB4D /* SANavigationViewController.m */; };
		9AC7E0E82CE595D000944F3B /* SAVerifyListModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 0805C28822B1F6DD00021D25 /* SAVerifyListModel.m */; };
		9AC7E0E92CE595D000944F3B /* SAOrderConfigApiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A3BA6D4285ACE1C0041A741 /* SAOrderConfigApiManager.m */; };
		9AC7E0EB2CE595D000944F3B /* libbz2.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 9A0F351F2AD3CE370080FD21 /* libbz2.tbd */; };
		9AC7E0EC2CE595D000944F3B /* libz.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 9A0F35202AD3CE430080FD21 /* libz.tbd */; };
		9AC7E0ED2CE595D000944F3B /* libz.1.2.5.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 9A0F35212AD3CE540080FD21 /* libz.1.2.5.tbd */; };
		9AC7E0EE2CE595D000944F3B /* EsignSDK.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 9A4BC7102CBD18230051D311 /* EsignSDK.framework */; };
		9AC7E0EF2CE595D000944F3B /* libiconv.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 9A0F35222AD3CE750080FD21 /* libiconv.tbd */; };
		9AC7E0F02CE595D000944F3B /* CoreTelephony.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 08D0A96C22C37F6C00D68951 /* CoreTelephony.framework */; };
		9AC7E0F12CE595D000944F3B /* libc++.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 08D0A96A22C37F4B00D68951 /* libc++.tbd */; };
		9AC7E0F22CE595D000944F3B /* CoreMotion.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 08675B5B22C1F7EB007CCC9F /* CoreMotion.framework */; };
		9AC7E0F32CE595D000944F3B /* VideoToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 9A0F351D2AD3CE1F0080FD21 /* VideoToolbox.framework */; };
		9AC7E0F42CE595D000944F3B /* CoreMedia.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 08F4C1C922C0A6440000B366 /* CoreMedia.framework */; };
		9AC7E0F52CE595D000944F3B /* AVFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 08F4C1C722C0A63A0000B366 /* AVFoundation.framework */; };
		9AC7E0F62CE595D000944F3B /* JavaScriptCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 08E3B51522A0DB6D00C6BA22 /* JavaScriptCore.framework */; };
		9AC7E0F72CE595D000944F3B /* SystemConfiguration.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 08675B5D22C1F7F8007CCC9F /* SystemConfiguration.framework */; };
		9AC7E0F82CE595D000944F3B /* Security.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 9A01F53F2ABC2FD20024CC0A /* Security.framework */; };
		9AC7E0F92CE595D000944F3B /* (null) in Frameworks */ = {isa = PBXBuildFile; };
		9AC7E0FA2CE595D000944F3B /* Pods_StagingApp.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 49EAE65573E009493E201410 /* Pods_StagingApp.framework */; };
		9AC7E0FB2CE595D000944F3B /* Pods_StagingApp.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = F80DFFF8ED02B4B42D2E74DE /* Pods_StagingApp.framework */; };
		9AC7E0FD2CE595D000944F3B /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 9AFDCD832846553000850B41 /* LaunchScreen.storyboard */; };
		9AC7E0FE2CE595D000944F3B /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 9A150AA82214092B00840F54 /* Images.xcassets */; };
		9AC7E0FF2CE595D000944F3B /* Qdraw.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 9A01F5492ABD33170024CC0A /* Qdraw.bundle */; };
		9AC7E1002CE595D000944F3B /* EsignSDK.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 9A4BC7112CBD18230051D311 /* EsignSDK.bundle */; };
		9AC7E1012CE595D000944F3B /* Xinruihua-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = 9A4C52FE2CD8DBF0004BC9CE /* Xinruihua-Info.plist */; };
		9AC7E1022CE595D000944F3B /* Panding.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 9AA557762C8D839D00F902A5 /* Panding.bundle */; };
		9AC7E1032CE595D000944F3B /* InfoPlist.strings in Resources */ = {isa = PBXBuildFile; fileRef = 9AA0904F2856EEAE00E835CB /* InfoPlist.strings */; };
		9AC7E1042CE595D000944F3B /* Localizable.strings in Resources */ = {isa = PBXBuildFile; fileRef = 08CE10FB22A8C2FB00E30241 /* Localizable.strings */; };
		9AC7E1052CE595D000944F3B /* pay_loading.gif in Resources */ = {isa = PBXBuildFile; fileRef = 9A48D71C2CCBC26400A3B04F /* pay_loading.gif */; };
		9AC7E1062CE595D000944F3B /* loading.gif in Resources */ = {isa = PBXBuildFile; fileRef = 9A6480482CC6452900C20F02 /* loading.gif */; };
		9AC7E1072CE595D000944F3B /* Pointer.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 9AA557962C8D839D00F902A5 /* Pointer.bundle */; };
		9AC7E1082CE595D000944F3B /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 9AA557832C8D839D00F902A5 /* PrivacyInfo.xcprivacy */; };
		9AC7E1122CE5970B00944F3B /* Haoxianghua-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = 9AC7E1112CE5970B00944F3B /* Haoxianghua-Info.plist */; };
		9AC7E1132CE5970B00944F3B /* Haoxianghua-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = 9AC7E1112CE5970B00944F3B /* Haoxianghua-Info.plist */; };
		9AC7E1142CE5970B00944F3B /* Haoxianghua-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = 9AC7E1112CE5970B00944F3B /* Haoxianghua-Info.plist */; };
		9ACA66652AC1345E00D6250B /* SALayoutConstraint+MASDebugAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ACA664D2AC1345E00D6250B /* SALayoutConstraint+MASDebugAdditions.m */; };
		9ACA66662AC1345F00D6250B /* SAViewController+MASAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ACA66562AC1345E00D6250B /* SAViewController+MASAdditions.m */; };
		9ACA66672AC1345F00D6250B /* SACompositeConstraint.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ACA665A2AC1345E00D6250B /* SACompositeConstraint.m */; };
		9ACA66682AC1345F00D6250B /* SAConstraintMaker.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ACA665B2AC1345E00D6250B /* SAConstraintMaker.m */; };
		9ACA66692AC1345F00D6250B /* SALayoutConstraint.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ACA665C2AC1345E00D6250B /* SALayoutConstraint.m */; };
		9ACA666A2AC1345F00D6250B /* SAArray+MASAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ACA665D2AC1345E00D6250B /* SAArray+MASAdditions.m */; };
		9ACA666B2AC1345F00D6250B /* SAView+MASAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ACA665E2AC1345E00D6250B /* SAView+MASAdditions.m */; };
		9ACA666C2AC1345F00D6250B /* SAConstraint.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ACA66612AC1345E00D6250B /* SAConstraint.m */; };
		9ACA666D2AC1345F00D6250B /* SAViewConstraint.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ACA66632AC1345E00D6250B /* SAViewConstraint.m */; };
		9ACA666E2AC1345F00D6250B /* SAViewAttribute.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ACA66642AC1345E00D6250B /* SAViewAttribute.m */; };
		9ACF14E22C8D93DD0057A6D1 /* SAConfirmSmsAlertView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ACF14E12C8D93DD0057A6D1 /* SAConfirmSmsAlertView.m */; };
		9ACF14E52C8DB42A0057A6D1 /* SAGetSupportBankListApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ACF14E42C8DB42A0057A6D1 /* SAGetSupportBankListApi.m */; };
		9ACF14E82C8DB6200057A6D1 /* SAMyCardsListViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ACF14E72C8DB6200057A6D1 /* SAMyCardsListViewController.m */; };
		9ACF14EB2C8DB6350057A6D1 /* SAConfirmmBankViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ACF14EA2C8DB6350057A6D1 /* SAConfirmmBankViewController.m */; };
		9ACF14EE2C8DB6550057A6D1 /* SAGetMyCardsApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ACF14ED2C8DB6550057A6D1 /* SAGetMyCardsApi.m */; };
		9ACF14F12C8DB71E0057A6D1 /* SACardListCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ACF14F02C8DB71E0057A6D1 /* SACardListCell.m */; };
		9ACF14F42C8DBC5F0057A6D1 /* SAMyBankItemModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ACF14F32C8DBC5F0057A6D1 /* SAMyBankItemModel.m */; };
		9ACF14F72C8DC04C0057A6D1 /* SAGetBorrowAgainApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 9ACF14F62C8DC04C0057A6D1 /* SAGetBorrowAgainApi.m */; };
		9AD150A52C8AA0080033A9D9 /* SAImage+ChangeColor.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AD150A32C8AA0080033A9D9 /* SAImage+ChangeColor.m */; };
		9AE3D2082CCA488C008C0D58 /* NPInviteAlertViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AE3D2072CCA488C008C0D58 /* NPInviteAlertViewController.m */; };
		9AEC08742C898F4D008475BD /* SAVerifyBankCardViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AEC08732C898F4D008475BD /* SAVerifyBankCardViewController.m */; };
		9AEC08772C899B1E008475BD /* SABankItemView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AEC08762C899B1E008475BD /* SABankItemView.m */; };
		9AEC087A2C89A11D008475BD /* SABindCardSendCodeApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AEC08792C89A11D008475BD /* SABindCardSendCodeApi.m */; };
		9AEC087D2C89A136008475BD /* SABindCardResendSmsApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AEC087C2C89A136008475BD /* SABindCardResendSmsApi.m */; };
		9AEC08832C89A597008475BD /* SASaveBindCardInfoApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AEC08822C89A597008475BD /* SASaveBindCardInfoApi.m */; };
		9AEC08862C89A679008475BD /* SABindCardInfoModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AEC08852C89A679008475BD /* SABindCardInfoModel.m */; };
		9AEC08892C89BCCF008475BD /* SABankNameModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AEC08882C89BCCF008475BD /* SABankNameModel.m */; };
		9AEC088C2C89BD26008475BD /* SABankBinListViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AEC088B2C89BD26008475BD /* SABankBinListViewController.m */; };
		9AEC088F2C89D150008475BD /* SAQueryCardBinApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AEC088E2C89D150008475BD /* SAQueryCardBinApi.m */; };
		9AEC08922C89DAA0008475BD /* SASettingViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AEC08912C89DAA0008475BD /* SASettingViewController.m */; };
		9AF5C3E22C8AE656000EF027 /* SAServiceView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AF5C3E12C8AE656000EF027 /* SAServiceView.m */; };
		9AF5C3EB2C8B0193000EF027 /* SARepaymentViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AF5C3EA2C8B0193000EF027 /* SARepaymentViewController.m */; };
		9AF5C3EE2C8B0212000EF027 /* SARepaymentCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AF5C3ED2C8B0212000EF027 /* SARepaymentCell.m */; };
		9AF5C3F42C8B0253000EF027 /* SARepayListModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AF5C3F32C8B0253000EF027 /* SARepayListModel.m */; };
		9AF5C3F72C8B053B000EF027 /* SAGetRepayBillListApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AF5C3F62C8B053B000EF027 /* SAGetRepayBillListApi.m */; };
		9AF5C3FA2C8B05CA000EF027 /* SAGetPaidBillListApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AF5C3F92C8B05CA000EF027 /* SAGetPaidBillListApi.m */; };
		9AF5C40E2C8B0B77000EF027 /* SAJQCache.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AF5C3FE2C8B0B77000EF027 /* SAJQCache.m */; };
		9AF5C40F2C8B0B77000EF027 /* SATabedSlideView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AF5C4042C8B0B77000EF027 /* SATabedSlideView.m */; };
		9AF5C4102C8B0B77000EF027 /* SAScrollTabbarView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AF5C4092C8B0B77000EF027 /* SAScrollTabbarView.m */; };
		9AF5C4112C8B0B77000EF027 /* SAFixedTabbarView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AF5C40A2C8B0B77000EF027 /* SAFixedTabbarView.m */; };
		9AF5C4122C8B0B77000EF027 /* SAUtility.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AF5C40B2C8B0B77000EF027 /* SAUtility.m */; };
		9AF5C4132C8B0B77000EF027 /* SACustomSlideView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AF5C40C2C8B0B77000EF027 /* SACustomSlideView.m */; };
		9AF5C4142C8B0B77000EF027 /* SASlideView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AF5C40D2C8B0B77000EF027 /* SASlideView.m */; };
		9AF5C4172C8B113B000EF027 /* SARepayListVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AF5C4162C8B113B000EF027 /* SARepayListVC.m */; };
		9AF5C41A2C8B1155000EF027 /* SAPaidListVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AF5C4192C8B1155000EF027 /* SAPaidListVC.m */; };
		9AFB991E222005B400F294D4 /* SAResponseHandle.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AFB9916222005B400F294D4 /* SAResponseHandle.m */; };
		9AFB991F222005B400F294D4 /* SARequestHandle.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AFB9919222005B400F294D4 /* SARequestHandle.m */; };
		9AFB9920222005B400F294D4 /* SABasicService.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AFB991B222005B400F294D4 /* SABasicService.m */; };
		9AFB99392220082200F294D4 /* SAScope.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AFB99352220082200F294D4 /* SAScope.m */; };
		9AFB993A2220082200F294D4 /* SARuntimeExtensions.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AFB99362220082200F294D4 /* SARuntimeExtensions.m */; };
		9AFDCD842846553000850B41 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 9AFDCD832846553000850B41 /* LaunchScreen.storyboard */; };
		D4F87E74C92A7E9057CEE5B6 /* Pods_StagingApp.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 49EAE65573E009493E201410 /* Pods_StagingApp.framework */; };
/* End PBXBuildFile section */

/* Begin PBXCopyFilesBuildPhase section */
		9A344B6528336E8B004AEE1D /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = **********;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		9A4C52F62CD8DBAC004BC9CE /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = **********;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		9AC7E1092CE595D000944F3B /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = **********;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		03167121A558418DA6B55141 /* Roboto.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = Roboto.ttf; path = "../node_modules/native-base/Fonts/Roboto.ttf"; sourceTree = "<group>"; };
		0805C28422B1F54600021D25 /* SAVerifyDataListApiManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAVerifyDataListApiManager.h; sourceTree = "<group>"; };
		0805C28522B1F54600021D25 /* SAVerifyDataListApiManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAVerifyDataListApiManager.m; sourceTree = "<group>"; };
		0805C28722B1F6DD00021D25 /* SAVerifyListModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAVerifyListModel.h; sourceTree = "<group>"; };
		0805C28822B1F6DD00021D25 /* SAVerifyListModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAVerifyListModel.m; sourceTree = "<group>"; };
		0805C28A22B1F6F900021D25 /* SAVerifyListItemModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAVerifyListItemModel.h; sourceTree = "<group>"; };
		0805C28B22B1F6F900021D25 /* SAVerifyListItemModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAVerifyListItemModel.m; sourceTree = "<group>"; };
		0805C2A722B24A7800021D25 /* SAVerifyListManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAVerifyListManager.h; sourceTree = "<group>"; };
		0805C2A822B24A7800021D25 /* SAVerifyListManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAVerifyListManager.m; sourceTree = "<group>"; };
		0805C2D722B24DF900021D25 /* SAVerifyBasicInfoViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAVerifyBasicInfoViewController.h; sourceTree = "<group>"; };
		0805C2D822B24DF900021D25 /* SAVerifyBasicInfoViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAVerifyBasicInfoViewController.m; sourceTree = "<group>"; };
		0805C2EF22B2570800021D25 /* SAGetPersonalInfoApiManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAGetPersonalInfoApiManager.h; sourceTree = "<group>"; };
		0805C2F022B2570800021D25 /* SAGetPersonalInfoApiManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAGetPersonalInfoApiManager.m; sourceTree = "<group>"; };
		0805C2F222B25B2000021D25 /* SAPersonalInfoModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAPersonalInfoModel.h; sourceTree = "<group>"; };
		0805C2F322B25B2000021D25 /* SAPersonalInfoModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAPersonalInfoModel.m; sourceTree = "<group>"; };
		0805C2F822B25C5B00021D25 /* SAPersonalInfoCheckModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAPersonalInfoCheckModel.h; sourceTree = "<group>"; };
		0805C2F922B25C5B00021D25 /* SAPersonalInfoCheckModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAPersonalInfoCheckModel.m; sourceTree = "<group>"; };
		0811F47022CB41D000CDBBD1 /* SAErrorView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAErrorView.h; sourceTree = "<group>"; };
		0811F47122CB41D000CDBBD1 /* SAErrorView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAErrorView.m; sourceTree = "<group>"; };
		082B160622BB871600A4C7C7 /* SAPayClientInfoApiManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAPayClientInfoApiManager.h; sourceTree = "<group>"; };
		082B160722BB871600A4C7C7 /* SAPayClientInfoApiManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAPayClientInfoApiManager.m; sourceTree = "<group>"; };
		082B160922BB8CCA00A4C7C7 /* SAPayWayModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAPayWayModel.h; sourceTree = "<group>"; };
		082B160A22BB8CCA00A4C7C7 /* SAPayWayModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAPayWayModel.m; sourceTree = "<group>"; };
		082B162222BBAECA00A4C7C7 /* SAPayClientChargesApiManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAPayClientChargesApiManager.h; sourceTree = "<group>"; };
		082B162322BBAECA00A4C7C7 /* SAPayClientChargesApiManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAPayClientChargesApiManager.m; sourceTree = "<group>"; };
		083AB35222B36DEA003B4BB4 /* SAPersonalVerifyModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAPersonalVerifyModel.h; sourceTree = "<group>"; };
		083AB35322B36DEA003B4BB4 /* SAPersonalVerifyModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAPersonalVerifyModel.m; sourceTree = "<group>"; };
		083D64D82313E2E400810B26 /* SASubmitProductInfoModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SASubmitProductInfoModel.h; sourceTree = "<group>"; };
		083D64D92313E2E400810B26 /* SASubmitProductInfoModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SASubmitProductInfoModel.m; sourceTree = "<group>"; };
		0845D8C622AE6ACF0044AB4D /* SABasicViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SABasicViewController.m; sourceTree = "<group>"; };
		0845D8C722AE6ACF0044AB4D /* SATabBarViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SATabBarViewController.m; sourceTree = "<group>"; };
		0845D8C822AE6ACF0044AB4D /* SATabBarViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SATabBarViewController.h; sourceTree = "<group>"; };
		0845D8C922AE6ACF0044AB4D /* SANavigationViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SANavigationViewController.h; sourceTree = "<group>"; };
		0845D8CA22AE6ACF0044AB4D /* SABasicViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SABasicViewController.h; sourceTree = "<group>"; };
		0845D8CB22AE6ACF0044AB4D /* SANavigationViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SANavigationViewController.m; sourceTree = "<group>"; };
		0845D91322AE6BB40044AB4D /* SANoNetView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SANoNetView.m; sourceTree = "<group>"; };
		0845D91422AE6BB40044AB4D /* SANoNetView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SANoNetView.h; sourceTree = "<group>"; };
		0845D93E22AE6C880044AB4D /* SAHomePageViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SAHomePageViewController.h; sourceTree = "<group>"; };
		0845D93F22AE6C880044AB4D /* SAHomePageViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SAHomePageViewController.m; sourceTree = "<group>"; };
		0845D94522AE6C880044AB4D /* SAMineViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SAMineViewController.m; sourceTree = "<group>"; };
		0845D94622AE6C880044AB4D /* SAMineViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SAMineViewController.h; sourceTree = "<group>"; };
		0845D95022AE6D1D0044AB4D /* SALoginViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SALoginViewController.m; sourceTree = "<group>"; };
		0845D95122AE6D1D0044AB4D /* SALoginViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SALoginViewController.h; sourceTree = "<group>"; };
		0845D95622AE6D670044AB4D /* SATool.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SATool.h; sourceTree = "<group>"; };
		0845D95722AE6D670044AB4D /* SACommonTool.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SACommonTool.h; sourceTree = "<group>"; };
		0845D95822AE6D670044AB4D /* SATool.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SATool.m; sourceTree = "<group>"; };
		0845D95922AE6D670044AB4D /* SACommonTool.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SACommonTool.m; sourceTree = "<group>"; };
		0845D95C22AE6D670044AB4D /* SANormalRefresh.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SANormalRefresh.m; sourceTree = "<group>"; };
		0845D95D22AE6D670044AB4D /* SANormalRefresh.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SANormalRefresh.h; sourceTree = "<group>"; };
		0845D96022AE6D670044AB4D /* SAMutableDictionary+Extension.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "SAMutableDictionary+Extension.m"; sourceTree = "<group>"; };
		0845D96122AE6D670044AB4D /* SADictionary+Common.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "SADictionary+Common.h"; sourceTree = "<group>"; };
		0845D96222AE6D670044AB4D /* SAMutableDictionary+Extension.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "SAMutableDictionary+Extension.h"; sourceTree = "<group>"; };
		0845D96322AE6D680044AB4D /* SADictionary+Common.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "SADictionary+Common.m"; sourceTree = "<group>"; };
		0845D96522AE6D680044AB4D /* SAColor+Extension.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "SAColor+Extension.h"; sourceTree = "<group>"; };
		0845D96622AE6D680044AB4D /* SAColor+Extension.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "SAColor+Extension.m"; sourceTree = "<group>"; };
		0845D96822AE6D680044AB4D /* SAAlertController+Orientation.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "SAAlertController+Orientation.m"; sourceTree = "<group>"; };
		0845D96922AE6D680044AB4D /* SAAlertController+Orientation.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "SAAlertController+Orientation.h"; sourceTree = "<group>"; };
		0845D96B22AE6D680044AB4D /* SAView+Extension.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "SAView+Extension.h"; sourceTree = "<group>"; };
		0845D96C22AE6D680044AB4D /* SAView+Extension.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "SAView+Extension.m"; sourceTree = "<group>"; };
		0845D96E22AE6D680044AB4D /* SAFont+Extension.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "SAFont+Extension.h"; sourceTree = "<group>"; };
		0845D96F22AE6D680044AB4D /* SAFont+Extension.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "SAFont+Extension.m"; sourceTree = "<group>"; };
		0845D97122AE6D680044AB4D /* SAString+Extension.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "SAString+Extension.h"; sourceTree = "<group>"; };
		0845D97222AE6D680044AB4D /* SAString+Extension.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "SAString+Extension.m"; sourceTree = "<group>"; };
		0845D97422AE6D680044AB4D /* SADate+Extension.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "SADate+Extension.h"; sourceTree = "<group>"; };
		0845D97522AE6D680044AB4D /* SADate+Extension.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "SADate+Extension.m"; sourceTree = "<group>"; };
		0845D97722AE6D680044AB4D /* SALocationManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SALocationManager.h; sourceTree = "<group>"; };
		0845D97822AE6D680044AB4D /* SALocationManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SALocationManager.m; sourceTree = "<group>"; };
		0845D97D22AE6D680044AB4D /* SADeviceTool.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SADeviceTool.m; sourceTree = "<group>"; };
		0845D97E22AE6D680044AB4D /* SABCTool.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SABCTool.m; sourceTree = "<group>"; };
		0845D97F22AE6D680044AB4D /* SAKeyChainTool.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SAKeyChainTool.h; sourceTree = "<group>"; };
		0845D98022AE6D680044AB4D /* SAKeyChainTool.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SAKeyChainTool.m; sourceTree = "<group>"; };
		0845D98122AE6D680044AB4D /* SABCTool.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SABCTool.h; sourceTree = "<group>"; };
		0845D98222AE6D680044AB4D /* SADeviceTool.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SADeviceTool.h; sourceTree = "<group>"; };
		0856E5C122B2793C00D5C183 /* SACenterYTextView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SACenterYTextView.h; sourceTree = "<group>"; };
		0856E5C222B2793C00D5C183 /* SACenterYTextView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SACenterYTextView.m; sourceTree = "<group>"; };
		0856E5C422B279CB00D5C183 /* SAVerifyInfoCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAVerifyInfoCell.h; sourceTree = "<group>"; };
		0856E5C522B279CB00D5C183 /* SAVerifyInfoCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAVerifyInfoCell.m; sourceTree = "<group>"; };
		08675B5B22C1F7EB007CCC9F /* CoreMotion.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreMotion.framework; path = System/Library/Frameworks/CoreMotion.framework; sourceTree = SDKROOT; };
		08675B5D22C1F7F8007CCC9F /* SystemConfiguration.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SystemConfiguration.framework; path = System/Library/Frameworks/SystemConfiguration.framework; sourceTree = SDKROOT; };
		087802FF22B0967D000A7A4F /* SAGradientView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SAGradientView.h; sourceTree = "<group>"; };
		0878030022B0967D000A7A4F /* SAGradientView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SAGradientView.m; sourceTree = "<group>"; };
		0878033422B0E898000A7A4F /* SAUserCenterApiManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAUserCenterApiManager.h; sourceTree = "<group>"; };
		0878033522B0E898000A7A4F /* SAUserCenterApiManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAUserCenterApiManager.m; sourceTree = "<group>"; };
		0878034C22B10C35000A7A4F /* SAVerifyCodeApiManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAVerifyCodeApiManager.h; sourceTree = "<group>"; };
		0878034D22B10C35000A7A4F /* SAVerifyCodeApiManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAVerifyCodeApiManager.m; sourceTree = "<group>"; };
		0878035022B110B8000A7A4F /* SAVerifyCodeInputView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAVerifyCodeInputView.h; sourceTree = "<group>"; };
		0878035122B110B8000A7A4F /* SAVerifyCodeInputView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAVerifyCodeInputView.m; sourceTree = "<group>"; };
		0878035322B138E9000A7A4F /* SAUserCenterModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAUserCenterModel.h; sourceTree = "<group>"; };
		0878035422B138E9000A7A4F /* SAUserCenterModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAUserCenterModel.m; sourceTree = "<group>"; };
		0880D0C022BB2357001C979F /* SAAuditProgressItemModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAAuditProgressItemModel.h; sourceTree = "<group>"; };
		0880D0C122BB2357001C979F /* SAAuditProgressItemModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAAuditProgressItemModel.m; sourceTree = "<group>"; };
		089A56B822E9B3FC005BEBD5 /* SASaveDeviceChannelApiManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SASaveDeviceChannelApiManager.h; sourceTree = "<group>"; };
		089A56B922E9B3FC005BEBD5 /* SASaveDeviceChannelApiManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SASaveDeviceChannelApiManager.m; sourceTree = "<group>"; };
		08A6BAEB22B1E67F001CA577 /* SADeviceInfoSaveApiManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SADeviceInfoSaveApiManager.h; sourceTree = "<group>"; };
		08A6BAEC22B1E67F001CA577 /* SADeviceInfoSaveApiManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SADeviceInfoSaveApiManager.m; sourceTree = "<group>"; };
		08AB288B22BF606900CAE59C /* SAWifiManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAWifiManager.h; sourceTree = "<group>"; };
		08AB288C22BF606900CAE59C /* SAWifiManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAWifiManager.m; sourceTree = "<group>"; };
		08B1EC4722B8815A001367BB /* SAShapeLayer+Extension.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "SAShapeLayer+Extension.h"; sourceTree = "<group>"; };
		08B1EC4822B8815A001367BB /* SAShapeLayer+Extension.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "SAShapeLayer+Extension.m"; sourceTree = "<group>"; };
		08B1EC4A22B88754001367BB /* SACommonWebViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SACommonWebViewController.h; sourceTree = "<group>"; };
		08B1EC4B22B88754001367BB /* SACommonWebViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SACommonWebViewController.m; sourceTree = "<group>"; };
		08B1EC6122B8B46C001367BB /* SAActionSheet.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAActionSheet.h; sourceTree = "<group>"; };
		08B1EC6222B8B46C001367BB /* SAActionSheet.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAActionSheet.m; sourceTree = "<group>"; };
		08B1ECBF22B8CEC5001367BB /* SAPhotoManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAPhotoManager.h; sourceTree = "<group>"; };
		08B1ECC022B8CEC5001367BB /* SAPhotoManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAPhotoManager.m; sourceTree = "<group>"; };
		08B1ED1322B8DC14001367BB /* SATokenApiManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SATokenApiManager.h; sourceTree = "<group>"; };
		08B1ED1422B8DC14001367BB /* SATokenApiManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SATokenApiManager.m; sourceTree = "<group>"; };
		08B1ED2222B8DDCE001367BB /* SAAliyunOSSManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAAliyunOSSManager.h; sourceTree = "<group>"; };
		08B1ED2322B8DDCE001367BB /* SAAliyunOSSManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAAliyunOSSManager.m; sourceTree = "<group>"; };
		08B4A93C22B79A0D006B35D4 /* SAContactsManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAContactsManager.h; sourceTree = "<group>"; };
		08B4A93D22B79A0D006B35D4 /* SAContactsManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAContactsManager.m; sourceTree = "<group>"; };
		08CE109822A8AA4100E30241 /* SANetworkConst.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SANetworkConst.h; sourceTree = "<group>"; };
		08CE109922A8AA4100E30241 /* SANetworkConst.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SANetworkConst.m; sourceTree = "<group>"; };
		08CE115722A8E7A300E30241 /* SAUserManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAUserManager.h; sourceTree = "<group>"; };
		08CE115822A8E7A300E30241 /* SAUserManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAUserManager.m; sourceTree = "<group>"; };
		08CE116122A8F73F00E30241 /* SAAppCodeManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAAppCodeManager.h; sourceTree = "<group>"; };
		08CE116222A8F73F00E30241 /* SAAppCodeManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAAppCodeManager.m; sourceTree = "<group>"; };
		08CE116422A8F79400E30241 /* SAAppConfigApiManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAAppConfigApiManager.h; sourceTree = "<group>"; };
		08CE116522A8F79400E30241 /* SAAppConfigApiManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAAppConfigApiManager.m; sourceTree = "<group>"; };
		08D0A89F22C31B9700D68951 /* SAVersionUpdateViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAVersionUpdateViewController.h; sourceTree = "<group>"; };
		08D0A8A022C31B9700D68951 /* SAVersionUpdateViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAVersionUpdateViewController.m; sourceTree = "<group>"; };
		08D0A96A22C37F4B00D68951 /* libc++.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = "libc++.tbd"; path = "usr/lib/libc++.tbd"; sourceTree = SDKROOT; };
		08D0A96C22C37F6C00D68951 /* CoreTelephony.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreTelephony.framework; path = System/Library/Frameworks/CoreTelephony.framework; sourceTree = SDKROOT; };
		08D14F0E22BC7E1E0094582F /* SAClientChargesModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAClientChargesModel.h; sourceTree = "<group>"; };
		08D14F0F22BC7E1E0094582F /* SAClientChargesModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAClientChargesModel.m; sourceTree = "<group>"; };
		08D14F3922BC7E970094582F /* SARepaymentModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SARepaymentModel.h; sourceTree = "<group>"; };
		08D14F3A22BC7E970094582F /* SARepaymentModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SARepaymentModel.m; sourceTree = "<group>"; };
		08D14F5922BCCB4C0094582F /* SAAlertSureTipsViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAAlertSureTipsViewController.h; sourceTree = "<group>"; };
		08D14F5A22BCCB4C0094582F /* SAAlertSureTipsViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAAlertSureTipsViewController.m; sourceTree = "<group>"; };
		08DA5D0822C9A9E800DB3163 /* StagingApp.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; name = StagingApp.entitlements; path = StagingApp/StagingApp.entitlements; sourceTree = "<group>"; };
		08E3B51522A0DB6D00C6BA22 /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = System/Library/Frameworks/JavaScriptCore.framework; sourceTree = SDKROOT; };
		08E66BCE22B4F7C7005E62D9 /* SABankListModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SABankListModel.h; sourceTree = "<group>"; };
		08E66BCF22B4F7C7005E62D9 /* SABankListModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SABankListModel.m; sourceTree = "<group>"; };
		08EC05D12315245200D3C93C /* SAHistoryOrderModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAHistoryOrderModel.h; sourceTree = "<group>"; };
		08EC05D22315245200D3C93C /* SAHistoryOrderModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAHistoryOrderModel.m; sourceTree = "<group>"; };
		08F4C12122C063380000B366 /* SASaveGPSApiManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SASaveGPSApiManager.h; sourceTree = "<group>"; };
		08F4C12222C063380000B366 /* SASaveGPSApiManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SASaveGPSApiManager.m; sourceTree = "<group>"; };
		08F4C18C22C078330000B366 /* SAManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAManager.h; sourceTree = "<group>"; };
		08F4C18D22C078330000B366 /* SAManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAManager.m; sourceTree = "<group>"; };
		08F4C1C722C0A63A0000B366 /* AVFoundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AVFoundation.framework; path = System/Library/Frameworks/AVFoundation.framework; sourceTree = SDKROOT; };
		08F4C1C922C0A6440000B366 /* CoreMedia.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreMedia.framework; path = System/Library/Frameworks/CoreMedia.framework; sourceTree = SDKROOT; };
		08F5A3F5231517BF00D75C55 /* SAOrderConfirmApiManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAOrderConfirmApiManager.h; sourceTree = "<group>"; };
		08F5A3F6231517BF00D75C55 /* SAOrderConfirmApiManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAOrderConfirmApiManager.m; sourceTree = "<group>"; };
		08FF04B522AF3EC500DF17E6 /* SAMainHomeApiManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAMainHomeApiManager.h; sourceTree = "<group>"; };
		08FF04B622AF3EC500DF17E6 /* SAMainHomeApiManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAMainHomeApiManager.m; sourceTree = "<group>"; };
		08FF04ED22AF424E00DF17E6 /* SAMobileLoginApiManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAMobileLoginApiManager.h; sourceTree = "<group>"; };
		08FF04EE22AF424E00DF17E6 /* SAMobileLoginApiManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAMobileLoginApiManager.m; sourceTree = "<group>"; };
		08FF04F022AF432800DF17E6 /* SALogoutApiManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SALogoutApiManager.h; sourceTree = "<group>"; };
		08FF04F122AF432800DF17E6 /* SALogoutApiManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SALogoutApiManager.m; sourceTree = "<group>"; };
		08FF04F922AF455E00DF17E6 /* SAHomeModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAHomeModel.h; sourceTree = "<group>"; };
		08FF04FA22AF455E00DF17E6 /* SAHomeModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAHomeModel.m; sourceTree = "<group>"; };
		08FF050F22AF539700DF17E6 /* SAHomeProtocolUrlsModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAHomeProtocolUrlsModel.h; sourceTree = "<group>"; };
		08FF051022AF539700DF17E6 /* SAHomeProtocolUrlsModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAHomeProtocolUrlsModel.m; sourceTree = "<group>"; };
		08FF051222AF53CF00DF17E6 /* SAHomeProtocolModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAHomeProtocolModel.h; sourceTree = "<group>"; };
		08FF051322AF53CF00DF17E6 /* SAHomeProtocolModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAHomeProtocolModel.m; sourceTree = "<group>"; };
		08FF051522AF5A2F00DF17E6 /* SAOrderInfoModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAOrderInfoModel.h; sourceTree = "<group>"; };
		08FF051622AF5A2F00DF17E6 /* SAOrderInfoModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAOrderInfoModel.m; sourceTree = "<group>"; };
		08FF051822AF5BE500DF17E6 /* SABorrowPurposeTypeModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SABorrowPurposeTypeModel.h; sourceTree = "<group>"; };
		08FF051922AF5BE500DF17E6 /* SABorrowPurposeTypeModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SABorrowPurposeTypeModel.m; sourceTree = "<group>"; };
		08FF051B22AF5C7600DF17E6 /* SABorrowCashModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SABorrowCashModel.h; sourceTree = "<group>"; };
		08FF051C22AF5C7600DF17E6 /* SABorrowCashModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SABorrowCashModel.m; sourceTree = "<group>"; };
		08FF051E22AF5CCF00DF17E6 /* SABorrowCashPeriodModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SABorrowCashPeriodModel.h; sourceTree = "<group>"; };
		08FF051F22AF5CCF00DF17E6 /* SABorrowCashPeriodModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SABorrowCashPeriodModel.m; sourceTree = "<group>"; };
		08FF054022AF777900DF17E6 /* SAImage+Extension.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "SAImage+Extension.h"; sourceTree = "<group>"; };
		08FF054122AF777900DF17E6 /* SAImage+Extension.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "SAImage+Extension.m"; sourceTree = "<group>"; };
		08FF055E22AF923600DF17E6 /* SAHomeRejectCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAHomeRejectCell.h; sourceTree = "<group>"; };
		08FF055F22AF923600DF17E6 /* SAHomeRejectCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAHomeRejectCell.m; sourceTree = "<group>"; };
		08FF056D22AF9B4400DF17E6 /* SAHomeBasicCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAHomeBasicCell.h; sourceTree = "<group>"; };
		08FF056E22AF9B4400DF17E6 /* SAHomeBasicCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAHomeBasicCell.m; sourceTree = "<group>"; };
		08FF05B122AFAAE300DF17E6 /* SAMainTopBgView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAMainTopBgView.h; sourceTree = "<group>"; };
		08FF05B222AFAAE300DF17E6 /* SAMainTopBgView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAMainTopBgView.m; sourceTree = "<group>"; };
		08FF4C1F22B604390095863C /* SAPickerTextField.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAPickerTextField.h; sourceTree = "<group>"; };
		08FF4C2022B604390095863C /* SAPickerTextField.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAPickerTextField.m; sourceTree = "<group>"; };
		08FF4C5722B61C010095863C /* SASavePersonalInfoApiManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SASavePersonalInfoApiManager.h; sourceTree = "<group>"; };
		08FF4C5822B61C010095863C /* SASavePersonalInfoApiManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SASavePersonalInfoApiManager.m; sourceTree = "<group>"; };
		08FF4C5D22B61CF20095863C /* SAGetContactInfoApiManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAGetContactInfoApiManager.h; sourceTree = "<group>"; };
		08FF4C5E22B61CF20095863C /* SAGetContactInfoApiManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAGetContactInfoApiManager.m; sourceTree = "<group>"; };
		08FF4C6022B61D040095863C /* SASaveContactInfoApiManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SASaveContactInfoApiManager.h; sourceTree = "<group>"; };
		08FF4C6122B61D040095863C /* SASaveContactInfoApiManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SASaveContactInfoApiManager.m; sourceTree = "<group>"; };
		08FF4C6A22B623870095863C /* SAContactSelectModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAContactSelectModel.h; sourceTree = "<group>"; };
		08FF4C6B22B623870095863C /* SAContactSelectModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAContactSelectModel.m; sourceTree = "<group>"; };
		08FF4C7022B626420095863C /* SAContactSectionModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAContactSectionModel.h; sourceTree = "<group>"; };
		08FF4C7122B626420095863C /* SAContactSectionModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAContactSectionModel.m; sourceTree = "<group>"; };
		08FF4C7322B626580095863C /* SAContactRowModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAContactRowModel.h; sourceTree = "<group>"; };
		08FF4C7422B626580095863C /* SAContactRowModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAContactRowModel.m; sourceTree = "<group>"; };
		098C17B06702499AA4BD2907 /* EvilIcons.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = EvilIcons.ttf; path = "../node_modules/native-base/Fonts/EvilIcons.ttf"; sourceTree = "<group>"; };
		13959F7AC7337A91E9FFB0B1 /* Pods-FreeDom.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-FreeDom.release.xcconfig"; path = "Pods/Target Support Files/Pods-FreeDom/Pods-FreeDom.release.xcconfig"; sourceTree = "<group>"; };
		13B07F961A680F5B00A75B9A /* StagingApp.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = StagingApp.app; sourceTree = BUILT_PRODUCTS_DIR; };
		2D16E6891FA4F8E400B85C8A /* libReact.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; path = libReact.a; sourceTree = BUILT_PRODUCTS_DIR; };
		30B3816DCC0B4A7F8741E5CE /* FontAwesome5_Solid.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = FontAwesome5_Solid.ttf; path = "../node_modules/native-base/Fonts/FontAwesome5_Solid.ttf"; sourceTree = "<group>"; };
		3CDBCBF7A8FC47FA8B2D2021 /* Octicons.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = Octicons.ttf; path = "../node_modules/native-base/Fonts/Octicons.ttf"; sourceTree = "<group>"; };
		3CF1BDA3CFE91B8A0832A6CE /* Pods-StagingApp.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-StagingApp.release.xcconfig"; path = "Pods/Target Support Files/Pods-StagingApp/Pods-StagingApp.release.xcconfig"; sourceTree = "<group>"; };
		49EAE65573E009493E201410 /* Pods_StagingApp.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_StagingApp.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		4CB91B09649C496C8238C00B /* SimpleLineIcons.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = SimpleLineIcons.ttf; path = "../node_modules/native-base/Fonts/SimpleLineIcons.ttf"; sourceTree = "<group>"; };
		4DCF1E6BB79240EEB437CCA8 /* Zocial.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = Zocial.ttf; path = "../node_modules/native-base/Fonts/Zocial.ttf"; sourceTree = "<group>"; };
		4F70A708D4F6FD08F7971BE2 /* Pods-FreeDom.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-FreeDom.debug.xcconfig"; path = "Pods/Target Support Files/Pods-FreeDom/Pods-FreeDom.debug.xcconfig"; sourceTree = "<group>"; };
		5932DFB3983F459EBE38D514 /* Foundation.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = Foundation.ttf; path = "../node_modules/native-base/Fonts/Foundation.ttf"; sourceTree = "<group>"; };
		59CD6C4F034940178E06C457 /* rubicon-icon-font.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "rubicon-icon-font.ttf"; path = "../node_modules/native-base/Fonts/rubicon-icon-font.ttf"; sourceTree = "<group>"; };
		6E2B2ADF18C6423E8A996B56 /* Entypo.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = Entypo.ttf; path = "../node_modules/native-base/Fonts/Entypo.ttf"; sourceTree = "<group>"; };
		6F71D3A1BC8941E484276B55 /* FontAwesome5_Brands.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = FontAwesome5_Brands.ttf; path = "../node_modules/native-base/Fonts/FontAwesome5_Brands.ttf"; sourceTree = "<group>"; };
		7DA21020CC7A4C8590FF6C19 /* Roboto_medium.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = Roboto_medium.ttf; path = "../node_modules/native-base/Fonts/Roboto_medium.ttf"; sourceTree = "<group>"; };
		88301A4F13D943ABA8141D95 /* Feather.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = Feather.ttf; path = "../node_modules/native-base/Fonts/Feather.ttf"; sourceTree = "<group>"; };
		8B9D717CF3BC7C90C3435085 /* Pods_FreeXin.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_FreeXin.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		91DC73B8B7394A5F9CA573F8 /* MaterialIcons.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = MaterialIcons.ttf; path = "../node_modules/native-base/Fonts/MaterialIcons.ttf"; sourceTree = "<group>"; };
		96FFD84637A245F392DE27AA /* FontAwesome.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = FontAwesome.ttf; path = "../node_modules/native-base/Fonts/FontAwesome.ttf"; sourceTree = "<group>"; };
		9A01F53C2ABC2FC10024CC0A /* SAObjC.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SAObjC.h; sourceTree = "<group>"; };
		9A01F53D2ABC2FC10024CC0A /* SAObjC.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SAObjC.m; sourceTree = "<group>"; };
		9A01F53F2ABC2FD20024CC0A /* Security.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Security.framework; path = System/Library/Frameworks/Security.framework; sourceTree = SDKROOT; };
		9A01F5422ABD33170024CC0A /* SAScrollView+MJRefresh.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "SAScrollView+MJRefresh.m"; sourceTree = "<group>"; };
		9A01F5432ABD33170024CC0A /* SACollectionViewLayout+MJRefresh.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "SACollectionViewLayout+MJRefresh.m"; sourceTree = "<group>"; };
		9A01F5442ABD33170024CC0A /* SARefreshConst.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SARefreshConst.m; sourceTree = "<group>"; };
		9A01F5452ABD33170024CC0A /* SARefreshConfig.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SARefreshConfig.m; sourceTree = "<group>"; };
		9A01F5462ABD33170024CC0A /* SAScrollView+MJExtension.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "SAScrollView+MJExtension.h"; sourceTree = "<group>"; };
		9A01F5472ABD33170024CC0A /* MJRefresh.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MJRefresh.h; sourceTree = "<group>"; };
		9A01F5482ABD33170024CC0A /* SABundle+MJRefresh.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "SABundle+MJRefresh.h"; sourceTree = "<group>"; };
		9A01F5492ABD33170024CC0A /* Qdraw.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = Qdraw.bundle; sourceTree = "<group>"; };
		9A01F54A2ABD33170024CC0A /* SAView+MJExtension.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "SAView+MJExtension.h"; sourceTree = "<group>"; };
		9A01F54B2ABD33170024CC0A /* SAScrollView+MJExtension.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "SAScrollView+MJExtension.m"; sourceTree = "<group>"; };
		9A01F54C2ABD33170024CC0A /* SARefreshConfig.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SARefreshConfig.h; sourceTree = "<group>"; };
		9A01F54D2ABD33170024CC0A /* SARefreshConst.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SARefreshConst.h; sourceTree = "<group>"; };
		9A01F54E2ABD33170024CC0A /* SACollectionViewLayout+MJRefresh.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "SACollectionViewLayout+MJRefresh.h"; sourceTree = "<group>"; };
		9A01F54F2ABD33170024CC0A /* SAScrollView+MJRefresh.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "SAScrollView+MJRefresh.h"; sourceTree = "<group>"; };
		9A01F5502ABD33170024CC0A /* SABundle+MJRefresh.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "SABundle+MJRefresh.m"; sourceTree = "<group>"; };
		9A01F5512ABD33170024CC0A /* SAView+MJExtension.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "SAView+MJExtension.m"; sourceTree = "<group>"; };
		9A01F5552ABD33170024CC0A /* SARefreshBackGifFooter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SARefreshBackGifFooter.h; sourceTree = "<group>"; };
		9A01F5562ABD33170024CC0A /* SARefreshBackStateFooter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SARefreshBackStateFooter.h; sourceTree = "<group>"; };
		9A01F5572ABD33170024CC0A /* SARefreshBackNormalFooter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SARefreshBackNormalFooter.h; sourceTree = "<group>"; };
		9A01F5582ABD33170024CC0A /* SARefreshBackGifFooter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SARefreshBackGifFooter.m; sourceTree = "<group>"; };
		9A01F5592ABD33170024CC0A /* SARefreshBackStateFooter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SARefreshBackStateFooter.m; sourceTree = "<group>"; };
		9A01F55A2ABD33170024CC0A /* SARefreshBackNormalFooter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SARefreshBackNormalFooter.m; sourceTree = "<group>"; };
		9A01F55C2ABD33170024CC0A /* SARefreshAutoStateFooter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SARefreshAutoStateFooter.h; sourceTree = "<group>"; };
		9A01F55D2ABD33170024CC0A /* SARefreshAutoNormalFooter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SARefreshAutoNormalFooter.h; sourceTree = "<group>"; };
		9A01F55E2ABD33170024CC0A /* SARefreshAutoGifFooter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SARefreshAutoGifFooter.h; sourceTree = "<group>"; };
		9A01F55F2ABD33170024CC0A /* SARefreshAutoStateFooter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SARefreshAutoStateFooter.m; sourceTree = "<group>"; };
		9A01F5602ABD33170024CC0A /* SARefreshAutoGifFooter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SARefreshAutoGifFooter.m; sourceTree = "<group>"; };
		9A01F5612ABD33170024CC0A /* SARefreshAutoNormalFooter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SARefreshAutoNormalFooter.m; sourceTree = "<group>"; };
		9A01F5632ABD33170024CC0A /* SARefreshNormalTrailer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SARefreshNormalTrailer.h; sourceTree = "<group>"; };
		9A01F5642ABD33170024CC0A /* SARefreshStateTrailer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SARefreshStateTrailer.m; sourceTree = "<group>"; };
		9A01F5652ABD33170024CC0A /* SARefreshNormalTrailer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SARefreshNormalTrailer.m; sourceTree = "<group>"; };
		9A01F5662ABD33170024CC0A /* SARefreshStateTrailer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SARefreshStateTrailer.h; sourceTree = "<group>"; };
		9A01F5682ABD33170024CC0A /* SARefreshNormalHeader.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SARefreshNormalHeader.m; sourceTree = "<group>"; };
		9A01F5692ABD33170024CC0A /* SARefreshStateHeader.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SARefreshStateHeader.h; sourceTree = "<group>"; };
		9A01F56A2ABD33170024CC0A /* SARefreshGifHeader.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SARefreshGifHeader.h; sourceTree = "<group>"; };
		9A01F56B2ABD33170024CC0A /* SARefreshNormalHeader.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SARefreshNormalHeader.h; sourceTree = "<group>"; };
		9A01F56C2ABD33170024CC0A /* SARefreshStateHeader.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SARefreshStateHeader.m; sourceTree = "<group>"; };
		9A01F56D2ABD33170024CC0A /* SARefreshGifHeader.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SARefreshGifHeader.m; sourceTree = "<group>"; };
		9A01F56F2ABD33170024CC0A /* SARefreshFooter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SARefreshFooter.m; sourceTree = "<group>"; };
		9A01F5702ABD33170024CC0A /* SARefreshComponent.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SARefreshComponent.h; sourceTree = "<group>"; };
		9A01F5712ABD33170024CC0A /* SARefreshHeader.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SARefreshHeader.m; sourceTree = "<group>"; };
		9A01F5722ABD33170024CC0A /* SARefreshAutoFooter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SARefreshAutoFooter.h; sourceTree = "<group>"; };
		9A01F5732ABD33170024CC0A /* SARefreshTrailer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SARefreshTrailer.h; sourceTree = "<group>"; };
		9A01F5742ABD33170024CC0A /* SARefreshBackFooter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SARefreshBackFooter.m; sourceTree = "<group>"; };
		9A01F5752ABD33170024CC0A /* SARefreshAutoFooter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SARefreshAutoFooter.m; sourceTree = "<group>"; };
		9A01F5762ABD33170024CC0A /* SARefreshHeader.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SARefreshHeader.h; sourceTree = "<group>"; };
		9A01F5772ABD33170024CC0A /* SARefreshComponent.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SARefreshComponent.m; sourceTree = "<group>"; };
		9A01F5782ABD33170024CC0A /* SARefreshFooter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SARefreshFooter.h; sourceTree = "<group>"; };
		9A01F5792ABD33170024CC0A /* SARefreshBackFooter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SARefreshBackFooter.h; sourceTree = "<group>"; };
		9A01F57A2ABD33170024CC0A /* SARefreshTrailer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SARefreshTrailer.m; sourceTree = "<group>"; };
		9A021B1C2AB8622F007EC584 /* SAGetCitiesListApiManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAGetCitiesListApiManager.h; sourceTree = "<group>"; };
		9A021B1D2AB8622F007EC584 /* SAGetCitiesListApiManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAGetCitiesListApiManager.m; sourceTree = "<group>"; };
		9A02DF532CC656C000431F1B /* SABannerModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SABannerModel.h; sourceTree = "<group>"; };
		9A02DF542CC656C000431F1B /* SABannerModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SABannerModel.m; sourceTree = "<group>"; };
		9A02DF562CC6593400431F1B /* SABannerView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SABannerView.h; sourceTree = "<group>"; };
		9A02DF572CC6593400431F1B /* SABannerView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SABannerView.m; sourceTree = "<group>"; };
		9A02DF592CC65D2600431F1B /* SABannerCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SABannerCell.h; sourceTree = "<group>"; };
		9A02DF5A2CC65D2600431F1B /* SABannerCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SABannerCell.m; sourceTree = "<group>"; };
		9A02DF5C2CC66F0100431F1B /* SAPaidCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAPaidCell.h; sourceTree = "<group>"; };
		9A02DF5D2CC66F0100431F1B /* SAPaidCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAPaidCell.m; sourceTree = "<group>"; };
		9A09512E2CBE076C0053D586 /* SAFaceBackAlert.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SAFaceBackAlert.m; sourceTree = "<group>"; };
		9A09512F2CBE076C0053D586 /* SAFaceBackAlert.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SAFaceBackAlert.h; sourceTree = "<group>"; };
		9A0A39622A7A02EC0095AB86 /* SASmsCodeViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SASmsCodeViewController.h; sourceTree = "<group>"; };
		9A0A39632A7A02EC0095AB86 /* SASmsCodeViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SASmsCodeViewController.m; sourceTree = "<group>"; };
		9A0F34FA2AD3C08A0080FD21 /* zh-Hans */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hans"; path = "zh-Hans.lproj/Localizable.strings"; sourceTree = "<group>"; };
		9A0F34FB2AD3C08A0080FD21 /* zh-Hans */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hans"; path = "zh-Hans.lproj/InfoPlist.strings"; sourceTree = "<group>"; };
		9A0F351D2AD3CE1F0080FD21 /* VideoToolbox.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = VideoToolbox.framework; path = System/Library/Frameworks/VideoToolbox.framework; sourceTree = SDKROOT; };
		9A0F351F2AD3CE370080FD21 /* libbz2.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libbz2.tbd; path = usr/lib/libbz2.tbd; sourceTree = SDKROOT; };
		9A0F35202AD3CE430080FD21 /* libz.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libz.tbd; path = usr/lib/libz.tbd; sourceTree = SDKROOT; };
		9A0F35212AD3CE540080FD21 /* libz.1.2.5.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libz.1.2.5.tbd; path = usr/lib/libz.1.2.5.tbd; sourceTree = SDKROOT; };
		9A0F35222AD3CE750080FD21 /* libiconv.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libiconv.tbd; path = usr/lib/libiconv.tbd; sourceTree = SDKROOT; };
		9A150AA22214092A00840F54 /* AppDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AppDelegate.h; sourceTree = "<group>"; };
		9A150AA32214092A00840F54 /* AppDelegate.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AppDelegate.m; sourceTree = "<group>"; };
		9A150AA72214092B00840F54 /* main.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
		9A150AA82214092B00840F54 /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Images.xcassets; sourceTree = "<group>"; };
		9A150AE12214092B00840F54 /* SANetworkingConfigurationManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SANetworkingConfigurationManager.h; sourceTree = "<group>"; };
		9A150AE22214092B00840F54 /* SALoggerConfiguration.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SALoggerConfiguration.m; sourceTree = "<group>"; };
		9A150AE32214092B00840F54 /* SANetworkingConfigurationManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SANetworkingConfigurationManager.m; sourceTree = "<group>"; };
		9A150AE42214092B00840F54 /* SALoggerConfiguration.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SALoggerConfiguration.h; sourceTree = "<group>"; };
		9A150AE52214092B00840F54 /* SANetworking.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SANetworking.h; sourceTree = "<group>"; };
		9A150AE82214092B00840F54 /* SALogger.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SALogger.m; sourceTree = "<group>"; };
		9A150AE92214092B00840F54 /* SALogger.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SALogger.h; sourceTree = "<group>"; };
		9A150AEA2214092B00840F54 /* QLNetworkEnum.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = QLNetworkEnum.h; sourceTree = "<group>"; };
		9A150AEC2214092B00840F54 /* SAResponse.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SAResponse.h; sourceTree = "<group>"; };
		9A150AED2214092B00840F54 /* SAResponse.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SAResponse.m; sourceTree = "<group>"; };
		9A150AEF2214092B00840F54 /* QLAPIManagerInterceptor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = QLAPIManagerInterceptor.h; sourceTree = "<group>"; };
		9A150AF02214092B00840F54 /* QLAPIManagerValidator.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = QLAPIManagerValidator.h; sourceTree = "<group>"; };
		9A150AF12214092B00840F54 /* QLAPIManagerDataReformer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = QLAPIManagerDataReformer.h; sourceTree = "<group>"; };
		9A150AF22214092B00840F54 /* QLAPIManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = QLAPIManager.h; sourceTree = "<group>"; };
		9A150AF42214092B00840F54 /* SABaseManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SABaseManager.m; sourceTree = "<group>"; };
		9A150AF52214092B00840F54 /* SABaseManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SABaseManager.h; sourceTree = "<group>"; };
		9A150AF72214092B00840F54 /* SAApiProxy.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SAApiProxy.m; sourceTree = "<group>"; };
		9A150AF82214092B00840F54 /* SAApiProxy.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SAApiProxy.h; sourceTree = "<group>"; };
		9A150AFA2214092B00840F54 /* SACache.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SACache.h; sourceTree = "<group>"; };
		9A150AFB2214092B00840F54 /* SACachedObject.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SACachedObject.h; sourceTree = "<group>"; };
		9A150AFC2214092B00840F54 /* SACache.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SACache.m; sourceTree = "<group>"; };
		9A150AFD2214092B00840F54 /* SACachedObject.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SACachedObject.m; sourceTree = "<group>"; };
		9A150B002214092B00840F54 /* SARequestGenerator.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SARequestGenerator.m; sourceTree = "<group>"; };
		9A150B012214092B00840F54 /* SARequestGenerator.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SARequestGenerator.h; sourceTree = "<group>"; };
		9A150B032214092B00840F54 /* SARequest+QLNetworkingMethods.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "SARequest+QLNetworkingMethods.m"; sourceTree = "<group>"; };
		9A150B042214092B00840F54 /* SAObject+QLNetworkingMethods.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "SAObject+QLNetworkingMethods.m"; sourceTree = "<group>"; };
		9A150B052214092B00840F54 /* SAArray+QLNetworkingMethods.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "SAArray+QLNetworkingMethods.m"; sourceTree = "<group>"; };
		9A150B062214092B00840F54 /* SAString+QLNetworkingMethods.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "SAString+QLNetworkingMethods.m"; sourceTree = "<group>"; };
		9A150B072214092B00840F54 /* SADictionary+QLNetworkingMethods.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "SADictionary+QLNetworkingMethods.m"; sourceTree = "<group>"; };
		9A150B082214092B00840F54 /* SAMutableString+QLNetworkingMethods.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "SAMutableString+QLNetworkingMethods.m"; sourceTree = "<group>"; };
		9A150B092214092B00840F54 /* SAObject+QLNetworkingMethods.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "SAObject+QLNetworkingMethods.h"; sourceTree = "<group>"; };
		9A150B0A2214092B00840F54 /* SARequest+QLNetworkingMethods.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "SARequest+QLNetworkingMethods.h"; sourceTree = "<group>"; };
		9A150B0B2214092B00840F54 /* SAMutableString+QLNetworkingMethods.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "SAMutableString+QLNetworkingMethods.h"; sourceTree = "<group>"; };
		9A150B0C2214092B00840F54 /* SADictionary+QLNetworkingMethods.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "SADictionary+QLNetworkingMethods.h"; sourceTree = "<group>"; };
		9A150B0D2214092B00840F54 /* SAArray+QLNetworkingMethods.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "SAArray+QLNetworkingMethods.h"; sourceTree = "<group>"; };
		9A150B0E2214092B00840F54 /* SAString+QLNetworkingMethods.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "SAString+QLNetworkingMethods.h"; sourceTree = "<group>"; };
		9A150B102214092B00840F54 /* SAService.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SAService.m; sourceTree = "<group>"; };
		9A150B112214092B00840F54 /* SAServiceFactory.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SAServiceFactory.h; sourceTree = "<group>"; };
		9A150B122214092B00840F54 /* SAServiceFactory.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SAServiceFactory.m; sourceTree = "<group>"; };
		9A150B132214092B00840F54 /* SAService.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SAService.h; sourceTree = "<group>"; };
		9A15F9752840B5E900D79C39 /* SAHomeReviewingCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAHomeReviewingCell.h; sourceTree = "<group>"; };
		9A15F9762840B5E900D79C39 /* SAHomeReviewingCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAHomeReviewingCell.m; sourceTree = "<group>"; };
		9A15F9A32840E22D00D79C39 /* SAHomeBankModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAHomeBankModel.h; sourceTree = "<group>"; };
		9A15F9A42840E22D00D79C39 /* SAHomeBankModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAHomeBankModel.m; sourceTree = "<group>"; };
		9A16888D2C8ED0E800A98C9B /* SATextView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SATextView.h; sourceTree = "<group>"; };
		9A16888E2C8ED0E800A98C9B /* SATextView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SATextView.m; sourceTree = "<group>"; };
		9A1688902C8EFA3000A98C9B /* SAGetBankRealNameApi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAGetBankRealNameApi.h; sourceTree = "<group>"; };
		9A1688912C8EFA3000A98C9B /* SAGetBankRealNameApi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAGetBankRealNameApi.m; sourceTree = "<group>"; };
		9A1702522C8866CB00FD811E /* SAProtocolViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAProtocolViewController.h; sourceTree = "<group>"; };
		9A1702532C8866CB00FD811E /* SAProtocolViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAProtocolViewController.m; sourceTree = "<group>"; };
		9A1702552C8964D100FD811E /* SAGetProtocolApiManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAGetProtocolApiManager.h; sourceTree = "<group>"; };
		9A1702562C8964D100FD811E /* SAGetProtocolApiManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAGetProtocolApiManager.m; sourceTree = "<group>"; };
		9A2245A729418F5F00DA1F9D /* SAProductModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAProductModel.h; sourceTree = "<group>"; };
		9A2245A829418F5F00DA1F9D /* SAProductModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAProductModel.m; sourceTree = "<group>"; };
		9A2245AA2941BAC400DA1F9D /* SAOrderDetailViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAOrderDetailViewController.h; sourceTree = "<group>"; };
		9A2245AB2941BAC400DA1F9D /* SAOrderDetailViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAOrderDetailViewController.m; sourceTree = "<group>"; };
		9A2245AD2941D07A00DA1F9D /* SALoanRecordModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SALoanRecordModel.h; sourceTree = "<group>"; };
		9A2245AE2941D07A00DA1F9D /* SALoanRecordModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SALoanRecordModel.m; sourceTree = "<group>"; };
		9A2245C72948A3EE00DA1F9D /* NPRequestMethodProtocol.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = NPRequestMethodProtocol.h; sourceTree = "<group>"; };
		9A2245D1294AED5400DA1F9D /* SACommonNoDataView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SACommonNoDataView.h; sourceTree = "<group>"; };
		9A2245D2294AED5400DA1F9D /* SACommonNoDataView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SACommonNoDataView.m; sourceTree = "<group>"; };
		9A2506DB2AD41375008C1068 /* SAGetOcrConfigApiManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAGetOcrConfigApiManager.h; sourceTree = "<group>"; };
		9A2506DC2AD41375008C1068 /* SAGetOcrConfigApiManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAGetOcrConfigApiManager.m; sourceTree = "<group>"; };
		9A2506DE2AD4F318008C1068 /* SAFaceSubmitApiManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAFaceSubmitApiManager.h; sourceTree = "<group>"; };
		9A2506DF2AD4F318008C1068 /* SAFaceSubmitApiManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAFaceSubmitApiManager.m; sourceTree = "<group>"; };
		9A2506E12AD506B4008C1068 /* SAGetCarrierUrlApiManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAGetCarrierUrlApiManager.h; sourceTree = "<group>"; };
		9A2506E22AD506B4008C1068 /* SAGetCarrierUrlApiManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAGetCarrierUrlApiManager.m; sourceTree = "<group>"; };
		9A2506E72AD53FA3008C1068 /* SAOfflineRepayViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAOfflineRepayViewController.h; sourceTree = "<group>"; };
		9A2506E82AD53FA3008C1068 /* SAOfflineRepayViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAOfflineRepayViewController.m; sourceTree = "<group>"; };
		9A2506EA2AD53FD6008C1068 /* SAOfflineRepayInfoApiManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAOfflineRepayInfoApiManager.h; sourceTree = "<group>"; };
		9A2506EB2AD53FD6008C1068 /* SAOfflineRepayInfoApiManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAOfflineRepayInfoApiManager.m; sourceTree = "<group>"; };
		9A2506ED2AD54A97008C1068 /* SAUploadModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAUploadModel.h; sourceTree = "<group>"; };
		9A2506EE2AD54A97008C1068 /* SAUploadModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAUploadModel.m; sourceTree = "<group>"; };
		9A2506F02AD54AA5008C1068 /* SANLUploadManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SANLUploadManager.h; sourceTree = "<group>"; };
		9A2506F12AD54AA5008C1068 /* SANLUploadManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SANLUploadManager.m; sourceTree = "<group>"; };
		9A2506F32AD54D43008C1068 /* SAGetFileTokenApiManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAGetFileTokenApiManager.h; sourceTree = "<group>"; };
		9A2506F42AD54D43008C1068 /* SAGetFileTokenApiManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAGetFileTokenApiManager.m; sourceTree = "<group>"; };
		9A2506F62AD54F08008C1068 /* SAFileUploadApiManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAFileUploadApiManager.h; sourceTree = "<group>"; };
		9A2506F72AD54F08008C1068 /* SAFileUploadApiManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAFileUploadApiManager.m; sourceTree = "<group>"; };
		9A2506F92AD656E4008C1068 /* SAS3Manager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAS3Manager.h; sourceTree = "<group>"; };
		9A2506FA2AD656E4008C1068 /* SAS3Manager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAS3Manager.m; sourceTree = "<group>"; };
		9A2533052C902CA90093728D /* SARepayH5ViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SARepayH5ViewController.h; sourceTree = "<group>"; };
		9A2533062C902CA90093728D /* SARepayH5ViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SARepayH5ViewController.m; sourceTree = "<group>"; };
		9A3387662CCA562D0056585A /* NPTradeAlertViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = NPTradeAlertViewController.h; sourceTree = "<group>"; };
		9A3387672CCA562D0056585A /* NPTradeAlertViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = NPTradeAlertViewController.m; sourceTree = "<group>"; };
		9A3387692CCA5AE90056585A /* SAHomeLoaningCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SAHomeLoaningCell.h; sourceTree = "<group>"; };
		9A33876A2CCA5AE90056585A /* SAHomeLoaningCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SAHomeLoaningCell.m; sourceTree = "<group>"; };
		9A33876C2CCA64740056585A /* NPHomeStepModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = NPHomeStepModel.h; sourceTree = "<group>"; };
		9A33876D2CCA64740056585A /* NPHomeStepModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = NPHomeStepModel.m; sourceTree = "<group>"; };
		9A35DE06283E03C300C387B7 /* SAToast+UIView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "SAToast+UIView.m"; sourceTree = "<group>"; };
		9A35DE07283E03C300C387B7 /* SAToast+UIView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "SAToast+UIView.h"; sourceTree = "<group>"; };
		9A35DE09283E046D00C387B7 /* SAString+Size.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "SAString+Size.h"; sourceTree = "<group>"; };
		9A35DE0A283E046D00C387B7 /* SAString+Size.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "SAString+Size.m"; sourceTree = "<group>"; };
		9A35DE49283E1F8800C387B7 /* SAGetLiveConfigManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAGetLiveConfigManager.h; sourceTree = "<group>"; };
		9A35DE4A283E1F8800C387B7 /* SAGetLiveConfigManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAGetLiveConfigManager.m; sourceTree = "<group>"; };
		9A35DE6A283E38C400C387B7 /* SAAdvanceManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAAdvanceManager.h; sourceTree = "<group>"; };
		9A35DE6B283E38C400C387B7 /* SAAdvanceManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAAdvanceManager.m; sourceTree = "<group>"; };
		9A35DE6D283F1AFA00C387B7 /* SAVerifyIdentityViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAVerifyIdentityViewController.h; sourceTree = "<group>"; };
		9A35DE6E283F1AFA00C387B7 /* SAVerifyIdentityViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAVerifyIdentityViewController.m; sourceTree = "<group>"; };
		9A35DE85283F46EF00C387B7 /* SAUploadManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAUploadManager.h; sourceTree = "<group>"; };
		9A35DE86283F46EF00C387B7 /* SAUploadManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAUploadManager.m; sourceTree = "<group>"; };
		9A35DE88283F84D700C387B7 /* SASubmitManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SASubmitManager.h; sourceTree = "<group>"; };
		9A35DE89283F84D700C387B7 /* SASubmitManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SASubmitManager.m; sourceTree = "<group>"; };
		9A35DE8E283F95BA00C387B7 /* SACompareUploadManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SACompareUploadManager.h; sourceTree = "<group>"; };
		9A35DE8F283F95BA00C387B7 /* SACompareUploadManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SACompareUploadManager.m; sourceTree = "<group>"; };
		9A35DE91283FA9D400C387B7 /* SAHomeNormalCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAHomeNormalCell.h; sourceTree = "<group>"; };
		9A35DE92283FA9D400C387B7 /* SAHomeNormalCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAHomeNormalCell.m; sourceTree = "<group>"; };
		9A3BA6D3285ACE1C0041A741 /* SAOrderConfigApiManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAOrderConfigApiManager.h; sourceTree = "<group>"; };
		9A3BA6D4285ACE1C0041A741 /* SAOrderConfigApiManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAOrderConfigApiManager.m; sourceTree = "<group>"; };
		9A4288272844A4E9000FE25F /* SAUploadInfoManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAUploadInfoManager.h; sourceTree = "<group>"; };
		9A4288282844A4E9000FE25F /* SAUploadInfoManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAUploadInfoManager.m; sourceTree = "<group>"; };
		9A42882B2844B83F000FE25F /* SAData+Compress.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "SAData+Compress.h"; sourceTree = "<group>"; };
		9A42882C2844B83F000FE25F /* SAData+Compress.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "SAData+Compress.m"; sourceTree = "<group>"; };
		9A48D7102CCBA59500A3B04F /* SAPayStatusViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAPayStatusViewController.h; sourceTree = "<group>"; };
		9A48D7112CCBA59500A3B04F /* SAPayStatusViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAPayStatusViewController.m; sourceTree = "<group>"; };
		9A48D7162CCBB5F000A3B04F /* SARepayCashierModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SARepayCashierModel.h; sourceTree = "<group>"; };
		9A48D7172CCBB5F000A3B04F /* SARepayCashierModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SARepayCashierModel.m; sourceTree = "<group>"; };
		9A48D7192CCBB80500A3B04F /* NPCashierCardCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = NPCashierCardCell.h; sourceTree = "<group>"; };
		9A48D71A2CCBB80500A3B04F /* NPCashierCardCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = NPCashierCardCell.m; sourceTree = "<group>"; };
		9A48D71C2CCBC26400A3B04F /* pay_loading.gif */ = {isa = PBXFileReference; lastKnownFileType = image.gif; path = pay_loading.gif; sourceTree = "<group>"; };
		9A4BC7102CBD18230051D311 /* EsignSDK.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = EsignSDK.framework; sourceTree = "<group>"; };
		9A4BC7112CBD18230051D311 /* EsignSDK.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = EsignSDK.bundle; sourceTree = "<group>"; };
		9A4C52FC2CD8DBAC004BC9CE /* Xiandaihua.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Xiandaihua.app; sourceTree = BUILT_PRODUCTS_DIR; };
		9A4C52FE2CD8DBF0004BC9CE /* Xinruihua-Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = "Xinruihua-Info.plist"; sourceTree = "<group>"; };
		9A6480482CC6452900C20F02 /* loading.gif */ = {isa = PBXFileReference; lastKnownFileType = image.gif; path = loading.gif; sourceTree = "<group>"; };
		9A64804A2CC6492800C20F02 /* SACustomHudImg.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SACustomHudImg.h; sourceTree = "<group>"; };
		9A64804B2CC6492900C20F02 /* SACustomHudImg.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SACustomHudImg.m; sourceTree = "<group>"; };
		9A6E92742BF5BDDD003D679C /* SAMarqueeView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SAMarqueeView.h; sourceTree = "<group>"; };
		9A6E92752BF5BDDD003D679C /* SAMarqueeView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SAMarqueeView.m; sourceTree = "<group>"; };
		9A6E927E2BF5E86B003D679C /* SAGetCustomerUrlApiManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAGetCustomerUrlApiManager.h; sourceTree = "<group>"; };
		9A6E927F2BF5E86C003D679C /* SAGetCustomerUrlApiManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAGetCustomerUrlApiManager.m; sourceTree = "<group>"; };
		9A818DD42BF5A721008DF462 /* SACycleVerticalView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SACycleVerticalView.m; sourceTree = "<group>"; };
		9A818DD52BF5A721008DF462 /* SACycleView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SACycleView.m; sourceTree = "<group>"; };
		9A818DD62BF5A721008DF462 /* SACycleVerticalView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SACycleVerticalView.h; sourceTree = "<group>"; };
		9A818DD72BF5A721008DF462 /* SACycleView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SACycleView.h; sourceTree = "<group>"; };
		9A9FFD9F2CCB4CB400DF3EE9 /* NPOnlineRepayViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = NPOnlineRepayViewController.h; sourceTree = "<group>"; };
		9A9FFDA02CCB4CB400DF3EE9 /* NPOnlineRepayViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = NPOnlineRepayViewController.m; sourceTree = "<group>"; };
		9A9FFDA22CCB4ED300DF3EE9 /* SAGetCashierInfoAPi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAGetCashierInfoAPi.h; sourceTree = "<group>"; };
		9A9FFDA32CCB4ED300DF3EE9 /* SAGetCashierInfoAPi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAGetCashierInfoAPi.m; sourceTree = "<group>"; };
		9A9FFDA52CCB4F8100DF3EE9 /* SACashierDoPayApi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SACashierDoPayApi.h; sourceTree = "<group>"; };
		9A9FFDA62CCB4F8100DF3EE9 /* SACashierDoPayApi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SACashierDoPayApi.m; sourceTree = "<group>"; };
		9A9FFDA82CCB513A00DF3EE9 /* SACashierPayStatusApi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SACashierPayStatusApi.h; sourceTree = "<group>"; };
		9A9FFDA92CCB513A00DF3EE9 /* SACashierPayStatusApi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SACashierPayStatusApi.m; sourceTree = "<group>"; };
		9AA090492856EBC900E835CB /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		9AA557592C8B309300F902A5 /* SAGetBillDetailApi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAGetBillDetailApi.h; sourceTree = "<group>"; };
		9AA5575A2C8B309300F902A5 /* SAGetBillDetailApi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAGetBillDetailApi.m; sourceTree = "<group>"; };
		9AA5575C2C8D4AE700F902A5 /* SAHomeConfirmCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAHomeConfirmCell.h; sourceTree = "<group>"; };
		9AA5575D2C8D4AE700F902A5 /* SAHomeConfirmCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAHomeConfirmCell.m; sourceTree = "<group>"; };
		9AA5575F2C8D4B7F00F902A5 /* SAConfrimTradeViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAConfrimTradeViewController.h; sourceTree = "<group>"; };
		9AA557602C8D4B7F00F902A5 /* SAConfrimTradeViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAConfrimTradeViewController.m; sourceTree = "<group>"; };
		9AA557652C8D4CC200F902A5 /* SAGetTradeDetailApi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAGetTradeDetailApi.h; sourceTree = "<group>"; };
		9AA557662C8D4CC200F902A5 /* SAGetTradeDetailApi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAGetTradeDetailApi.m; sourceTree = "<group>"; };
		9AA557682C8D4D0F00F902A5 /* SASubmitTradeApi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SASubmitTradeApi.h; sourceTree = "<group>"; };
		9AA557692C8D4D0F00F902A5 /* SASubmitTradeApi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SASubmitTradeApi.m; sourceTree = "<group>"; };
		9AA5576B2C8D4D5C00F902A5 /* SAGetEsignUrlApi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAGetEsignUrlApi.h; sourceTree = "<group>"; };
		9AA5576C2C8D4D5C00F902A5 /* SAGetEsignUrlApi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAGetEsignUrlApi.m; sourceTree = "<group>"; };
		9AA5576E2C8D52D300F902A5 /* SATradeDetailCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SATradeDetailCell.h; sourceTree = "<group>"; };
		9AA5576F2C8D52D300F902A5 /* SATradeDetailCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SATradeDetailCell.m; sourceTree = "<group>"; };
		9AA557732C8D839D00F902A5 /* SAPickerAlertView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SAPickerAlertView.m; sourceTree = "<group>"; };
		9AA557742C8D839D00F902A5 /* SABundle+BRPickerView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "SABundle+BRPickerView.h"; sourceTree = "<group>"; };
		9AA557752C8D839D00F902A5 /* BRPickerViewMacro.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BRPickerViewMacro.h; sourceTree = "<group>"; };
		9AA557762C8D839D00F902A5 /* Panding.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = Panding.bundle; sourceTree = "<group>"; };
		9AA557772C8D839D00F902A5 /* SAPickerStyle.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SAPickerStyle.m; sourceTree = "<group>"; };
		9AA557782C8D839D00F902A5 /* SABundle+BRPickerView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "SABundle+BRPickerView.m"; sourceTree = "<group>"; };
		9AA557792C8D839D00F902A5 /* SAPickerAlertView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SAPickerAlertView.h; sourceTree = "<group>"; };
		9AA5577A2C8D839D00F902A5 /* SAPickerStyle.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SAPickerStyle.h; sourceTree = "<group>"; };
		9AA5577C2C8D839D00F902A5 /* SADatePickerView+BR.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "SADatePickerView+BR.m"; sourceTree = "<group>"; };
		9AA5577D2C8D839D00F902A5 /* SADatePickerView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SADatePickerView.h; sourceTree = "<group>"; };
		9AA5577E2C8D839D00F902A5 /* SADate+BRPickerView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "SADate+BRPickerView.m"; sourceTree = "<group>"; };
		9AA5577F2C8D839D00F902A5 /* SADatePickerView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SADatePickerView.m; sourceTree = "<group>"; };
		9AA557802C8D839D00F902A5 /* SADatePickerView+BR.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "SADatePickerView+BR.h"; sourceTree = "<group>"; };
		9AA557812C8D839D00F902A5 /* SADate+BRPickerView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "SADate+BRPickerView.h"; sourceTree = "<group>"; };
		9AA557822C8D839D00F902A5 /* BRPickerView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BRPickerView.h; sourceTree = "<group>"; };
		9AA557832C8D839D00F902A5 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xml; path = PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		9AA557852C8D839D00F902A5 /* SATextPickerView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SATextPickerView.h; sourceTree = "<group>"; };
		9AA557862C8D839D00F902A5 /* SATextModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SATextModel.h; sourceTree = "<group>"; };
		9AA557872C8D839D00F902A5 /* SATextModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SATextModel.m; sourceTree = "<group>"; };
		9AA557882C8D839D00F902A5 /* SATextPickerView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SATextPickerView.m; sourceTree = "<group>"; };
		9AA5578B2C8D839D00F902A5 /* SAResultModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SAResultModel.m; sourceTree = "<group>"; };
		9AA5578C2C8D839D00F902A5 /* SAStringPickerView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SAStringPickerView.h; sourceTree = "<group>"; };
		9AA5578D2C8D839D00F902A5 /* SAResultModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SAResultModel.h; sourceTree = "<group>"; };
		9AA5578E2C8D839D00F902A5 /* SAStringPickerView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SAStringPickerView.m; sourceTree = "<group>"; };
		9AA557902C8D839D00F902A5 /* SABaseView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SABaseView.m; sourceTree = "<group>"; };
		9AA557912C8D839D00F902A5 /* SABaseView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SABaseView.h; sourceTree = "<group>"; };
		9AA557932C8D839D00F902A5 /* SAAddressModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SAAddressModel.m; sourceTree = "<group>"; };
		9AA557942C8D839D00F902A5 /* Pointer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Pointer.h; sourceTree = "<group>"; };
		9AA557952C8D839D00F902A5 /* SAAddressModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SAAddressModel.h; sourceTree = "<group>"; };
		9AA557962C8D839D00F902A5 /* Pointer.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = Pointer.bundle; sourceTree = "<group>"; };
		9AA557972C8D839D00F902A5 /* Pointer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = Pointer.m; sourceTree = "<group>"; };
		9AB09A71284276F7005C55AB /* SAGetRegManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAGetRegManager.h; sourceTree = "<group>"; };
		9AB09A72284276F7005C55AB /* SAGetRegManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAGetRegManager.m; sourceTree = "<group>"; };
		9ABB8DEC22140D5200FBF613 /* SAPrefix.pch */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAPrefix.pch; sourceTree = "<group>"; };
		9ABB8E1322141F1B00FBF613 /* SAMainManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAMainManager.h; sourceTree = "<group>"; };
		9ABB8E1422141F1B00FBF613 /* SAMainManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAMainManager.m; sourceTree = "<group>"; };
		9ABE6D1E2CBF9EB8003B5100 /* SAContactItemCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAContactItemCell.h; sourceTree = "<group>"; };
		9ABE6D1F2CBF9EB8003B5100 /* SAContactItemCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAContactItemCell.m; sourceTree = "<group>"; };
		9ABE6D212CBF9F3A003B5100 /* SAVerifyContactsViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAVerifyContactsViewController.h; sourceTree = "<group>"; };
		9ABE6D222CBF9F3A003B5100 /* SAVerifyContactsViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAVerifyContactsViewController.m; sourceTree = "<group>"; };
		9AC7E10F2CE595D000944F3B /* Haoxianghua.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Haoxianghua.app; sourceTree = BUILT_PRODUCTS_DIR; };
		9AC7E1112CE5970B00944F3B /* Haoxianghua-Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = "Haoxianghua-Info.plist"; sourceTree = "<group>"; };
		9ACA664C2AC1345E00D6250B /* SACompositeConstraint.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SACompositeConstraint.h; sourceTree = "<group>"; };
		9ACA664D2AC1345E00D6250B /* SALayoutConstraint+MASDebugAdditions.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "SALayoutConstraint+MASDebugAdditions.m"; sourceTree = "<group>"; };
		9ACA664E2AC1345E00D6250B /* MASConstraint+Private.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "MASConstraint+Private.h"; sourceTree = "<group>"; };
		9ACA664F2AC1345E00D6250B /* SALayoutConstraint.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SALayoutConstraint.h; sourceTree = "<group>"; };
		9ACA66502AC1345E00D6250B /* NSArray+MASShorthandAdditions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSArray+MASShorthandAdditions.h"; sourceTree = "<group>"; };
		9ACA66512AC1345E00D6250B /* SAConstraintMaker.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SAConstraintMaker.h; sourceTree = "<group>"; };
		9ACA66522AC1345E00D6250B /* SAView+MASAdditions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "SAView+MASAdditions.h"; sourceTree = "<group>"; };
		9ACA66532AC1345E00D6250B /* SAArray+MASAdditions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "SAArray+MASAdditions.h"; sourceTree = "<group>"; };
		9ACA66542AC1345E00D6250B /* MASUtilities.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MASUtilities.h; sourceTree = "<group>"; };
		9ACA66552AC1345E00D6250B /* SAViewAttribute.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SAViewAttribute.h; sourceTree = "<group>"; };
		9ACA66562AC1345E00D6250B /* SAViewController+MASAdditions.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "SAViewController+MASAdditions.m"; sourceTree = "<group>"; };
		9ACA66572AC1345E00D6250B /* SAViewConstraint.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SAViewConstraint.h; sourceTree = "<group>"; };
		9ACA66582AC1345E00D6250B /* SAConstraint.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SAConstraint.h; sourceTree = "<group>"; };
		9ACA66592AC1345E00D6250B /* SALayoutConstraint+MASDebugAdditions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "SALayoutConstraint+MASDebugAdditions.h"; sourceTree = "<group>"; };
		9ACA665A2AC1345E00D6250B /* SACompositeConstraint.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SACompositeConstraint.m; sourceTree = "<group>"; };
		9ACA665B2AC1345E00D6250B /* SAConstraintMaker.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SAConstraintMaker.m; sourceTree = "<group>"; };
		9ACA665C2AC1345E00D6250B /* SALayoutConstraint.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SALayoutConstraint.m; sourceTree = "<group>"; };
		9ACA665D2AC1345E00D6250B /* SAArray+MASAdditions.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "SAArray+MASAdditions.m"; sourceTree = "<group>"; };
		9ACA665E2AC1345E00D6250B /* SAView+MASAdditions.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "SAView+MASAdditions.m"; sourceTree = "<group>"; };
		9ACA665F2AC1345E00D6250B /* View+MASShorthandAdditions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "View+MASShorthandAdditions.h"; sourceTree = "<group>"; };
		9ACA66602AC1345E00D6250B /* Masonry.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Masonry.h; sourceTree = "<group>"; };
		9ACA66612AC1345E00D6250B /* SAConstraint.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SAConstraint.m; sourceTree = "<group>"; };
		9ACA66622AC1345E00D6250B /* SAViewController+MASAdditions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "SAViewController+MASAdditions.h"; sourceTree = "<group>"; };
		9ACA66632AC1345E00D6250B /* SAViewConstraint.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SAViewConstraint.m; sourceTree = "<group>"; };
		9ACA66642AC1345E00D6250B /* SAViewAttribute.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SAViewAttribute.m; sourceTree = "<group>"; };
		9ACF14E02C8D93DD0057A6D1 /* SAConfirmSmsAlertView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAConfirmSmsAlertView.h; sourceTree = "<group>"; };
		9ACF14E12C8D93DD0057A6D1 /* SAConfirmSmsAlertView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAConfirmSmsAlertView.m; sourceTree = "<group>"; };
		9ACF14E32C8DB42A0057A6D1 /* SAGetSupportBankListApi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAGetSupportBankListApi.h; sourceTree = "<group>"; };
		9ACF14E42C8DB42A0057A6D1 /* SAGetSupportBankListApi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAGetSupportBankListApi.m; sourceTree = "<group>"; };
		9ACF14E62C8DB6200057A6D1 /* SAMyCardsListViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAMyCardsListViewController.h; sourceTree = "<group>"; };
		9ACF14E72C8DB6200057A6D1 /* SAMyCardsListViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAMyCardsListViewController.m; sourceTree = "<group>"; };
		9ACF14E92C8DB6350057A6D1 /* SAConfirmmBankViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAConfirmmBankViewController.h; sourceTree = "<group>"; };
		9ACF14EA2C8DB6350057A6D1 /* SAConfirmmBankViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAConfirmmBankViewController.m; sourceTree = "<group>"; };
		9ACF14EC2C8DB6550057A6D1 /* SAGetMyCardsApi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAGetMyCardsApi.h; sourceTree = "<group>"; };
		9ACF14ED2C8DB6550057A6D1 /* SAGetMyCardsApi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAGetMyCardsApi.m; sourceTree = "<group>"; };
		9ACF14EF2C8DB71E0057A6D1 /* SACardListCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SACardListCell.h; sourceTree = "<group>"; };
		9ACF14F02C8DB71E0057A6D1 /* SACardListCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SACardListCell.m; sourceTree = "<group>"; };
		9ACF14F22C8DBC5F0057A6D1 /* SAMyBankItemModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAMyBankItemModel.h; sourceTree = "<group>"; };
		9ACF14F32C8DBC5F0057A6D1 /* SAMyBankItemModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAMyBankItemModel.m; sourceTree = "<group>"; };
		9ACF14F52C8DC04C0057A6D1 /* SAGetBorrowAgainApi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAGetBorrowAgainApi.h; sourceTree = "<group>"; };
		9ACF14F62C8DC04C0057A6D1 /* SAGetBorrowAgainApi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAGetBorrowAgainApi.m; sourceTree = "<group>"; };
		9AD150A32C8AA0080033A9D9 /* SAImage+ChangeColor.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "SAImage+ChangeColor.m"; sourceTree = "<group>"; };
		9AD150A42C8AA0080033A9D9 /* SAImage+ChangeColor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "SAImage+ChangeColor.h"; sourceTree = "<group>"; };
		9AE3D2062CCA488C008C0D58 /* NPInviteAlertViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = NPInviteAlertViewController.h; sourceTree = "<group>"; };
		9AE3D2072CCA488C008C0D58 /* NPInviteAlertViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = NPInviteAlertViewController.m; sourceTree = "<group>"; };
		9AEC08722C898F4D008475BD /* SAVerifyBankCardViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAVerifyBankCardViewController.h; sourceTree = "<group>"; };
		9AEC08732C898F4D008475BD /* SAVerifyBankCardViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAVerifyBankCardViewController.m; sourceTree = "<group>"; };
		9AEC08752C899B1E008475BD /* SABankItemView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SABankItemView.h; sourceTree = "<group>"; };
		9AEC08762C899B1E008475BD /* SABankItemView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SABankItemView.m; sourceTree = "<group>"; };
		9AEC08782C89A11D008475BD /* SABindCardSendCodeApi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SABindCardSendCodeApi.h; sourceTree = "<group>"; };
		9AEC08792C89A11D008475BD /* SABindCardSendCodeApi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SABindCardSendCodeApi.m; sourceTree = "<group>"; };
		9AEC087B2C89A136008475BD /* SABindCardResendSmsApi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SABindCardResendSmsApi.h; sourceTree = "<group>"; };
		9AEC087C2C89A136008475BD /* SABindCardResendSmsApi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SABindCardResendSmsApi.m; sourceTree = "<group>"; };
		9AEC08812C89A597008475BD /* SASaveBindCardInfoApi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SASaveBindCardInfoApi.h; sourceTree = "<group>"; };
		9AEC08822C89A597008475BD /* SASaveBindCardInfoApi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SASaveBindCardInfoApi.m; sourceTree = "<group>"; };
		9AEC08842C89A679008475BD /* SABindCardInfoModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SABindCardInfoModel.h; sourceTree = "<group>"; };
		9AEC08852C89A679008475BD /* SABindCardInfoModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SABindCardInfoModel.m; sourceTree = "<group>"; };
		9AEC08872C89BCCF008475BD /* SABankNameModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SABankNameModel.h; sourceTree = "<group>"; };
		9AEC08882C89BCCF008475BD /* SABankNameModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SABankNameModel.m; sourceTree = "<group>"; };
		9AEC088A2C89BD26008475BD /* SABankBinListViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SABankBinListViewController.h; sourceTree = "<group>"; };
		9AEC088B2C89BD26008475BD /* SABankBinListViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SABankBinListViewController.m; sourceTree = "<group>"; };
		9AEC088D2C89D150008475BD /* SAQueryCardBinApi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAQueryCardBinApi.h; sourceTree = "<group>"; };
		9AEC088E2C89D150008475BD /* SAQueryCardBinApi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAQueryCardBinApi.m; sourceTree = "<group>"; };
		9AEC08902C89DAA0008475BD /* SASettingViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SASettingViewController.h; sourceTree = "<group>"; };
		9AEC08912C89DAA0008475BD /* SASettingViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SASettingViewController.m; sourceTree = "<group>"; };
		9AF5C3E02C8AE656000EF027 /* SAServiceView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAServiceView.h; sourceTree = "<group>"; };
		9AF5C3E12C8AE656000EF027 /* SAServiceView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAServiceView.m; sourceTree = "<group>"; };
		9AF5C3E92C8B0193000EF027 /* SARepaymentViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SARepaymentViewController.h; sourceTree = "<group>"; };
		9AF5C3EA2C8B0193000EF027 /* SARepaymentViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SARepaymentViewController.m; sourceTree = "<group>"; };
		9AF5C3EC2C8B0212000EF027 /* SARepaymentCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SARepaymentCell.h; sourceTree = "<group>"; };
		9AF5C3ED2C8B0212000EF027 /* SARepaymentCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SARepaymentCell.m; sourceTree = "<group>"; };
		9AF5C3F22C8B0253000EF027 /* SARepayListModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SARepayListModel.h; sourceTree = "<group>"; };
		9AF5C3F32C8B0253000EF027 /* SARepayListModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SARepayListModel.m; sourceTree = "<group>"; };
		9AF5C3F52C8B053B000EF027 /* SAGetRepayBillListApi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAGetRepayBillListApi.h; sourceTree = "<group>"; };
		9AF5C3F62C8B053B000EF027 /* SAGetRepayBillListApi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAGetRepayBillListApi.m; sourceTree = "<group>"; };
		9AF5C3F82C8B05CA000EF027 /* SAGetPaidBillListApi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAGetPaidBillListApi.h; sourceTree = "<group>"; };
		9AF5C3F92C8B05CA000EF027 /* SAGetPaidBillListApi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAGetPaidBillListApi.m; sourceTree = "<group>"; };
		9AF5C3FC2C8B0B77000EF027 /* SATabedSlideView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SATabedSlideView.h; sourceTree = "<group>"; };
		9AF5C3FE2C8B0B77000EF027 /* SAJQCache.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SAJQCache.m; sourceTree = "<group>"; };
		9AF5C3FF2C8B0B77000EF027 /* DLCacheProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DLCacheProtocol.h; sourceTree = "<group>"; };
		9AF5C4002C8B0B77000EF027 /* SAJQCache.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SAJQCache.h; sourceTree = "<group>"; };
		9AF5C4012C8B0B77000EF027 /* SAUtility.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SAUtility.h; sourceTree = "<group>"; };
		9AF5C4022C8B0B77000EF027 /* SASlideView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SASlideView.h; sourceTree = "<group>"; };
		9AF5C4032C8B0B77000EF027 /* SACustomSlideView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SACustomSlideView.h; sourceTree = "<group>"; };
		9AF5C4042C8B0B77000EF027 /* SATabedSlideView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SATabedSlideView.m; sourceTree = "<group>"; };
		9AF5C4062C8B0B77000EF027 /* DLSlideTabbarProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DLSlideTabbarProtocol.h; sourceTree = "<group>"; };
		9AF5C4072C8B0B77000EF027 /* SAScrollTabbarView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SAScrollTabbarView.h; sourceTree = "<group>"; };
		9AF5C4082C8B0B77000EF027 /* SAFixedTabbarView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SAFixedTabbarView.h; sourceTree = "<group>"; };
		9AF5C4092C8B0B77000EF027 /* SAScrollTabbarView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SAScrollTabbarView.m; sourceTree = "<group>"; };
		9AF5C40A2C8B0B77000EF027 /* SAFixedTabbarView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SAFixedTabbarView.m; sourceTree = "<group>"; };
		9AF5C40B2C8B0B77000EF027 /* SAUtility.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SAUtility.m; sourceTree = "<group>"; };
		9AF5C40C2C8B0B77000EF027 /* SACustomSlideView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SACustomSlideView.m; sourceTree = "<group>"; };
		9AF5C40D2C8B0B77000EF027 /* SASlideView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SASlideView.m; sourceTree = "<group>"; };
		9AF5C4152C8B113B000EF027 /* SARepayListVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SARepayListVC.h; sourceTree = "<group>"; };
		9AF5C4162C8B113B000EF027 /* SARepayListVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SARepayListVC.m; sourceTree = "<group>"; };
		9AF5C4182C8B1155000EF027 /* SAPaidListVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAPaidListVC.h; sourceTree = "<group>"; };
		9AF5C4192C8B1155000EF027 /* SAPaidListVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SAPaidListVC.m; sourceTree = "<group>"; };
		9AFB9888221FEEA600F294D4 /* SACommonConfig.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SACommonConfig.h; sourceTree = "<group>"; };
		9AFB9914222005B300F294D4 /* SAResponseHandle.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SAResponseHandle.h; sourceTree = "<group>"; };
		9AFB9916222005B400F294D4 /* SAResponseHandle.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SAResponseHandle.m; sourceTree = "<group>"; };
		9AFB9917222005B400F294D4 /* SARequestHandle.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SARequestHandle.h; sourceTree = "<group>"; };
		9AFB9919222005B400F294D4 /* SARequestHandle.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SARequestHandle.m; sourceTree = "<group>"; };
		9AFB991A222005B400F294D4 /* SABasicService.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SABasicService.h; sourceTree = "<group>"; };
		9AFB991B222005B400F294D4 /* SABasicService.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SABasicService.m; sourceTree = "<group>"; };
		9AFB99332220082200F294D4 /* SARuntimeExtensions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SARuntimeExtensions.h; sourceTree = "<group>"; };
		9AFB99342220082200F294D4 /* metamacros.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = metamacros.h; sourceTree = "<group>"; };
		9AFB99352220082200F294D4 /* SAScope.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SAScope.m; sourceTree = "<group>"; };
		9AFB99362220082200F294D4 /* SARuntimeExtensions.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SARuntimeExtensions.m; sourceTree = "<group>"; };
		9AFB99372220082200F294D4 /* SAScope.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SAScope.h; sourceTree = "<group>"; };
		9AFB99382220082200F294D4 /* EXTKeyPathCoding.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = EXTKeyPathCoding.h; sourceTree = "<group>"; };
		9AFDCD832846553000850B41 /* LaunchScreen.storyboard */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; path = LaunchScreen.storyboard; sourceTree = "<group>"; };
		B1A7DD6585FE063FBFDD755D /* Pods-StagingApp.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-StagingApp.debug.xcconfig"; path = "Pods/Target Support Files/Pods-StagingApp/Pods-StagingApp.debug.xcconfig"; sourceTree = "<group>"; };
		B7FA4A5F314A3F3B4EC310A9 /* Pods_FreeDom.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_FreeDom.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		BE52B8F47FD6C8DAACFA509F /* Pods-FreeXin.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-FreeXin.debug.xcconfig"; path = "Pods/Target Support Files/Pods-FreeXin/Pods-FreeXin.debug.xcconfig"; sourceTree = "<group>"; };
		C80BD436105A4C7496CD70F6 /* Ionicons.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = Ionicons.ttf; path = "../node_modules/native-base/Fonts/Ionicons.ttf"; sourceTree = "<group>"; };
		D4E67254066D4B2B949044F9 /* MaterialCommunityIcons.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = MaterialCommunityIcons.ttf; path = "../node_modules/native-base/Fonts/MaterialCommunityIcons.ttf"; sourceTree = "<group>"; };
		DB188983BA9244EF958DCBC7 /* AntDesign.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = AntDesign.ttf; path = "../node_modules/native-base/Fonts/AntDesign.ttf"; sourceTree = "<group>"; };
		F3E223FF8E1F4674AE65E3B6 /* FontAwesome5_Regular.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = FontAwesome5_Regular.ttf; path = "../node_modules/native-base/Fonts/FontAwesome5_Regular.ttf"; sourceTree = "<group>"; };
		F6A35BB17FEC20FD7BA651C6 /* Pods-FreeXin.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-FreeXin.release.xcconfig"; path = "Pods/Target Support Files/Pods-FreeXin/Pods-FreeXin.release.xcconfig"; sourceTree = "<group>"; };
		F80DFFF8ED02B4B42D2E74DE /* Pods_StagingApp.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_StagingApp.framework; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		13B07F8C1A680F5B00A75B9A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				9A0F35262AD3CEA90080FD21 /* libbz2.tbd in Frameworks */,
				9A0F35252AD3CE9F0080FD21 /* libz.tbd in Frameworks */,
				9A0F35242AD3CE950080FD21 /* libz.1.2.5.tbd in Frameworks */,
				9A4BC7122CBD18230051D311 /* EsignSDK.framework in Frameworks */,
				9A0F35232AD3CE8A0080FD21 /* libiconv.tbd in Frameworks */,
				08D0A96D22C37F6C00D68951 /* CoreTelephony.framework in Frameworks */,
				08D0A96B22C37F4B00D68951 /* libc++.tbd in Frameworks */,
				08675B5C22C1F7EC007CCC9F /* CoreMotion.framework in Frameworks */,
				9A0F351E2AD3CE1F0080FD21 /* VideoToolbox.framework in Frameworks */,
				08F4C1CA22C0A6440000B366 /* CoreMedia.framework in Frameworks */,
				08F4C1C822C0A63A0000B366 /* AVFoundation.framework in Frameworks */,
				08E3B51622A0DB6D00C6BA22 /* JavaScriptCore.framework in Frameworks */,
				9A344B552833698A004AEE1D /* SystemConfiguration.framework in Frameworks */,
				9A01F5402ABC2FD30024CC0A /* Security.framework in Frameworks */,
				745683A290BCBD35D46535D2 /* (null) in Frameworks */,
				D4F87E74C92A7E9057CEE5B6 /* Pods_StagingApp.framework in Frameworks */,
				22BDEAA5C0D817416B0B2077 /* Pods_StagingApp.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9A4C52D82CD8DBAC004BC9CE /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				9A4C52D92CD8DBAC004BC9CE /* libbz2.tbd in Frameworks */,
				9A4C52DA2CD8DBAC004BC9CE /* libz.tbd in Frameworks */,
				9A4C52DB2CD8DBAC004BC9CE /* libz.1.2.5.tbd in Frameworks */,
				9A4C52DC2CD8DBAC004BC9CE /* EsignSDK.framework in Frameworks */,
				9A4C52DD2CD8DBAC004BC9CE /* libiconv.tbd in Frameworks */,
				9A4C52DE2CD8DBAC004BC9CE /* CoreTelephony.framework in Frameworks */,
				9A4C52DF2CD8DBAC004BC9CE /* libc++.tbd in Frameworks */,
				9A4C52E02CD8DBAC004BC9CE /* CoreMotion.framework in Frameworks */,
				9A4C52E12CD8DBAC004BC9CE /* VideoToolbox.framework in Frameworks */,
				9A4C52E22CD8DBAC004BC9CE /* CoreMedia.framework in Frameworks */,
				9A4C52E32CD8DBAC004BC9CE /* AVFoundation.framework in Frameworks */,
				9A4C52E42CD8DBAC004BC9CE /* JavaScriptCore.framework in Frameworks */,
				9A4C52E52CD8DBAC004BC9CE /* SystemConfiguration.framework in Frameworks */,
				9A4C52E62CD8DBAC004BC9CE /* Security.framework in Frameworks */,
				9A4C52E72CD8DBAC004BC9CE /* (null) in Frameworks */,
				9A4C52E82CD8DBAC004BC9CE /* Pods_StagingApp.framework in Frameworks */,
				9A4C52E92CD8DBAC004BC9CE /* Pods_StagingApp.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9AC7E0EA2CE595D000944F3B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				9AC7E0EB2CE595D000944F3B /* libbz2.tbd in Frameworks */,
				9AC7E0EC2CE595D000944F3B /* libz.tbd in Frameworks */,
				9AC7E0ED2CE595D000944F3B /* libz.1.2.5.tbd in Frameworks */,
				9AC7E0EE2CE595D000944F3B /* EsignSDK.framework in Frameworks */,
				9AC7E0EF2CE595D000944F3B /* libiconv.tbd in Frameworks */,
				9AC7E0F02CE595D000944F3B /* CoreTelephony.framework in Frameworks */,
				9AC7E0F12CE595D000944F3B /* libc++.tbd in Frameworks */,
				9AC7E0F22CE595D000944F3B /* CoreMotion.framework in Frameworks */,
				9AC7E0F32CE595D000944F3B /* VideoToolbox.framework in Frameworks */,
				9AC7E0F42CE595D000944F3B /* CoreMedia.framework in Frameworks */,
				9AC7E0F52CE595D000944F3B /* AVFoundation.framework in Frameworks */,
				9AC7E0F62CE595D000944F3B /* JavaScriptCore.framework in Frameworks */,
				9AC7E0F72CE595D000944F3B /* SystemConfiguration.framework in Frameworks */,
				9AC7E0F82CE595D000944F3B /* Security.framework in Frameworks */,
				9AC7E0F92CE595D000944F3B /* (null) in Frameworks */,
				9AC7E0FA2CE595D000944F3B /* Pods_StagingApp.framework in Frameworks */,
				9AC7E0FB2CE595D000944F3B /* Pods_StagingApp.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		0805C27E22B1F45000021D25 /* Profile */ = {
			isa = PBXGroup;
			children = (
				0805C28D22B1FB5300021D25 /* VC */,
				0805C27F22B1F4A000021D25 /* Views */,
			);
			path = Profile;
			sourceTree = "<group>";
		};
		0805C27F22B1F4A000021D25 /* Views */ = {
			isa = PBXGroup;
			children = (
				0856E5C122B2793C00D5C183 /* SACenterYTextView.h */,
				0856E5C222B2793C00D5C183 /* SACenterYTextView.m */,
				0856E5C422B279CB00D5C183 /* SAVerifyInfoCell.h */,
				0856E5C522B279CB00D5C183 /* SAVerifyInfoCell.m */,
				9AEC08752C899B1E008475BD /* SABankItemView.h */,
				9AEC08762C899B1E008475BD /* SABankItemView.m */,
				9A09512F2CBE076C0053D586 /* SAFaceBackAlert.h */,
				9A09512E2CBE076C0053D586 /* SAFaceBackAlert.m */,
				9ABE6D1E2CBF9EB8003B5100 /* SAContactItemCell.h */,
				9ABE6D1F2CBF9EB8003B5100 /* SAContactItemCell.m */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		0805C28022B1F4D300021D25 /* Profile */ = {
			isa = PBXGroup;
			children = (
				0805C28122B1F4D900021D25 /* Manager */,
				0805C28222B1F4E100021D25 /* Api */,
				0805C28322B1F4E900021D25 /* Model */,
			);
			path = Profile;
			sourceTree = "<group>";
		};
		0805C28122B1F4D900021D25 /* Manager */ = {
			isa = PBXGroup;
			children = (
				0805C2A722B24A7800021D25 /* SAVerifyListManager.h */,
				0805C2A822B24A7800021D25 /* SAVerifyListManager.m */,
				08F4C18C22C078330000B366 /* SAManager.h */,
				08F4C18D22C078330000B366 /* SAManager.m */,
				9A35DE6A283E38C400C387B7 /* SAAdvanceManager.h */,
				9A35DE6B283E38C400C387B7 /* SAAdvanceManager.m */,
			);
			path = Manager;
			sourceTree = "<group>";
		};
		0805C28222B1F4E100021D25 /* Api */ = {
			isa = PBXGroup;
			children = (
				08F4C18522C075D30000B366 /* Face */,
				0805C28422B1F54600021D25 /* SAVerifyDataListApiManager.h */,
				0805C28522B1F54600021D25 /* SAVerifyDataListApiManager.m */,
				0805C2EF22B2570800021D25 /* SAGetPersonalInfoApiManager.h */,
				0805C2F022B2570800021D25 /* SAGetPersonalInfoApiManager.m */,
				08FF4C5722B61C010095863C /* SASavePersonalInfoApiManager.h */,
				08FF4C5822B61C010095863C /* SASavePersonalInfoApiManager.m */,
				08FF4C5D22B61CF20095863C /* SAGetContactInfoApiManager.h */,
				08FF4C5E22B61CF20095863C /* SAGetContactInfoApiManager.m */,
				9A021B1C2AB8622F007EC584 /* SAGetCitiesListApiManager.h */,
				9A021B1D2AB8622F007EC584 /* SAGetCitiesListApiManager.m */,
				9A2506E12AD506B4008C1068 /* SAGetCarrierUrlApiManager.h */,
				9A2506E22AD506B4008C1068 /* SAGetCarrierUrlApiManager.m */,
				08FF4C6022B61D040095863C /* SASaveContactInfoApiManager.h */,
				08FF4C6122B61D040095863C /* SASaveContactInfoApiManager.m */,
				9AEC08782C89A11D008475BD /* SABindCardSendCodeApi.h */,
				9AEC08792C89A11D008475BD /* SABindCardSendCodeApi.m */,
				9AEC087B2C89A136008475BD /* SABindCardResendSmsApi.h */,
				9AEC087C2C89A136008475BD /* SABindCardResendSmsApi.m */,
				9ACF14E32C8DB42A0057A6D1 /* SAGetSupportBankListApi.h */,
				9ACF14E42C8DB42A0057A6D1 /* SAGetSupportBankListApi.m */,
				9AEC08812C89A597008475BD /* SASaveBindCardInfoApi.h */,
				9AEC08822C89A597008475BD /* SASaveBindCardInfoApi.m */,
				9AEC088D2C89D150008475BD /* SAQueryCardBinApi.h */,
				9AEC088E2C89D150008475BD /* SAQueryCardBinApi.m */,
				9ACF14EC2C8DB6550057A6D1 /* SAGetMyCardsApi.h */,
				9ACF14ED2C8DB6550057A6D1 /* SAGetMyCardsApi.m */,
			);
			path = Api;
			sourceTree = "<group>";
		};
		0805C28322B1F4E900021D25 /* Model */ = {
			isa = PBXGroup;
			children = (
				08FF4C6322B622F20095863C /* Contact */,
				08E66BCA22B4F791005E62D9 /* Bank */,
				083AB35522B36DF6003B4BB4 /* Personal */,
				0805C28722B1F6DD00021D25 /* SAVerifyListModel.h */,
				0805C28822B1F6DD00021D25 /* SAVerifyListModel.m */,
				0805C28A22B1F6F900021D25 /* SAVerifyListItemModel.h */,
				0805C28B22B1F6F900021D25 /* SAVerifyListItemModel.m */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		0805C28D22B1FB5300021D25 /* VC */ = {
			isa = PBXGroup;
			children = (
				9A35DE6D283F1AFA00C387B7 /* SAVerifyIdentityViewController.h */,
				9A35DE6E283F1AFA00C387B7 /* SAVerifyIdentityViewController.m */,
				0805C2D722B24DF900021D25 /* SAVerifyBasicInfoViewController.h */,
				0805C2D822B24DF900021D25 /* SAVerifyBasicInfoViewController.m */,
				9ABE6D212CBF9F3A003B5100 /* SAVerifyContactsViewController.h */,
				9ABE6D222CBF9F3A003B5100 /* SAVerifyContactsViewController.m */,
				9AEC08722C898F4D008475BD /* SAVerifyBankCardViewController.h */,
				9AEC08732C898F4D008475BD /* SAVerifyBankCardViewController.m */,
				9AEC088A2C89BD26008475BD /* SABankBinListViewController.h */,
				9AEC088B2C89BD26008475BD /* SABankBinListViewController.m */,
			);
			path = VC;
			sourceTree = "<group>";
		};
		083AB35522B36DF6003B4BB4 /* Personal */ = {
			isa = PBXGroup;
			children = (
				083AB35222B36DEA003B4BB4 /* SAPersonalVerifyModel.h */,
				083AB35322B36DEA003B4BB4 /* SAPersonalVerifyModel.m */,
				0805C2F222B25B2000021D25 /* SAPersonalInfoModel.h */,
				0805C2F322B25B2000021D25 /* SAPersonalInfoModel.m */,
				0805C2F822B25C5B00021D25 /* SAPersonalInfoCheckModel.h */,
				0805C2F922B25C5B00021D25 /* SAPersonalInfoCheckModel.m */,
			);
			path = Personal;
			sourceTree = "<group>";
		};
		0845D91222AE6BB40044AB4D /* NoNetView */ = {
			isa = PBXGroup;
			children = (
				0845D91422AE6BB40044AB4D /* SANoNetView.h */,
				0845D91322AE6BB40044AB4D /* SANoNetView.m */,
			);
			path = NoNetView;
			sourceTree = "<group>";
		};
		0845D93C22AE6C880044AB4D /* Home */ = {
			isa = PBXGroup;
			children = (
				0845D93D22AE6C880044AB4D /* VC */,
				0845D94022AE6C880044AB4D /* Views */,
			);
			path = Home;
			sourceTree = "<group>";
		};
		0845D93D22AE6C880044AB4D /* VC */ = {
			isa = PBXGroup;
			children = (
				0845D93E22AE6C880044AB4D /* SAHomePageViewController.h */,
				0845D93F22AE6C880044AB4D /* SAHomePageViewController.m */,
				9AA5575F2C8D4B7F00F902A5 /* SAConfrimTradeViewController.h */,
				9AA557602C8D4B7F00F902A5 /* SAConfrimTradeViewController.m */,
			);
			path = VC;
			sourceTree = "<group>";
		};
		0845D94022AE6C880044AB4D /* Views */ = {
			isa = PBXGroup;
			children = (
				08FF05B022AFAAB400DF17E6 /* HomeCell */,
				9AA557712C8D839D00F902A5 /* BRPickerView */,
				9AE3D2062CCA488C008C0D58 /* NPInviteAlertViewController.h */,
				9AE3D2072CCA488C008C0D58 /* NPInviteAlertViewController.m */,
				9A3387662CCA562D0056585A /* NPTradeAlertViewController.h */,
				9A3387672CCA562D0056585A /* NPTradeAlertViewController.m */,
				9AF5C3E02C8AE656000EF027 /* SAServiceView.h */,
				9AF5C3E12C8AE656000EF027 /* SAServiceView.m */,
				9AA5576E2C8D52D300F902A5 /* SATradeDetailCell.h */,
				9AA5576F2C8D52D300F902A5 /* SATradeDetailCell.m */,
				9ACF14E02C8D93DD0057A6D1 /* SAConfirmSmsAlertView.h */,
				9ACF14E12C8D93DD0057A6D1 /* SAConfirmSmsAlertView.m */,
				9A2245D1294AED5400DA1F9D /* SACommonNoDataView.h */,
				9A2245D2294AED5400DA1F9D /* SACommonNoDataView.m */,
				9A02DF562CC6593400431F1B /* SABannerView.h */,
				9A02DF572CC6593400431F1B /* SABannerView.m */,
				9A02DF592CC65D2600431F1B /* SABannerCell.h */,
				9A02DF5A2CC65D2600431F1B /* SABannerCell.m */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		0845D94322AE6C880044AB4D /* Mine */ = {
			isa = PBXGroup;
			children = (
				0845D94422AE6C880044AB4D /* VC */,
				0845D94722AE6C880044AB4D /* Views */,
			);
			path = Mine;
			sourceTree = "<group>";
		};
		0845D94422AE6C880044AB4D /* VC */ = {
			isa = PBXGroup;
			children = (
				0845D94622AE6C880044AB4D /* SAMineViewController.h */,
				0845D94522AE6C880044AB4D /* SAMineViewController.m */,
				9AEC08902C89DAA0008475BD /* SASettingViewController.h */,
				9AEC08912C89DAA0008475BD /* SASettingViewController.m */,
				9ACF14E62C8DB6200057A6D1 /* SAMyCardsListViewController.h */,
				9ACF14E72C8DB6200057A6D1 /* SAMyCardsListViewController.m */,
				9ACF14E92C8DB6350057A6D1 /* SAConfirmmBankViewController.h */,
				9ACF14EA2C8DB6350057A6D1 /* SAConfirmmBankViewController.m */,
			);
			path = VC;
			sourceTree = "<group>";
		};
		0845D94722AE6C880044AB4D /* Views */ = {
			isa = PBXGroup;
			children = (
				08FF05B122AFAAE300DF17E6 /* SAMainTopBgView.h */,
				08FF05B222AFAAE300DF17E6 /* SAMainTopBgView.m */,
				9ACF14EF2C8DB71E0057A6D1 /* SACardListCell.h */,
				9ACF14F02C8DB71E0057A6D1 /* SACardListCell.m */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		0845D94D22AE6D1D0044AB4D /* Login */ = {
			isa = PBXGroup;
			children = (
				0845D94E22AE6D1D0044AB4D /* VC */,
				0878034F22B1108B000A7A4F /* Views */,
			);
			path = Login;
			sourceTree = "<group>";
		};
		0845D94E22AE6D1D0044AB4D /* VC */ = {
			isa = PBXGroup;
			children = (
				0845D95122AE6D1D0044AB4D /* SALoginViewController.h */,
				0845D95022AE6D1D0044AB4D /* SALoginViewController.m */,
				9A0A39622A7A02EC0095AB86 /* SASmsCodeViewController.h */,
				9A0A39632A7A02EC0095AB86 /* SASmsCodeViewController.m */,
			);
			path = VC;
			sourceTree = "<group>";
		};
		0845D95522AE6D670044AB4D /* Common */ = {
			isa = PBXGroup;
			children = (
				0845D95622AE6D670044AB4D /* SATool.h */,
				0845D95822AE6D670044AB4D /* SATool.m */,
				0845D95722AE6D670044AB4D /* SACommonTool.h */,
				0845D95922AE6D670044AB4D /* SACommonTool.m */,
				0811F47022CB41D000CDBBD1 /* SAErrorView.h */,
				0811F47122CB41D000CDBBD1 /* SAErrorView.m */,
			);
			path = Common;
			sourceTree = "<group>";
		};
		0845D95B22AE6D670044AB4D /* Subclass */ = {
			isa = PBXGroup;
			children = (
				087802FF22B0967D000A7A4F /* SAGradientView.h */,
				0878030022B0967D000A7A4F /* SAGradientView.m */,
				0845D95D22AE6D670044AB4D /* SANormalRefresh.h */,
				0845D95C22AE6D670044AB4D /* SANormalRefresh.m */,
			);
			path = Subclass;
			sourceTree = "<group>";
		};
		0845D95E22AE6D670044AB4D /* Category */ = {
			isa = PBXGroup;
			children = (
				9A42882A2844B826000FE25F /* NSData */,
				9A35DE05283E03C300C387B7 /* Toast */,
				08B1EC4322B88128001367BB /* CAShapeLayer */,
				08FF053F22AF776B00DF17E6 /* UIImage */,
				0845D95F22AE6D670044AB4D /* NSDictionary */,
				0845D96422AE6D680044AB4D /* UIColor */,
				0845D96722AE6D680044AB4D /* UIAlertController */,
				0845D96A22AE6D680044AB4D /* UIView */,
				0845D96D22AE6D680044AB4D /* UIFont */,
				0845D97022AE6D680044AB4D /* NSString */,
				0845D97322AE6D680044AB4D /* NSDate */,
			);
			path = Category;
			sourceTree = "<group>";
		};
		0845D95F22AE6D670044AB4D /* NSDictionary */ = {
			isa = PBXGroup;
			children = (
				0845D96022AE6D670044AB4D /* SAMutableDictionary+Extension.m */,
				0845D96122AE6D670044AB4D /* SADictionary+Common.h */,
				0845D96222AE6D670044AB4D /* SAMutableDictionary+Extension.h */,
				0845D96322AE6D680044AB4D /* SADictionary+Common.m */,
			);
			path = NSDictionary;
			sourceTree = "<group>";
		};
		0845D96422AE6D680044AB4D /* UIColor */ = {
			isa = PBXGroup;
			children = (
				0845D96522AE6D680044AB4D /* SAColor+Extension.h */,
				0845D96622AE6D680044AB4D /* SAColor+Extension.m */,
			);
			path = UIColor;
			sourceTree = "<group>";
		};
		0845D96722AE6D680044AB4D /* UIAlertController */ = {
			isa = PBXGroup;
			children = (
				0845D96822AE6D680044AB4D /* SAAlertController+Orientation.m */,
				0845D96922AE6D680044AB4D /* SAAlertController+Orientation.h */,
			);
			path = UIAlertController;
			sourceTree = "<group>";
		};
		0845D96A22AE6D680044AB4D /* UIView */ = {
			isa = PBXGroup;
			children = (
				0845D96B22AE6D680044AB4D /* SAView+Extension.h */,
				0845D96C22AE6D680044AB4D /* SAView+Extension.m */,
			);
			path = UIView;
			sourceTree = "<group>";
		};
		0845D96D22AE6D680044AB4D /* UIFont */ = {
			isa = PBXGroup;
			children = (
				0845D96E22AE6D680044AB4D /* SAFont+Extension.h */,
				0845D96F22AE6D680044AB4D /* SAFont+Extension.m */,
			);
			path = UIFont;
			sourceTree = "<group>";
		};
		0845D97022AE6D680044AB4D /* NSString */ = {
			isa = PBXGroup;
			children = (
				0845D97122AE6D680044AB4D /* SAString+Extension.h */,
				0845D97222AE6D680044AB4D /* SAString+Extension.m */,
				9A35DE09283E046D00C387B7 /* SAString+Size.h */,
				9A35DE0A283E046D00C387B7 /* SAString+Size.m */,
			);
			path = NSString;
			sourceTree = "<group>";
		};
		0845D97322AE6D680044AB4D /* NSDate */ = {
			isa = PBXGroup;
			children = (
				0845D97422AE6D680044AB4D /* SADate+Extension.h */,
				0845D97522AE6D680044AB4D /* SADate+Extension.m */,
			);
			path = NSDate;
			sourceTree = "<group>";
		};
		0845D97622AE6D680044AB4D /* Location */ = {
			isa = PBXGroup;
			children = (
				0845D97722AE6D680044AB4D /* SALocationManager.h */,
				0845D97822AE6D680044AB4D /* SALocationManager.m */,
			);
			path = Location;
			sourceTree = "<group>";
		};
		0845D97C22AE6D680044AB4D /* Device */ = {
			isa = PBXGroup;
			children = (
				0845D98222AE6D680044AB4D /* SADeviceTool.h */,
				0845D97D22AE6D680044AB4D /* SADeviceTool.m */,
				0845D98122AE6D680044AB4D /* SABCTool.h */,
				0845D97E22AE6D680044AB4D /* SABCTool.m */,
				0845D97F22AE6D680044AB4D /* SAKeyChainTool.h */,
				0845D98022AE6D680044AB4D /* SAKeyChainTool.m */,
			);
			path = Device;
			sourceTree = "<group>";
		};
		0878034F22B1108B000A7A4F /* Views */ = {
			isa = PBXGroup;
			children = (
				0878035022B110B8000A7A4F /* SAVerifyCodeInputView.h */,
				0878035122B110B8000A7A4F /* SAVerifyCodeInputView.m */,
				9A16888D2C8ED0E800A98C9B /* SATextView.h */,
				9A16888E2C8ED0E800A98C9B /* SATextView.m */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		08AB286222BF604400CAE59C /* Wifi */ = {
			isa = PBXGroup;
			children = (
				08AB288B22BF606900CAE59C /* SAWifiManager.h */,
				08AB288C22BF606900CAE59C /* SAWifiManager.m */,
			);
			path = Wifi;
			sourceTree = "<group>";
		};
		08B1EC4322B88128001367BB /* CAShapeLayer */ = {
			isa = PBXGroup;
			children = (
				08B1EC4722B8815A001367BB /* SAShapeLayer+Extension.h */,
				08B1EC4822B8815A001367BB /* SAShapeLayer+Extension.m */,
			);
			path = CAShapeLayer;
			sourceTree = "<group>";
		};
		08B1EC4D22B8875B001367BB /* VC */ = {
			isa = PBXGroup;
			children = (
				08B1EC4A22B88754001367BB /* SACommonWebViewController.h */,
				08B1EC4B22B88754001367BB /* SACommonWebViewController.m */,
				9A2533052C902CA90093728D /* SARepayH5ViewController.h */,
				9A2533062C902CA90093728D /* SARepayH5ViewController.m */,
				9A1702522C8866CB00FD811E /* SAProtocolViewController.h */,
				9A1702532C8866CB00FD811E /* SAProtocolViewController.m */,
				08D14F5922BCCB4C0094582F /* SAAlertSureTipsViewController.h */,
				08D14F5A22BCCB4C0094582F /* SAAlertSureTipsViewController.m */,
				08D0A89F22C31B9700D68951 /* SAVersionUpdateViewController.h */,
				08D0A8A022C31B9700D68951 /* SAVersionUpdateViewController.m */,
			);
			path = VC;
			sourceTree = "<group>";
		};
		08B1EC6022B8B44E001367BB /* Sheet */ = {
			isa = PBXGroup;
			children = (
				08B1EC6122B8B46C001367BB /* SAActionSheet.h */,
				08B1EC6222B8B46C001367BB /* SAActionSheet.m */,
			);
			path = Sheet;
			sourceTree = "<group>";
		};
		08B1ECBE22B8CEAE001367BB /* Photo */ = {
			isa = PBXGroup;
			children = (
				08B1ECBF22B8CEC5001367BB /* SAPhotoManager.h */,
				08B1ECC022B8CEC5001367BB /* SAPhotoManager.m */,
			);
			path = Photo;
			sourceTree = "<group>";
		};
		08B1ECEC22B8DB1C001367BB /* Upload */ = {
			isa = PBXGroup;
			children = (
				9A2506F02AD54AA5008C1068 /* SANLUploadManager.h */,
				9A2506F12AD54AA5008C1068 /* SANLUploadManager.m */,
				9A2506ED2AD54A97008C1068 /* SAUploadModel.h */,
				9A2506EE2AD54A97008C1068 /* SAUploadModel.m */,
				9A2506F32AD54D43008C1068 /* SAGetFileTokenApiManager.h */,
				9A2506F42AD54D43008C1068 /* SAGetFileTokenApiManager.m */,
				9A2506F62AD54F08008C1068 /* SAFileUploadApiManager.h */,
				9A2506F72AD54F08008C1068 /* SAFileUploadApiManager.m */,
				08B1ED2222B8DDCE001367BB /* SAAliyunOSSManager.h */,
				08B1ED2322B8DDCE001367BB /* SAAliyunOSSManager.m */,
				9A2506F92AD656E4008C1068 /* SAS3Manager.h */,
				9A2506FA2AD656E4008C1068 /* SAS3Manager.m */,
				08B1ED1322B8DC14001367BB /* SATokenApiManager.h */,
				08B1ED1422B8DC14001367BB /* SATokenApiManager.m */,
			);
			path = Upload;
			sourceTree = "<group>";
		};
		08B4A91522B799F7006B35D4 /* Contacts */ = {
			isa = PBXGroup;
			children = (
				08B4A93C22B79A0D006B35D4 /* SAContactsManager.h */,
				08B4A93D22B79A0D006B35D4 /* SAContactsManager.m */,
			);
			path = Contacts;
			sourceTree = "<group>";
		};
		08CE112F22A8E78000E30241 /* Common */ = {
			isa = PBXGroup;
			children = (
				08CE116022A8F70A00E30241 /* Api */,
				08CE115622A8E79200E30241 /* Manager */,
			);
			path = Common;
			sourceTree = "<group>";
		};
		08CE115622A8E79200E30241 /* Manager */ = {
			isa = PBXGroup;
			children = (
				08CE116122A8F73F00E30241 /* SAAppCodeManager.h */,
				08CE116222A8F73F00E30241 /* SAAppCodeManager.m */,
			);
			path = Manager;
			sourceTree = "<group>";
		};
		08CE115E22A8F6B400E30241 /* User */ = {
			isa = PBXGroup;
			children = (
				08CE115F22A8F6CE00E30241 /* Manager */,
				08FF04EB22AF421300DF17E6 /* Api */,
				08FF04EC22AF421A00DF17E6 /* Model */,
			);
			path = User;
			sourceTree = "<group>";
		};
		08CE115F22A8F6CE00E30241 /* Manager */ = {
			isa = PBXGroup;
			children = (
				08CE115722A8E7A300E30241 /* SAUserManager.h */,
				08CE115822A8E7A300E30241 /* SAUserManager.m */,
			);
			path = Manager;
			sourceTree = "<group>";
		};
		08CE116022A8F70A00E30241 /* Api */ = {
			isa = PBXGroup;
			children = (
				08CE116422A8F79400E30241 /* SAAppConfigApiManager.h */,
				08CE116522A8F79400E30241 /* SAAppConfigApiManager.m */,
				08A6BAEB22B1E67F001CA577 /* SADeviceInfoSaveApiManager.h */,
				08A6BAEC22B1E67F001CA577 /* SADeviceInfoSaveApiManager.m */,
				089A56B822E9B3FC005BEBD5 /* SASaveDeviceChannelApiManager.h */,
				089A56B922E9B3FC005BEBD5 /* SASaveDeviceChannelApiManager.m */,
				9AB09A71284276F7005C55AB /* SAGetRegManager.h */,
				9AB09A72284276F7005C55AB /* SAGetRegManager.m */,
				9A4288272844A4E9000FE25F /* SAUploadInfoManager.h */,
				9A4288282844A4E9000FE25F /* SAUploadInfoManager.m */,
				9A6E927E2BF5E86B003D679C /* SAGetCustomerUrlApiManager.h */,
				9A6E927F2BF5E86C003D679C /* SAGetCustomerUrlApiManager.m */,
				9A1702552C8964D100FD811E /* SAGetProtocolApiManager.h */,
				9A1702562C8964D100FD811E /* SAGetProtocolApiManager.m */,
			);
			path = Api;
			sourceTree = "<group>";
		};
		08E66BCA22B4F791005E62D9 /* Bank */ = {
			isa = PBXGroup;
			children = (
				08E66BCE22B4F7C7005E62D9 /* SABankListModel.h */,
				08E66BCF22B4F7C7005E62D9 /* SABankListModel.m */,
				9AEC08842C89A679008475BD /* SABindCardInfoModel.h */,
				9AEC08852C89A679008475BD /* SABindCardInfoModel.m */,
				9AEC08872C89BCCF008475BD /* SABankNameModel.h */,
				9AEC08882C89BCCF008475BD /* SABankNameModel.m */,
				9ACF14F22C8DBC5F0057A6D1 /* SAMyBankItemModel.h */,
				9ACF14F32C8DBC5F0057A6D1 /* SAMyBankItemModel.m */,
			);
			path = Bank;
			sourceTree = "<group>";
		};
		08F4C18522C075D30000B366 /* Face */ = {
			isa = PBXGroup;
			children = (
				9A35DE49283E1F8800C387B7 /* SAGetLiveConfigManager.h */,
				9A35DE4A283E1F8800C387B7 /* SAGetLiveConfigManager.m */,
				9A35DE85283F46EF00C387B7 /* SAUploadManager.h */,
				9A35DE86283F46EF00C387B7 /* SAUploadManager.m */,
				9A35DE88283F84D700C387B7 /* SASubmitManager.h */,
				9A35DE89283F84D700C387B7 /* SASubmitManager.m */,
				9A2506DE2AD4F318008C1068 /* SAFaceSubmitApiManager.h */,
				9A2506DF2AD4F318008C1068 /* SAFaceSubmitApiManager.m */,
				9A35DE8E283F95BA00C387B7 /* SACompareUploadManager.h */,
				9A35DE8F283F95BA00C387B7 /* SACompareUploadManager.m */,
				9A2506DB2AD41375008C1068 /* SAGetOcrConfigApiManager.h */,
				9A2506DC2AD41375008C1068 /* SAGetOcrConfigApiManager.m */,
			);
			path = Face;
			sourceTree = "<group>";
		};
		08FF04EB22AF421300DF17E6 /* Api */ = {
			isa = PBXGroup;
			children = (
				0878034C22B10C35000A7A4F /* SAVerifyCodeApiManager.h */,
				0878034D22B10C35000A7A4F /* SAVerifyCodeApiManager.m */,
				08FF04ED22AF424E00DF17E6 /* SAMobileLoginApiManager.h */,
				08FF04EE22AF424E00DF17E6 /* SAMobileLoginApiManager.m */,
				08FF04F022AF432800DF17E6 /* SALogoutApiManager.h */,
				08FF04F122AF432800DF17E6 /* SALogoutApiManager.m */,
				0878033422B0E898000A7A4F /* SAUserCenterApiManager.h */,
				0878033522B0E898000A7A4F /* SAUserCenterApiManager.m */,
			);
			path = Api;
			sourceTree = "<group>";
		};
		08FF04EC22AF421A00DF17E6 /* Model */ = {
			isa = PBXGroup;
			children = (
				0878035322B138E9000A7A4F /* SAUserCenterModel.h */,
				0878035422B138E9000A7A4F /* SAUserCenterModel.m */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		08FF053F22AF776B00DF17E6 /* UIImage */ = {
			isa = PBXGroup;
			children = (
				08FF054022AF777900DF17E6 /* SAImage+Extension.h */,
				08FF054122AF777900DF17E6 /* SAImage+Extension.m */,
				9AD150A42C8AA0080033A9D9 /* SAImage+ChangeColor.h */,
				9AD150A32C8AA0080033A9D9 /* SAImage+ChangeColor.m */,
			);
			path = UIImage;
			sourceTree = "<group>";
		};
		08FF055022AF854F00DF17E6 /* Common */ = {
			isa = PBXGroup;
			children = (
				08B1EC4D22B8875B001367BB /* VC */,
				08FF055122AF855700DF17E6 /* Views */,
			);
			path = Common;
			sourceTree = "<group>";
		};
		08FF055122AF855700DF17E6 /* Views */ = {
			isa = PBXGroup;
			children = (
				08FF4C1F22B604390095863C /* SAPickerTextField.h */,
				08FF4C2022B604390095863C /* SAPickerTextField.m */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		08FF05B022AFAAB400DF17E6 /* HomeCell */ = {
			isa = PBXGroup;
			children = (
				08FF056D22AF9B4400DF17E6 /* SAHomeBasicCell.h */,
				08FF056E22AF9B4400DF17E6 /* SAHomeBasicCell.m */,
				9A35DE91283FA9D400C387B7 /* SAHomeNormalCell.h */,
				9A35DE92283FA9D400C387B7 /* SAHomeNormalCell.m */,
				9A15F9752840B5E900D79C39 /* SAHomeReviewingCell.h */,
				9A15F9762840B5E900D79C39 /* SAHomeReviewingCell.m */,
				9AA5575C2C8D4AE700F902A5 /* SAHomeConfirmCell.h */,
				9AA5575D2C8D4AE700F902A5 /* SAHomeConfirmCell.m */,
				08FF055E22AF923600DF17E6 /* SAHomeRejectCell.h */,
				08FF055F22AF923600DF17E6 /* SAHomeRejectCell.m */,
				9A3387692CCA5AE90056585A /* SAHomeLoaningCell.h */,
				9A33876A2CCA5AE90056585A /* SAHomeLoaningCell.m */,
			);
			path = HomeCell;
			sourceTree = "<group>";
		};
		08FF4C6322B622F20095863C /* Contact */ = {
			isa = PBXGroup;
			children = (
				08FF4C7022B626420095863C /* SAContactSectionModel.h */,
				08FF4C7122B626420095863C /* SAContactSectionModel.m */,
				08FF4C7322B626580095863C /* SAContactRowModel.h */,
				08FF4C7422B626580095863C /* SAContactRowModel.m */,
				08FF4C6A22B623870095863C /* SAContactSelectModel.h */,
				08FF4C6B22B623870095863C /* SAContactSelectModel.m */,
			);
			path = Contact;
			sourceTree = "<group>";
		};
		13B07FAE1A68108700A75B9A /* StagingApp */ = {
			isa = PBXGroup;
			children = (
				08DA5D0822C9A9E800DB3163 /* StagingApp.entitlements */,
				9A150AA12214092A00840F54 /* AppDelegate */,
				9A150A9A2214092A00840F54 /* Base */,
				9A150A9C2214092A00840F54 /* Modules */,
				9A150AAA2214092B00840F54 /* Pages */,
				9A150AAE2214092B00840F54 /* Tools */,
				9A150B152214092B00840F54 /* Config */,
				9A150AA42214092B00840F54 /* Resources */,
				9A150A9B2214092A00840F54 /* Vendor */,
			);
			name = StagingApp;
			sourceTree = "<group>";
		};
		2D16E6871FA4F8E400B85C8A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				9A0F35222AD3CE750080FD21 /* libiconv.tbd */,
				9A0F35212AD3CE540080FD21 /* libz.1.2.5.tbd */,
				9A0F35202AD3CE430080FD21 /* libz.tbd */,
				9A0F351F2AD3CE370080FD21 /* libbz2.tbd */,
				9A0F351D2AD3CE1F0080FD21 /* VideoToolbox.framework */,
				9A01F53F2ABC2FD20024CC0A /* Security.framework */,
				08D0A96C22C37F6C00D68951 /* CoreTelephony.framework */,
				08D0A96A22C37F4B00D68951 /* libc++.tbd */,
				08675B5D22C1F7F8007CCC9F /* SystemConfiguration.framework */,
				08675B5B22C1F7EB007CCC9F /* CoreMotion.framework */,
				08F4C1C922C0A6440000B366 /* CoreMedia.framework */,
				08F4C1C722C0A63A0000B366 /* AVFoundation.framework */,
				08E3B51522A0DB6D00C6BA22 /* JavaScriptCore.framework */,
				2D16E6891FA4F8E400B85C8A /* libReact.a */,
				B7FA4A5F314A3F3B4EC310A9 /* Pods_FreeDom.framework */,
				8B9D717CF3BC7C90C3435085 /* Pods_FreeXin.framework */,
				F80DFFF8ED02B4B42D2E74DE /* Pods_StagingApp.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		7A547F6DBF03500A8E2DF7FE /* Pods */ = {
			isa = PBXGroup;
			children = (
				B1A7DD6585FE063FBFDD755D /* Pods-StagingApp.debug.xcconfig */,
				3CF1BDA3CFE91B8A0832A6CE /* Pods-StagingApp.release.xcconfig */,
				4F70A708D4F6FD08F7971BE2 /* Pods-FreeDom.debug.xcconfig */,
				13959F7AC7337A91E9FFB0B1 /* Pods-FreeDom.release.xcconfig */,
				BE52B8F47FD6C8DAACFA509F /* Pods-FreeXin.debug.xcconfig */,
				F6A35BB17FEC20FD7BA651C6 /* Pods-FreeXin.release.xcconfig */,
			);
			name = Pods;
			sourceTree = "<group>";
		};
		83CBB9F61A601CBA00E9B192 = {
			isa = PBXGroup;
			children = (
				13B07FAE1A68108700A75B9A /* StagingApp */,
				83CBBA001A601CBA00E9B192 /* Products */,
				2D16E6871FA4F8E400B85C8A /* Frameworks */,
				7A547F6DBF03500A8E2DF7FE /* Pods */,
				93953D65FE0E4595B8499880 /* Resources */,
				9A818DE02BF5AF45008DF462 /* Recovered References */,
			);
			indentWidth = 4;
			sourceTree = "<group>";
			tabWidth = 4;
			usesTabs = 0;
		};
		83CBBA001A601CBA00E9B192 /* Products */ = {
			isa = PBXGroup;
			children = (
				13B07F961A680F5B00A75B9A /* StagingApp.app */,
				9A4C52FC2CD8DBAC004BC9CE /* Xiandaihua.app */,
				9AC7E10F2CE595D000944F3B /* Haoxianghua.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		93953D65FE0E4595B8499880 /* Resources */ = {
			isa = PBXGroup;
			children = (
				DB188983BA9244EF958DCBC7 /* AntDesign.ttf */,
				6E2B2ADF18C6423E8A996B56 /* Entypo.ttf */,
				098C17B06702499AA4BD2907 /* EvilIcons.ttf */,
				88301A4F13D943ABA8141D95 /* Feather.ttf */,
				96FFD84637A245F392DE27AA /* FontAwesome.ttf */,
				6F71D3A1BC8941E484276B55 /* FontAwesome5_Brands.ttf */,
				F3E223FF8E1F4674AE65E3B6 /* FontAwesome5_Regular.ttf */,
				30B3816DCC0B4A7F8741E5CE /* FontAwesome5_Solid.ttf */,
				5932DFB3983F459EBE38D514 /* Foundation.ttf */,
				C80BD436105A4C7496CD70F6 /* Ionicons.ttf */,
				D4E67254066D4B2B949044F9 /* MaterialCommunityIcons.ttf */,
				91DC73B8B7394A5F9CA573F8 /* MaterialIcons.ttf */,
				3CDBCBF7A8FC47FA8B2D2021 /* Octicons.ttf */,
				7DA21020CC7A4C8590FF6C19 /* Roboto_medium.ttf */,
				03167121A558418DA6B55141 /* Roboto.ttf */,
				59CD6C4F034940178E06C457 /* rubicon-icon-font.ttf */,
				4CB91B09649C496C8238C00B /* SimpleLineIcons.ttf */,
				4DCF1E6BB79240EEB437CCA8 /* Zocial.ttf */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		9A01F53B2ABC2FC10024CC0A /* RSA */ = {
			isa = PBXGroup;
			children = (
				9A01F53C2ABC2FC10024CC0A /* SAObjC.h */,
				9A01F53D2ABC2FC10024CC0A /* SAObjC.m */,
			);
			path = RSA;
			sourceTree = "<group>";
		};
		9A01F5412ABD33170024CC0A /* MJRefresh */ = {
			isa = PBXGroup;
			children = (
				9A01F5422ABD33170024CC0A /* SAScrollView+MJRefresh.m */,
				9A01F5432ABD33170024CC0A /* SACollectionViewLayout+MJRefresh.m */,
				9A01F5442ABD33170024CC0A /* SARefreshConst.m */,
				9A01F5452ABD33170024CC0A /* SARefreshConfig.m */,
				9A01F5462ABD33170024CC0A /* SAScrollView+MJExtension.h */,
				9A01F5472ABD33170024CC0A /* MJRefresh.h */,
				9A01F5482ABD33170024CC0A /* SABundle+MJRefresh.h */,
				9A01F5492ABD33170024CC0A /* Qdraw.bundle */,
				9A01F54A2ABD33170024CC0A /* SAView+MJExtension.h */,
				9A01F54B2ABD33170024CC0A /* SAScrollView+MJExtension.m */,
				9A01F54C2ABD33170024CC0A /* SARefreshConfig.h */,
				9A01F54D2ABD33170024CC0A /* SARefreshConst.h */,
				9A01F54E2ABD33170024CC0A /* SACollectionViewLayout+MJRefresh.h */,
				9A01F54F2ABD33170024CC0A /* SAScrollView+MJRefresh.h */,
				9A01F5502ABD33170024CC0A /* SABundle+MJRefresh.m */,
				9A01F5512ABD33170024CC0A /* SAView+MJExtension.m */,
				9A01F5522ABD33170024CC0A /* Custom */,
				9A01F56E2ABD33170024CC0A /* Base */,
			);
			path = MJRefresh;
			sourceTree = "<group>";
		};
		9A01F5522ABD33170024CC0A /* Custom */ = {
			isa = PBXGroup;
			children = (
				9A01F5532ABD33170024CC0A /* Footer */,
				9A01F5622ABD33170024CC0A /* Trailer */,
				9A01F5672ABD33170024CC0A /* Header */,
			);
			path = Custom;
			sourceTree = "<group>";
		};
		9A01F5532ABD33170024CC0A /* Footer */ = {
			isa = PBXGroup;
			children = (
				9A01F5542ABD33170024CC0A /* Back */,
				9A01F55B2ABD33170024CC0A /* Auto */,
			);
			path = Footer;
			sourceTree = "<group>";
		};
		9A01F5542ABD33170024CC0A /* Back */ = {
			isa = PBXGroup;
			children = (
				9A01F5552ABD33170024CC0A /* SARefreshBackGifFooter.h */,
				9A01F5562ABD33170024CC0A /* SARefreshBackStateFooter.h */,
				9A01F5572ABD33170024CC0A /* SARefreshBackNormalFooter.h */,
				9A01F5582ABD33170024CC0A /* SARefreshBackGifFooter.m */,
				9A01F5592ABD33170024CC0A /* SARefreshBackStateFooter.m */,
				9A01F55A2ABD33170024CC0A /* SARefreshBackNormalFooter.m */,
			);
			path = Back;
			sourceTree = "<group>";
		};
		9A01F55B2ABD33170024CC0A /* Auto */ = {
			isa = PBXGroup;
			children = (
				9A01F55C2ABD33170024CC0A /* SARefreshAutoStateFooter.h */,
				9A01F55D2ABD33170024CC0A /* SARefreshAutoNormalFooter.h */,
				9A01F55E2ABD33170024CC0A /* SARefreshAutoGifFooter.h */,
				9A01F55F2ABD33170024CC0A /* SARefreshAutoStateFooter.m */,
				9A01F5602ABD33170024CC0A /* SARefreshAutoGifFooter.m */,
				9A01F5612ABD33170024CC0A /* SARefreshAutoNormalFooter.m */,
			);
			path = Auto;
			sourceTree = "<group>";
		};
		9A01F5622ABD33170024CC0A /* Trailer */ = {
			isa = PBXGroup;
			children = (
				9A01F5632ABD33170024CC0A /* SARefreshNormalTrailer.h */,
				9A01F5642ABD33170024CC0A /* SARefreshStateTrailer.m */,
				9A01F5652ABD33170024CC0A /* SARefreshNormalTrailer.m */,
				9A01F5662ABD33170024CC0A /* SARefreshStateTrailer.h */,
			);
			path = Trailer;
			sourceTree = "<group>";
		};
		9A01F5672ABD33170024CC0A /* Header */ = {
			isa = PBXGroup;
			children = (
				9A01F5682ABD33170024CC0A /* SARefreshNormalHeader.m */,
				9A01F5692ABD33170024CC0A /* SARefreshStateHeader.h */,
				9A01F56A2ABD33170024CC0A /* SARefreshGifHeader.h */,
				9A01F56B2ABD33170024CC0A /* SARefreshNormalHeader.h */,
				9A01F56C2ABD33170024CC0A /* SARefreshStateHeader.m */,
				9A01F56D2ABD33170024CC0A /* SARefreshGifHeader.m */,
			);
			path = Header;
			sourceTree = "<group>";
		};
		9A01F56E2ABD33170024CC0A /* Base */ = {
			isa = PBXGroup;
			children = (
				9A01F56F2ABD33170024CC0A /* SARefreshFooter.m */,
				9A01F5702ABD33170024CC0A /* SARefreshComponent.h */,
				9A01F5712ABD33170024CC0A /* SARefreshHeader.m */,
				9A01F5722ABD33170024CC0A /* SARefreshAutoFooter.h */,
				9A01F5732ABD33170024CC0A /* SARefreshTrailer.h */,
				9A01F5742ABD33170024CC0A /* SARefreshBackFooter.m */,
				9A01F5752ABD33170024CC0A /* SARefreshAutoFooter.m */,
				9A01F5762ABD33170024CC0A /* SARefreshHeader.h */,
				9A01F5772ABD33170024CC0A /* SARefreshComponent.m */,
				9A01F5782ABD33170024CC0A /* SARefreshFooter.h */,
				9A01F5792ABD33170024CC0A /* SARefreshBackFooter.h */,
				9A01F57A2ABD33170024CC0A /* SARefreshTrailer.m */,
			);
			path = Base;
			sourceTree = "<group>";
		};
		9A150A9A2214092A00840F54 /* Base */ = {
			isa = PBXGroup;
			children = (
				9ABB8DF922140F5C00FBF613 /* VC */,
				9AFB98E02220053F00F294D4 /* Net */,
			);
			name = Base;
			path = StagingApp/Base;
			sourceTree = "<group>";
		};
		9A150A9B2214092A00840F54 /* Vendor */ = {
			isa = PBXGroup;
			children = (
				9A150ADF2214092B00840F54 /* SANetworking */,
				9A4BC70F2CBD18230051D311 /* EsignSDK */,
				9ACA664B2AC1345E00D6250B /* Masonry */,
				9A01F5412ABD33170024CC0A /* MJRefresh */,
				9AFB99322220082200F294D4 /* extobjc */,
			);
			name = Vendor;
			path = StagingApp/Vendor;
			sourceTree = "<group>";
		};
		9A150A9C2214092A00840F54 /* Modules */ = {
			isa = PBXGroup;
			children = (
				9A150A9D2214092A00840F54 /* Home */,
				9AF5C3EF2C8B0228000EF027 /* Repay */,
				08B1ECEC22B8DB1C001367BB /* Upload */,
				0805C28022B1F4D300021D25 /* Profile */,
				08CE115E22A8F6B400E30241 /* User */,
				08CE112F22A8E78000E30241 /* Common */,
			);
			name = Modules;
			path = StagingApp/Modules;
			sourceTree = "<group>";
		};
		9A150A9D2214092A00840F54 /* Home */ = {
			isa = PBXGroup;
			children = (
				9A150A9E2214092A00840F54 /* Manager */,
				9A150A9F2214092A00840F54 /* Model */,
				9A150AA02214092A00840F54 /* Api */,
			);
			path = Home;
			sourceTree = "<group>";
		};
		9A150A9E2214092A00840F54 /* Manager */ = {
			isa = PBXGroup;
			children = (
				9ABB8E1322141F1B00FBF613 /* SAMainManager.h */,
				9ABB8E1422141F1B00FBF613 /* SAMainManager.m */,
			);
			path = Manager;
			sourceTree = "<group>";
		};
		9A150A9F2214092A00840F54 /* Model */ = {
			isa = PBXGroup;
			children = (
				08FF04F922AF455E00DF17E6 /* SAHomeModel.h */,
				08FF04FA22AF455E00DF17E6 /* SAHomeModel.m */,
				9A33876C2CCA64740056585A /* NPHomeStepModel.h */,
				9A33876D2CCA64740056585A /* NPHomeStepModel.m */,
				9A02DF532CC656C000431F1B /* SABannerModel.h */,
				9A02DF542CC656C000431F1B /* SABannerModel.m */,
				9A15F9A32840E22D00D79C39 /* SAHomeBankModel.h */,
				9A15F9A42840E22D00D79C39 /* SAHomeBankModel.m */,
				08FF050F22AF539700DF17E6 /* SAHomeProtocolUrlsModel.h */,
				08FF051022AF539700DF17E6 /* SAHomeProtocolUrlsModel.m */,
				08FF051222AF53CF00DF17E6 /* SAHomeProtocolModel.h */,
				08FF051322AF53CF00DF17E6 /* SAHomeProtocolModel.m */,
				08FF051522AF5A2F00DF17E6 /* SAOrderInfoModel.h */,
				08FF051622AF5A2F00DF17E6 /* SAOrderInfoModel.m */,
				0880D0C022BB2357001C979F /* SAAuditProgressItemModel.h */,
				0880D0C122BB2357001C979F /* SAAuditProgressItemModel.m */,
				08FF051822AF5BE500DF17E6 /* SABorrowPurposeTypeModel.h */,
				08FF051922AF5BE500DF17E6 /* SABorrowPurposeTypeModel.m */,
				08FF051B22AF5C7600DF17E6 /* SABorrowCashModel.h */,
				08FF051C22AF5C7600DF17E6 /* SABorrowCashModel.m */,
				08FF051E22AF5CCF00DF17E6 /* SABorrowCashPeriodModel.h */,
				08FF051F22AF5CCF00DF17E6 /* SABorrowCashPeriodModel.m */,
				082B160922BB8CCA00A4C7C7 /* SAPayWayModel.h */,
				082B160A22BB8CCA00A4C7C7 /* SAPayWayModel.m */,
				08D14F0E22BC7E1E0094582F /* SAClientChargesModel.h */,
				08D14F0F22BC7E1E0094582F /* SAClientChargesModel.m */,
				08D14F3922BC7E970094582F /* SARepaymentModel.h */,
				08D14F3A22BC7E970094582F /* SARepaymentModel.m */,
				083D64D82313E2E400810B26 /* SASubmitProductInfoModel.h */,
				083D64D92313E2E400810B26 /* SASubmitProductInfoModel.m */,
				08EC05D12315245200D3C93C /* SAHistoryOrderModel.h */,
				08EC05D22315245200D3C93C /* SAHistoryOrderModel.m */,
				9A2245A729418F5F00DA1F9D /* SAProductModel.h */,
				9A2245A829418F5F00DA1F9D /* SAProductModel.m */,
				9A2245AD2941D07A00DA1F9D /* SALoanRecordModel.h */,
				9A2245AE2941D07A00DA1F9D /* SALoanRecordModel.m */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		9A150AA02214092A00840F54 /* Api */ = {
			isa = PBXGroup;
			children = (
				08FF04B522AF3EC500DF17E6 /* SAMainHomeApiManager.h */,
				08FF04B622AF3EC500DF17E6 /* SAMainHomeApiManager.m */,
				082B160622BB871600A4C7C7 /* SAPayClientInfoApiManager.h */,
				082B160722BB871600A4C7C7 /* SAPayClientInfoApiManager.m */,
				082B162222BBAECA00A4C7C7 /* SAPayClientChargesApiManager.h */,
				082B162322BBAECA00A4C7C7 /* SAPayClientChargesApiManager.m */,
				08F5A3F5231517BF00D75C55 /* SAOrderConfirmApiManager.h */,
				08F5A3F6231517BF00D75C55 /* SAOrderConfirmApiManager.m */,
				08F4C12122C063380000B366 /* SASaveGPSApiManager.h */,
				08F4C12222C063380000B366 /* SASaveGPSApiManager.m */,
				9A3BA6D3285ACE1C0041A741 /* SAOrderConfigApiManager.h */,
				9A3BA6D4285ACE1C0041A741 /* SAOrderConfigApiManager.m */,
				9A2506EA2AD53FD6008C1068 /* SAOfflineRepayInfoApiManager.h */,
				9A2506EB2AD53FD6008C1068 /* SAOfflineRepayInfoApiManager.m */,
				9AA557652C8D4CC200F902A5 /* SAGetTradeDetailApi.h */,
				9AA557662C8D4CC200F902A5 /* SAGetTradeDetailApi.m */,
				9AA557682C8D4D0F00F902A5 /* SASubmitTradeApi.h */,
				9AA557692C8D4D0F00F902A5 /* SASubmitTradeApi.m */,
				9AA5576B2C8D4D5C00F902A5 /* SAGetEsignUrlApi.h */,
				9AA5576C2C8D4D5C00F902A5 /* SAGetEsignUrlApi.m */,
				9ACF14F52C8DC04C0057A6D1 /* SAGetBorrowAgainApi.h */,
				9ACF14F62C8DC04C0057A6D1 /* SAGetBorrowAgainApi.m */,
				9A1688902C8EFA3000A98C9B /* SAGetBankRealNameApi.h */,
				9A1688912C8EFA3000A98C9B /* SAGetBankRealNameApi.m */,
			);
			path = Api;
			sourceTree = "<group>";
		};
		9A150AA12214092A00840F54 /* AppDelegate */ = {
			isa = PBXGroup;
			children = (
				9A150AA22214092A00840F54 /* AppDelegate.h */,
				9A150AA32214092A00840F54 /* AppDelegate.m */,
			);
			name = AppDelegate;
			path = StagingApp/AppDelegate;
			sourceTree = "<group>";
		};
		9A150AA42214092B00840F54 /* Resources */ = {
			isa = PBXGroup;
			children = (
				9A48D71C2CCBC26400A3B04F /* pay_loading.gif */,
				9A6480482CC6452900C20F02 /* loading.gif */,
				9AA090492856EBC900E835CB /* Info.plist */,
				9A4C52FE2CD8DBF0004BC9CE /* Xinruihua-Info.plist */,
				9AC7E1112CE5970B00944F3B /* Haoxianghua-Info.plist */,
				9A150AA72214092B00840F54 /* main.m */,
				9A150AA82214092B00840F54 /* Images.xcassets */,
				9AFDCD832846553000850B41 /* LaunchScreen.storyboard */,
				08CE10FB22A8C2FB00E30241 /* Localizable.strings */,
				9AA0904F2856EEAE00E835CB /* InfoPlist.strings */,
			);
			name = Resources;
			path = StagingApp/Resources;
			sourceTree = "<group>";
		};
		9A150AAA2214092B00840F54 /* Pages */ = {
			isa = PBXGroup;
			children = (
				0845D93C22AE6C880044AB4D /* Home */,
				9AF5C3E62C8B015C000EF027 /* Repayment */,
				08FF055022AF854F00DF17E6 /* Common */,
				0805C27E22B1F45000021D25 /* Profile */,
				0845D94322AE6C880044AB4D /* Mine */,
				0845D94D22AE6D1D0044AB4D /* Login */,
			);
			name = Pages;
			path = StagingApp/Pages;
			sourceTree = "<group>";
		};
		9A150AAE2214092B00840F54 /* Tools */ = {
			isa = PBXGroup;
			children = (
				9A01F53B2ABC2FC10024CC0A /* RSA */,
				08B1EC6022B8B44E001367BB /* Sheet */,
				9A6E92732BF5BDDD003D679C /* UUMarqueeView */,
				9A818DD32BF5A721008DF462 /* ZBCycleVerticalView */,
				08AB286222BF604400CAE59C /* Wifi */,
				08B1ECBE22B8CEAE001367BB /* Photo */,
				08B4A91522B799F7006B35D4 /* Contacts */,
				0845D95E22AE6D670044AB4D /* Category */,
				0845D95522AE6D670044AB4D /* Common */,
				0845D95B22AE6D670044AB4D /* Subclass */,
				0845D97C22AE6D680044AB4D /* Device */,
				0845D97622AE6D680044AB4D /* Location */,
			);
			name = Tools;
			path = StagingApp/Tools;
			sourceTree = "<group>";
		};
		9A150ADF2214092B00840F54 /* SANetworking */ = {
			isa = PBXGroup;
			children = (
				9A150AE52214092B00840F54 /* SANetworking.h */,
				9A150AE02214092B00840F54 /* Configurations */,
				9A150AE62214092B00840F54 /* Components */,
				9A150AFE2214092B00840F54 /* Generators */,
				9A150B022214092B00840F54 /* Categories */,
				9A150B0F2214092B00840F54 /* Services */,
			);
			path = SANetworking;
			sourceTree = "<group>";
		};
		9A150AE02214092B00840F54 /* Configurations */ = {
			isa = PBXGroup;
			children = (
				9A150AE42214092B00840F54 /* SALoggerConfiguration.h */,
				9A150AE22214092B00840F54 /* SALoggerConfiguration.m */,
				9A150AE12214092B00840F54 /* SANetworkingConfigurationManager.h */,
				9A150AE32214092B00840F54 /* SANetworkingConfigurationManager.m */,
			);
			path = Configurations;
			sourceTree = "<group>";
		};
		9A150AE62214092B00840F54 /* Components */ = {
			isa = PBXGroup;
			children = (
				9A150AEA2214092B00840F54 /* QLNetworkEnum.h */,
				9A150AE72214092B00840F54 /* LogComponents */,
				9A150AEB2214092B00840F54 /* URLResponse */,
				9A150AEE2214092B00840F54 /* Protocol */,
				9A150AF32214092B00840F54 /* BaseAPIManager */,
				9A150AF62214092B00840F54 /* APIProxy */,
				9A150AF92214092B00840F54 /* CacheComponents */,
			);
			path = Components;
			sourceTree = "<group>";
		};
		9A150AE72214092B00840F54 /* LogComponents */ = {
			isa = PBXGroup;
			children = (
				9A150AE92214092B00840F54 /* SALogger.h */,
				9A150AE82214092B00840F54 /* SALogger.m */,
			);
			path = LogComponents;
			sourceTree = "<group>";
		};
		9A150AEB2214092B00840F54 /* URLResponse */ = {
			isa = PBXGroup;
			children = (
				9A150AEC2214092B00840F54 /* SAResponse.h */,
				9A150AED2214092B00840F54 /* SAResponse.m */,
			);
			path = URLResponse;
			sourceTree = "<group>";
		};
		9A150AEE2214092B00840F54 /* Protocol */ = {
			isa = PBXGroup;
			children = (
				9A150AEF2214092B00840F54 /* QLAPIManagerInterceptor.h */,
				9A150AF02214092B00840F54 /* QLAPIManagerValidator.h */,
				9A150AF12214092B00840F54 /* QLAPIManagerDataReformer.h */,
				9A150AF22214092B00840F54 /* QLAPIManager.h */,
			);
			path = Protocol;
			sourceTree = "<group>";
		};
		9A150AF32214092B00840F54 /* BaseAPIManager */ = {
			isa = PBXGroup;
			children = (
				9A150AF52214092B00840F54 /* SABaseManager.h */,
				9A150AF42214092B00840F54 /* SABaseManager.m */,
			);
			path = BaseAPIManager;
			sourceTree = "<group>";
		};
		9A150AF62214092B00840F54 /* APIProxy */ = {
			isa = PBXGroup;
			children = (
				9A150AF82214092B00840F54 /* SAApiProxy.h */,
				9A150AF72214092B00840F54 /* SAApiProxy.m */,
			);
			path = APIProxy;
			sourceTree = "<group>";
		};
		9A150AF92214092B00840F54 /* CacheComponents */ = {
			isa = PBXGroup;
			children = (
				9A150AFA2214092B00840F54 /* SACache.h */,
				9A150AFC2214092B00840F54 /* SACache.m */,
				9A150AFB2214092B00840F54 /* SACachedObject.h */,
				9A150AFD2214092B00840F54 /* SACachedObject.m */,
			);
			path = CacheComponents;
			sourceTree = "<group>";
		};
		9A150AFE2214092B00840F54 /* Generators */ = {
			isa = PBXGroup;
			children = (
				9A150AFF2214092B00840F54 /* RequestGenerator */,
			);
			path = Generators;
			sourceTree = "<group>";
		};
		9A150AFF2214092B00840F54 /* RequestGenerator */ = {
			isa = PBXGroup;
			children = (
				9A150B012214092B00840F54 /* SARequestGenerator.h */,
				9A150B002214092B00840F54 /* SARequestGenerator.m */,
			);
			path = RequestGenerator;
			sourceTree = "<group>";
		};
		9A150B022214092B00840F54 /* Categories */ = {
			isa = PBXGroup;
			children = (
				9A150B0A2214092B00840F54 /* SARequest+QLNetworkingMethods.h */,
				9A150B032214092B00840F54 /* SARequest+QLNetworkingMethods.m */,
				9A150B092214092B00840F54 /* SAObject+QLNetworkingMethods.h */,
				9A150B042214092B00840F54 /* SAObject+QLNetworkingMethods.m */,
				9A150B0D2214092B00840F54 /* SAArray+QLNetworkingMethods.h */,
				9A150B052214092B00840F54 /* SAArray+QLNetworkingMethods.m */,
				9A150B0E2214092B00840F54 /* SAString+QLNetworkingMethods.h */,
				9A150B062214092B00840F54 /* SAString+QLNetworkingMethods.m */,
				9A150B0C2214092B00840F54 /* SADictionary+QLNetworkingMethods.h */,
				9A150B072214092B00840F54 /* SADictionary+QLNetworkingMethods.m */,
				9A150B0B2214092B00840F54 /* SAMutableString+QLNetworkingMethods.h */,
				9A150B082214092B00840F54 /* SAMutableString+QLNetworkingMethods.m */,
			);
			path = Categories;
			sourceTree = "<group>";
		};
		9A150B0F2214092B00840F54 /* Services */ = {
			isa = PBXGroup;
			children = (
				9A150B132214092B00840F54 /* SAService.h */,
				9A150B102214092B00840F54 /* SAService.m */,
				9A150B112214092B00840F54 /* SAServiceFactory.h */,
				9A150B122214092B00840F54 /* SAServiceFactory.m */,
			);
			path = Services;
			sourceTree = "<group>";
		};
		9A150B152214092B00840F54 /* Config */ = {
			isa = PBXGroup;
			children = (
				9ABB8DEC22140D5200FBF613 /* SAPrefix.pch */,
				9AFB9888221FEEA600F294D4 /* SACommonConfig.h */,
			);
			name = Config;
			path = StagingApp/Config;
			sourceTree = "<group>";
		};
		9A35DE05283E03C300C387B7 /* Toast */ = {
			isa = PBXGroup;
			children = (
				9A35DE06283E03C300C387B7 /* SAToast+UIView.m */,
				9A35DE07283E03C300C387B7 /* SAToast+UIView.h */,
			);
			path = Toast;
			sourceTree = "<group>";
		};
		9A42882A2844B826000FE25F /* NSData */ = {
			isa = PBXGroup;
			children = (
				9A42882B2844B83F000FE25F /* SAData+Compress.h */,
				9A42882C2844B83F000FE25F /* SAData+Compress.m */,
			);
			path = NSData;
			sourceTree = "<group>";
		};
		9A4BC70F2CBD18230051D311 /* EsignSDK */ = {
			isa = PBXGroup;
			children = (
				9A4BC7102CBD18230051D311 /* EsignSDK.framework */,
				9A4BC7112CBD18230051D311 /* EsignSDK.bundle */,
			);
			path = EsignSDK;
			sourceTree = "<group>";
		};
		9A6E92732BF5BDDD003D679C /* UUMarqueeView */ = {
			isa = PBXGroup;
			children = (
				9A6E92742BF5BDDD003D679C /* SAMarqueeView.h */,
				9A6E92752BF5BDDD003D679C /* SAMarqueeView.m */,
			);
			path = UUMarqueeView;
			sourceTree = "<group>";
		};
		9A818DD32BF5A721008DF462 /* ZBCycleVerticalView */ = {
			isa = PBXGroup;
			children = (
				9A818DD62BF5A721008DF462 /* SACycleVerticalView.h */,
				9A818DD42BF5A721008DF462 /* SACycleVerticalView.m */,
				9A818DD72BF5A721008DF462 /* SACycleView.h */,
				9A818DD52BF5A721008DF462 /* SACycleView.m */,
			);
			path = ZBCycleVerticalView;
			sourceTree = "<group>";
		};
		9A818DE02BF5AF45008DF462 /* Recovered References */ = {
			isa = PBXGroup;
			children = (
				49EAE65573E009493E201410 /* Pods_StagingApp.framework */,
			);
			name = "Recovered References";
			sourceTree = "<group>";
		};
		9AA557712C8D839D00F902A5 /* BRPickerView */ = {
			isa = PBXGroup;
			children = (
				9AA557722C8D839D00F902A5 /* Core */,
				9AA5577B2C8D839D00F902A5 /* DatePicker */,
				9AA557822C8D839D00F902A5 /* BRPickerView.h */,
				9AA557832C8D839D00F902A5 /* PrivacyInfo.xcprivacy */,
				9AA557842C8D839D00F902A5 /* TextPicker */,
				9AA557892C8D839D00F902A5 /* Deprecated */,
			);
			path = BRPickerView;
			sourceTree = "<group>";
		};
		9AA557722C8D839D00F902A5 /* Core */ = {
			isa = PBXGroup;
			children = (
				9AA557732C8D839D00F902A5 /* SAPickerAlertView.m */,
				9AA557742C8D839D00F902A5 /* SABundle+BRPickerView.h */,
				9AA557752C8D839D00F902A5 /* BRPickerViewMacro.h */,
				9AA557762C8D839D00F902A5 /* Panding.bundle */,
				9AA557772C8D839D00F902A5 /* SAPickerStyle.m */,
				9AA557782C8D839D00F902A5 /* SABundle+BRPickerView.m */,
				9AA557792C8D839D00F902A5 /* SAPickerAlertView.h */,
				9AA5577A2C8D839D00F902A5 /* SAPickerStyle.h */,
			);
			path = Core;
			sourceTree = "<group>";
		};
		9AA5577B2C8D839D00F902A5 /* DatePicker */ = {
			isa = PBXGroup;
			children = (
				9AA5577C2C8D839D00F902A5 /* SADatePickerView+BR.m */,
				9AA5577D2C8D839D00F902A5 /* SADatePickerView.h */,
				9AA5577E2C8D839D00F902A5 /* SADate+BRPickerView.m */,
				9AA5577F2C8D839D00F902A5 /* SADatePickerView.m */,
				9AA557802C8D839D00F902A5 /* SADatePickerView+BR.h */,
				9AA557812C8D839D00F902A5 /* SADate+BRPickerView.h */,
			);
			path = DatePicker;
			sourceTree = "<group>";
		};
		9AA557842C8D839D00F902A5 /* TextPicker */ = {
			isa = PBXGroup;
			children = (
				9AA557852C8D839D00F902A5 /* SATextPickerView.h */,
				9AA557862C8D839D00F902A5 /* SATextModel.h */,
				9AA557872C8D839D00F902A5 /* SATextModel.m */,
				9AA557882C8D839D00F902A5 /* SATextPickerView.m */,
			);
			path = TextPicker;
			sourceTree = "<group>";
		};
		9AA557892C8D839D00F902A5 /* Deprecated */ = {
			isa = PBXGroup;
			children = (
				9AA5578A2C8D839D00F902A5 /* StringPickerView */,
				9AA5578F2C8D839D00F902A5 /* Base */,
				9AA557922C8D839D00F902A5 /* AddressPickerView */,
			);
			path = Deprecated;
			sourceTree = "<group>";
		};
		9AA5578A2C8D839D00F902A5 /* StringPickerView */ = {
			isa = PBXGroup;
			children = (
				9AA5578B2C8D839D00F902A5 /* SAResultModel.m */,
				9AA5578C2C8D839D00F902A5 /* SAStringPickerView.h */,
				9AA5578D2C8D839D00F902A5 /* SAResultModel.h */,
				9AA5578E2C8D839D00F902A5 /* SAStringPickerView.m */,
			);
			path = StringPickerView;
			sourceTree = "<group>";
		};
		9AA5578F2C8D839D00F902A5 /* Base */ = {
			isa = PBXGroup;
			children = (
				9AA557902C8D839D00F902A5 /* SABaseView.m */,
				9AA557912C8D839D00F902A5 /* SABaseView.h */,
			);
			path = Base;
			sourceTree = "<group>";
		};
		9AA557922C8D839D00F902A5 /* AddressPickerView */ = {
			isa = PBXGroup;
			children = (
				9AA557932C8D839D00F902A5 /* SAAddressModel.m */,
				9AA557942C8D839D00F902A5 /* Pointer.h */,
				9AA557952C8D839D00F902A5 /* SAAddressModel.h */,
				9AA557962C8D839D00F902A5 /* Pointer.bundle */,
				9AA557972C8D839D00F902A5 /* Pointer.m */,
			);
			path = AddressPickerView;
			sourceTree = "<group>";
		};
		9ABB8DF922140F5C00FBF613 /* VC */ = {
			isa = PBXGroup;
			children = (
				0845D8CA22AE6ACF0044AB4D /* SABasicViewController.h */,
				0845D8C622AE6ACF0044AB4D /* SABasicViewController.m */,
				0845D8C922AE6ACF0044AB4D /* SANavigationViewController.h */,
				0845D8CB22AE6ACF0044AB4D /* SANavigationViewController.m */,
				0845D8C822AE6ACF0044AB4D /* SATabBarViewController.h */,
				0845D8C722AE6ACF0044AB4D /* SATabBarViewController.m */,
				9A64804A2CC6492800C20F02 /* SACustomHudImg.h */,
				9A64804B2CC6492900C20F02 /* SACustomHudImg.m */,
			);
			path = VC;
			sourceTree = "<group>";
		};
		9ACA664B2AC1345E00D6250B /* Masonry */ = {
			isa = PBXGroup;
			children = (
				9ACA664C2AC1345E00D6250B /* SACompositeConstraint.h */,
				9ACA664D2AC1345E00D6250B /* SALayoutConstraint+MASDebugAdditions.m */,
				9ACA664E2AC1345E00D6250B /* MASConstraint+Private.h */,
				9ACA664F2AC1345E00D6250B /* SALayoutConstraint.h */,
				9ACA66502AC1345E00D6250B /* NSArray+MASShorthandAdditions.h */,
				9ACA66512AC1345E00D6250B /* SAConstraintMaker.h */,
				9ACA66522AC1345E00D6250B /* SAView+MASAdditions.h */,
				9ACA66532AC1345E00D6250B /* SAArray+MASAdditions.h */,
				9ACA66542AC1345E00D6250B /* MASUtilities.h */,
				9ACA66552AC1345E00D6250B /* SAViewAttribute.h */,
				9ACA66562AC1345E00D6250B /* SAViewController+MASAdditions.m */,
				9ACA66572AC1345E00D6250B /* SAViewConstraint.h */,
				9ACA66582AC1345E00D6250B /* SAConstraint.h */,
				9ACA66592AC1345E00D6250B /* SALayoutConstraint+MASDebugAdditions.h */,
				9ACA665A2AC1345E00D6250B /* SACompositeConstraint.m */,
				9ACA665B2AC1345E00D6250B /* SAConstraintMaker.m */,
				9ACA665C2AC1345E00D6250B /* SALayoutConstraint.m */,
				9ACA665D2AC1345E00D6250B /* SAArray+MASAdditions.m */,
				9ACA665E2AC1345E00D6250B /* SAView+MASAdditions.m */,
				9ACA665F2AC1345E00D6250B /* View+MASShorthandAdditions.h */,
				9ACA66602AC1345E00D6250B /* Masonry.h */,
				9ACA66612AC1345E00D6250B /* SAConstraint.m */,
				9ACA66622AC1345E00D6250B /* SAViewController+MASAdditions.h */,
				9ACA66632AC1345E00D6250B /* SAViewConstraint.m */,
				9ACA66642AC1345E00D6250B /* SAViewAttribute.m */,
			);
			path = Masonry;
			sourceTree = "<group>";
		};
		9AF5C3E62C8B015C000EF027 /* Repayment */ = {
			isa = PBXGroup;
			children = (
				9AF5C3E72C8B016C000EF027 /* VC */,
				9AF5C3E82C8B0171000EF027 /* Views */,
			);
			path = Repayment;
			sourceTree = "<group>";
		};
		9AF5C3E72C8B016C000EF027 /* VC */ = {
			isa = PBXGroup;
			children = (
				9AF5C3E92C8B0193000EF027 /* SARepaymentViewController.h */,
				9AF5C3EA2C8B0193000EF027 /* SARepaymentViewController.m */,
				9AF5C4152C8B113B000EF027 /* SARepayListVC.h */,
				9AF5C4162C8B113B000EF027 /* SARepayListVC.m */,
				9AF5C4182C8B1155000EF027 /* SAPaidListVC.h */,
				9AF5C4192C8B1155000EF027 /* SAPaidListVC.m */,
				9A2245AA2941BAC400DA1F9D /* SAOrderDetailViewController.h */,
				9A2245AB2941BAC400DA1F9D /* SAOrderDetailViewController.m */,
				9A9FFD9F2CCB4CB400DF3EE9 /* NPOnlineRepayViewController.h */,
				9A9FFDA02CCB4CB400DF3EE9 /* NPOnlineRepayViewController.m */,
				9A48D7102CCBA59500A3B04F /* SAPayStatusViewController.h */,
				9A48D7112CCBA59500A3B04F /* SAPayStatusViewController.m */,
				9A2506E72AD53FA3008C1068 /* SAOfflineRepayViewController.h */,
				9A2506E82AD53FA3008C1068 /* SAOfflineRepayViewController.m */,
			);
			path = VC;
			sourceTree = "<group>";
		};
		9AF5C3E82C8B0171000EF027 /* Views */ = {
			isa = PBXGroup;
			children = (
				9AF5C3EC2C8B0212000EF027 /* SARepaymentCell.h */,
				9AF5C3ED2C8B0212000EF027 /* SARepaymentCell.m */,
				9A02DF5C2CC66F0100431F1B /* SAPaidCell.h */,
				9A02DF5D2CC66F0100431F1B /* SAPaidCell.m */,
				9A48D7192CCBB80500A3B04F /* NPCashierCardCell.h */,
				9A48D71A2CCBB80500A3B04F /* NPCashierCardCell.m */,
				9AF5C3FB2C8B0B77000EF027 /* DLSlideView */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		9AF5C3EF2C8B0228000EF027 /* Repay */ = {
			isa = PBXGroup;
			children = (
				9AF5C3F12C8B0239000EF027 /* Model */,
				9AF5C3F02C8B0233000EF027 /* Api */,
			);
			path = Repay;
			sourceTree = "<group>";
		};
		9AF5C3F02C8B0233000EF027 /* Api */ = {
			isa = PBXGroup;
			children = (
				9AF5C3F52C8B053B000EF027 /* SAGetRepayBillListApi.h */,
				9AF5C3F62C8B053B000EF027 /* SAGetRepayBillListApi.m */,
				9AF5C3F82C8B05CA000EF027 /* SAGetPaidBillListApi.h */,
				9AF5C3F92C8B05CA000EF027 /* SAGetPaidBillListApi.m */,
				9AA557592C8B309300F902A5 /* SAGetBillDetailApi.h */,
				9AA5575A2C8B309300F902A5 /* SAGetBillDetailApi.m */,
				9A9FFDA22CCB4ED300DF3EE9 /* SAGetCashierInfoAPi.h */,
				9A9FFDA32CCB4ED300DF3EE9 /* SAGetCashierInfoAPi.m */,
				9A9FFDA52CCB4F8100DF3EE9 /* SACashierDoPayApi.h */,
				9A9FFDA62CCB4F8100DF3EE9 /* SACashierDoPayApi.m */,
				9A9FFDA82CCB513A00DF3EE9 /* SACashierPayStatusApi.h */,
				9A9FFDA92CCB513A00DF3EE9 /* SACashierPayStatusApi.m */,
			);
			path = Api;
			sourceTree = "<group>";
		};
		9AF5C3F12C8B0239000EF027 /* Model */ = {
			isa = PBXGroup;
			children = (
				9AF5C3F22C8B0253000EF027 /* SARepayListModel.h */,
				9AF5C3F32C8B0253000EF027 /* SARepayListModel.m */,
				9A48D7162CCBB5F000A3B04F /* SARepayCashierModel.h */,
				9A48D7172CCBB5F000A3B04F /* SARepayCashierModel.m */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		9AF5C3FB2C8B0B77000EF027 /* DLSlideView */ = {
			isa = PBXGroup;
			children = (
				9AF5C3FC2C8B0B77000EF027 /* SATabedSlideView.h */,
				9AF5C3FD2C8B0B77000EF027 /* DLCache */,
				9AF5C4012C8B0B77000EF027 /* SAUtility.h */,
				9AF5C4022C8B0B77000EF027 /* SASlideView.h */,
				9AF5C4032C8B0B77000EF027 /* SACustomSlideView.h */,
				9AF5C4042C8B0B77000EF027 /* SATabedSlideView.m */,
				9AF5C4052C8B0B77000EF027 /* DLTabbarView */,
				9AF5C40B2C8B0B77000EF027 /* SAUtility.m */,
				9AF5C40C2C8B0B77000EF027 /* SACustomSlideView.m */,
				9AF5C40D2C8B0B77000EF027 /* SASlideView.m */,
			);
			path = DLSlideView;
			sourceTree = "<group>";
		};
		9AF5C3FD2C8B0B77000EF027 /* DLCache */ = {
			isa = PBXGroup;
			children = (
				9AF5C3FE2C8B0B77000EF027 /* SAJQCache.m */,
				9AF5C3FF2C8B0B77000EF027 /* DLCacheProtocol.h */,
				9AF5C4002C8B0B77000EF027 /* SAJQCache.h */,
			);
			path = DLCache;
			sourceTree = "<group>";
		};
		9AF5C4052C8B0B77000EF027 /* DLTabbarView */ = {
			isa = PBXGroup;
			children = (
				9AF5C4062C8B0B77000EF027 /* DLSlideTabbarProtocol.h */,
				9AF5C4072C8B0B77000EF027 /* SAScrollTabbarView.h */,
				9AF5C4082C8B0B77000EF027 /* SAFixedTabbarView.h */,
				9AF5C4092C8B0B77000EF027 /* SAScrollTabbarView.m */,
				9AF5C40A2C8B0B77000EF027 /* SAFixedTabbarView.m */,
			);
			path = DLTabbarView;
			sourceTree = "<group>";
		};
		9AFB98E02220053F00F294D4 /* Net */ = {
			isa = PBXGroup;
			children = (
				0845D91222AE6BB40044AB4D /* NoNetView */,
				9AFB991A222005B400F294D4 /* SABasicService.h */,
				9AFB991B222005B400F294D4 /* SABasicService.m */,
				9AFB9917222005B400F294D4 /* SARequestHandle.h */,
				9AFB9919222005B400F294D4 /* SARequestHandle.m */,
				9AFB9914222005B300F294D4 /* SAResponseHandle.h */,
				9AFB9916222005B400F294D4 /* SAResponseHandle.m */,
				08CE109822A8AA4100E30241 /* SANetworkConst.h */,
				08CE109922A8AA4100E30241 /* SANetworkConst.m */,
				9A2245C72948A3EE00DA1F9D /* NPRequestMethodProtocol.h */,
			);
			path = Net;
			sourceTree = "<group>";
		};
		9AFB99322220082200F294D4 /* extobjc */ = {
			isa = PBXGroup;
			children = (
				9AFB99342220082200F294D4 /* metamacros.h */,
				9AFB99382220082200F294D4 /* EXTKeyPathCoding.h */,
				9AFB99332220082200F294D4 /* SARuntimeExtensions.h */,
				9AFB99362220082200F294D4 /* SARuntimeExtensions.m */,
				9AFB99372220082200F294D4 /* SAScope.h */,
				9AFB99352220082200F294D4 /* SAScope.m */,
			);
			path = extobjc;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		13B07F861A680F5B00A75B9A /* StagingApp */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "StagingApp" */;
			buildPhases = (
				D0DC7500B8ED95B3C3FCC088 /* [CP] Check Pods Manifest.lock */,
				13B07F871A680F5B00A75B9A /* Sources */,
				13B07F8C1A680F5B00A75B9A /* Frameworks */,
				13B07F8E1A680F5B00A75B9A /* Resources */,
				9A344B6528336E8B004AEE1D /* Embed Frameworks */,
				FEFB203B7F7E5D438185354B /* [CP] Embed Pods Frameworks */,
				0F16FAD0C0393AC52F914DEC /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = StagingApp;
			productName = "Hello World";
			productReference = 13B07F961A680F5B00A75B9A /* StagingApp.app */;
			productType = "com.apple.product-type.application";
		};
		9A4C51D02CD8DBAC004BC9CE /* Xiandaihua */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 9A4C52F92CD8DBAC004BC9CE /* Build configuration list for PBXNativeTarget "Xiandaihua" */;
			buildPhases = (
				9A4C51D12CD8DBAC004BC9CE /* [CP] Check Pods Manifest.lock */,
				9A4C51D22CD8DBAC004BC9CE /* Sources */,
				9A4C52D82CD8DBAC004BC9CE /* Frameworks */,
				9A4C52EA2CD8DBAC004BC9CE /* Resources */,
				9A4C52F62CD8DBAC004BC9CE /* Embed Frameworks */,
				9A4C52F72CD8DBAC004BC9CE /* [CP] Embed Pods Frameworks */,
				9A4C52F82CD8DBAC004BC9CE /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = Xiandaihua;
			productName = "Hello World";
			productReference = 9A4C52FC2CD8DBAC004BC9CE /* Xiandaihua.app */;
			productType = "com.apple.product-type.application";
		};
		9AC7DFE22CE595D000944F3B /* Haoxianghua */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 9AC7E10C2CE595D000944F3B /* Build configuration list for PBXNativeTarget "Haoxianghua" */;
			buildPhases = (
				9AC7DFE32CE595D000944F3B /* [CP] Check Pods Manifest.lock */,
				9AC7DFE42CE595D000944F3B /* Sources */,
				9AC7E0EA2CE595D000944F3B /* Frameworks */,
				9AC7E0FC2CE595D000944F3B /* Resources */,
				9AC7E1092CE595D000944F3B /* Embed Frameworks */,
				9AC7E10A2CE595D000944F3B /* [CP] Embed Pods Frameworks */,
				9AC7E10B2CE595D000944F3B /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = Haoxianghua;
			productName = "Hello World";
			productReference = 9AC7E10F2CE595D000944F3B /* Haoxianghua.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		83CBB9F71A601CBA00E9B192 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				CLASSPREFIX = NP;
				LastUpgradeCheck = 1320;
				ORGANIZATIONNAME = Facebook;
				TargetAttributes = {
					13B07F861A680F5B00A75B9A = {
						DevelopmentTeam = 37472BGYG5;
						ProvisioningStyle = Manual;
						SystemCapabilities = {
							com.apple.AccessWiFi = {
								enabled = 1;
							};
						};
					};
					9A4C51D02CD8DBAC004BC9CE = {
						DevelopmentTeam = 37472BGYG5;
						ProvisioningStyle = Manual;
					};
					9AC7DFE22CE595D000944F3B = {
						DevelopmentTeam = 37472BGYG5;
						ProvisioningStyle = Manual;
					};
				};
			};
			buildConfigurationList = 83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "StagingApp" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = "zh-Hans";
			hasScannedForEncodings = 0;
			knownRegions = (
				Base,
				"zh-Hans",
			);
			mainGroup = 83CBB9F61A601CBA00E9B192;
			productRefGroup = 83CBBA001A601CBA00E9B192 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				13B07F861A680F5B00A75B9A /* StagingApp */,
				9A4C51D02CD8DBAC004BC9CE /* Xiandaihua */,
				9AC7DFE22CE595D000944F3B /* Haoxianghua */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		13B07F8E1A680F5B00A75B9A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				9AFDCD842846553000850B41 /* LaunchScreen.storyboard in Resources */,
				9A150B192214092B00840F54 /* Images.xcassets in Resources */,
				9A01F57F2ABD33170024CC0A /* Qdraw.bundle in Resources */,
				9A4BC7132CBD18230051D311 /* EsignSDK.bundle in Resources */,
				9A4C52FF2CD8DBF0004BC9CE /* Xinruihua-Info.plist in Resources */,
				9AC7E1122CE5970B00944F3B /* Haoxianghua-Info.plist in Resources */,
				9AA557992C8D839D00F902A5 /* Panding.bundle in Resources */,
				9AA0904D2856EEAE00E835CB /* InfoPlist.strings in Resources */,
				08CE10F922A8C2FB00E30241 /* Localizable.strings in Resources */,
				9A48D71D2CCBC26400A3B04F /* pay_loading.gif in Resources */,
				9A6480492CC6452900C20F02 /* loading.gif in Resources */,
				9AA557A62C8D839D00F902A5 /* Pointer.bundle in Resources */,
				9AA5579F2C8D839D00F902A5 /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9A4C52EA2CD8DBAC004BC9CE /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				9A4C52EB2CD8DBAC004BC9CE /* LaunchScreen.storyboard in Resources */,
				9A4C52EC2CD8DBAC004BC9CE /* Images.xcassets in Resources */,
				9A4C52ED2CD8DBAC004BC9CE /* Qdraw.bundle in Resources */,
				9A4C52EE2CD8DBAC004BC9CE /* EsignSDK.bundle in Resources */,
				9A4C53002CD8DBF0004BC9CE /* Xinruihua-Info.plist in Resources */,
				9AC7E1132CE5970B00944F3B /* Haoxianghua-Info.plist in Resources */,
				9A4C52EF2CD8DBAC004BC9CE /* Panding.bundle in Resources */,
				9A4C52F02CD8DBAC004BC9CE /* InfoPlist.strings in Resources */,
				9A4C52F12CD8DBAC004BC9CE /* Localizable.strings in Resources */,
				9A4C52F22CD8DBAC004BC9CE /* pay_loading.gif in Resources */,
				9A4C52F32CD8DBAC004BC9CE /* loading.gif in Resources */,
				9A4C52F42CD8DBAC004BC9CE /* Pointer.bundle in Resources */,
				9A4C52F52CD8DBAC004BC9CE /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9AC7E0FC2CE595D000944F3B /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				9AC7E0FD2CE595D000944F3B /* LaunchScreen.storyboard in Resources */,
				9AC7E0FE2CE595D000944F3B /* Images.xcassets in Resources */,
				9AC7E0FF2CE595D000944F3B /* Qdraw.bundle in Resources */,
				9AC7E1002CE595D000944F3B /* EsignSDK.bundle in Resources */,
				9AC7E1012CE595D000944F3B /* Xinruihua-Info.plist in Resources */,
				9AC7E1142CE5970B00944F3B /* Haoxianghua-Info.plist in Resources */,
				9AC7E1022CE595D000944F3B /* Panding.bundle in Resources */,
				9AC7E1032CE595D000944F3B /* InfoPlist.strings in Resources */,
				9AC7E1042CE595D000944F3B /* Localizable.strings in Resources */,
				9AC7E1052CE595D000944F3B /* pay_loading.gif in Resources */,
				9AC7E1062CE595D000944F3B /* loading.gif in Resources */,
				9AC7E1072CE595D000944F3B /* Pointer.bundle in Resources */,
				9AC7E1082CE595D000944F3B /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		0F16FAD0C0393AC52F914DEC /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-StagingApp/Pods-StagingApp-resources.sh",
				"${PODS_ROOT}/../StagingApp/Vendor/TencentCloudHuiyanSDKFace_framework/Resources/face-tracker-v001.bundle",
				"${PODS_ROOT}/../StagingApp/Vendor/TencentCloudHuiyanSDKFace_framework/Resources/TencentCloudHuiyanSDKFace.bundle",
				"${PODS_ROOT}/../StagingApp/Vendor/TencentCloudHuiyanSDKFace_framework/Resources/TencentCloudHuiyanSDKWill.bundle",
			);
			name = "[CP] Copy Pods Resources";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/face-tracker-v001.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/TencentCloudHuiyanSDKFace.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/TencentCloudHuiyanSDKWill.bundle",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-StagingApp/Pods-StagingApp-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		9A4C51D12CD8DBAC004BC9CE /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-StagingApp-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		9A4C52F72CD8DBAC004BC9CE /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-StagingApp/Pods-StagingApp-frameworks.sh",
				"${BUILT_PRODUCTS_DIR}/AFNetworking/AFNetworking.framework",
				"${BUILT_PRODUCTS_DIR}/AWSAuthCore/AWSAuthCore.framework",
				"${BUILT_PRODUCTS_DIR}/AWSCognitoIdentityProvider/AWSCognitoIdentityProvider.framework",
				"${BUILT_PRODUCTS_DIR}/AWSCognitoIdentityProviderASF/AWSCognitoIdentityProviderASF.framework",
				"${BUILT_PRODUCTS_DIR}/AWSCore/AWSCore.framework",
				"${BUILT_PRODUCTS_DIR}/AWSMobileClient/AWSMobileClient.framework",
				"${BUILT_PRODUCTS_DIR}/AWSPinpoint/AWSPinpoint.framework",
				"${BUILT_PRODUCTS_DIR}/AWSS3/AWSS3.framework",
				"${BUILT_PRODUCTS_DIR}/AliyunOSSiOS/AliyunOSSiOS.framework",
				"${BUILT_PRODUCTS_DIR}/CRBoxInputView/CRBoxInputView.framework",
				"${BUILT_PRODUCTS_DIR}/HandyFrame/HandyFrame.framework",
				"${BUILT_PRODUCTS_DIR}/IQKeyboardManager/IQKeyboardManager.framework",
				"${BUILT_PRODUCTS_DIR}/MBProgressHUD/MBProgressHUD.framework",
				"${BUILT_PRODUCTS_DIR}/MJExtension/MJExtension.framework",
				"${BUILT_PRODUCTS_DIR}/Masonry/Masonry.framework",
				"${BUILT_PRODUCTS_DIR}/Reachability/Reachability.framework",
				"${BUILT_PRODUCTS_DIR}/SDWebImage/SDWebImage.framework",
				"${BUILT_PRODUCTS_DIR}/TYCyclePagerView/TYCyclePagerView.framework",
				"${BUILT_PRODUCTS_DIR}/WebViewJavascriptBridge/WebViewJavascriptBridge.framework",
				"${BUILT_PRODUCTS_DIR}/YYCache/YYCache.framework",
				"${BUILT_PRODUCTS_DIR}/YYModel/YYModel.framework",
			);
			name = "[CP] Embed Pods Frameworks";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/AFNetworking.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/AWSAuthCore.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/AWSCognitoIdentityProvider.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/AWSCognitoIdentityProviderASF.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/AWSCore.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/AWSMobileClient.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/AWSPinpoint.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/AWSS3.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/AliyunOSSiOS.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/CRBoxInputView.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/HandyFrame.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/IQKeyboardManager.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/MBProgressHUD.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/MJExtension.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/Masonry.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/Reachability.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/SDWebImage.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/TYCyclePagerView.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/WebViewJavascriptBridge.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/YYCache.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/YYModel.framework",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-StagingApp/Pods-StagingApp-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		9A4C52F82CD8DBAC004BC9CE /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-StagingApp/Pods-StagingApp-resources.sh",
				"${PODS_ROOT}/../StagingApp/Vendor/TencentCloudHuiyanSDKFace_framework/Resources/face-tracker-v001.bundle",
				"${PODS_ROOT}/../StagingApp/Vendor/TencentCloudHuiyanSDKFace_framework/Resources/TencentCloudHuiyanSDKFace.bundle",
				"${PODS_ROOT}/../StagingApp/Vendor/TencentCloudHuiyanSDKFace_framework/Resources/TencentCloudHuiyanSDKWill.bundle",
			);
			name = "[CP] Copy Pods Resources";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/face-tracker-v001.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/TencentCloudHuiyanSDKFace.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/TencentCloudHuiyanSDKWill.bundle",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-StagingApp/Pods-StagingApp-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		9AC7DFE32CE595D000944F3B /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-StagingApp-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		9AC7E10A2CE595D000944F3B /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-StagingApp/Pods-StagingApp-frameworks.sh",
				"${BUILT_PRODUCTS_DIR}/AFNetworking/AFNetworking.framework",
				"${BUILT_PRODUCTS_DIR}/AWSAuthCore/AWSAuthCore.framework",
				"${BUILT_PRODUCTS_DIR}/AWSCognitoIdentityProvider/AWSCognitoIdentityProvider.framework",
				"${BUILT_PRODUCTS_DIR}/AWSCognitoIdentityProviderASF/AWSCognitoIdentityProviderASF.framework",
				"${BUILT_PRODUCTS_DIR}/AWSCore/AWSCore.framework",
				"${BUILT_PRODUCTS_DIR}/AWSMobileClient/AWSMobileClient.framework",
				"${BUILT_PRODUCTS_DIR}/AWSPinpoint/AWSPinpoint.framework",
				"${BUILT_PRODUCTS_DIR}/AWSS3/AWSS3.framework",
				"${BUILT_PRODUCTS_DIR}/AliyunOSSiOS/AliyunOSSiOS.framework",
				"${BUILT_PRODUCTS_DIR}/CRBoxInputView/CRBoxInputView.framework",
				"${BUILT_PRODUCTS_DIR}/HandyFrame/HandyFrame.framework",
				"${BUILT_PRODUCTS_DIR}/IQKeyboardManager/IQKeyboardManager.framework",
				"${BUILT_PRODUCTS_DIR}/MBProgressHUD/MBProgressHUD.framework",
				"${BUILT_PRODUCTS_DIR}/MJExtension/MJExtension.framework",
				"${BUILT_PRODUCTS_DIR}/Masonry/Masonry.framework",
				"${BUILT_PRODUCTS_DIR}/Reachability/Reachability.framework",
				"${BUILT_PRODUCTS_DIR}/SDWebImage/SDWebImage.framework",
				"${BUILT_PRODUCTS_DIR}/TYCyclePagerView/TYCyclePagerView.framework",
				"${BUILT_PRODUCTS_DIR}/WebViewJavascriptBridge/WebViewJavascriptBridge.framework",
				"${BUILT_PRODUCTS_DIR}/YYCache/YYCache.framework",
				"${BUILT_PRODUCTS_DIR}/YYModel/YYModel.framework",
			);
			name = "[CP] Embed Pods Frameworks";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/AFNetworking.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/AWSAuthCore.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/AWSCognitoIdentityProvider.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/AWSCognitoIdentityProviderASF.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/AWSCore.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/AWSMobileClient.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/AWSPinpoint.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/AWSS3.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/AliyunOSSiOS.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/CRBoxInputView.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/HandyFrame.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/IQKeyboardManager.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/MBProgressHUD.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/MJExtension.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/Masonry.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/Reachability.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/SDWebImage.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/TYCyclePagerView.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/WebViewJavascriptBridge.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/YYCache.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/YYModel.framework",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-StagingApp/Pods-StagingApp-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		9AC7E10B2CE595D000944F3B /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-StagingApp/Pods-StagingApp-resources.sh",
				"${PODS_ROOT}/../StagingApp/Vendor/TencentCloudHuiyanSDKFace_framework/Resources/face-tracker-v001.bundle",
				"${PODS_ROOT}/../StagingApp/Vendor/TencentCloudHuiyanSDKFace_framework/Resources/TencentCloudHuiyanSDKFace.bundle",
				"${PODS_ROOT}/../StagingApp/Vendor/TencentCloudHuiyanSDKFace_framework/Resources/TencentCloudHuiyanSDKWill.bundle",
			);
			name = "[CP] Copy Pods Resources";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/face-tracker-v001.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/TencentCloudHuiyanSDKFace.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/TencentCloudHuiyanSDKWill.bundle",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-StagingApp/Pods-StagingApp-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		D0DC7500B8ED95B3C3FCC088 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-StagingApp-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		FEFB203B7F7E5D438185354B /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-StagingApp/Pods-StagingApp-frameworks.sh",
				"${BUILT_PRODUCTS_DIR}/AFNetworking/AFNetworking.framework",
				"${BUILT_PRODUCTS_DIR}/AWSAuthCore/AWSAuthCore.framework",
				"${BUILT_PRODUCTS_DIR}/AWSCognitoIdentityProvider/AWSCognitoIdentityProvider.framework",
				"${BUILT_PRODUCTS_DIR}/AWSCognitoIdentityProviderASF/AWSCognitoIdentityProviderASF.framework",
				"${BUILT_PRODUCTS_DIR}/AWSCore/AWSCore.framework",
				"${BUILT_PRODUCTS_DIR}/AWSMobileClient/AWSMobileClient.framework",
				"${BUILT_PRODUCTS_DIR}/AWSPinpoint/AWSPinpoint.framework",
				"${BUILT_PRODUCTS_DIR}/AWSS3/AWSS3.framework",
				"${BUILT_PRODUCTS_DIR}/AliyunOSSiOS/AliyunOSSiOS.framework",
				"${BUILT_PRODUCTS_DIR}/CRBoxInputView/CRBoxInputView.framework",
				"${BUILT_PRODUCTS_DIR}/HandyFrame/HandyFrame.framework",
				"${BUILT_PRODUCTS_DIR}/IQKeyboardManager/IQKeyboardManager.framework",
				"${BUILT_PRODUCTS_DIR}/MBProgressHUD/MBProgressHUD.framework",
				"${BUILT_PRODUCTS_DIR}/MJExtension/MJExtension.framework",
				"${BUILT_PRODUCTS_DIR}/Masonry/Masonry.framework",
				"${BUILT_PRODUCTS_DIR}/Reachability/Reachability.framework",
				"${BUILT_PRODUCTS_DIR}/SDWebImage/SDWebImage.framework",
				"${BUILT_PRODUCTS_DIR}/TYCyclePagerView/TYCyclePagerView.framework",
				"${BUILT_PRODUCTS_DIR}/WebViewJavascriptBridge/WebViewJavascriptBridge.framework",
				"${BUILT_PRODUCTS_DIR}/YYCache/YYCache.framework",
				"${BUILT_PRODUCTS_DIR}/YYModel/YYModel.framework",
			);
			name = "[CP] Embed Pods Frameworks";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/AFNetworking.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/AWSAuthCore.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/AWSCognitoIdentityProvider.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/AWSCognitoIdentityProviderASF.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/AWSCore.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/AWSMobileClient.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/AWSPinpoint.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/AWSS3.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/AliyunOSSiOS.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/CRBoxInputView.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/HandyFrame.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/IQKeyboardManager.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/MBProgressHUD.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/MJExtension.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/Masonry.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/Reachability.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/SDWebImage.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/TYCyclePagerView.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/WebViewJavascriptBridge.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/YYCache.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/YYModel.framework",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-StagingApp/Pods-StagingApp-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		13B07F871A680F5B00A75B9A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				9A150B3C2214092B00840F54 /* SAService.m in Sources */,
				9A2506F82AD54F08008C1068 /* SAFileUploadApiManager.m in Sources */,
				08FF04B722AF3EC500DF17E6 /* SAMainHomeApiManager.m in Sources */,
				9A150B302214092B00840F54 /* SAResponse.m in Sources */,
				0845D98722AE6D680044AB4D /* SADictionary+Common.m in Sources */,
				9ACA666B2AC1345F00D6250B /* SAView+MASAdditions.m in Sources */,
				0845D98422AE6D680044AB4D /* SACommonTool.m in Sources */,
				9AE3D2082CCA488C008C0D58 /* NPInviteAlertViewController.m in Sources */,
				08D0A8A122C31B9700D68951 /* SAVersionUpdateViewController.m in Sources */,
				0845D98622AE6D680044AB4D /* SAMutableDictionary+Extension.m in Sources */,
				9ACF14E82C8DB6200057A6D1 /* SAMyCardsListViewController.m in Sources */,
				0845D98D22AE6D680044AB4D /* SADate+Extension.m in Sources */,
				9AFB991F222005B400F294D4 /* SARequestHandle.m in Sources */,
				08FF051122AF539700DF17E6 /* SAHomeProtocolUrlsModel.m in Sources */,
				9AF5C4112C8B0B77000EF027 /* SAFixedTabbarView.m in Sources */,
				9A2506E02AD4F318008C1068 /* SAFaceSubmitApiManager.m in Sources */,
				0805C2F122B2570800021D25 /* SAGetPersonalInfoApiManager.m in Sources */,
				9A01F5892ABD33170024CC0A /* SARefreshStateTrailer.m in Sources */,
				9ACA666D2AC1345F00D6250B /* SAViewConstraint.m in Sources */,
				08FF05B322AFAAE300DF17E6 /* SAMainTopBgView.m in Sources */,
				0856E5C622B279CB00D5C183 /* SAVerifyInfoCell.m in Sources */,
				9A01F5932ABD33170024CC0A /* SARefreshTrailer.m in Sources */,
				9AF5C4172C8B113B000EF027 /* SARepayListVC.m in Sources */,
				9A16888F2C8ED0E800A98C9B /* SATextView.m in Sources */,
				0805C2F422B25B2000021D25 /* SAPersonalInfoModel.m in Sources */,
				08B1ECC122B8CEC5001367BB /* SAPhotoManager.m in Sources */,
				08CE109A22A8AA4100E30241 /* SANetworkConst.m in Sources */,
				9A2506DD2AD41375008C1068 /* SAGetOcrConfigApiManager.m in Sources */,
				9AA557A12C8D839D00F902A5 /* SATextPickerView.m in Sources */,
				9A01F57D2ABD33170024CC0A /* SARefreshConst.m in Sources */,
				9A2506F22AD54AA5008C1068 /* SANLUploadManager.m in Sources */,
				9A01F5832ABD33170024CC0A /* SARefreshBackGifFooter.m in Sources */,
				9A01F5842ABD33170024CC0A /* SARefreshBackStateFooter.m in Sources */,
				9A0A39642A7A02EC0095AB86 /* SASmsCodeViewController.m in Sources */,
				9A35DE6F283F1AFA00C387B7 /* SAVerifyIdentityViewController.m in Sources */,
				9AA557A02C8D839D00F902A5 /* SATextModel.m in Sources */,
				9AF5C41A2C8B1155000EF027 /* SAPaidListVC.m in Sources */,
				9AA557982C8D839D00F902A5 /* SAPickerAlertView.m in Sources */,
				9A150B372214092B00840F54 /* SAObject+QLNetworkingMethods.m in Sources */,
				08FF052022AF5CCF00DF17E6 /* SABorrowCashPeriodModel.m in Sources */,
				0805C2FA22B25C5B00021D25 /* SAPersonalInfoCheckModel.m in Sources */,
				9AA5576D2C8D4D5C00F902A5 /* SAGetEsignUrlApi.m in Sources */,
				9A9FFDA12CCB4CB400DF3EE9 /* NPOnlineRepayViewController.m in Sources */,
				0845D8CD22AE6ACF0044AB4D /* SABasicViewController.m in Sources */,
				9A150B322214092B00840F54 /* SAApiProxy.m in Sources */,
				9A01F53E2ABC2FC10024CC0A /* SAObjC.m in Sources */,
				08FF054222AF777900DF17E6 /* SAImage+Extension.m in Sources */,
				082B160822BB871600A4C7C7 /* SAPayClientInfoApiManager.m in Sources */,
				9A35DE0B283E046D00C387B7 /* SAString+Size.m in Sources */,
				9AF5C4132C8B0B77000EF027 /* SACustomSlideView.m in Sources */,
				9A35DE93283FA9D400C387B7 /* SAHomeNormalCell.m in Sources */,
				0845D99122AE6D680044AB4D /* SABCTool.m in Sources */,
				0845D98A22AE6D680044AB4D /* SAView+Extension.m in Sources */,
				08B1EC4922B8815A001367BB /* SAShapeLayer+Extension.m in Sources */,
				9A150B392214092B00840F54 /* SAString+QLNetworkingMethods.m in Sources */,
				082B160B22BB8CCA00A4C7C7 /* SAPayWayModel.m in Sources */,
				9AEC08832C89A597008475BD /* SASaveBindCardInfoApi.m in Sources */,
				9AA557612C8D4B7F00F902A5 /* SAConfrimTradeViewController.m in Sources */,
				9ACF14E22C8D93DD0057A6D1 /* SAConfirmSmsAlertView.m in Sources */,
				08B4A93E22B79A0D006B35D4 /* SAContactsManager.m in Sources */,
				9A01F58D2ABD33170024CC0A /* SARefreshGifHeader.m in Sources */,
				9A150B312214092B00840F54 /* SABaseManager.m in Sources */,
				08FF4C2122B604390095863C /* SAPickerTextField.m in Sources */,
				9A150B2E2214092B00840F54 /* SANetworkingConfigurationManager.m in Sources */,
				9A01F58E2ABD33170024CC0A /* SARefreshFooter.m in Sources */,
				9A9FFDA42CCB4ED300DF3EE9 /* SAGetCashierInfoAPi.m in Sources */,
				9A64804C2CC6492900C20F02 /* SACustomHudImg.m in Sources */,
				9AEC08922C89DAA0008475BD /* SASettingViewController.m in Sources */,
				08FF4C5F22B61CF20095863C /* SAGetContactInfoApiManager.m in Sources */,
				9AA5576A2C8D4D0F00F902A5 /* SASubmitTradeApi.m in Sources */,
				9A1702542C8866CB00FD811E /* SAProtocolViewController.m in Sources */,
				9AF5C3E22C8AE656000EF027 /* SAServiceView.m in Sources */,
				0845D8CE22AE6ACF0044AB4D /* SATabBarViewController.m in Sources */,
				9A818DD82BF5A721008DF462 /* SACycleVerticalView.m in Sources */,
				08FF051D22AF5C7700DF17E6 /* SABorrowCashModel.m in Sources */,
				0845D94A22AE6C880044AB4D /* SAHomePageViewController.m in Sources */,
				9A2506E92AD53FA3008C1068 /* SAOfflineRepayViewController.m in Sources */,
				9A021B1E2AB8622F007EC584 /* SAGetCitiesListApiManager.m in Sources */,
				9A6E92762BF5BDDD003D679C /* SAMarqueeView.m in Sources */,
				9A02DF552CC656C000431F1B /* SABannerModel.m in Sources */,
				9AEC08772C899B1E008475BD /* SABankItemView.m in Sources */,
				08FF056F22AF9B4400DF17E6 /* SAHomeBasicCell.m in Sources */,
				0845D98B22AE6D680044AB4D /* SAFont+Extension.m in Sources */,
				9AEC08742C898F4D008475BD /* SAVerifyBankCardViewController.m in Sources */,
				9AA557A72C8D839D00F902A5 /* Pointer.m in Sources */,
				9ACA666E2AC1345F00D6250B /* SAViewAttribute.m in Sources */,
				9A01F5822ABD33170024CC0A /* SAView+MJExtension.m in Sources */,
				08D14F5B22BCCB4C0094582F /* SAAlertSureTipsViewController.m in Sources */,
				0845D98922AE6D680044AB4D /* SAAlertController+Orientation.m in Sources */,
				9AFB991E222005B400F294D4 /* SAResponseHandle.m in Sources */,
				08CE116322A8F73F00E30241 /* SAAppCodeManager.m in Sources */,
				9A150B3D2214092B00840F54 /* SAServiceFactory.m in Sources */,
				9A01F5802ABD33170024CC0A /* SAScrollView+MJExtension.m in Sources */,
				08E66BD022B4F7C7005E62D9 /* SABankListModel.m in Sources */,
				9AEC088C2C89BD26008475BD /* SABankBinListViewController.m in Sources */,
				9A02DF5B2CC65D2600431F1B /* SABannerCell.m in Sources */,
				9A02DF5E2CC66F0100431F1B /* SAPaidCell.m in Sources */,
				9AA5579A2C8D839D00F902A5 /* SAPickerStyle.m in Sources */,
				9AF5C3EB2C8B0193000EF027 /* SARepaymentViewController.m in Sources */,
				08CE116622A8F79400E30241 /* SAAppConfigApiManager.m in Sources */,
				9A35DE90283F95BA00C387B7 /* SACompareUploadManager.m in Sources */,
				9A01F5912ABD33170024CC0A /* SARefreshAutoFooter.m in Sources */,
				9ACA66672AC1345F00D6250B /* SACompositeConstraint.m in Sources */,
				9AA557A22C8D839D00F902A5 /* SAResultModel.m in Sources */,
				9A150B362214092B00840F54 /* SARequest+QLNetworkingMethods.m in Sources */,
				9AA5575E2C8D4AE700F902A5 /* SAHomeConfirmCell.m in Sources */,
				0845D98E22AE6D680044AB4D /* SALocationManager.m in Sources */,
				0845D95422AE6D1D0044AB4D /* SALoginViewController.m in Sources */,
				9AA557A32C8D839D00F902A5 /* SAStringPickerView.m in Sources */,
				08FF051422AF53CF00DF17E6 /* SAHomeProtocolModel.m in Sources */,
				9A150B3B2214092B00840F54 /* SAMutableString+QLNetworkingMethods.m in Sources */,
				9A150B352214092B00840F54 /* SARequestGenerator.m in Sources */,
				0805C28C22B1F6F900021D25 /* SAVerifyListItemModel.m in Sources */,
				9A1702572C8964D100FD811E /* SAGetProtocolApiManager.m in Sources */,
				9A42882D2844B83F000FE25F /* SAData+Compress.m in Sources */,
				9AA557672C8D4CC200F902A5 /* SAGetTradeDetailApi.m in Sources */,
				9A818DDB2BF5A721008DF462 /* SACycleView.m in Sources */,
				9ACA66682AC1345F00D6250B /* SAConstraintMaker.m in Sources */,
				9AA5579D2C8D839D00F902A5 /* SADate+BRPickerView.m in Sources */,
				9A4288292844A4E9000FE25F /* SAUploadInfoManager.m in Sources */,
				08F4C18E22C078330000B366 /* SAManager.m in Sources */,
				9A150B2F2214092B00840F54 /* SALogger.m in Sources */,
				0845D99022AE6D680044AB4D /* SADeviceTool.m in Sources */,
				9A150B2D2214092B00840F54 /* SALoggerConfiguration.m in Sources */,
				08EC05D32315245200D3C93C /* SAHistoryOrderModel.m in Sources */,
				0878035222B110B8000A7A4F /* SAVerifyCodeInputView.m in Sources */,
				9AFB99392220082200F294D4 /* SAScope.m in Sources */,
				08FF04EF22AF424E00DF17E6 /* SAMobileLoginApiManager.m in Sources */,
				08FF051A22AF5BE500DF17E6 /* SABorrowPurposeTypeModel.m in Sources */,
				9A35DE87283F46EF00C387B7 /* SAUploadManager.m in Sources */,
				9A2506E32AD506B4008C1068 /* SAGetCarrierUrlApiManager.m in Sources */,
				9A01F58C2ABD33170024CC0A /* SARefreshStateHeader.m in Sources */,
				0845D91522AE6BB40044AB4D /* SANoNetView.m in Sources */,
				9A35DE6C283E38C400C387B7 /* SAAdvanceManager.m in Sources */,
				9A35DE08283E03C400C387B7 /* SAToast+UIView.m in Sources */,
				0878033622B0E898000A7A4F /* SAUserCenterApiManager.m in Sources */,
				9ACF14F42C8DBC5F0057A6D1 /* SAMyBankItemModel.m in Sources */,
				08FF4C5922B61C010095863C /* SASavePersonalInfoApiManager.m in Sources */,
				08A6BAED22B1E67F001CA577 /* SADeviceInfoSaveApiManager.m in Sources */,
				9AA557A52C8D839D00F902A5 /* SAAddressModel.m in Sources */,
				9A150B182214092B00840F54 /* main.m in Sources */,
				9ABE6D232CBF9F3A003B5100 /* SAVerifyContactsViewController.m in Sources */,
				0880D0C222BB2357001C979F /* SAAuditProgressItemModel.m in Sources */,
				9A2506EF2AD54A97008C1068 /* SAUploadModel.m in Sources */,
				9A15F9772840B5EA00D79C39 /* SAHomeReviewingCell.m in Sources */,
				9AA557A42C8D839D00F902A5 /* SABaseView.m in Sources */,
				9AEC087D2C89A136008475BD /* SABindCardResendSmsApi.m in Sources */,
				083AB35422B36DEA003B4BB4 /* SAPersonalVerifyModel.m in Sources */,
				9AF5C40F2C8B0B77000EF027 /* SATabedSlideView.m in Sources */,
				9A2533072C902CA90093728D /* SARepayH5ViewController.m in Sources */,
				9A2245D3294AED5400DA1F9D /* SACommonNoDataView.m in Sources */,
				08FF051722AF5A2F00DF17E6 /* SAOrderInfoModel.m in Sources */,
				08F4C12322C063380000B366 /* SASaveGPSApiManager.m in Sources */,
				9AA557702C8D52D300F902A5 /* SATradeDetailCell.m in Sources */,
				08D14F1022BC7E1E0094582F /* SAClientChargesModel.m in Sources */,
				9AB09A73284276F7005C55AB /* SAGetRegManager.m in Sources */,
				9A48D71B2CCBB80500A3B04F /* NPCashierCardCell.m in Sources */,
				9A01F57E2ABD33170024CC0A /* SARefreshConfig.m in Sources */,
				9AF5C4102C8B0B77000EF027 /* SAScrollTabbarView.m in Sources */,
				0811F47222CB41D000CDBBD1 /* SAErrorView.m in Sources */,
				9A01F5862ABD33170024CC0A /* SARefreshAutoStateFooter.m in Sources */,
				9ACA66652AC1345E00D6250B /* SALayoutConstraint+MASDebugAdditions.m in Sources */,
				9A01F58B2ABD33170024CC0A /* SARefreshNormalHeader.m in Sources */,
				08FF4C6C22B623870095863C /* SAContactSelectModel.m in Sources */,
				08FF4C7522B626580095863C /* SAContactRowModel.m in Sources */,
				9A2245AF2941D07A00DA1F9D /* SALoanRecordModel.m in Sources */,
				9AF5C3F42C8B0253000EF027 /* SARepayListModel.m in Sources */,
				9ACF14EB2C8DB6350057A6D1 /* SAConfirmmBankViewController.m in Sources */,
				0878035522B138E9000A7A4F /* SAUserCenterModel.m in Sources */,
				9A01F5852ABD33170024CC0A /* SARefreshBackNormalFooter.m in Sources */,
				9AEC088F2C89D150008475BD /* SAQueryCardBinApi.m in Sources */,
				0805C2A922B24A7800021D25 /* SAVerifyListManager.m in Sources */,
				08AB288D22BF606900CAE59C /* SAWifiManager.m in Sources */,
				9ABB8E1522141F1B00FBF613 /* SAMainManager.m in Sources */,
				9A1688922C8EFA3000A98C9B /* SAGetBankRealNameApi.m in Sources */,
				9ACF14F72C8DC04C0057A6D1 /* SAGetBorrowAgainApi.m in Sources */,
				9A0951302CBE076C0053D586 /* SAFaceBackAlert.m in Sources */,
				9ABE6D202CBF9EB8003B5100 /* SAContactItemCell.m in Sources */,
				9A01F57C2ABD33170024CC0A /* SACollectionViewLayout+MJRefresh.m in Sources */,
				9A33876E2CCA64740056585A /* NPHomeStepModel.m in Sources */,
				9A150B3A2214092B00840F54 /* SADictionary+QLNetworkingMethods.m in Sources */,
				9AFB993A2220082200F294D4 /* SARuntimeExtensions.m in Sources */,
				083D64DA2313E2E400810B26 /* SASubmitProductInfoModel.m in Sources */,
				9AF5C3EE2C8B0212000EF027 /* SARepaymentCell.m in Sources */,
				9ACA666A2AC1345F00D6250B /* SAArray+MASAdditions.m in Sources */,
				9AFB9920222005B400F294D4 /* SABasicService.m in Sources */,
				08B1ED2422B8DDCE001367BB /* SAAliyunOSSManager.m in Sources */,
				0878030122B0967D000A7A4F /* SAGradientView.m in Sources */,
				9AA5579B2C8D839D00F902A5 /* SABundle+BRPickerView.m in Sources */,
				9A2245A929418F5F00DA1F9D /* SAProductModel.m in Sources */,
				9AEC087A2C89A11D008475BD /* SABindCardSendCodeApi.m in Sources */,
				0845D98322AE6D680044AB4D /* SATool.m in Sources */,
				08FF056022AF923600DF17E6 /* SAHomeRejectCell.m in Sources */,
				9AEC08892C89BCCF008475BD /* SABankNameModel.m in Sources */,
				9AF5C4122C8B0B77000EF027 /* SAUtility.m in Sources */,
				9AA5575B2C8B309300F902A5 /* SAGetBillDetailApi.m in Sources */,
				0845D98C22AE6D680044AB4D /* SAString+Extension.m in Sources */,
				9AF5C40E2C8B0B77000EF027 /* SAJQCache.m in Sources */,
				9ACF14F12C8DB71E0057A6D1 /* SACardListCell.m in Sources */,
				9A2506FB2AD656E4008C1068 /* SAS3Manager.m in Sources */,
				9A02DF582CC6593400431F1B /* SABannerView.m in Sources */,
				9A35DE8A283F84D700C387B7 /* SASubmitManager.m in Sources */,
				08FF4C7222B626420095863C /* SAContactSectionModel.m in Sources */,
				0856E5C322B2793C00D5C183 /* SACenterYTextView.m in Sources */,
				9AA5579E2C8D839D00F902A5 /* SADatePickerView.m in Sources */,
				0845D94C22AE6C880044AB4D /* SAMineViewController.m in Sources */,
				082B162422BBAECA00A4C7C7 /* SAPayClientChargesApiManager.m in Sources */,
				9ACA66692AC1345F00D6250B /* SALayoutConstraint.m in Sources */,
				9A01F5872ABD33170024CC0A /* SARefreshAutoGifFooter.m in Sources */,
				9A35DE4B283E1F8800C387B7 /* SAGetLiveConfigManager.m in Sources */,
				9A9FFDAA2CCB513A00DF3EE9 /* SACashierPayStatusApi.m in Sources */,
				9A15F9A52840E22D00D79C39 /* SAHomeBankModel.m in Sources */,
				9A150B382214092B00840F54 /* SAArray+QLNetworkingMethods.m in Sources */,
				9AD150A52C8AA0080033A9D9 /* SAImage+ChangeColor.m in Sources */,
				9A01F5882ABD33170024CC0A /* SARefreshAutoNormalFooter.m in Sources */,
				9A150B162214092B00840F54 /* AppDelegate.m in Sources */,
				9AF5C3FA2C8B05CA000EF027 /* SAGetPaidBillListApi.m in Sources */,
				0878034E22B10C35000A7A4F /* SAVerifyCodeApiManager.m in Sources */,
				9A33876B2CCA5AE90056585A /* SAHomeLoaningCell.m in Sources */,
				08FF04F222AF432800DF17E6 /* SALogoutApiManager.m in Sources */,
				9ACF14E52C8DB42A0057A6D1 /* SAGetSupportBankListApi.m in Sources */,
				9A01F5922ABD33170024CC0A /* SARefreshComponent.m in Sources */,
				0845D99222AE6D680044AB4D /* SAKeyChainTool.m in Sources */,
				08F5A3F7231517BF00D75C55 /* SAOrderConfirmApiManager.m in Sources */,
				9A01F5902ABD33170024CC0A /* SARefreshBackFooter.m in Sources */,
				9A2506F52AD54D43008C1068 /* SAGetFileTokenApiManager.m in Sources */,
				9AF5C4142C8B0B77000EF027 /* SASlideView.m in Sources */,
				9AEC08862C89A679008475BD /* SABindCardInfoModel.m in Sources */,
				08FF4C6222B61D040095863C /* SASaveContactInfoApiManager.m in Sources */,
				0805C28622B1F54600021D25 /* SAVerifyDataListApiManager.m in Sources */,
				9A01F58A2ABD33170024CC0A /* SARefreshNormalTrailer.m in Sources */,
				08B1ED1522B8DC14001367BB /* SATokenApiManager.m in Sources */,
				9ACF14EE2C8DB6550057A6D1 /* SAGetMyCardsApi.m in Sources */,
				9A01F5812ABD33170024CC0A /* SABundle+MJRefresh.m in Sources */,
				9A6E92802BF5E86C003D679C /* SAGetCustomerUrlApiManager.m in Sources */,
				0845D98822AE6D680044AB4D /* SAColor+Extension.m in Sources */,
				08CE115922A8E7A300E30241 /* SAUserManager.m in Sources */,
				9A9FFDA72CCB4F8100DF3EE9 /* SACashierDoPayApi.m in Sources */,
				08B1EC6322B8B46C001367BB /* SAActionSheet.m in Sources */,
				9A2506EC2AD53FD6008C1068 /* SAOfflineRepayInfoApiManager.m in Sources */,
				9A150B332214092B00840F54 /* SACache.m in Sources */,
				9A2245AC2941BAC400DA1F9D /* SAOrderDetailViewController.m in Sources */,
				9AF5C3F72C8B053B000EF027 /* SAGetRepayBillListApi.m in Sources */,
				9ACA66662AC1345F00D6250B /* SAViewController+MASAdditions.m in Sources */,
				0805C2D922B24DF900021D25 /* SAVerifyBasicInfoViewController.m in Sources */,
				0845D98522AE6D680044AB4D /* SANormalRefresh.m in Sources */,
				08B1EC4C22B88754001367BB /* SACommonWebViewController.m in Sources */,
				08FF04FB22AF455E00DF17E6 /* SAHomeModel.m in Sources */,
				9A01F57B2ABD33170024CC0A /* SAScrollView+MJRefresh.m in Sources */,
				9A150B342214092B00840F54 /* SACachedObject.m in Sources */,
				9ACA666C2AC1345F00D6250B /* SAConstraint.m in Sources */,
				08D14F3B22BC7E970094582F /* SARepaymentModel.m in Sources */,
				9A01F58F2ABD33170024CC0A /* SARefreshHeader.m in Sources */,
				9AA5579C2C8D839D00F902A5 /* SADatePickerView+BR.m in Sources */,
				9A48D7182CCBB5F000A3B04F /* SARepayCashierModel.m in Sources */,
				089A56BA22E9B3FC005BEBD5 /* SASaveDeviceChannelApiManager.m in Sources */,
				9A48D7122CCBA59500A3B04F /* SAPayStatusViewController.m in Sources */,
				9A3387682CCA562D0056585A /* NPTradeAlertViewController.m in Sources */,
				0845D8CF22AE6ACF0044AB4D /* SANavigationViewController.m in Sources */,
				0805C28922B1F6DD00021D25 /* SAVerifyListModel.m in Sources */,
				9A3BA6D5285ACE1C0041A741 /* SAOrderConfigApiManager.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9A4C51D22CD8DBAC004BC9CE /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				9A4C51D32CD8DBAC004BC9CE /* SAService.m in Sources */,
				9A4C51D42CD8DBAC004BC9CE /* SAFileUploadApiManager.m in Sources */,
				9A4C51D52CD8DBAC004BC9CE /* SAMainHomeApiManager.m in Sources */,
				9A4C51D62CD8DBAC004BC9CE /* SAResponse.m in Sources */,
				9A4C51D72CD8DBAC004BC9CE /* SADictionary+Common.m in Sources */,
				9A4C51D82CD8DBAC004BC9CE /* SAView+MASAdditions.m in Sources */,
				9A4C51D92CD8DBAC004BC9CE /* SACommonTool.m in Sources */,
				9A4C51DA2CD8DBAC004BC9CE /* NPInviteAlertViewController.m in Sources */,
				9A4C51DB2CD8DBAC004BC9CE /* SAVersionUpdateViewController.m in Sources */,
				9A4C51DC2CD8DBAC004BC9CE /* SAMutableDictionary+Extension.m in Sources */,
				9A4C51DD2CD8DBAC004BC9CE /* SAMyCardsListViewController.m in Sources */,
				9A4C51DE2CD8DBAC004BC9CE /* SADate+Extension.m in Sources */,
				9A4C51DF2CD8DBAC004BC9CE /* SARequestHandle.m in Sources */,
				9A4C51E02CD8DBAC004BC9CE /* SAHomeProtocolUrlsModel.m in Sources */,
				9A4C51E12CD8DBAC004BC9CE /* SAFixedTabbarView.m in Sources */,
				9A4C51E22CD8DBAC004BC9CE /* SAFaceSubmitApiManager.m in Sources */,
				9A4C51E32CD8DBAC004BC9CE /* SAGetPersonalInfoApiManager.m in Sources */,
				9A4C51E42CD8DBAC004BC9CE /* SARefreshStateTrailer.m in Sources */,
				9A4C51E52CD8DBAC004BC9CE /* SAViewConstraint.m in Sources */,
				9A4C51E62CD8DBAC004BC9CE /* SAMainTopBgView.m in Sources */,
				9A4C51E72CD8DBAC004BC9CE /* SAVerifyInfoCell.m in Sources */,
				9A4C51E82CD8DBAC004BC9CE /* SARefreshTrailer.m in Sources */,
				9A4C51E92CD8DBAC004BC9CE /* SARepayListVC.m in Sources */,
				9A4C51EA2CD8DBAC004BC9CE /* SATextView.m in Sources */,
				9A4C51EB2CD8DBAC004BC9CE /* SAPersonalInfoModel.m in Sources */,
				9A4C51EC2CD8DBAC004BC9CE /* SAPhotoManager.m in Sources */,
				9A4C51ED2CD8DBAC004BC9CE /* SANetworkConst.m in Sources */,
				9A4C51EE2CD8DBAC004BC9CE /* SAGetOcrConfigApiManager.m in Sources */,
				9A4C51EF2CD8DBAC004BC9CE /* SATextPickerView.m in Sources */,
				9A4C51F02CD8DBAC004BC9CE /* SARefreshConst.m in Sources */,
				9A4C51F12CD8DBAC004BC9CE /* SANLUploadManager.m in Sources */,
				9A4C51F22CD8DBAC004BC9CE /* SARefreshBackGifFooter.m in Sources */,
				9A4C51F32CD8DBAC004BC9CE /* SARefreshBackStateFooter.m in Sources */,
				9A4C51F42CD8DBAC004BC9CE /* SASmsCodeViewController.m in Sources */,
				9A4C51F52CD8DBAC004BC9CE /* SAVerifyIdentityViewController.m in Sources */,
				9A4C51F62CD8DBAC004BC9CE /* SATextModel.m in Sources */,
				9A4C51F72CD8DBAC004BC9CE /* SAPaidListVC.m in Sources */,
				9A4C51F82CD8DBAC004BC9CE /* SAPickerAlertView.m in Sources */,
				9A4C51F92CD8DBAC004BC9CE /* SAObject+QLNetworkingMethods.m in Sources */,
				9A4C51FA2CD8DBAC004BC9CE /* SABorrowCashPeriodModel.m in Sources */,
				9A4C51FB2CD8DBAC004BC9CE /* SAPersonalInfoCheckModel.m in Sources */,
				9A4C51FC2CD8DBAC004BC9CE /* SAGetEsignUrlApi.m in Sources */,
				9A4C51FD2CD8DBAC004BC9CE /* NPOnlineRepayViewController.m in Sources */,
				9A4C51FE2CD8DBAC004BC9CE /* SABasicViewController.m in Sources */,
				9A4C51FF2CD8DBAC004BC9CE /* SAApiProxy.m in Sources */,
				9A4C52002CD8DBAC004BC9CE /* SAObjC.m in Sources */,
				9A4C52012CD8DBAC004BC9CE /* SAImage+Extension.m in Sources */,
				9A4C52022CD8DBAC004BC9CE /* SAPayClientInfoApiManager.m in Sources */,
				9A4C52032CD8DBAC004BC9CE /* SAString+Size.m in Sources */,
				9A4C52042CD8DBAC004BC9CE /* SACustomSlideView.m in Sources */,
				9A4C52052CD8DBAC004BC9CE /* SAHomeNormalCell.m in Sources */,
				9A4C52062CD8DBAC004BC9CE /* SABCTool.m in Sources */,
				9A4C52072CD8DBAC004BC9CE /* SAView+Extension.m in Sources */,
				9A4C52082CD8DBAC004BC9CE /* SAShapeLayer+Extension.m in Sources */,
				9A4C52092CD8DBAC004BC9CE /* SAString+QLNetworkingMethods.m in Sources */,
				9A4C520A2CD8DBAC004BC9CE /* SAPayWayModel.m in Sources */,
				9A4C520B2CD8DBAC004BC9CE /* SASaveBindCardInfoApi.m in Sources */,
				9A4C520C2CD8DBAC004BC9CE /* SAConfrimTradeViewController.m in Sources */,
				9A4C520D2CD8DBAC004BC9CE /* SAConfirmSmsAlertView.m in Sources */,
				9A4C520E2CD8DBAC004BC9CE /* SAContactsManager.m in Sources */,
				9A4C520F2CD8DBAC004BC9CE /* SARefreshGifHeader.m in Sources */,
				9A4C52102CD8DBAC004BC9CE /* SABaseManager.m in Sources */,
				9A4C52112CD8DBAC004BC9CE /* SAPickerTextField.m in Sources */,
				9A4C52122CD8DBAC004BC9CE /* SANetworkingConfigurationManager.m in Sources */,
				9A4C52132CD8DBAC004BC9CE /* SARefreshFooter.m in Sources */,
				9A4C52142CD8DBAC004BC9CE /* SAGetCashierInfoAPi.m in Sources */,
				9A4C52152CD8DBAC004BC9CE /* SACustomHudImg.m in Sources */,
				9A4C52162CD8DBAC004BC9CE /* SASettingViewController.m in Sources */,
				9A4C52172CD8DBAC004BC9CE /* SAGetContactInfoApiManager.m in Sources */,
				9A4C52182CD8DBAC004BC9CE /* SASubmitTradeApi.m in Sources */,
				9A4C52192CD8DBAC004BC9CE /* SAProtocolViewController.m in Sources */,
				9A4C521A2CD8DBAC004BC9CE /* SAServiceView.m in Sources */,
				9A4C521B2CD8DBAC004BC9CE /* SATabBarViewController.m in Sources */,
				9A4C521C2CD8DBAC004BC9CE /* SACycleVerticalView.m in Sources */,
				9A4C521D2CD8DBAC004BC9CE /* SABorrowCashModel.m in Sources */,
				9A4C521E2CD8DBAC004BC9CE /* SAHomePageViewController.m in Sources */,
				9A4C521F2CD8DBAC004BC9CE /* SAOfflineRepayViewController.m in Sources */,
				9A4C52202CD8DBAC004BC9CE /* SAGetCitiesListApiManager.m in Sources */,
				9A4C52212CD8DBAC004BC9CE /* SAMarqueeView.m in Sources */,
				9A4C52222CD8DBAC004BC9CE /* SABannerModel.m in Sources */,
				9A4C52232CD8DBAC004BC9CE /* SABankItemView.m in Sources */,
				9A4C52242CD8DBAC004BC9CE /* SAHomeBasicCell.m in Sources */,
				9A4C52252CD8DBAC004BC9CE /* SAFont+Extension.m in Sources */,
				9A4C52262CD8DBAC004BC9CE /* SAVerifyBankCardViewController.m in Sources */,
				9A4C52272CD8DBAC004BC9CE /* Pointer.m in Sources */,
				9A4C52282CD8DBAC004BC9CE /* SAViewAttribute.m in Sources */,
				9A4C52292CD8DBAC004BC9CE /* SAView+MJExtension.m in Sources */,
				9A4C522A2CD8DBAC004BC9CE /* SAAlertSureTipsViewController.m in Sources */,
				9A4C522B2CD8DBAC004BC9CE /* SAAlertController+Orientation.m in Sources */,
				9A4C522C2CD8DBAC004BC9CE /* SAResponseHandle.m in Sources */,
				9A4C522D2CD8DBAC004BC9CE /* SAAppCodeManager.m in Sources */,
				9A4C522E2CD8DBAC004BC9CE /* SAServiceFactory.m in Sources */,
				9A4C522F2CD8DBAC004BC9CE /* SAScrollView+MJExtension.m in Sources */,
				9A4C52302CD8DBAC004BC9CE /* SABankListModel.m in Sources */,
				9A4C52312CD8DBAC004BC9CE /* SABankBinListViewController.m in Sources */,
				9A4C52322CD8DBAC004BC9CE /* SABannerCell.m in Sources */,
				9A4C52332CD8DBAC004BC9CE /* SAPaidCell.m in Sources */,
				9A4C52342CD8DBAC004BC9CE /* SAPickerStyle.m in Sources */,
				9A4C52352CD8DBAC004BC9CE /* SARepaymentViewController.m in Sources */,
				9A4C52362CD8DBAC004BC9CE /* SAAppConfigApiManager.m in Sources */,
				9A4C52372CD8DBAC004BC9CE /* SACompareUploadManager.m in Sources */,
				9A4C52382CD8DBAC004BC9CE /* SARefreshAutoFooter.m in Sources */,
				9A4C52392CD8DBAC004BC9CE /* SACompositeConstraint.m in Sources */,
				9A4C523A2CD8DBAC004BC9CE /* SAResultModel.m in Sources */,
				9A4C523B2CD8DBAC004BC9CE /* SARequest+QLNetworkingMethods.m in Sources */,
				9A4C523C2CD8DBAC004BC9CE /* SAHomeConfirmCell.m in Sources */,
				9A4C523D2CD8DBAC004BC9CE /* SALocationManager.m in Sources */,
				9A4C523E2CD8DBAC004BC9CE /* SALoginViewController.m in Sources */,
				9A4C523F2CD8DBAC004BC9CE /* SAStringPickerView.m in Sources */,
				9A4C52402CD8DBAC004BC9CE /* SAHomeProtocolModel.m in Sources */,
				9A4C52412CD8DBAC004BC9CE /* SAMutableString+QLNetworkingMethods.m in Sources */,
				9A4C52422CD8DBAC004BC9CE /* SARequestGenerator.m in Sources */,
				9A4C52432CD8DBAC004BC9CE /* SAVerifyListItemModel.m in Sources */,
				9A4C52442CD8DBAC004BC9CE /* SAGetProtocolApiManager.m in Sources */,
				9A4C52452CD8DBAC004BC9CE /* SAData+Compress.m in Sources */,
				9A4C52462CD8DBAC004BC9CE /* SAGetTradeDetailApi.m in Sources */,
				9A4C52472CD8DBAC004BC9CE /* SACycleView.m in Sources */,
				9A4C52482CD8DBAC004BC9CE /* SAConstraintMaker.m in Sources */,
				9A4C52492CD8DBAC004BC9CE /* SADate+BRPickerView.m in Sources */,
				9A4C524A2CD8DBAC004BC9CE /* SAUploadInfoManager.m in Sources */,
				9A4C524B2CD8DBAC004BC9CE /* SAManager.m in Sources */,
				9A4C524C2CD8DBAC004BC9CE /* SALogger.m in Sources */,
				9A4C524D2CD8DBAC004BC9CE /* SADeviceTool.m in Sources */,
				9A4C524E2CD8DBAC004BC9CE /* SALoggerConfiguration.m in Sources */,
				9A4C524F2CD8DBAC004BC9CE /* SAHistoryOrderModel.m in Sources */,
				9A4C52502CD8DBAC004BC9CE /* SAVerifyCodeInputView.m in Sources */,
				9A4C52512CD8DBAC004BC9CE /* SAScope.m in Sources */,
				9A4C52522CD8DBAC004BC9CE /* SAMobileLoginApiManager.m in Sources */,
				9A4C52532CD8DBAC004BC9CE /* SABorrowPurposeTypeModel.m in Sources */,
				9A4C52542CD8DBAC004BC9CE /* SAUploadManager.m in Sources */,
				9A4C52552CD8DBAC004BC9CE /* SAGetCarrierUrlApiManager.m in Sources */,
				9A4C52562CD8DBAC004BC9CE /* SARefreshStateHeader.m in Sources */,
				9A4C52572CD8DBAC004BC9CE /* SANoNetView.m in Sources */,
				9A4C52582CD8DBAC004BC9CE /* SAAdvanceManager.m in Sources */,
				9A4C52592CD8DBAC004BC9CE /* SAToast+UIView.m in Sources */,
				9A4C525A2CD8DBAC004BC9CE /* SAUserCenterApiManager.m in Sources */,
				9A4C525B2CD8DBAC004BC9CE /* SAMyBankItemModel.m in Sources */,
				9A4C525C2CD8DBAC004BC9CE /* SASavePersonalInfoApiManager.m in Sources */,
				9A4C525D2CD8DBAC004BC9CE /* SADeviceInfoSaveApiManager.m in Sources */,
				9A4C525E2CD8DBAC004BC9CE /* SAAddressModel.m in Sources */,
				9A4C525F2CD8DBAC004BC9CE /* main.m in Sources */,
				9A4C52602CD8DBAC004BC9CE /* SAVerifyContactsViewController.m in Sources */,
				9A4C52612CD8DBAC004BC9CE /* SAAuditProgressItemModel.m in Sources */,
				9A4C52622CD8DBAC004BC9CE /* SAUploadModel.m in Sources */,
				9A4C52632CD8DBAC004BC9CE /* SAHomeReviewingCell.m in Sources */,
				9A4C52642CD8DBAC004BC9CE /* SABaseView.m in Sources */,
				9A4C52652CD8DBAC004BC9CE /* SABindCardResendSmsApi.m in Sources */,
				9A4C52662CD8DBAC004BC9CE /* SAPersonalVerifyModel.m in Sources */,
				9A4C52672CD8DBAC004BC9CE /* SATabedSlideView.m in Sources */,
				9A4C52682CD8DBAC004BC9CE /* SARepayH5ViewController.m in Sources */,
				9A4C52692CD8DBAC004BC9CE /* SACommonNoDataView.m in Sources */,
				9A4C526A2CD8DBAC004BC9CE /* SAOrderInfoModel.m in Sources */,
				9A4C526B2CD8DBAC004BC9CE /* SASaveGPSApiManager.m in Sources */,
				9A4C526C2CD8DBAC004BC9CE /* SATradeDetailCell.m in Sources */,
				9A4C526D2CD8DBAC004BC9CE /* SAClientChargesModel.m in Sources */,
				9A4C526E2CD8DBAC004BC9CE /* SAGetRegManager.m in Sources */,
				9A4C526F2CD8DBAC004BC9CE /* NPCashierCardCell.m in Sources */,
				9A4C52702CD8DBAC004BC9CE /* SARefreshConfig.m in Sources */,
				9A4C52712CD8DBAC004BC9CE /* SAScrollTabbarView.m in Sources */,
				9A4C52722CD8DBAC004BC9CE /* SAErrorView.m in Sources */,
				9A4C52732CD8DBAC004BC9CE /* SARefreshAutoStateFooter.m in Sources */,
				9A4C52742CD8DBAC004BC9CE /* SALayoutConstraint+MASDebugAdditions.m in Sources */,
				9A4C52752CD8DBAC004BC9CE /* SARefreshNormalHeader.m in Sources */,
				9A4C52762CD8DBAC004BC9CE /* SAContactSelectModel.m in Sources */,
				9A4C52772CD8DBAC004BC9CE /* SAContactRowModel.m in Sources */,
				9A4C52782CD8DBAC004BC9CE /* SALoanRecordModel.m in Sources */,
				9A4C52792CD8DBAC004BC9CE /* SARepayListModel.m in Sources */,
				9A4C527A2CD8DBAC004BC9CE /* SAConfirmmBankViewController.m in Sources */,
				9A4C527B2CD8DBAC004BC9CE /* SAUserCenterModel.m in Sources */,
				9A4C527C2CD8DBAC004BC9CE /* SARefreshBackNormalFooter.m in Sources */,
				9A4C527D2CD8DBAC004BC9CE /* SAQueryCardBinApi.m in Sources */,
				9A4C527E2CD8DBAC004BC9CE /* SAVerifyListManager.m in Sources */,
				9A4C527F2CD8DBAC004BC9CE /* SAWifiManager.m in Sources */,
				9A4C52802CD8DBAC004BC9CE /* SAMainManager.m in Sources */,
				9A4C52812CD8DBAC004BC9CE /* SAGetBankRealNameApi.m in Sources */,
				9A4C52822CD8DBAC004BC9CE /* SAGetBorrowAgainApi.m in Sources */,
				9A4C52832CD8DBAC004BC9CE /* SAFaceBackAlert.m in Sources */,
				9A4C52842CD8DBAC004BC9CE /* SAContactItemCell.m in Sources */,
				9A4C52852CD8DBAC004BC9CE /* SACollectionViewLayout+MJRefresh.m in Sources */,
				9A4C52862CD8DBAC004BC9CE /* NPHomeStepModel.m in Sources */,
				9A4C52872CD8DBAC004BC9CE /* SADictionary+QLNetworkingMethods.m in Sources */,
				9A4C52882CD8DBAC004BC9CE /* SARuntimeExtensions.m in Sources */,
				9A4C52892CD8DBAC004BC9CE /* SASubmitProductInfoModel.m in Sources */,
				9A4C528A2CD8DBAC004BC9CE /* SARepaymentCell.m in Sources */,
				9A4C528B2CD8DBAC004BC9CE /* SAArray+MASAdditions.m in Sources */,
				9A4C528C2CD8DBAC004BC9CE /* SABasicService.m in Sources */,
				9A4C528D2CD8DBAC004BC9CE /* SAAliyunOSSManager.m in Sources */,
				9A4C528E2CD8DBAC004BC9CE /* SAGradientView.m in Sources */,
				9A4C528F2CD8DBAC004BC9CE /* SABundle+BRPickerView.m in Sources */,
				9A4C52902CD8DBAC004BC9CE /* SAProductModel.m in Sources */,
				9A4C52912CD8DBAC004BC9CE /* SABindCardSendCodeApi.m in Sources */,
				9A4C52922CD8DBAC004BC9CE /* SATool.m in Sources */,
				9A4C52932CD8DBAC004BC9CE /* SAHomeRejectCell.m in Sources */,
				9A4C52942CD8DBAC004BC9CE /* SABankNameModel.m in Sources */,
				9A4C52952CD8DBAC004BC9CE /* SAUtility.m in Sources */,
				9A4C52962CD8DBAC004BC9CE /* SAGetBillDetailApi.m in Sources */,
				9A4C52972CD8DBAC004BC9CE /* SAString+Extension.m in Sources */,
				9A4C52982CD8DBAC004BC9CE /* SAJQCache.m in Sources */,
				9A4C52992CD8DBAC004BC9CE /* SACardListCell.m in Sources */,
				9A4C529A2CD8DBAC004BC9CE /* SAS3Manager.m in Sources */,
				9A4C529B2CD8DBAC004BC9CE /* SABannerView.m in Sources */,
				9A4C529C2CD8DBAC004BC9CE /* SASubmitManager.m in Sources */,
				9A4C529D2CD8DBAC004BC9CE /* SAContactSectionModel.m in Sources */,
				9A4C529E2CD8DBAC004BC9CE /* SACenterYTextView.m in Sources */,
				9A4C529F2CD8DBAC004BC9CE /* SADatePickerView.m in Sources */,
				9A4C52A02CD8DBAC004BC9CE /* SAMineViewController.m in Sources */,
				9A4C52A12CD8DBAC004BC9CE /* SAPayClientChargesApiManager.m in Sources */,
				9A4C52A22CD8DBAC004BC9CE /* SALayoutConstraint.m in Sources */,
				9A4C52A32CD8DBAC004BC9CE /* SARefreshAutoGifFooter.m in Sources */,
				9A4C52A42CD8DBAC004BC9CE /* SAGetLiveConfigManager.m in Sources */,
				9A4C52A52CD8DBAC004BC9CE /* SACashierPayStatusApi.m in Sources */,
				9A4C52A62CD8DBAC004BC9CE /* SAHomeBankModel.m in Sources */,
				9A4C52A72CD8DBAC004BC9CE /* SAArray+QLNetworkingMethods.m in Sources */,
				9A4C52A82CD8DBAC004BC9CE /* SAImage+ChangeColor.m in Sources */,
				9A4C52A92CD8DBAC004BC9CE /* SARefreshAutoNormalFooter.m in Sources */,
				9A4C52AA2CD8DBAC004BC9CE /* AppDelegate.m in Sources */,
				9A4C52AB2CD8DBAC004BC9CE /* SAGetPaidBillListApi.m in Sources */,
				9A4C52AC2CD8DBAC004BC9CE /* SAVerifyCodeApiManager.m in Sources */,
				9A4C52AD2CD8DBAC004BC9CE /* SAHomeLoaningCell.m in Sources */,
				9A4C52AE2CD8DBAC004BC9CE /* SALogoutApiManager.m in Sources */,
				9A4C52AF2CD8DBAC004BC9CE /* SAGetSupportBankListApi.m in Sources */,
				9A4C52B02CD8DBAC004BC9CE /* SARefreshComponent.m in Sources */,
				9A4C52B12CD8DBAC004BC9CE /* SAKeyChainTool.m in Sources */,
				9A4C52B22CD8DBAC004BC9CE /* SAOrderConfirmApiManager.m in Sources */,
				9A4C52B32CD8DBAC004BC9CE /* SARefreshBackFooter.m in Sources */,
				9A4C52B42CD8DBAC004BC9CE /* SAGetFileTokenApiManager.m in Sources */,
				9A4C52B52CD8DBAC004BC9CE /* SASlideView.m in Sources */,
				9A4C52B62CD8DBAC004BC9CE /* SABindCardInfoModel.m in Sources */,
				9A4C52B72CD8DBAC004BC9CE /* SASaveContactInfoApiManager.m in Sources */,
				9A4C52B82CD8DBAC004BC9CE /* SAVerifyDataListApiManager.m in Sources */,
				9A4C52B92CD8DBAC004BC9CE /* SARefreshNormalTrailer.m in Sources */,
				9A4C52BA2CD8DBAC004BC9CE /* SATokenApiManager.m in Sources */,
				9A4C52BB2CD8DBAC004BC9CE /* SAGetMyCardsApi.m in Sources */,
				9A4C52BC2CD8DBAC004BC9CE /* SABundle+MJRefresh.m in Sources */,
				9A4C52BD2CD8DBAC004BC9CE /* SAGetCustomerUrlApiManager.m in Sources */,
				9A4C52BE2CD8DBAC004BC9CE /* SAColor+Extension.m in Sources */,
				9A4C52BF2CD8DBAC004BC9CE /* SAUserManager.m in Sources */,
				9A4C52C02CD8DBAC004BC9CE /* SACashierDoPayApi.m in Sources */,
				9A4C52C12CD8DBAC004BC9CE /* SAActionSheet.m in Sources */,
				9A4C52C22CD8DBAC004BC9CE /* SAOfflineRepayInfoApiManager.m in Sources */,
				9A4C52C32CD8DBAC004BC9CE /* SACache.m in Sources */,
				9A4C52C42CD8DBAC004BC9CE /* SAOrderDetailViewController.m in Sources */,
				9A4C52C52CD8DBAC004BC9CE /* SAGetRepayBillListApi.m in Sources */,
				9A4C52C62CD8DBAC004BC9CE /* SAViewController+MASAdditions.m in Sources */,
				9A4C52C72CD8DBAC004BC9CE /* SAVerifyBasicInfoViewController.m in Sources */,
				9A4C52C82CD8DBAC004BC9CE /* SANormalRefresh.m in Sources */,
				9A4C52C92CD8DBAC004BC9CE /* SACommonWebViewController.m in Sources */,
				9A4C52CA2CD8DBAC004BC9CE /* SAHomeModel.m in Sources */,
				9A4C52CB2CD8DBAC004BC9CE /* SAScrollView+MJRefresh.m in Sources */,
				9A4C52CC2CD8DBAC004BC9CE /* SACachedObject.m in Sources */,
				9A4C52CD2CD8DBAC004BC9CE /* SAConstraint.m in Sources */,
				9A4C52CE2CD8DBAC004BC9CE /* SARepaymentModel.m in Sources */,
				9A4C52CF2CD8DBAC004BC9CE /* SARefreshHeader.m in Sources */,
				9A4C52D02CD8DBAC004BC9CE /* SADatePickerView+BR.m in Sources */,
				9A4C52D12CD8DBAC004BC9CE /* SARepayCashierModel.m in Sources */,
				9A4C52D22CD8DBAC004BC9CE /* SASaveDeviceChannelApiManager.m in Sources */,
				9A4C52D32CD8DBAC004BC9CE /* SAPayStatusViewController.m in Sources */,
				9A4C52D42CD8DBAC004BC9CE /* NPTradeAlertViewController.m in Sources */,
				9A4C52D52CD8DBAC004BC9CE /* SANavigationViewController.m in Sources */,
				9A4C52D62CD8DBAC004BC9CE /* SAVerifyListModel.m in Sources */,
				9A4C52D72CD8DBAC004BC9CE /* SAOrderConfigApiManager.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9AC7DFE42CE595D000944F3B /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				9AC7DFE52CE595D000944F3B /* SAService.m in Sources */,
				9AC7DFE62CE595D000944F3B /* SAFileUploadApiManager.m in Sources */,
				9AC7DFE72CE595D000944F3B /* SAMainHomeApiManager.m in Sources */,
				9AC7DFE82CE595D000944F3B /* SAResponse.m in Sources */,
				9AC7DFE92CE595D000944F3B /* SADictionary+Common.m in Sources */,
				9AC7DFEA2CE595D000944F3B /* SAView+MASAdditions.m in Sources */,
				9AC7DFEB2CE595D000944F3B /* SACommonTool.m in Sources */,
				9AC7DFEC2CE595D000944F3B /* NPInviteAlertViewController.m in Sources */,
				9AC7DFED2CE595D000944F3B /* SAVersionUpdateViewController.m in Sources */,
				9AC7DFEE2CE595D000944F3B /* SAMutableDictionary+Extension.m in Sources */,
				9AC7DFEF2CE595D000944F3B /* SAMyCardsListViewController.m in Sources */,
				9AC7DFF02CE595D000944F3B /* SADate+Extension.m in Sources */,
				9AC7DFF12CE595D000944F3B /* SARequestHandle.m in Sources */,
				9AC7DFF22CE595D000944F3B /* SAHomeProtocolUrlsModel.m in Sources */,
				9AC7DFF32CE595D000944F3B /* SAFixedTabbarView.m in Sources */,
				9AC7DFF42CE595D000944F3B /* SAFaceSubmitApiManager.m in Sources */,
				9AC7DFF52CE595D000944F3B /* SAGetPersonalInfoApiManager.m in Sources */,
				9AC7DFF62CE595D000944F3B /* SARefreshStateTrailer.m in Sources */,
				9AC7DFF72CE595D000944F3B /* SAViewConstraint.m in Sources */,
				9AC7DFF82CE595D000944F3B /* SAMainTopBgView.m in Sources */,
				9AC7DFF92CE595D000944F3B /* SAVerifyInfoCell.m in Sources */,
				9AC7DFFA2CE595D000944F3B /* SARefreshTrailer.m in Sources */,
				9AC7DFFB2CE595D000944F3B /* SARepayListVC.m in Sources */,
				9AC7DFFC2CE595D000944F3B /* SATextView.m in Sources */,
				9AC7DFFD2CE595D000944F3B /* SAPersonalInfoModel.m in Sources */,
				9AC7DFFE2CE595D000944F3B /* SAPhotoManager.m in Sources */,
				9AC7DFFF2CE595D000944F3B /* SANetworkConst.m in Sources */,
				9AC7E0002CE595D000944F3B /* SAGetOcrConfigApiManager.m in Sources */,
				9AC7E0012CE595D000944F3B /* SATextPickerView.m in Sources */,
				9AC7E0022CE595D000944F3B /* SARefreshConst.m in Sources */,
				9AC7E0032CE595D000944F3B /* SANLUploadManager.m in Sources */,
				9AC7E0042CE595D000944F3B /* SARefreshBackGifFooter.m in Sources */,
				9AC7E0052CE595D000944F3B /* SARefreshBackStateFooter.m in Sources */,
				9AC7E0062CE595D000944F3B /* SASmsCodeViewController.m in Sources */,
				9AC7E0072CE595D000944F3B /* SAVerifyIdentityViewController.m in Sources */,
				9AC7E0082CE595D000944F3B /* SATextModel.m in Sources */,
				9AC7E0092CE595D000944F3B /* SAPaidListVC.m in Sources */,
				9AC7E00A2CE595D000944F3B /* SAPickerAlertView.m in Sources */,
				9AC7E00B2CE595D000944F3B /* SAObject+QLNetworkingMethods.m in Sources */,
				9AC7E00C2CE595D000944F3B /* SABorrowCashPeriodModel.m in Sources */,
				9AC7E00D2CE595D000944F3B /* SAPersonalInfoCheckModel.m in Sources */,
				9AC7E00E2CE595D000944F3B /* SAGetEsignUrlApi.m in Sources */,
				9AC7E00F2CE595D000944F3B /* NPOnlineRepayViewController.m in Sources */,
				9AC7E0102CE595D000944F3B /* SABasicViewController.m in Sources */,
				9AC7E0112CE595D000944F3B /* SAApiProxy.m in Sources */,
				9AC7E0122CE595D000944F3B /* SAObjC.m in Sources */,
				9AC7E0132CE595D000944F3B /* SAImage+Extension.m in Sources */,
				9AC7E0142CE595D000944F3B /* SAPayClientInfoApiManager.m in Sources */,
				9AC7E0152CE595D000944F3B /* SAString+Size.m in Sources */,
				9AC7E0162CE595D000944F3B /* SACustomSlideView.m in Sources */,
				9AC7E0172CE595D000944F3B /* SAHomeNormalCell.m in Sources */,
				9AC7E0182CE595D000944F3B /* SABCTool.m in Sources */,
				9AC7E0192CE595D000944F3B /* SAView+Extension.m in Sources */,
				9AC7E01A2CE595D000944F3B /* SAShapeLayer+Extension.m in Sources */,
				9AC7E01B2CE595D000944F3B /* SAString+QLNetworkingMethods.m in Sources */,
				9AC7E01C2CE595D000944F3B /* SAPayWayModel.m in Sources */,
				9AC7E01D2CE595D000944F3B /* SASaveBindCardInfoApi.m in Sources */,
				9AC7E01E2CE595D000944F3B /* SAConfrimTradeViewController.m in Sources */,
				9AC7E01F2CE595D000944F3B /* SAConfirmSmsAlertView.m in Sources */,
				9AC7E0202CE595D000944F3B /* SAContactsManager.m in Sources */,
				9AC7E0212CE595D000944F3B /* SARefreshGifHeader.m in Sources */,
				9AC7E0222CE595D000944F3B /* SABaseManager.m in Sources */,
				9AC7E0232CE595D000944F3B /* SAPickerTextField.m in Sources */,
				9AC7E0242CE595D000944F3B /* SANetworkingConfigurationManager.m in Sources */,
				9AC7E0252CE595D000944F3B /* SARefreshFooter.m in Sources */,
				9AC7E0262CE595D000944F3B /* SAGetCashierInfoAPi.m in Sources */,
				9AC7E0272CE595D000944F3B /* SACustomHudImg.m in Sources */,
				9AC7E0282CE595D000944F3B /* SASettingViewController.m in Sources */,
				9AC7E0292CE595D000944F3B /* SAGetContactInfoApiManager.m in Sources */,
				9AC7E02A2CE595D000944F3B /* SASubmitTradeApi.m in Sources */,
				9AC7E02B2CE595D000944F3B /* SAProtocolViewController.m in Sources */,
				9AC7E02C2CE595D000944F3B /* SAServiceView.m in Sources */,
				9AC7E02D2CE595D000944F3B /* SATabBarViewController.m in Sources */,
				9AC7E02E2CE595D000944F3B /* SACycleVerticalView.m in Sources */,
				9AC7E02F2CE595D000944F3B /* SABorrowCashModel.m in Sources */,
				9AC7E0302CE595D000944F3B /* SAHomePageViewController.m in Sources */,
				9AC7E0312CE595D000944F3B /* SAOfflineRepayViewController.m in Sources */,
				9AC7E0322CE595D000944F3B /* SAGetCitiesListApiManager.m in Sources */,
				9AC7E0332CE595D000944F3B /* SAMarqueeView.m in Sources */,
				9AC7E0342CE595D000944F3B /* SABannerModel.m in Sources */,
				9AC7E0352CE595D000944F3B /* SABankItemView.m in Sources */,
				9AC7E0362CE595D000944F3B /* SAHomeBasicCell.m in Sources */,
				9AC7E0372CE595D000944F3B /* SAFont+Extension.m in Sources */,
				9AC7E0382CE595D000944F3B /* SAVerifyBankCardViewController.m in Sources */,
				9AC7E0392CE595D000944F3B /* Pointer.m in Sources */,
				9AC7E03A2CE595D000944F3B /* SAViewAttribute.m in Sources */,
				9AC7E03B2CE595D000944F3B /* SAView+MJExtension.m in Sources */,
				9AC7E03C2CE595D000944F3B /* SAAlertSureTipsViewController.m in Sources */,
				9AC7E03D2CE595D000944F3B /* SAAlertController+Orientation.m in Sources */,
				9AC7E03E2CE595D000944F3B /* SAResponseHandle.m in Sources */,
				9AC7E03F2CE595D000944F3B /* SAAppCodeManager.m in Sources */,
				9AC7E0402CE595D000944F3B /* SAServiceFactory.m in Sources */,
				9AC7E0412CE595D000944F3B /* SAScrollView+MJExtension.m in Sources */,
				9AC7E0422CE595D000944F3B /* SABankListModel.m in Sources */,
				9AC7E0432CE595D000944F3B /* SABankBinListViewController.m in Sources */,
				9AC7E0442CE595D000944F3B /* SABannerCell.m in Sources */,
				9AC7E0452CE595D000944F3B /* SAPaidCell.m in Sources */,
				9AC7E0462CE595D000944F3B /* SAPickerStyle.m in Sources */,
				9AC7E0472CE595D000944F3B /* SARepaymentViewController.m in Sources */,
				9AC7E0482CE595D000944F3B /* SAAppConfigApiManager.m in Sources */,
				9AC7E0492CE595D000944F3B /* SACompareUploadManager.m in Sources */,
				9AC7E04A2CE595D000944F3B /* SARefreshAutoFooter.m in Sources */,
				9AC7E04B2CE595D000944F3B /* SACompositeConstraint.m in Sources */,
				9AC7E04C2CE595D000944F3B /* SAResultModel.m in Sources */,
				9AC7E04D2CE595D000944F3B /* SARequest+QLNetworkingMethods.m in Sources */,
				9AC7E04E2CE595D000944F3B /* SAHomeConfirmCell.m in Sources */,
				9AC7E04F2CE595D000944F3B /* SALocationManager.m in Sources */,
				9AC7E0502CE595D000944F3B /* SALoginViewController.m in Sources */,
				9AC7E0512CE595D000944F3B /* SAStringPickerView.m in Sources */,
				9AC7E0522CE595D000944F3B /* SAHomeProtocolModel.m in Sources */,
				9AC7E0532CE595D000944F3B /* SAMutableString+QLNetworkingMethods.m in Sources */,
				9AC7E0542CE595D000944F3B /* SARequestGenerator.m in Sources */,
				9AC7E0552CE595D000944F3B /* SAVerifyListItemModel.m in Sources */,
				9AC7E0562CE595D000944F3B /* SAGetProtocolApiManager.m in Sources */,
				9AC7E0572CE595D000944F3B /* SAData+Compress.m in Sources */,
				9AC7E0582CE595D000944F3B /* SAGetTradeDetailApi.m in Sources */,
				9AC7E0592CE595D000944F3B /* SACycleView.m in Sources */,
				9AC7E05A2CE595D000944F3B /* SAConstraintMaker.m in Sources */,
				9AC7E05B2CE595D000944F3B /* SADate+BRPickerView.m in Sources */,
				9AC7E05C2CE595D000944F3B /* SAUploadInfoManager.m in Sources */,
				9AC7E05D2CE595D000944F3B /* SAManager.m in Sources */,
				9AC7E05E2CE595D000944F3B /* SALogger.m in Sources */,
				9AC7E05F2CE595D000944F3B /* SADeviceTool.m in Sources */,
				9AC7E0602CE595D000944F3B /* SALoggerConfiguration.m in Sources */,
				9AC7E0612CE595D000944F3B /* SAHistoryOrderModel.m in Sources */,
				9AC7E0622CE595D000944F3B /* SAVerifyCodeInputView.m in Sources */,
				9AC7E0632CE595D000944F3B /* SAScope.m in Sources */,
				9AC7E0642CE595D000944F3B /* SAMobileLoginApiManager.m in Sources */,
				9AC7E0652CE595D000944F3B /* SABorrowPurposeTypeModel.m in Sources */,
				9AC7E0662CE595D000944F3B /* SAUploadManager.m in Sources */,
				9AC7E0672CE595D000944F3B /* SAGetCarrierUrlApiManager.m in Sources */,
				9AC7E0682CE595D000944F3B /* SARefreshStateHeader.m in Sources */,
				9AC7E0692CE595D000944F3B /* SANoNetView.m in Sources */,
				9AC7E06A2CE595D000944F3B /* SAAdvanceManager.m in Sources */,
				9AC7E06B2CE595D000944F3B /* SAToast+UIView.m in Sources */,
				9AC7E06C2CE595D000944F3B /* SAUserCenterApiManager.m in Sources */,
				9AC7E06D2CE595D000944F3B /* SAMyBankItemModel.m in Sources */,
				9AC7E06E2CE595D000944F3B /* SASavePersonalInfoApiManager.m in Sources */,
				9AC7E06F2CE595D000944F3B /* SADeviceInfoSaveApiManager.m in Sources */,
				9AC7E0702CE595D000944F3B /* SAAddressModel.m in Sources */,
				9AC7E0712CE595D000944F3B /* main.m in Sources */,
				9AC7E0722CE595D000944F3B /* SAVerifyContactsViewController.m in Sources */,
				9AC7E0732CE595D000944F3B /* SAAuditProgressItemModel.m in Sources */,
				9AC7E0742CE595D000944F3B /* SAUploadModel.m in Sources */,
				9AC7E0752CE595D000944F3B /* SAHomeReviewingCell.m in Sources */,
				9AC7E0762CE595D000944F3B /* SABaseView.m in Sources */,
				9AC7E0772CE595D000944F3B /* SABindCardResendSmsApi.m in Sources */,
				9AC7E0782CE595D000944F3B /* SAPersonalVerifyModel.m in Sources */,
				9AC7E0792CE595D000944F3B /* SATabedSlideView.m in Sources */,
				9AC7E07A2CE595D000944F3B /* SARepayH5ViewController.m in Sources */,
				9AC7E07B2CE595D000944F3B /* SACommonNoDataView.m in Sources */,
				9AC7E07C2CE595D000944F3B /* SAOrderInfoModel.m in Sources */,
				9AC7E07D2CE595D000944F3B /* SASaveGPSApiManager.m in Sources */,
				9AC7E07E2CE595D000944F3B /* SATradeDetailCell.m in Sources */,
				9AC7E07F2CE595D000944F3B /* SAClientChargesModel.m in Sources */,
				9AC7E0802CE595D000944F3B /* SAGetRegManager.m in Sources */,
				9AC7E0812CE595D000944F3B /* NPCashierCardCell.m in Sources */,
				9AC7E0822CE595D000944F3B /* SARefreshConfig.m in Sources */,
				9AC7E0832CE595D000944F3B /* SAScrollTabbarView.m in Sources */,
				9AC7E0842CE595D000944F3B /* SAErrorView.m in Sources */,
				9AC7E0852CE595D000944F3B /* SARefreshAutoStateFooter.m in Sources */,
				9AC7E0862CE595D000944F3B /* SALayoutConstraint+MASDebugAdditions.m in Sources */,
				9AC7E0872CE595D000944F3B /* SARefreshNormalHeader.m in Sources */,
				9AC7E0882CE595D000944F3B /* SAContactSelectModel.m in Sources */,
				9AC7E0892CE595D000944F3B /* SAContactRowModel.m in Sources */,
				9AC7E08A2CE595D000944F3B /* SALoanRecordModel.m in Sources */,
				9AC7E08B2CE595D000944F3B /* SARepayListModel.m in Sources */,
				9AC7E08C2CE595D000944F3B /* SAConfirmmBankViewController.m in Sources */,
				9AC7E08D2CE595D000944F3B /* SAUserCenterModel.m in Sources */,
				9AC7E08E2CE595D000944F3B /* SARefreshBackNormalFooter.m in Sources */,
				9AC7E08F2CE595D000944F3B /* SAQueryCardBinApi.m in Sources */,
				9AC7E0902CE595D000944F3B /* SAVerifyListManager.m in Sources */,
				9AC7E0912CE595D000944F3B /* SAWifiManager.m in Sources */,
				9AC7E0922CE595D000944F3B /* SAMainManager.m in Sources */,
				9AC7E0932CE595D000944F3B /* SAGetBankRealNameApi.m in Sources */,
				9AC7E0942CE595D000944F3B /* SAGetBorrowAgainApi.m in Sources */,
				9AC7E0952CE595D000944F3B /* SAFaceBackAlert.m in Sources */,
				9AC7E0962CE595D000944F3B /* SAContactItemCell.m in Sources */,
				9AC7E0972CE595D000944F3B /* SACollectionViewLayout+MJRefresh.m in Sources */,
				9AC7E0982CE595D000944F3B /* NPHomeStepModel.m in Sources */,
				9AC7E0992CE595D000944F3B /* SADictionary+QLNetworkingMethods.m in Sources */,
				9AC7E09A2CE595D000944F3B /* SARuntimeExtensions.m in Sources */,
				9AC7E09B2CE595D000944F3B /* SASubmitProductInfoModel.m in Sources */,
				9AC7E09C2CE595D000944F3B /* SARepaymentCell.m in Sources */,
				9AC7E09D2CE595D000944F3B /* SAArray+MASAdditions.m in Sources */,
				9AC7E09E2CE595D000944F3B /* SABasicService.m in Sources */,
				9AC7E09F2CE595D000944F3B /* SAAliyunOSSManager.m in Sources */,
				9AC7E0A02CE595D000944F3B /* SAGradientView.m in Sources */,
				9AC7E0A12CE595D000944F3B /* SABundle+BRPickerView.m in Sources */,
				9AC7E0A22CE595D000944F3B /* SAProductModel.m in Sources */,
				9AC7E0A32CE595D000944F3B /* SABindCardSendCodeApi.m in Sources */,
				9AC7E0A42CE595D000944F3B /* SATool.m in Sources */,
				9AC7E0A52CE595D000944F3B /* SAHomeRejectCell.m in Sources */,
				9AC7E0A62CE595D000944F3B /* SABankNameModel.m in Sources */,
				9AC7E0A72CE595D000944F3B /* SAUtility.m in Sources */,
				9AC7E0A82CE595D000944F3B /* SAGetBillDetailApi.m in Sources */,
				9AC7E0A92CE595D000944F3B /* SAString+Extension.m in Sources */,
				9AC7E0AA2CE595D000944F3B /* SAJQCache.m in Sources */,
				9AC7E0AB2CE595D000944F3B /* SACardListCell.m in Sources */,
				9AC7E0AC2CE595D000944F3B /* SAS3Manager.m in Sources */,
				9AC7E0AD2CE595D000944F3B /* SABannerView.m in Sources */,
				9AC7E0AE2CE595D000944F3B /* SASubmitManager.m in Sources */,
				9AC7E0AF2CE595D000944F3B /* SAContactSectionModel.m in Sources */,
				9AC7E0B02CE595D000944F3B /* SACenterYTextView.m in Sources */,
				9AC7E0B12CE595D000944F3B /* SADatePickerView.m in Sources */,
				9AC7E0B22CE595D000944F3B /* SAMineViewController.m in Sources */,
				9AC7E0B32CE595D000944F3B /* SAPayClientChargesApiManager.m in Sources */,
				9AC7E0B42CE595D000944F3B /* SALayoutConstraint.m in Sources */,
				9AC7E0B52CE595D000944F3B /* SARefreshAutoGifFooter.m in Sources */,
				9AC7E0B62CE595D000944F3B /* SAGetLiveConfigManager.m in Sources */,
				9AC7E0B72CE595D000944F3B /* SACashierPayStatusApi.m in Sources */,
				9AC7E0B82CE595D000944F3B /* SAHomeBankModel.m in Sources */,
				9AC7E0B92CE595D000944F3B /* SAArray+QLNetworkingMethods.m in Sources */,
				9AC7E0BA2CE595D000944F3B /* SAImage+ChangeColor.m in Sources */,
				9AC7E0BB2CE595D000944F3B /* SARefreshAutoNormalFooter.m in Sources */,
				9AC7E0BC2CE595D000944F3B /* AppDelegate.m in Sources */,
				9AC7E0BD2CE595D000944F3B /* SAGetPaidBillListApi.m in Sources */,
				9AC7E0BE2CE595D000944F3B /* SAVerifyCodeApiManager.m in Sources */,
				9AC7E0BF2CE595D000944F3B /* SAHomeLoaningCell.m in Sources */,
				9AC7E0C02CE595D000944F3B /* SALogoutApiManager.m in Sources */,
				9AC7E0C12CE595D000944F3B /* SAGetSupportBankListApi.m in Sources */,
				9AC7E0C22CE595D000944F3B /* SARefreshComponent.m in Sources */,
				9AC7E0C32CE595D000944F3B /* SAKeyChainTool.m in Sources */,
				9AC7E0C42CE595D000944F3B /* SAOrderConfirmApiManager.m in Sources */,
				9AC7E0C52CE595D000944F3B /* SARefreshBackFooter.m in Sources */,
				9AC7E0C62CE595D000944F3B /* SAGetFileTokenApiManager.m in Sources */,
				9AC7E0C72CE595D000944F3B /* SASlideView.m in Sources */,
				9AC7E0C82CE595D000944F3B /* SABindCardInfoModel.m in Sources */,
				9AC7E0C92CE595D000944F3B /* SASaveContactInfoApiManager.m in Sources */,
				9AC7E0CA2CE595D000944F3B /* SAVerifyDataListApiManager.m in Sources */,
				9AC7E0CB2CE595D000944F3B /* SARefreshNormalTrailer.m in Sources */,
				9AC7E0CC2CE595D000944F3B /* SATokenApiManager.m in Sources */,
				9AC7E0CD2CE595D000944F3B /* SAGetMyCardsApi.m in Sources */,
				9AC7E0CE2CE595D000944F3B /* SABundle+MJRefresh.m in Sources */,
				9AC7E0CF2CE595D000944F3B /* SAGetCustomerUrlApiManager.m in Sources */,
				9AC7E0D02CE595D000944F3B /* SAColor+Extension.m in Sources */,
				9AC7E0D12CE595D000944F3B /* SAUserManager.m in Sources */,
				9AC7E0D22CE595D000944F3B /* SACashierDoPayApi.m in Sources */,
				9AC7E0D32CE595D000944F3B /* SAActionSheet.m in Sources */,
				9AC7E0D42CE595D000944F3B /* SAOfflineRepayInfoApiManager.m in Sources */,
				9AC7E0D52CE595D000944F3B /* SACache.m in Sources */,
				9AC7E0D62CE595D000944F3B /* SAOrderDetailViewController.m in Sources */,
				9AC7E0D72CE595D000944F3B /* SAGetRepayBillListApi.m in Sources */,
				9AC7E0D82CE595D000944F3B /* SAViewController+MASAdditions.m in Sources */,
				9AC7E0D92CE595D000944F3B /* SAVerifyBasicInfoViewController.m in Sources */,
				9AC7E0DA2CE595D000944F3B /* SANormalRefresh.m in Sources */,
				9AC7E0DB2CE595D000944F3B /* SACommonWebViewController.m in Sources */,
				9AC7E0DC2CE595D000944F3B /* SAHomeModel.m in Sources */,
				9AC7E0DD2CE595D000944F3B /* SAScrollView+MJRefresh.m in Sources */,
				9AC7E0DE2CE595D000944F3B /* SACachedObject.m in Sources */,
				9AC7E0DF2CE595D000944F3B /* SAConstraint.m in Sources */,
				9AC7E0E02CE595D000944F3B /* SARepaymentModel.m in Sources */,
				9AC7E0E12CE595D000944F3B /* SARefreshHeader.m in Sources */,
				9AC7E0E22CE595D000944F3B /* SADatePickerView+BR.m in Sources */,
				9AC7E0E32CE595D000944F3B /* SARepayCashierModel.m in Sources */,
				9AC7E0E42CE595D000944F3B /* SASaveDeviceChannelApiManager.m in Sources */,
				9AC7E0E52CE595D000944F3B /* SAPayStatusViewController.m in Sources */,
				9AC7E0E62CE595D000944F3B /* NPTradeAlertViewController.m in Sources */,
				9AC7E0E72CE595D000944F3B /* SANavigationViewController.m in Sources */,
				9AC7E0E82CE595D000944F3B /* SAVerifyListModel.m in Sources */,
				9AC7E0E92CE595D000944F3B /* SAOrderConfigApiManager.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		08CE10FB22A8C2FB00E30241 /* Localizable.strings */ = {
			isa = PBXVariantGroup;
			children = (
				9A0F34FA2AD3C08A0080FD21 /* zh-Hans */,
			);
			name = Localizable.strings;
			sourceTree = "<group>";
		};
		9AA0904F2856EEAE00E835CB /* InfoPlist.strings */ = {
			isa = PBXVariantGroup;
			children = (
				9A0F34FB2AD3C08A0080FD21 /* zh-Hans */,
			);
			name = InfoPlist.strings;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		13B07F941A680F5B00A75B9A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B1A7DD6585FE063FBFDD755D /* Pods-StagingApp.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = "";
				CODE_SIGN_ENTITLEMENTS = StagingApp/StagingApp.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 100;
				DEAD_CODE_STRIPPING = NO;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 37472BGYG5;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/StagingApp/Vendor/OCRIDCardIndonesia/MegviiSDK",
					"$(PROJECT_DIR)/StagingApp/Vendor/Face/Face++",
					"$(PROJECT_DIR)/StagingApp/Vendor/Face/Advance",
					"$(PROJECT_DIR)/StagingApp/Tools/JhtMarquee_SDK",
					"$(PROJECT_DIR)/StagingApp/Vendor/EsignSDK",
				);
				GCC_INPUT_FILETYPE = automatic;
				GCC_PREFIX_HEADER = StagingApp/Config/SAPrefix.pch;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"COCOAPODS=1",
					"DEBUG_MODE=1",
					"STAGING_ONE=1",
				);
				INFOPLIST_FILE = "$(SRCROOT)/StagingApp/Resources/Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = Test;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.finance";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/StagingApp/Vendor/OCRIDCardIndonesia/MegviiSDK",
					"$(PROJECT_DIR)/StagingApp/Vendor/libDFActionLiveness",
				);
				MARKETING_VERSION = 1.0.0;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CFLAGS = "";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
					"-lstdc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.fenqi.onlyTest;
				PRODUCT_MODULE_NAME = StagingApp;
				PRODUCT_NAME = StagingApp;
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = TestDev;
				SWIFT_ENABLE_BATCH_MODE = NO;
				SWIFT_VERSION = 4.0;
				VALIDATE_WORKSPACE = YES;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		13B07F951A680F5B00A75B9A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 3CF1BDA3CFE91B8A0832A6CE /* Pods-StagingApp.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = "";
				CODE_SIGN_ENTITLEMENTS = StagingApp/StagingApp.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 100;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 37472BGYG5;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/StagingApp/Vendor/OCRIDCardIndonesia/MegviiSDK",
					"$(PROJECT_DIR)/StagingApp/Vendor/Face/Face++",
					"$(PROJECT_DIR)/StagingApp/Vendor/Face/Advance",
					"$(PROJECT_DIR)/StagingApp/Tools/JhtMarquee_SDK",
					"$(PROJECT_DIR)/StagingApp/Vendor/EsignSDK",
				);
				GCC_INPUT_FILETYPE = automatic;
				GCC_PREFIX_HEADER = StagingApp/Config/SAPrefix.pch;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"COCOAPODS=1",
					"STAGING_ONE=1",
				);
				INFOPLIST_FILE = "$(SRCROOT)/StagingApp/Resources/Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = Test;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.finance";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/StagingApp/Vendor/OCRIDCardIndonesia",
					"$(PROJECT_DIR)/StagingApp/Vendor/OCRIDCardIndonesia/MegviiSDK",
					"$(PROJECT_DIR)/StagingApp/Vendor/libDFActionLiveness",
				);
				MARKETING_VERSION = 1.0.0;
				OTHER_CFLAGS = "";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
					"-lstdc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.fenqi.onlyTest;
				PRODUCT_MODULE_NAME = StagingApp;
				PRODUCT_NAME = StagingApp;
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = TestDev;
				SWIFT_ENABLE_BATCH_MODE = NO;
				SWIFT_VERSION = 4.0;
				VALIDATE_WORKSPACE = YES;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		83CBBA201A601CBA00E9B192 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		83CBBA211A601CBA00E9B192 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		9A4C52FA2CD8DBAC004BC9CE /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B1A7DD6585FE063FBFDD755D /* Pods-StagingApp.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = "";
				CODE_SIGN_ENTITLEMENTS = StagingApp/StagingApp.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 100;
				DEAD_CODE_STRIPPING = NO;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 37472BGYG5;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/StagingApp/Vendor/OCRIDCardIndonesia/MegviiSDK",
					"$(PROJECT_DIR)/StagingApp/Vendor/Face/Face++",
					"$(PROJECT_DIR)/StagingApp/Vendor/Face/Advance",
					"$(PROJECT_DIR)/StagingApp/Tools/JhtMarquee_SDK",
					"$(PROJECT_DIR)/StagingApp/Vendor/EsignSDK",
				);
				GCC_INPUT_FILETYPE = automatic;
				GCC_PREFIX_HEADER = StagingApp/Config/SAPrefix.pch;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"COCOAPODS=1",
					"DEBUG_MODE=1",
					"STAGING_ONE=1",
				);
				INFOPLIST_FILE = "$(SRCROOT)/StagingApp/Resources/Xinruihua-Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = "现代花";
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.finance";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/StagingApp/Vendor/OCRIDCardIndonesia/MegviiSDK",
					"$(PROJECT_DIR)/StagingApp/Vendor/libDFActionLiveness",
				);
				MARKETING_VERSION = 1.0.0;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CFLAGS = "";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
					"-lstdc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.fenqi.xiandaihua;
				PRODUCT_MODULE_NAME = StagingApp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = XiandaihuaDev;
				SWIFT_ENABLE_BATCH_MODE = NO;
				SWIFT_VERSION = 4.0;
				VALIDATE_WORKSPACE = YES;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		9A4C52FB2CD8DBAC004BC9CE /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 3CF1BDA3CFE91B8A0832A6CE /* Pods-StagingApp.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = "";
				CODE_SIGN_ENTITLEMENTS = StagingApp/StagingApp.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 100;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 37472BGYG5;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/StagingApp/Vendor/OCRIDCardIndonesia/MegviiSDK",
					"$(PROJECT_DIR)/StagingApp/Vendor/Face/Face++",
					"$(PROJECT_DIR)/StagingApp/Vendor/Face/Advance",
					"$(PROJECT_DIR)/StagingApp/Tools/JhtMarquee_SDK",
					"$(PROJECT_DIR)/StagingApp/Vendor/EsignSDK",
				);
				GCC_INPUT_FILETYPE = automatic;
				GCC_PREFIX_HEADER = StagingApp/Config/SAPrefix.pch;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"COCOAPODS=1",
					"STAGING_ONE=1",
				);
				INFOPLIST_FILE = "$(SRCROOT)/StagingApp/Resources/Xinruihua-Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = "现代花";
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.finance";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/StagingApp/Vendor/OCRIDCardIndonesia",
					"$(PROJECT_DIR)/StagingApp/Vendor/OCRIDCardIndonesia/MegviiSDK",
					"$(PROJECT_DIR)/StagingApp/Vendor/libDFActionLiveness",
				);
				MARKETING_VERSION = 1.0.0;
				OTHER_CFLAGS = "";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
					"-lstdc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.fenqi.xiandaihua;
				PRODUCT_MODULE_NAME = StagingApp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = XiandaihuaDev;
				SWIFT_ENABLE_BATCH_MODE = NO;
				SWIFT_VERSION = 4.0;
				VALIDATE_WORKSPACE = YES;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		9AC7E10D2CE595D000944F3B /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B1A7DD6585FE063FBFDD755D /* Pods-StagingApp.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "AppIcon-Haoxianghua";
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = "";
				CODE_SIGN_ENTITLEMENTS = StagingApp/StagingApp.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 100;
				DEAD_CODE_STRIPPING = NO;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 37472BGYG5;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/StagingApp/Vendor/OCRIDCardIndonesia/MegviiSDK",
					"$(PROJECT_DIR)/StagingApp/Vendor/Face/Face++",
					"$(PROJECT_DIR)/StagingApp/Vendor/Face/Advance",
					"$(PROJECT_DIR)/StagingApp/Tools/JhtMarquee_SDK",
					"$(PROJECT_DIR)/StagingApp/Vendor/EsignSDK",
				);
				GCC_INPUT_FILETYPE = automatic;
				GCC_PREFIX_HEADER = StagingApp/Config/SAPrefix.pch;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"COCOAPODS=1",
					"DEBUG_MODE=1",
					"STAGING_TWO=1",
				);
				INFOPLIST_FILE = "$(SRCROOT)/StagingApp/Resources/Haoxianghua-Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = "豪享花";
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.finance";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/StagingApp/Vendor/OCRIDCardIndonesia/MegviiSDK",
					"$(PROJECT_DIR)/StagingApp/Vendor/libDFActionLiveness",
				);
				MARKETING_VERSION = 1.0.0;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CFLAGS = "";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
					"-lstdc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.fenqi.haoxianghua;
				PRODUCT_MODULE_NAME = StagingApp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = HaoxianghuaDev;
				SWIFT_ENABLE_BATCH_MODE = NO;
				SWIFT_VERSION = 4.0;
				VALIDATE_WORKSPACE = YES;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		9AC7E10E2CE595D000944F3B /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 3CF1BDA3CFE91B8A0832A6CE /* Pods-StagingApp.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "AppIcon-Haoxianghua";
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = "";
				CODE_SIGN_ENTITLEMENTS = StagingApp/StagingApp.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 100;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 37472BGYG5;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/StagingApp/Vendor/OCRIDCardIndonesia/MegviiSDK",
					"$(PROJECT_DIR)/StagingApp/Vendor/Face/Face++",
					"$(PROJECT_DIR)/StagingApp/Vendor/Face/Advance",
					"$(PROJECT_DIR)/StagingApp/Tools/JhtMarquee_SDK",
					"$(PROJECT_DIR)/StagingApp/Vendor/EsignSDK",
				);
				GCC_INPUT_FILETYPE = automatic;
				GCC_PREFIX_HEADER = StagingApp/Config/SAPrefix.pch;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"COCOAPODS=1",
					"STAGING_TWO=1",
				);
				INFOPLIST_FILE = "$(SRCROOT)/StagingApp/Resources/Haoxianghua-Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = "豪享花";
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.finance";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/StagingApp/Vendor/OCRIDCardIndonesia",
					"$(PROJECT_DIR)/StagingApp/Vendor/OCRIDCardIndonesia/MegviiSDK",
					"$(PROJECT_DIR)/StagingApp/Vendor/libDFActionLiveness",
				);
				MARKETING_VERSION = 1.0.0;
				OTHER_CFLAGS = "";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
					"-lstdc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.fenqi.haoxianghua;
				PRODUCT_MODULE_NAME = StagingApp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = HaoxianghuaDev;
				SWIFT_ENABLE_BATCH_MODE = NO;
				SWIFT_VERSION = 4.0;
				VALIDATE_WORKSPACE = YES;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "StagingApp" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				13B07F941A680F5B00A75B9A /* Debug */,
				13B07F951A680F5B00A75B9A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "StagingApp" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				83CBBA201A601CBA00E9B192 /* Debug */,
				83CBBA211A601CBA00E9B192 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		9A4C52F92CD8DBAC004BC9CE /* Build configuration list for PBXNativeTarget "Xiandaihua" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				9A4C52FA2CD8DBAC004BC9CE /* Debug */,
				9A4C52FB2CD8DBAC004BC9CE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		9AC7E10C2CE595D000944F3B /* Build configuration list for PBXNativeTarget "Haoxianghua" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				9AC7E10D2CE595D000944F3B /* Debug */,
				9AC7E10E2CE595D000944F3B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 83CBB9F71A601CBA00E9B192 /* Project object */;
}
