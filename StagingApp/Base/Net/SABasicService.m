
#import "SABasicService.h"
#import "SARequestHandle.h"
#import "SANetworkConst.h"
#import "SADeviceTool.h"
#import "SAUserManager.h"
#import "SATabBarViewController.h"
#import "SAVersionUpdateViewController.h"
#import "SANavigationViewController.h"
#import "SALoginViewController.h"


static NSString *kBasicApiOfflineUrl         =           @"http://192.168.10.16:9020";


#if STAGING_ONE

static NSString *kBasicApiOnlineUrl          =           @"https://app-api-xrh.hzxrhh.com:19001";

#elif STAGING_TWO

static NSString *kBasicApiOnlineUrl          =           @"https://hxh-appapi.wzhrdkj.com:19002";

#endif


@implementation SABasicService

#pragma mark - QLServiceProtocal

- (BOOL)isOnline
{
    return kNPServiceIsOnline;
}

- (NSString *)offlineApiBaseUrl
{
    return kBasicApiOfflineUrl;
    
}

- (NSString *)onlineApiBaseUrl
{
    return kBasicApiOnlineUrl;
    
}

- (NSString *)offlineApiVersion
{
    return @"";
}

- (NSString *)onlineApiVersion
{
    return @"";
}

- (NSString *)onlinePublicKey
{
    return @"";
}

- (NSString *)offlinePublicKey
{
    return @"";
}

- (NSString *)onlinePrivateKey
{
    return @"";
}

- (NSString *)offlinePrivateKey
{
    return @"";
}

- (NSDictionary *)extraHttpHeadParmasWithMethodName:(NSString *)method requestParams:(NSDictionary *)requestParams
{
    NSString *oriHeader = [SARequestHandle basicParamsHeadString:requestParams deleteSpace:NO] ;
    NSString *requestHeader = [SAObjC encryptString:[SAObjC encodeString:oriHeader] publicKey:COMMON_CRYPTO_RSA_PUBLIC];
    NSDictionary *extraHeaderDict = @{
        @"requestHeader": requestHeader,
        @"Content-Type": @"application/json;charset=utf-8",
        @"Accept-Language": @"zh" 
    };
    
    return extraHeaderDict;
}

- (BOOL)shouldCallBackByFailedOnCallingAPI:(id)data
{
    BOOL result = YES;
    NSDictionary *response = data;

    if([data isKindOfClass:[NSDictionary class]]){
        if([NSString judgeStringExist:data[@"enData"]]){
            NSString *dataStr =  [NSString decryptUseDES:data[@"enData"] key:DECODE_DES_KEY];
            response = [NSDictionary QL_stringToDict: dataStr];
        }
    }else{
        NSString *decodeStr = [SAObjC decryptString:data privateKey:COMMON_CRYPTO_RSA_PRIVATE];
        response = [NSDictionary QL_stringToDict: decodeStr];
    }
    
    NSString *code = [NSString stringWithFormat:@"%@", response[@"code"]];
    if ([code isEqualToString:kNPCodeUserTokenInvalid] || [code isEqualToString:@"12001"])
    {
        [SAUserManager loginOutClearInfo];
        [SATool textStateWindowHUD:response[@"msg"]];
        SANavigationViewController *navVC = [[SANavigationViewController alloc] initWithRootViewController:[[SALoginViewController alloc] init]];
        navVC.modalPresentationStyle = UIModalPresentationFullScreen;
        [[SACommonTool currentViewController] presentViewController:navVC animated:YES completion:nil];
        return NO;
    }
    else if ([code isEqualToString:kNPForceUpdate])
    {
        SAVersionUpdateViewController *vc = [SAVersionUpdateViewController showVersionUpdateAlertWithAppleId:nil withFromVc:nil];
        [vc presentFromView];
    }
    
    return result;
}

- (NSDictionary *)handleResponseData:(id)data
{
    NSDictionary *jsonData = data;

    if([data isKindOfClass:[NSDictionary class]]){
        if([NSString judgeStringExist:data[@"enData"]]){
            NSString *dataStr =  [NSString decryptUseDES:data[@"enData"] key:DECODE_DES_KEY];
            jsonData = [NSDictionary QL_stringToDict: dataStr];
        }
        return jsonData;
    }else {
        NSString *decodeStr = [SAObjC decryptString:data privateKey:COMMON_CRYPTO_RSA_PRIVATE];
        jsonData = [NSDictionary QL_stringToDict: decodeStr];
        NSLog(@"jsonData---%@", jsonData);
        return  jsonData;
    }
    
}

- (id)handleHTTPBody:(NSDictionary *)params
{
    if( params.allKeys.count > 0){
        NSString *oriString = [params QL_jsonString];
        NSString *rsaParams = [SAObjC encryptString:oriString publicKey:COMMON_CRYPTO_RSA_PUBLIC];
        return rsaParams;
    }
    
    return params;
}

- (void)resetPages {
    [SAUserManager loginOutClearInfo];
    [[SATabBarViewController sharedInstance] restTabBar];
}

- (void)resetPagesJumpLogin {
    [SAUserManager loginOutClearInfo];
    [[SATabBarViewController sharedInstance] restTabBar];
    [SAUserManager isNeedLoginSuccess:nil];
}

@end
