
#import "SABannerCell.h"

@implementation SABannerCell

#pragma mark - Life cycle

- (instancetype)initWithFrame:(CGRect)frame
{
    if (self = [super initWithFrame:frame])
    {
        self.backgroundColor = [UIColor clearColor];
        [self createUI];
    }
    
    return self;
}

- (void)createUI
{
    [self addSubview:self.imgV];
    self.imgV.layer.masksToBounds = YES;
    self.imgV.layer.cornerRadius = 6;
}

- (void)layoutSubviews 
{
    [super layoutSubviews];
    self.imgV.frame = self.bounds;
}


#pragma mark - setter

#pragma mark - getter

- (UIImageView *)imgV{
    FF_Fetch_UIImageViewWithColor(_imgV, [UIColor clearColor])
}

@end
