
#import "SAUserManager.h"


#import "SADeviceInfoSaveApiManager.h"
#import "SANavigationViewController.h"

#import "SALoginViewController.h"

static NSString * const kNPToken        = @"NPUserToken";
static NSString * const kNPPrefixUrl    = @"NPrefixUrl";
static NSString * const kNPUserStatus   = @"NPUserStatus";


@interface SAUserManager ()<QLAPIManagerCallBackDelegate, QLAPIManagerParamSource>

{
    NSString *_authorizationCode;
    BOOL _showAccountOnAppear;
}

@property (nonatomic, strong)SADeviceInfoSaveApiManager *deviceInfoApiMgr;

@property (nonatomic, strong)NSString *accountJson;

@property (nonatomic, copy)LoginSuccessBlock succesBlock;

@end

@implementation SAUserManager

static SAUserManager *instance;
+ (instancetype)sharedInstance {
    static dispatch_once_t once;
    dispatch_once(&once, ^{
        instance = [[self alloc] init];
    });
    return instance;
}


- (void)checkLoginProcess:(LoginSuccessBlock)loginComplete {
    self.succesBlock = loginComplete;
}

#pragma mark - 用户的借款状态

+ (NSInteger)getBorrowStatus{
    return [[NSUserDefaults standardUserDefaults] integerForKey:@"borrowstatus"];
}

+ (void)setBorrowStatus:(NSInteger)status
{
    NSLog(@"set000000---%ld", status);
    [[NSUserDefaults standardUserDefaults] setInteger:status forKey:@"borrowstatus"];
}


#pragma mark - QLAPIManagerCallBackDelegate
- (void)managerCallAPIDidSuccess:(SABaseManager *)manager {
    [SATool hideWindowHUD];
    if (manager == self.deviceInfoApiMgr) {
        if (self.succesBlock) {
            self.succesBlock();
        }
    }
}

- (void)managerCallAPIDidFailed:(SABaseManager *)manager {
    [SATool hideWindowHUD];
    NSString *failMsg  = [SAResponseHandle netWorkErrorMsg:[manager fetchDataWithReformer:nil] andErrorType:manager.errorType];
    [SATool textStateWindowHUD:failMsg];
}

#pragma mark - QLAPIManagerParamSource
- (NSDictionary *)paramsForApi:(SABaseManager *)manager {
    if (manager == self.deviceInfoApiMgr) {
        return @{
                 @"deviceBrand": [SADeviceTool deviceModelName],
                 @"hasRoot": [NSNumber numberWithBool:NO],
                 @"phoneVersion": [SADeviceTool deviceSystemVersion]
                 };
    }
    return [NSDictionary dictionary];
}


#pragma mark - getters


- (SADeviceInfoSaveApiManager *)deviceInfoApiMgr {
    if (_deviceInfoApiMgr == nil) {
        _deviceInfoApiMgr = [[SADeviceInfoSaveApiManager alloc] init];
        _deviceInfoApiMgr.delegate = self;
        _deviceInfoApiMgr.paramSource = self;
    }
    return _deviceInfoApiMgr;
}

#pragma mark - class function
+ (void)deallocUserManger {
    instance = nil;
}

+ (NSString *)getToken
{
    return [[NSUserDefaults standardUserDefaults] objectForKey:kNPToken];
}

+ (void)saveToken:(NSString *)token
{
    if (token.length == 0 || [token isKindOfClass:[NSNull class]]) {
        token = nil;
    }
    [[NSUserDefaults standardUserDefaults] setObject:token forKey:kNPToken];
    [[NSUserDefaults standardUserDefaults] synchronize];
}

+ (NSString *)getJumpPrefix
{
    return [[NSUserDefaults standardUserDefaults] objectForKey:kNPPrefixUrl];
}

+ (void)saveJumpPrefix:(NSString *)jumpPrefix
{
    if (![NSString judgeStringExist:jumpPrefix]) {
        jumpPrefix = nil;
    }
    [[NSUserDefaults standardUserDefaults] setObject:jumpPrefix forKey:kNPPrefixUrl];
    [[NSUserDefaults standardUserDefaults] synchronize];
}

+ (NSInteger)getUserStatus
{
    return [[NSUserDefaults standardUserDefaults] integerForKey:kNPUserStatus];
}

+ (void)saveUserStatus:(NSInteger)status
{
    [[NSUserDefaults standardUserDefaults] setInteger:status forKey:kNPUserStatus];
    [[NSUserDefaults standardUserDefaults] synchronize];
}

+ (BOOL)isLogin {
    if ([self getToken].length == 0) {
        return NO;
    } else {
        return YES;
    }
}

+ (void)loginOutClearInfo {
    [[NSUserDefaults standardUserDefaults] setObject:nil forKey:kNPToken];
    [[NSUserDefaults standardUserDefaults] synchronize];
    
}

+ (void)isNeedLoginSuccess:(LoginSuccessBlock)success {
    if (![self isLogin]) {
        UIViewController *vc = [SALoginViewController new];
        vc.hidesBottomBarWhenPushed = YES;
        [[SACommonTool currentViewController] presentViewController:vc animated:YES completion:nil];
    } else {
        if (success) {
            success();
        }
    }
}

@end
