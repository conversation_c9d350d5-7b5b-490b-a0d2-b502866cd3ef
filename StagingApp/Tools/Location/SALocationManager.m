
#import "SALocationManager.h"
#import <CoreLocation/CoreLocation.h>
#import "SACommonTool.h"
#import "SADeviceTool.h"

@interface SALocationManager ()<CLLocationManagerDelegate>

 
@property (nonatomic, strong) CLLocationManager *locationM;

@property (nonatomic, copy)PositionSuccess success;
@property (nonatomic, copy)PositionFail fail;

@property (nonatomic, assign) BOOL shouldSetting;


@end


@implementation SALocationManager

- (void)startCheckTipLocationResult:(PositionSuccess)successBlock andFail:(PositionFail)failBlock {
    
    self.shouldSetting = YES;

    self.success = successBlock;
    self.fail = failBlock;
    
    [self startLocation];
}

- (void)startDefaultLocationResult:(PositionSuccess)successBlock andFail:(PositionFail)failBlock {
    
    self.shouldSetting = NO;
    self.success = successBlock;
    self.fail = failBlock;
    

    
    if (@available(iOS 11.0, *)) {
        
        [self.locationM requestAlwaysAuthorization];
    }
    [self.locationM requestWhenInUseAuthorization];
    [self.locationM startUpdatingLocation];
}


- (void)startLocation {
    CLAuthorizationStatus status = [CLLocationManager authorizationStatus];
    switch (status) {
        case kCLAuthorizationStatusNotDetermined:
        {
            
            if (@available(iOS 11.0, *)) {
                
                [self.locationM requestAlwaysAuthorization];
            }
            [self.locationM requestWhenInUseAuthorization];
            [self.locationM startUpdatingLocation];
            break;
        }
        case kCLAuthorizationStatusRestricted:
        {
            if (self.fail) {
                self.fail(@"位置服务的活动限制，无法使用位置");
            }
            break;
        }
        case kCLAuthorizationStatusDenied:
        {
            [self jumpOpenLocation];
            break;
        }
        default:
        {
            [self.locationM startUpdatingLocation];
            break;
        }
    }
}

+ (BOOL)locationIsEnable {
    CLAuthorizationStatus status = [CLLocationManager authorizationStatus];
    BOOL result;
    switch (status) {
        case kCLAuthorizationStatusNotDetermined:
        {
            result = NO;
            break;
        }
        case kCLAuthorizationStatusRestricted:
        {
            result = NO;
            break;
        }
        case kCLAuthorizationStatusDenied:
        {
            result = NO;
            break;
        }
        default:
        {
            result = YES;
            break;
        }
    }
    return result;
}

- (void)jumpOpenLocation {
    
    UIAlertController *alertVc = [UIAlertController alertControllerWithTitle:NSLocalizedString(@"ixedFoot", nil)
                                                                     message:NSLocalizedString(@"uploadEviceSelect", nil) preferredStyle:UIAlertControllerStyleAlert];
    UIAlertAction *cancel = [UIAlertAction actionWithTitle:NSLocalizedString(@"serviceCtionDebug", nil) style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
        if (self.fail) {
            self.fail(@"当前操作需要您授权位置信息!");
        }
    }];
    
    UIAlertAction *sure = [UIAlertAction actionWithTitle:NSLocalizedString(@"handleRotocolMasonry", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        NSURL *url;
        if (@available(iOS 10.0, *)) {
            url = [NSURL URLWithString:UIApplicationOpenSettingsURLString];
        } else {
            
            NSString *str = [NSString stringWithFormat:@"prefs:root=LOCATION_SERVICES/%@", [SADeviceTool bundleId]];
            url = [NSURL URLWithString:str];
        }
        if ([[UIApplication sharedApplication] canOpenURL:url]) {
            [[UIApplication sharedApplication] openURL: url];
        }
    }];
    [alertVc addAction:cancel];
    [alertVc addAction:sure];
    
    [[SACommonTool currentViewController] presentViewController:alertVc animated:YES completion:nil];
}

#pragma mark - CLLocationManagerDelegate
- (void)locationManager:(CLLocationManager *)manager didUpdateLocations:(NSArray<CLLocation *> *)locations{
    
    
    [self.locationM stopUpdatingLocation];
    
    
    CLLocation *locat = [locations lastObject];
    
    
    
    
    self.longitude = [NSString stringWithFormat:@"%0.6f", locat.coordinate.longitude];
    self.latitude = [NSString stringWithFormat:@"%0.6f", locat.coordinate.latitude];
    
    
    if (self.success) {
        self.success(self);
    }
    self.success = nil;
     
}

- (void)checkError:(NSInteger)code {
    NSString *errorStr;
    switch (code) {
        case kCLErrorLocationUnknown:
            errorStr = @"目前位置未知,请重试";
            break;
        case kCLErrorDenied:
            if(self.shouldSetting){
                [self jumpOpenLocation];
            }else{
                errorStr = @"用户拒绝访问位置或范围";
            }
            break;
        case kCLErrorNetwork:
            errorStr = @"网络错误，无法获取位置信息";
            break;
        case kCLErrorGeocodeFoundNoResult:
            errorStr = @"地理编码没有结果";
            break;
        case kCLErrorGeocodeFoundPartialResult:
            errorStr = @"地理编码产生一部分结果";
            break;
        default:
            errorStr = @"其它位置错误，无法获取位置信息";
            break;
    }
    
    if (self.fail) {
        self.fail(errorStr);
        self.fail = nil;
    }
}

-(void)locationManager:(CLLocationManager *)manager didFailWithError:(NSError *)error {
    if (self.fail) {
        [self checkError:error.code];
    }
}

#pragma mark - getters
- (CLLocationManager *)locationM {
    if (_locationM == nil) {
        _locationM = [[CLLocationManager alloc] init];
        _locationM.delegate = self;
        _locationM.desiredAccuracy = kCLLocationAccuracyBest;
        _locationM.distanceFilter = 10.0;
    }
    return _locationM;
}

@end
