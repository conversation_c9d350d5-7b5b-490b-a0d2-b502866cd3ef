
#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface SALoanRecordModel : NSObject

@property (nonatomic, strong) NSString *productCode;
@property (nonatomic, strong) NSString *productLogo;
@property (nonatomic, strong) NSString *productName;
@property (nonatomic, strong) NSString *applyDate;
@property (nonatomic, strong) NSString *applyAmount;
@property (nonatomic, strong) NSString *period;
@property (nonatomic, strong) NSString *repaymentAmount;
@property (nonatomic, strong) NSString *repaymentDate;
@property (nonatomic, strong) NSString *repaymentDays;
@property (nonatomic, strong) NSString *overdueDays;
@property (nonatomic, strong) NSString *productJumpUrl;
@property (nonatomic, strong) NSString *orderStatusDesc;
@property (nonatomic, strong) NSString *buttonDesc;

@property (nonatomic, assign) NSInteger orderStatus;
@property (nonatomic, assign) NSInteger auditStatus;


+ (instancetype)objectWithDict:(NSDictionary *)dict;

@end

NS_ASSUME_NONNULL_END
