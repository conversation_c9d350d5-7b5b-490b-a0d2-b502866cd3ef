
#import <Foundation/Foundation.h>
#import "QLNetworkEnum.h"

@interface SAResponse : NSObject

@property (nonatomic, assign, readonly) NPURLResponseStatus status;
@property (nonatomic, copy, readonly)   NSString *contentString;
@property (nonatomic, copy, readonly)   id content;
@property (nonatomic, assign, readonly) NSInteger requestId;
@property (nonatomic, copy, readonly)   NSURLRequest *request;
@property (nonatomic, copy, readonly)   NSData *responseData;
@property (nonatomic, copy)             NSDictionary *requestParams;
@property (nonatomic, strong, readonly) NSError *error;

@property (nonatomic, assign, readonly) BOOL isCache;

- (instancetype)initWithResponseString:(NSString *)responseString
                             requestId:(NSNumber *)requestId
                               request:(NSURLRequest *)request
                          responseData:(NSData *)responseData
                                status:(NPURLResponseStatus)status;

- (instancetype)initWithResponseString:(NSString *)responseString
                             requestId:(NSNumber *)requestId
                               request:(NSURLRequest *)request
                          responseData:(NSData *)responseData
                                 error:(NSError *)error;

- (instancetype)initWithData:(NSData *)data;

@end
