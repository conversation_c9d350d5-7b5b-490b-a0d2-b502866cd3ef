//
//  SACashierPayStatusApi.m
//  StagingApp
//
//  Created by Hardeen on 2024/10/25.
//  Copyright © 2024 Facebook. All rights reserved.
//

#import "SACashierPayStatusApi.h"

@implementation SACashierPayStatusApi

- (instancetype)init
{
    if (self = [super init])
    {
        self.validator = self;
    }
    return self;
}

#pragma mark - CTAPIManager

- (NSString *)methodName
{
    return [self.methodSource methodForApi:self];
}

- (NSString *)serviceType
{
    return kNPBasicServiceKey;
}

- (QLAPIManagerRequestType)requestType
{
    return QLAPIManagerRequestTypeGet;
}

- (BOOL)shouldCache
{
    return NO;
}

#pragma mark - QLAPIManagerDataReformer

- (id)manager:(SABaseManager *)manager reformData:(NSDictionary *)data
{
    if ([manager isKindOfClass:[self class]])
    {
        return [data objectForKey:@"data"];
    }
    return nil;
}

#pragma mark - QLAPIManagerValidator

- (BOOL)manager:(SABaseManager *)manager isCorrectWithParamsData:(NSDictionary *)data
{
    return YES;
}

- (BOOL)manager:(SABaseManager *)manager isCorrectWithCallBackData:(id)data
{
    return [SAResponseHandle processRespDictCodeZero:data];
}
@end
