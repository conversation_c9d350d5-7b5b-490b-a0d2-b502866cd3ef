
#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface SATextModel : NSObject
 
@property (nullable, nonatomic, copy) NSString *code;
 
@property (nullable, nonatomic, copy) NSString *text;
 
@property (nullable, nonatomic, copy) NSArray<SATextModel *> *children;
 
@property (nonatomic, strong) NSString *parentCode;
 
@property (nullable, nonatomic, strong) id extras;
 
@property (nonatomic, assign) NSInteger index;

 
- (instancetype)initWithDictionary:(NSDictionary *)dictionary;

@end


@interface NSArray (BRPickerView)

+ (NSArray *)br_modelArrayWithJson:(NSArray *)dataArr mapper:(nullable NSDictionary *)mapper;

- (NSArray *)br_getValueArr:(NSString *)propertyName;

- (NSString *)br_joinValue:(NSString *)propertyName separator:(NSString *)separator;

- (NSString *)br_joinText:(NSString *)separator;

- (NSArray<SATextModel *> *)br_buildTreeArray;

@end

NS_ASSUME_NONNULL_END
