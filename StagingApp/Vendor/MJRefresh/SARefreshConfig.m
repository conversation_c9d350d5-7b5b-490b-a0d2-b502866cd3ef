
#import "SARefreshConfig.h"
#import "SARefreshConst.h"
#import "SABundle+MJRefresh.h"

@interface SARefreshConfig (Bundle)

+ (void)resetLanguageResourceCache;

@end

@implementation SARefreshConfig

static SARefreshConfig *mj_RefreshConfig = nil;

+ (instancetype)defaultConfig {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        mj_RefreshConfig = [[self alloc] init];
    });
    return mj_RefreshConfig;
}

- (void)setLanguageCode:(NSString *)languageCode {
    if ([languageCode isEqualToString:_languageCode]) {
        return;
    }
    
    _languageCode = languageCode;
    
    [SARefreshConfig resetLanguageResourceCache];
    [NSNotificationCenter.defaultCenter
     postNotificationName:MJRefreshDidChangeLanguageNotification object:self];
}

@end
