
#import "SAViewAttribute.h"
#import "SAConstraint.h"
#import "SALayoutConstraint.h"
#import "MASUtilities.h"

 
@interface SAViewConstraint : SAConstraint <NSCopying>

 
@property (nonatomic, strong, readonly) SAViewAttribute *firstViewAttribute;

 
@property (nonatomic, strong, readonly) SAViewAttribute *secondViewAttribute;

 
- (id)initWithFirstViewAttribute:(SAViewAttribute *)firstViewAttribute;

 
+ (NSArray *)installedConstraintsForView:(MAS_VIEW *)view;

@end
