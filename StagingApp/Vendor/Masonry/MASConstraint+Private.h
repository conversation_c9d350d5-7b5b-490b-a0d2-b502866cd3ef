
#import "SAConstraint.h"

@protocol MASConstraintDelegate;


@interface SAConstraint ()

 
@property (nonatomic, assign) BOOL updateExisting;

 
@property (nonatomic, weak) id<MASConstraintDelegate> delegate;

 
- (void)setLayoutConstantWithValue:(NSValue *)value;

@end


@interface SAConstraint (Abstract)

 
- (SAConstraint * (^)(id, NSLayoutRelation))equalToWithRelation;

 
- (SAConstraint *)addConstraintWithLayoutAttribute:(NSLayoutAttribute)layoutAttribute;

@end


@protocol MASConstraintDelegate <NSObject>

 
- (void)constraint:(SAConstraint *)constraint shouldBeReplacedWithConstraint:(SAConstraint *)replacementConstraint;

- (SAConstraint *)constraint:(SAConstraint *)constraint addConstraintWithLayoutAttribute:(NSLayoutAttribute)layoutAttribute;

@end
