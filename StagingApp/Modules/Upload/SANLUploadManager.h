
#import <Foundation/Foundation.h>
#import "SAUploadModel.h"



NS_ASSUME_NONNULL_BEGIN

typedef void(^NPUploadSuccess)(NSString *path);
typedef void(^NPUploadFailed)(NSError *error);


@interface SANLUploadManager : NSObject

@property (nonatomic, strong) SAUploadModel *upModel;

+ (instancetype)sharedInstance;

- (void)setup;

- (void)uploadObjectAsync:(NSData *)fileData
                  success:(NPUploadSuccess)successBlock
                   failed:(NPUploadFailed)failBlock;

- (void)uploadObjectAsync:(NSData *)fileData
                 fileType:(NPFileType)fileType
                  success:(NPUploadSuccess)successBlock
                   failed:(NPUploadFailed)failBlock;


@end

NS_ASSUME_NONNULL_END
