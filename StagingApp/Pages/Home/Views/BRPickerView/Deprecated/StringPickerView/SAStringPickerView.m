
#import "SAStringPickerView.h"

@interface SAStringPickerView ()<UIPickerViewDelegate, UIPickerViewDataSource>
{
    BOOL _dataSourceException; 
}
 
@property (nonatomic, strong) UIPickerView *pickerView;
 
@property (nonatomic, copy) NSString *mSelectValue;
 
@property (nonatomic, copy) NSArray <NSString *>* mSelectValues;

@property(nonatomic, assign) NSInteger rollingComponent;
@property(nonatomic, assign) NSInteger rollingRow;

 
@property (nullable, nonatomic, copy) NSArray *mDataSourceArr;

@end

@implementation SAStringPickerView

#pragma mark - 1.显示【单列】选择器
+ (void)showPickerWithTitle:(NSString *)title
              dataSourceArr:(NSArray *)dataSourceArr
                selectIndex:(NSInteger)selectIndex
                resultBlock:(BRStringResultModelBlock)resultBlock {
    [self showPickerWithTitle:title dataSourceArr:dataSourceArr selectIndex:selectIndex isAutoSelect:NO resultBlock:resultBlock];
}

#pragma mark - 2.显示【单列】选择器
+ (void)showPickerWithTitle:(NSString *)title
              dataSourceArr:(NSArray *)dataSourceArr
                selectIndex:(NSInteger)selectIndex
               isAutoSelect:(BOOL)isAutoSelect
                resultBlock:(BRStringResultModelBlock)resultBlock {
    
    SAStringPickerView *strPickerView = [[SAStringPickerView alloc]init];
    strPickerView.pickerMode = BRStringPickerComponentSingle;
    strPickerView.title = title;
    strPickerView.dataSourceArr = dataSourceArr;
    strPickerView.selectIndex = selectIndex;
    strPickerView.isAutoSelect = isAutoSelect;
    strPickerView.resultModelBlock = resultBlock;
    
    
    [strPickerView show];
}

#pragma mark - 3.显示【多列】选择器
+ (void)showMultiPickerWithTitle:(NSString *)title
                   dataSourceArr:(NSArray *)dataSourceArr
                    selectIndexs:(NSArray <NSNumber *>*)selectIndexs
                     resultBlock:(BRStringResultModelArrayBlock)resultBlock {
    [self showMultiPickerWithTitle:title dataSourceArr:dataSourceArr selectIndexs:selectIndexs isAutoSelect:NO resultBlock:resultBlock];
}

#pragma mark - 4.显示【多列】选择器
+ (void)showMultiPickerWithTitle:(NSString *)title
                   dataSourceArr:(NSArray *)dataSourceArr
                    selectIndexs:(NSArray <NSNumber *>*)selectIndexs
                    isAutoSelect:(BOOL)isAutoSelect
                     resultBlock:(BRStringResultModelArrayBlock)resultBlock {
    
    SAStringPickerView *strPickerView = [[SAStringPickerView alloc]init];
    strPickerView.pickerMode = BRStringPickerComponentMulti;
    strPickerView.title = title;
    strPickerView.dataSourceArr = dataSourceArr;
    strPickerView.selectIndexs = selectIndexs;
    strPickerView.isAutoSelect = isAutoSelect;
    strPickerView.resultModelArrayBlock = resultBlock;
    
    
    [strPickerView show];
}

#pragma mark - 5.显示【联动】选择器
+ (void)showLinkagePickerWithTitle:(nullable NSString *)title
                     dataSourceArr:(nullable NSArray *)dataSourceArr
                      selectIndexs:(nullable NSArray <NSNumber *> *)selectIndexs
                      isAutoSelect:(BOOL)isAutoSelect
                       resultBlock:(nullable BRStringResultModelArrayBlock)resultBlock {
    
    SAStringPickerView *strPickerView = [[SAStringPickerView alloc]init];
    strPickerView.pickerMode = BRStringPickerComponentLinkage;
    strPickerView.title = title;
    strPickerView.dataSourceArr = dataSourceArr;
    strPickerView.selectIndexs = selectIndexs;
    strPickerView.isAutoSelect = isAutoSelect;
    strPickerView.resultModelArrayBlock = resultBlock;
    
    
    [strPickerView show];
}

#pragma mark - 初始化自定义选择器
- (instancetype)initWithPickerMode:(BRStringPickerMode)pickerMode {
    if (self = [super init]) {
        self.pickerMode = pickerMode;
    }
    return self;
}

#pragma mark - 处理选择器数据
- (void)handlerPickerData {
    if (self.dataSourceArr.count == 0) {
        _dataSourceException = YES;
    }
    id item = [self.dataSourceArr firstObject];
    if (self.pickerMode == BRStringPickerComponentSingle) {
        _dataSourceException = [item isKindOfClass:[NSArray class]];
    } else if (self.pickerMode == BRStringPickerComponentMulti) {
        _dataSourceException = [item isKindOfClass:[NSString class]];
    } else if (self.pickerMode == BRStringPickerComponentLinkage) {
        _dataSourceException = ![item isKindOfClass:[SAResultModel class]];
    }
    if (_dataSourceException) {
        BRErrorLog(@"数据源异常！请检查选择器数据源的格式");
        return;
    }
    
    
    if (self.pickerMode == BRStringPickerComponentSingle) {
        self.mDataSourceArr = self.dataSourceArr;
        NSInteger selectIndex = 0;
        if (self.selectIndex > 0 && self.selectIndex < self.mDataSourceArr.count) {
            selectIndex = self.selectIndex;
        } else {
            if (self.mSelectValue) {
                id item = [self.mDataSourceArr firstObject];
                if ([item isKindOfClass:[SAResultModel class]]) {
                    for (NSInteger i = 0; i < self.mDataSourceArr.count; i++) {
                        SAResultModel *model = self.mDataSourceArr[i];
                        if ([model.value isEqualToString:self.mSelectValue]) {
                            selectIndex = i;
                            break;
                        }
                    }
                } else {
                    if ([self.mDataSourceArr containsObject:self.mSelectValue]) {
                        selectIndex = [self.mDataSourceArr indexOfObject:self.mSelectValue];
                    }
                }
            }
        }
        self.selectIndex = selectIndex;
        
    } else if (self.pickerMode == BRStringPickerComponentMulti) {
        self.mDataSourceArr = self.dataSourceArr;
        NSMutableArray *selectIndexs = [[NSMutableArray alloc]init];
        for (NSInteger i = 0; i < self.mDataSourceArr.count; i++) {
            NSArray *itemArr = self.mDataSourceArr[i];
            NSInteger row = 0;
            if (self.selectIndexs.count > 0) {
                if (i < self.selectIndexs.count) {
                    NSInteger index = [self.selectIndexs[i] integerValue];
                    row = ((index > 0 && index < itemArr.count) ? index : 0);
                }
            } else {
                if (self.mSelectValues.count > 0 && i < self.mSelectValues.count) {
                    NSString *value = self.mSelectValues[i];
                    id item = [itemArr firstObject];
                    if ([item isKindOfClass:[SAResultModel class]]) {
                        for (NSInteger j = 0; j < itemArr.count; j++) {
                            SAResultModel *model = itemArr[j];
                            if ([model.value isEqualToString:value]) {
                                row = j;
                                break;
                            }
                        }
                    } else {
                        if ([itemArr containsObject:value]) {
                            row = [itemArr indexOfObject:value];
                        }
                    }
                }
            }
            [selectIndexs addObject:@(row)];
        }
        self.selectIndexs = [selectIndexs copy];
        
    } else if (self.pickerMode == BRStringPickerComponentLinkage) {
        
        NSMutableArray *selectIndexs = [[NSMutableArray alloc]init];
        NSMutableArray *mDataSourceArr = [[NSMutableArray alloc]init];
        
        SAResultModel *selectModel = nil;
        BOOL hasNext = YES;
        NSInteger i = 0;

        NSMutableArray *dataArr = [self.dataSourceArr mutableCopy];
        
        do {
            NSArray *nextArr = [self getNextDataArr:dataArr selectModel:selectModel];
            
            if (nextArr.count == 0 || i > self.numberOfComponents - 1) {
                hasNext = NO;
                break;
            }
            
            NSInteger selectIndex = 0;
            if (self.selectIndexs.count > i && [self.selectIndexs[i] integerValue] < nextArr.count) {
                selectIndex = [self.selectIndexs[i] integerValue];
            }
            selectModel = nextArr[selectIndex];
            
            [selectIndexs addObject:@(selectIndex)];
            [mDataSourceArr addObject:nextArr];

            i++;
            
        } while (hasNext);
        
        self.selectIndexs = [selectIndexs copy];
        self.mDataSourceArr = [mDataSourceArr copy];
    }
}

- (NSArray <SAResultModel *>*)getNextDataArr:(NSArray *)dataArr selectModel:(SAResultModel *)selectModel {
    NSMutableArray *tempArr = [[NSMutableArray alloc]init];
    
    NSString *key = selectModel ? selectModel.key : @"-1";
    for (SAResultModel *model in dataArr) {
        if ([model.parentKey isEqualToString:key]) {
            [tempArr addObject:model];
        }
    }
    return [tempArr copy];
}

#pragma mark - 选择器
- (UIPickerView *)pickerView {
    if (!_pickerView) {
        CGFloat pickerHeaderViewHeight = self.pickerHeaderView ? self.pickerHeaderView.bounds.size.height : 0;
        _pickerView = [[UIPickerView alloc]initWithFrame:CGRectMake(0, self.pickerStyle.titleBarHeight + pickerHeaderViewHeight, self.keyView.bounds.size.width, self.pickerStyle.pickerHeight)];
        _pickerView.backgroundColor = self.pickerStyle.pickerColor;
        _pickerView.autoresizingMask = UIViewAutoresizingFlexibleBottomMargin | UIViewAutoresizingFlexibleRightMargin | UIViewAutoresizingFlexibleWidth;
        _pickerView.dataSource = self;
        _pickerView.delegate = self;
    }
    return _pickerView;
}

#pragma mark - UIPickerViewDataSource
- (NSInteger)numberOfComponentsInPickerView:(UIPickerView *)pickerView {
    switch (self.pickerMode) {
        case BRStringPickerComponentSingle:
            return 1;
        case BRStringPickerComponentMulti:
        case BRStringPickerComponentLinkage:
            if (self.pickerStyle.columnSpacing > 0) {
                return self.mDataSourceArr.count * 2 - 1;
            }
            return self.mDataSourceArr.count;
            
        default:
            break;
    }
}

- (NSInteger)pickerView:(UIPickerView *)pickerView numberOfRowsInComponent:(NSInteger)component {
    switch (self.pickerMode) {
        case BRStringPickerComponentSingle:
            return self.mDataSourceArr.count;
            break;
        case BRStringPickerComponentMulti:
        case BRStringPickerComponentLinkage:
        {
            if (self.pickerStyle.columnSpacing > 0) {
                if (component % 2 == 1) {
                    return 1;
                } else {
                    component = component / 2;
                }
            }
            NSArray *itemArr = self.mDataSourceArr[component];
            return itemArr.count;
        }
            break;
            
        default:
            break;
    }
}

#pragma mark - UIPickerViewDelegate
- (UIView *)pickerView:(UIPickerView *)pickerView viewForRow:(NSInteger)row forComponent:(NSInteger)component reusingView:(nullable UIView *)view {
    
    UILabel *label = (UILabel *)view;
    if (!label) {
        label = [[UILabel alloc]init];
        label.backgroundColor = [UIColor clearColor];
        label.textAlignment = NSTextAlignmentCenter;
        label.font = self.pickerStyle.pickerTextFont;
        label.textColor = self.pickerStyle.pickerTextColor;
        label.numberOfLines = self.pickerStyle.maxTextLines;
        
        label.adjustsFontSizeToFitWidth = YES;
        
        label.minimumScaleFactor = 0.5f;
    }
    
    
    [self.pickerStyle setupPickerSelectRowStyle:pickerView titleForRow:row forComponent:component];
    
    
    
    NSInteger selectRow = [pickerView selectedRowInComponent:component];
    if (selectRow >= 0) {
        self.rollingComponent = component;
        self.rollingRow = selectRow;
    }

    
    if (self.pickerMode == BRStringPickerComponentSingle) {
        id item = self.mDataSourceArr[row];
        if ([item isKindOfClass:[SAResultModel class]]) {
            SAResultModel *model = (SAResultModel *)item;
            label.text = model.value;
        } else {
            label.text = item;
        }
    } else if (self.pickerMode == BRStringPickerComponentMulti || self.pickerMode == BRStringPickerComponentLinkage) {
        
        
        if (self.pickerStyle.columnSpacing > 0) {
            if (component % 2 == 1) {
                label.text = @"";
                return label;
            } else {
                component = component / 2;
            }
        }
        
        NSArray *itemArr = self.mDataSourceArr[component];
        id item = [itemArr objectAtIndex:row];
        if ([item isKindOfClass:[SAResultModel class]]) {
            SAResultModel *model = (SAResultModel *)item;
            label.text = model.value;
        } else {
            label.text = item;
        }
    }
    
    return label;
}

- (BOOL)getRollingStatus:(UIView *)view {
    if ([view isKindOfClass:[UIScrollView class]]) {
        UIScrollView *scrollView = (UIScrollView *)view;
        if (scrollView.dragging || scrollView.decelerating) {
            
            return YES;
        }
    }
    
    for (UIView *subView in view.subviews) {
        if ([self getRollingStatus:subView]) {
            return YES;
        }
    }
    
    return NO;
}

- (BOOL)isRolling {
    return [self getRollingStatus:self.pickerView];
}

- (void)pickerView:(UIPickerView *)pickerView didSelectRow:(NSInteger)row inComponent:(NSInteger)component {
    switch (self.pickerMode) {
        case BRStringPickerComponentSingle:
        {
            self.selectIndex = row;
            
            
            if (self.changeModelBlock) {
                self.changeModelBlock([self getResultModel]);
            }
            
            
            if (self.isAutoSelect) {
                if (self.resultModelBlock) {
                    self.resultModelBlock([self getResultModel]);
                }
            }
        }
            break;
        case BRStringPickerComponentMulti:
        {
            
            if (self.pickerStyle.columnSpacing > 0) {
                if (component % 2 == 1) {
                    return;
                } else {
                    component = component / 2;
                }
            }
            
            if (component < self.selectIndexs.count) {
                NSMutableArray *mutableArr = [self.selectIndexs mutableCopy];
                [mutableArr replaceObjectAtIndex:component withObject:@(row)];
                self.selectIndexs = [mutableArr copy];
            }
            
            
            if (self.changeModelArrayBlock) {
                self.changeModelArrayBlock([self getResultModelArr]);
            }
            
            
            if (self.isAutoSelect) {
                if (self.resultModelArrayBlock) {
                    self.resultModelArrayBlock([self getResultModelArr]);
                }
            }
        }
            break;
        case BRStringPickerComponentLinkage:
        {
            
            if (self.pickerStyle.columnSpacing > 0) {
                if (component % 2 == 1) {
                    return;
                } else {
                    component = component / 2;
                }
            }
            
            if (component < self.selectIndexs.count) {
                NSMutableArray *selectIndexs = [[NSMutableArray alloc]init];
                for (NSInteger i = 0; i < self.selectIndexs.count; i++) {
                    if (i < component) {
                        [selectIndexs addObject:self.selectIndexs[i]];
                    } else if (i == component) {
                        [selectIndexs addObject:@(row)];
                    } else {
                        [selectIndexs addObject:@(0)];
                    }
                }
                self.selectIndexs = [selectIndexs copy];
            }
            
            
            [self reloadData];
            
            
            if (self.changeModelArrayBlock) {
                self.changeModelArrayBlock([self getResultModelArr]);
            }
            
            
            if (self.isAutoSelect) {
                if (self.resultModelArrayBlock) {
                    self.resultModelArrayBlock([self getResultModelArr]);
                }
            }
        }
            break;
            
        default:
            break;
    }
}

#pragma mark - 获取【单列】选择器选择的值
- (SAResultModel *)getResultModel {
    id item = self.selectIndex < self.mDataSourceArr.count ? self.mDataSourceArr[self.selectIndex] : nil;
    if ([item isKindOfClass:[SAResultModel class]]) {
        SAResultModel *model = (SAResultModel *)item;
        model.index = self.selectIndex;
        return model;
    } else {
        SAResultModel *model = [[SAResultModel alloc]init];
        model.index = self.selectIndex;
        model.value = item;
        return model;
    }
}

#pragma mark - 获取【多列】选择器选择的值
- (NSArray *)getResultModelArr {
    NSMutableArray *resultModelArr = [[NSMutableArray alloc]init];
    for (NSInteger i = 0; i < self.mDataSourceArr.count; i++) {
        NSInteger index = [self.selectIndexs[i] integerValue];
        NSArray *dataArr = self.mDataSourceArr[i];
        
        id item = index < dataArr.count ? dataArr[index] : nil;
        if ([item isKindOfClass:[SAResultModel class]]) {
            SAResultModel *model = (SAResultModel *)item;
            model.index = index;
            [resultModelArr addObject:model];
        } else {
            SAResultModel *model = [[SAResultModel alloc]init];
            model.index = index;
            model.value = item;
            [resultModelArr addObject:model];
        }
    }
    return [resultModelArr copy];
}

- (CGFloat)pickerView:(UIPickerView *)pickerView rowHeightForComponent:(NSInteger)component {
    return self.pickerStyle.rowHeight;
}

- (CGFloat)pickerView:(UIPickerView *)pickerView widthForComponent:(NSInteger)component {
    if (self.pickerStyle.columnSpacing > 0 && component % 2 == 1) {
        return self.pickerStyle.columnSpacing;
    }
    NSInteger columnCount = [self numberOfComponentsInPickerView:pickerView];
    CGFloat columnWidth = self.pickerView.bounds.size.width / columnCount;
    if (self.pickerStyle.columnWidth > 0 && self.pickerStyle.columnWidth <= columnWidth) {
        return self.pickerStyle.columnWidth;
    }
    return columnWidth;
}

#pragma mark - 重写父类方法
- (void)reloadData {
    
    [self handlerPickerData];
    
    [self.pickerView reloadAllComponents];
    
    if (self.pickerMode == BRStringPickerComponentSingle) {
        [self.pickerView selectRow:self.selectIndex inComponent:0 animated:self.selectRowAnimated];
    } else if (self.pickerMode == BRStringPickerComponentMulti || self.pickerMode == BRStringPickerComponentLinkage) {
        for (NSInteger i = 0; i < self.selectIndexs.count; i++) {
            NSNumber *row = [self.selectIndexs objectAtIndex:i];
            NSInteger component = i;
            if (self.pickerStyle.columnSpacing > 0) {
                component = i * 2;
            }
            [self.pickerView selectRow:[row integerValue] inComponent:component animated:self.selectRowAnimated];
        }
    }
}

- (void)addPickerToView:(UIView *)view {
    
    if (view) {
        
        [view setNeedsLayout];
        [view layoutIfNeeded];
        
        self.frame = view.bounds;
        CGFloat pickerHeaderViewHeight = self.pickerHeaderView ? self.pickerHeaderView.bounds.size.height : 0;
        CGFloat pickerFooterViewHeight = self.pickerFooterView ? self.pickerFooterView.bounds.size.height : 0;
        self.pickerView.frame = CGRectMake(0, pickerHeaderViewHeight, view.bounds.size.width, view.bounds.size.height - pickerHeaderViewHeight - pickerFooterViewHeight);
        [self addSubview:self.pickerView];
    } else {
        
        CGFloat pickerHeaderViewHeight = self.pickerHeaderView ? self.pickerHeaderView.bounds.size.height : 0;
        self.pickerView.frame = CGRectMake(0, self.pickerStyle.titleBarHeight + pickerHeaderViewHeight, self.keyView.bounds.size.width, self.pickerStyle.pickerHeight);
        
        [self.alertView addSubview:self.pickerView];
    }
    
    
    if (self.pickerStyle.clearPickerNewStyle) {
        [self.pickerStyle addSeparatorLineView:self.pickerView];
    }
    
    
    [self reloadData];
    
    __weak typeof(self) weakSelf = self;
    
    self.doneBlock = ^{
        if (weakSelf.isRolling) {
            NSLog(@"选择器滚动还未结束");
            
            
            [weakSelf pickerView:weakSelf.pickerView didSelectRow:weakSelf.rollingRow inComponent:weakSelf.rollingComponent];
        }
    
        
        if (weakSelf.pickerMode == BRStringPickerComponentSingle) {
            if (weakSelf.resultModelBlock) {
                weakSelf.resultModelBlock([weakSelf getResultModel]);
            }
        } else if (weakSelf.pickerMode == BRStringPickerComponentMulti || weakSelf.pickerMode == BRStringPickerComponentLinkage) {
            if (weakSelf.resultModelArrayBlock) {
                weakSelf.resultModelArrayBlock([weakSelf getResultModelArr]);
            }
        }
    };
    
    [super addPickerToView:view];
}

#pragma mark - 重写父类方法
- (void)addSubViewToPicker:(UIView *)customView {
    [self.pickerView addSubview:customView];
}

#pragma mark - 弹出选择器视图
- (void)show {
    [self addPickerToView:nil];
}

#pragma mark - 关闭选择器视图
- (void)dismiss {
    [self removePickerFromView:nil];
}

#pragma mark - setter 方法
- (void)setPlistName:(NSString *)plistName {
    NSString *path = [[NSBundle mainBundle] pathForResource:plistName ofType:nil];
    if (path && path.length > 0) {
        self.dataSourceArr = [[NSArray alloc] initWithContentsOfFile:path];
    }
}

- (void)setSelectValue:(NSString *)selectValue {
    self.mSelectValue = selectValue;
}

- (void)setSelectValues:(NSArray<NSString *> *)selectValues {
    self.mSelectValues = selectValues;
}

#pragma mark - getter 方法
- (NSArray *)mDataSourceArr {
    if (!_mDataSourceArr) {
        _mDataSourceArr = [NSArray array];
    }
    return _mDataSourceArr;
}

- (NSArray<NSNumber *> *)selectIndexs {
    if (!_selectIndexs) {
        _selectIndexs = [NSArray array];
    }
    return _selectIndexs;
}

- (NSArray<NSString *> *)mSelectValues {
    if (!_mSelectValues) {
        _mSelectValues = [NSArray array];
    }
    return _mSelectValues;
}

- (NSInteger)numberOfComponents {
    if (_numberOfComponents <= 0) {
        _numberOfComponents = 3;
    }
    return _numberOfComponents;
}

@end
