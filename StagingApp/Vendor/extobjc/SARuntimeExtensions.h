
#import <objc/runtime.h>

 
typedef enum {
     
    mtl_propertyMemoryManagementPolicyAssign = 0,

     
    mtl_propertyMemoryManagementPolicyRetain,

     
    mtl_propertyMemoryManagementPolicyCopy
} mtl_propertyMemoryManagementPolicy;

 
typedef struct {
     
    <PERSON><PERSON><PERSON> readonly;

     
    BOOL nonatomic;

     
    BOOL weak;

     
    BOOL canBeCollected;

     
    BOOL dynamic;

     
    mtl_propertyMemoryManagementPolicy memoryManagementPolicy;

     
    SEL getter;

     
    SEL setter;

     
    const char *ivar;

     
    Class objectClass;

     
    char type[];
} mtl_propertyAttributes;

 
mtl_propertyAttributes *mtl_copyPropertyAttributes (objc_property_t property);
