
#import <Foundation/Foundation.h>
#import "SAHomeProtocolModel.h"
#import "SARepaymentModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface SAClientChargesModel : NSObject

@property (nonatomic, copy)NSString *tip;
@property (nonatomic, copy)NSString *voucherTips;
@property (nonatomic, strong)SARepaymentModel *repaymentVo;
@property (nonatomic, strong)NSArray <SAHomeProtocolModel *>*protocolUrls;

+ (instancetype)objectWithDict:(NSDictionary *)dict;


@property (nonatomic, assign)CGFloat repaymentMiddleHeight;      

@property (nonatomic, assign)CGFloat repaymentFootHeight;        

@end

NS_ASSUME_NONNULL_END
