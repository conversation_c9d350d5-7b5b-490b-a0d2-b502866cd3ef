
#import <Foundation/Foundation.h>
#import "SAPersonalInfoCheckModel.h"

NS_ASSUME_NONNULL_BEGIN

@class SAPersonalInfoCheckModel;

@interface SAPersonalInfoModel : NSObject

@property (nonatomic, copy)NSString *paramName;
@property (nonatomic, copy)NSString *param;
@property (nonatomic, assign)NSInteger paramType;        
@property (nonatomic, copy)NSString *inputDesc;
@property (nonatomic, copy)NSString *inputValue;
@property (nonatomic, assign)BOOL requied;
@property (nonatomic, assign)BOOL readOnly;
@property (nonatomic, assign)BOOL show;
@property (nonatomic, assign)BOOL number;
@property (nonatomic, strong)NSArray <SAPersonalInfoCheckModel *>*selectVo;


@property (nonatomic, assign)CGFloat titleW;         
@property (nonatomic, assign)CGFloat cellHeight;         

 
@property (nonatomic, copy)NSString *inputStr;

 
@property (nonatomic, strong)SAPersonalInfoCheckModel *checkModel;

+ (NSMutableArray *)arrayTransformToModels:(NSArray *)array;

@end

NS_ASSUME_NONNULL_END
