
#import "SANLUploadManager.h"
#import "SAGetFileTokenApiManager.h"
#import "SAFileUploadApiManager.h"
#import "SAAliyunOSSManager.h"
#import "SAS3Manager.h"

@interface SANLUploadManager ()<QLAPIManagerCallBackDelegate, QLAPIManagerParamSource, NPRequestMethodProtocol>


@property (nonatomic, strong) SAGetFileTokenApiManager *infoManager;
@property (nonatomic, strong) SAFileUploadApiManager *upManager;

@property (nonatomic, strong) NSData *fileData;

@property (nonatomic, assign) NPFileType fileType;


@property (nonatomic, copy)   NPUploadSuccess   successBlock;
@property (nonatomic, copy)   NPUploadFailed    failedBlock;

@end

@implementation SANLUploadManager

+ (instancetype)sharedInstance
{
    static dispatch_once_t once;
    static id instance;
    dispatch_once(&once, ^{
        instance = [self new];
    });
    return instance;
}

#pragma mark - setup

- (void)setup
{
}

#pragma mark - 上传

- (void)uploadObjectAsync:(NSData *)fileData success:(NPUploadSuccess)successBlock failed:(NPUploadFailed)failBlock
{
    self.fileData = fileData;
    self.successBlock = successBlock;
    self.failedBlock =  failBlock;

    [self.infoManager loadData];
}

-  (void)uploadObjectAsync:(NSData *)fileData fileType:(NPFileType)fileType success:(NPUploadSuccess)successBlock failed:(NPUploadFailed)failBlock
{
    self.fileType = fileType;
    [self uploadObjectAsync:fileData success:successBlock failed:failBlock];
}

#pragma mark - 华为云上传

- (void)uploadHWS:(NSData *)fileData
{
    
    NSURL *url = [NSURL URLWithString:self.upModel.baseUrl];
    
    
    NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:url];
    [request setHTTPMethod:@"PUT"];
    
    [request setValue:@"multipart/form-data" forHTTPHeaderField:@"Content-Type"];
    
    NSURLSession *session = [NSURLSession sharedSession];
    
    
    NSURLSessionUploadTask *uploadTask = [session uploadTaskWithRequest:request
                                                               fromData:fileData
                                                      completionHandler:^(NSData *data, NSURLResponse *response, NSError *error) {
        
                if (error) {
                    NSLog(@"Error: %@", error);
                    [SATool textStateWindowHUD:@"上传失败，请重试！"];
                    self.failedBlock(error);
                } else {
                    if (data != nil) {
                        NSString *content = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
                        NSLog(@"Content: %@", content);
                    }
                    
                    NSLog(@"PutObject successfully");
                    self.successBlock(self.upModel.objUrl);
                }
    }];
    
    
    [uploadTask resume];
}

#pragma mark QLAPIManagerCallBackDelegate

- (void)managerCallAPIDidSuccess:(SABaseManager *)manager
{
    if (manager == self.infoManager)
    {
        NSDictionary *dataDict = [manager fetchDataWithReformer:self.infoManager];
        self.upModel = [SAUploadModel objectWithDict:dataDict];
        if(self.upModel.clientType == NPUploadType_AWS){
            [[SAS3Manager sharedInstance] setup];
        }
        
        switch (self.upModel.clientType) {
            case NPUploadType_OSS:
            {
                [[SAAliyunOSSManager sharedInstance] uploadObjectAsync:self.fileData success:^(NSDictionary * _Nonnull dict) {
                    self.successBlock(dict[kFileURL]);
                } failed:^(NSError * _Nonnull error) {
                    self.failedBlock(error);
                }];
            }
                break;
            case NPUploadType_AWS:
            {
                [[SAS3Manager sharedInstance] uploadObjectAsync:self.fileData success:^(NSString *path) {
                    self.successBlock(path);
                } failed:^(NSError * _Nonnull error) {
                    self.failedBlock(error);
                }];
            }
                break;
            case NPUploadType_Ser:
                [self.upManager loadData];
                break;
            case NPUploadType_HW:
                [self uploadHWS: self.fileData];
                break;
            default:
                break;
        }
    }
    
    if(manager == self.upManager)
    {
        NSDictionary *dataDict = [manager fetchDataWithReformer:self.upManager];
        NSLog(@"服务端上传---%@", dataDict);
        if(self.successBlock){
            self.successBlock(dataDict[@"filePath"]);
        }
    }
}

- (void)managerCallAPIDidFailed:(SABaseManager *)manager
{
    [SATool hideWindowHUD];

    NSString *failMsg  = [SAResponseHandle netWorkErrorMsg:[manager fetchDataWithReformer:nil] andErrorType:manager.errorType];
    [SATool textStateWindowHUD:failMsg];
}

#pragma mark QLAPIManagerParamSource

- (NSDictionary *)paramsForApi:(SABaseManager *)manager
{
    return [NSDictionary dictionary];
}

- (id)dataForUploadApi:(SABaseManager *)manager
{
    return self.fileData;
}

- (NSString *)methodForApi:(SABaseManager *)manager
{
    if (manager == self.infoManager) {
        return [NSString stringWithFormat:@"a42a7c0a5e1b4ae49806c2a1baf900ca/%ld", self.fileType];
    }
    return @"";
}

#pragma mark - getter

- (SAGetFileTokenApiManager *)infoManager{
    if(_infoManager == nil){
        _infoManager = [SAGetFileTokenApiManager new];
        _infoManager.delegate = self;
        _infoManager.paramSource = self;
        _infoManager.methodSource = self;
    }
    return _infoManager;
}

- (SAFileUploadApiManager *)upManager
{
    if(_upManager == nil){
        _upManager = [SAFileUploadApiManager new];
        _upManager.delegate = self;
        _upManager.paramSource = self;
    }
    return _upManager;
}

@end
