
#import <Foundation/Foundation.h>
#import "SAHomeProtocolUrlsModel.h"
#import "SAOrderInfoModel.h"
#import "SABorrowPurposeTypeModel.h"
#import "SABorrowCashModel.h"
#import "SAHomeBankModel.h"
#import "SABannerModel.h"
#import "NPHomeStepModel.h"

NS_ASSUME_NONNULL_BEGIN



 
static NSString * const State_WaitBorrow = @"APP/RenderTemplate/Product/Home";

static NSString * const State_AuditProgress = @"APP/RenderTemplate/Product/Audit";

static NSString * const State_LoanFail = @"APP/RenderTemplate/Product/PayFail";

static NSString * const State_WaitRepayment = @"APP/RenderTemplate/Product/RepaymentWatting";

static NSString * const State_OverduePay = @"APP/RenderTemplate/Product/Overdue";

static NSString * const State_AuditFail = @"APP/RenderTemplate/Product/AuditFail";

static NSString * const State_WaitMoney = @"APP/RenderTemplate/Product/LoanWaitting";

static NSString * const State_RepaySuccess = @"APP/RenderTemplate/Product/RepaymentSuccess";

static NSString * const State_ConfirmTrade = @"APP/RenderTemplate/Product/ConfirmTrade";

static NSString * const State_WaitSign = @"APP/RenderTemplate/Product/WaitSign";

static NSString * const State_CanSubmit = @"APP/RenderTemplate/Product/CanSubmit";


@interface SAHomeModel : NSObject

@property (nonatomic, copy)NSString *bigTips;
@property (nonatomic, copy)NSString *borrowProtocolUrl;
 
@property (nonatomic, assign)NSInteger borrowStatus;
@property (nonatomic, copy)NSString *borrowStatusText;
@property (nonatomic, copy)NSString *buttonJumpUrl;
@property (nonatomic, copy)NSString *buttonTxt;
@property (nonatomic, copy)NSString *centerBorrowStatusLogo;
@property (nonatomic, copy)NSString *centerBorrowStatusText;
@property (nonatomic, copy)NSString *errorTips;
@property (nonatomic, copy)NSString *leftBorrowStatusLogo;
@property (nonatomic, copy)NSString *leftBorrowStatusText;
@property (nonatomic, copy)NSString *rightBorrowStatusLogo;
@property (nonatomic, copy)NSString *rightBorrowStatusText;
@property (nonatomic, copy)NSString *secondButtonJumpUrl;
@property (nonatomic, copy)NSString *secondButtonTxt;
@property (nonatomic, copy)NSString *templateCode;
@property (nonatomic, copy)NSString *tips;
@property (nonatomic, strong) NSString *miniTips;
@property (nonatomic, strong) NSString *productDescTxt;
@property (nonatomic, assign) NSInteger auditCountDown;
@property (nonatomic, assign) NSInteger unfreezeDays;
@property (nonatomic, strong) NSString *amount;
@property (nonatomic, strong) NSString *period;
@property (nonatomic, strong) NSString *interest;
@property (nonatomic, strong) NSString *tradeNo;

@property (nonatomic, strong) NSString *productLikeUrl;
@property (nonatomic, assign) BOOL voucherButtonStatus;
@property (nonatomic, strong) NSString *voucherButtonTxt;

@property (nonatomic, assign) NSInteger showMaxProduct;
@property (nonatomic, strong) NSString *maxAmount;

@property (nonatomic, strong) NSArray *homeMessages;
@property (nonatomic, copy)   NSString *forceDialogTitle;
@property (nonatomic, copy)   NSString *forceDialogContent;

@property (nonatomic, strong)NSArray <SABorrowPurposeTypeModel *>*borrowPurposeList;
@property (nonatomic, strong)NSArray <SABorrowCashModel *>*borrowCashList;
@property (nonatomic, strong) SAOrderInfoModel  *orderInfo;
@property (nonatomic, strong) SAHomeProtocolUrlsModel  *protocolUrls;
@property (nonatomic, strong) SAHomeBankModel *bankInfoVo;

@property (nonatomic, strong) NSArray <SABannerModel *>*slideshowVOList;
@property (nonatomic, strong) NSArray <NPHomeStepModel *>*borrowStepVOList;
@property (nonatomic, strong) SABannerModel *refuseSlideshow;

 
@property (nonatomic, assign)CGFloat cellHeight;

+ (instancetype)objectWithDict:(NSDictionary *)dict;

@end

NS_ASSUME_NONNULL_END
