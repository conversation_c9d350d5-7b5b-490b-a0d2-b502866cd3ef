//
//  SAPayStatusViewController.m
//  StagingApp
//
//  Created by Hardeen on 2024/10/25.
//  Copyright © 2024 Facebook. All rights reserved.
//

#import "SAPayStatusViewController.h"
#import "SACashierPayStatusApi.h"

@interface SAPayStatusViewController ()<QLAPIManagerParamSource, QLAPIManagerCallBackDelegate, NPRequestMethodProtocol>

@property (nonatomic, strong) SACashierPayStatusApi *statusApi;

@property (nonatomic, strong) UIImageView *statusImg;
@property (nonatomic, strong) UILabel *statusLab;
@property (nonatomic, strong) SAGradientButton *reviewBtn;

@end

@implementation SAPayStatusViewController

- (void)viewDidAppear:(BOOL)animated
{
    [super viewDidAppear:animated];
    
    self.navigationController.interactivePopGestureRecognizer.enabled = NO;
}

- (void)viewDidDisappear:(BOOL)animated
{
    [super viewDidDisappear:animated];
    
    self.navigationController.interactivePopGestureRecognizer.enabled = YES;
}

- (void)viewWillAppear:(BOOL)animated
{
    [super viewWillAppear:animated];
    [self.navigationController setNavigationBarHidden:YES animated:YES];
}

- (void)viewDidLoad
{ 
    [super viewDidLoad];

    self.view.backgroundColor = [UIColor colorWithHex:0xf6f6f6];
    [self createUI];
    
    [self showActivityHUD:nil];
    [self.statusApi loadData];
}

- (void)createUI
{
    [self.view addSubview:self.statusImg];
    [self.view addSubview:self.statusLab];
    
    [self.view addSubview:self.reviewBtn];
    
    [self.statusImg mas_makeConstraints:^(SAConstraintMaker *make) {
        make.width.height.mas_equalTo(60);
        make.centerX.equalTo(self.view);
        make.top.equalTo(self.view).offset(100);
    }];
    [self.statusLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.centerX.equalTo(self.view);
        make.top.equalTo(self.statusImg.mas_bottom).offset(20);
    }];
    
    
    [self.reviewBtn mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.equalTo(self.view).offset(20);
        make.centerX.equalTo(self.view);
        make.top.equalTo(self.statusLab.mas_bottom).offset(30);
        make.height.mas_equalTo(48);
    }];
    self.reviewBtn.layer.masksToBounds = YES;
    self.reviewBtn.layer.cornerRadius = 7;

}

#pragma mark - Action

- (void)reviewAction
{
    [self.navigationController popToRootViewControllerAnimated:YES];
}

#pragma mark - QLAPIManagerCallBackDelegate

- (void)managerCallAPIDidSuccess:(SABaseManager *)manager
{
    [self hideHUDView];
    
    if(manager == self.statusApi)
    {
        NSDictionary *dataDict = [manager fetchDataWithReformer:self.statusApi];
        NSInteger payStatus = [dataDict[@"payStatus"] integerValue];
        if(payStatus == 2){
            self.statusImg.image = [UIImage imageNamed:@"success"];
            self.statusLab.text = @"支付成功，正在为您跳转，请稍等...";
            self.statusLab.textColor = [UIColor greenColor];
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [self reviewAction];
            });
        }else if (payStatus == 3){
            self.statusImg.image = [UIImage imageNamed:@"failed"];
            self.statusLab.text = [NSString stringWithFormat:@"%@, 支付失败", dataDict[@"errorMsg"]];
            self.statusLab.textColor = [UIColor redColor];
        }else{
            
            NSString *path = [[NSBundle mainBundle] pathForResource:@"pay_loading" ofType:@"gif"];
            NSData * data = [NSData dataWithContentsOfFile:path];
            UIImage *image = [UIImage sd_imageWithGIFData:data];
            self.statusImg.image = image;
            self.statusLab.text = @"支付中，请稍等...";
            self.statusLab.textColor = [UIColor colorWithHex:0x333333];

            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [self.statusApi loadData];
            });

        }
    }

}

- (void)managerCallAPIDidFailed:(SABaseManager *)manager
{
    [self hideHUDView];
    
    NSString *failMsg  = [SAResponseHandle netWorkErrorMsg:[manager fetchDataWithReformer:nil] andErrorType:manager.errorType];
    [SATool textStateHUD:failMsg];
}

#pragma mark - QLAPIManagerParamSource

- (NSDictionary *)paramsForApi:(SABaseManager *)manager
{
    return [NSDictionary dictionary];
}

- (NSString *)methodForApi:(SABaseManager *)manager
{
    if(manager == self.statusApi)
    {
//        return [NSString stringWithFormat:@"cashier/pay/status/%@", self.key];
        return [NSString stringWithFormat:@"8d4295c8a823401dbfdd2e092aa0015e/%@", self.key];
    }
    
    return @"";
}

#pragma mark - getter

- (UIImageView *)statusImg{
    NSString *path = [[NSBundle mainBundle] pathForResource:@"pay_loading" ofType:@"gif"];
    NSData * data = [NSData dataWithContentsOfFile:path];
    UIImage *image = [UIImage sd_imageWithGIFData:data];

    FF_Fetch_UIImageViewWithImage(_statusImg, image)
}


- (UILabel *)statusLab{
    FF_Fetch_UILable_CenterX(_statusLab, [UIColor blackColor], Font_XX6(14))
}

- (SAGradientButton *)reviewBtn{
    if (_reviewBtn == nil) {
        _reviewBtn = [SAGradientButton buttonWithType:UIButtonTypeCustom];
        [_reviewBtn gradientBackgroundColors:@[kButtonStartColor, kButtonEndColor] locations:nil startPoint:CGPointMake(0, 0.5) endPoint:CGPointMake(1, 0.5)];
        [_reviewBtn addTarget:self action:@selector(reviewAction) forControlEvents:UIControlEventTouchUpInside];
        [_reviewBtn setTitle:@"返回" forState:UIControlStateNormal];
        [_reviewBtn setTitleColor:kHomeTextColor forState:UIControlStateNormal];
    }
    
    return _reviewBtn;
}

- (SACashierPayStatusApi *)statusApi{
    if (_statusApi == nil) {
        _statusApi = [[SACashierPayStatusApi alloc] init];
        _statusApi.delegate = self;
        _statusApi.paramSource = self;
        _statusApi.methodSource = self;
    }
    return _statusApi;
}

@end
