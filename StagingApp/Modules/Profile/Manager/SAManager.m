
#import "SAManager.h"

#import "SADictionary+QLNetworkingMethods.h"

#import "SAActionSheet.h"
#import "SAPhotoManager.h"
#import "SAUploadManager.h"


@interface SAManager ()<QLAPIManagerCallBackDelegate, QLAPIManagerParamSource, NPActionSheetClickedDelegate, UINavigationControllerDelegate, UIImagePickerControllerDelegate>

@property (nonatomic, strong) NSDictionary *ocrDict;

@property (nonatomic, strong) NSString *ocrUrl;
@property (nonatomic, strong) SAUploadManager *uploadManager;

@property (nonatomic, strong) NSData *ocrData;        

@property (nonatomic, assign) NPCardType type;
@property (nonatomic, copy)   OCRSuccessBlock successBlock;

@end

@implementation SAManager

- (void)startOCRWithType:(NPCardType)type complete:(OCRSuccessBlock)success
{
    self.type = type;
    self.successBlock = success;
    [self startAdvance];
}

- (void)startAdvance
{
    SAActionSheet *sheet = [[SAActionSheet alloc] initWithDelegate:self cancleTitle:NSLocalizedString(@"itemOmpare", nil) otherTitles:NSLocalizedString(@"efreshPickerLocalizable", nil), NSLocalizedString(@"methodsChannel", nil), nil];
    [sheet show];
}

- (void)startUploadImg
{
    [[SANLUploadManager sharedInstance] uploadObjectAsync: self.ocrData
                                               fileType:NPFileType_Image
    success:^(NSString *path) {
        
        NSString *imageurl     = path;
        self.ocrUrl = imageurl;
        [self.uploadManager loadData];
        
    } failed:^(NSError * _Nonnull error) {
        [SATool hideWindowHUD];
    }];
}


#pragma mark - QLAPIManagerCallBackDelegate
- (void)managerCallAPIDidSuccess:(SABaseManager *)manager {

    [SATool hideWindowHUD];
    
    if (manager == self.uploadManager){
        
        NSDictionary *dataDict = [manager fetchDataWithReformer:self.uploadManager];
        NSDictionary *cardInfo = dataDict[@"idCardInfo"];
        NSString *detailId = dataDict[@"detailId"];
        if (self.successBlock) {
            self.successBlock(self.ocrUrl, cardInfo, self.ocrData, detailId);
        }
    }
}

- (void)managerCallAPIDidFailed:(SABaseManager *)manager
{
    [SATool hideWindowHUD];
    NSString *failMsg  = [SAResponseHandle netWorkErrorMsg:[manager fetchDataWithReformer:nil] andErrorType:manager.errorType];
    [SATool textStateWindowHUD:failMsg];
}

#pragma mark - QLAPIManagerParamSource
- (id)dataForUploadApi:(SABaseManager *)manager {
    
    return nil;
}

- (NSDictionary *)paramsForApi:(SABaseManager *)manager
{
    if (manager == self.uploadManager){
        return @{
            @"pictureUrl": self.ocrUrl,
            @"type": @1,
            @"pictureType": @(self.type),
        };
    }    return [NSDictionary dictionary];
}

#pragma mark - UIImagePickerControllerDelegate
- (void)imagePickerController:(UIImagePickerController *)picker didFinishPickingMediaWithInfo:(NSDictionary<NSString *,id> *)info{
    
    [picker dismissViewControllerAnimated:YES completion:nil];
    
    UIImage *image = info[UIImagePickerControllerOriginalImage];

    NSData *imgData = UIImageJPEGRepresentation(image, 0.1);
    self.ocrData = imgData;
    self.imgData = UIImageJPEGRepresentation(image, 0.1);
   
    
    [SATool showWindowHUD:nil];
    [self startUploadImg];
}

- (void)openAlbum {
    if (![SAPhotoManager AlbumAuthorization]) {
        return;
    }
    
    UIImagePickerController *pickerVc = [[UIImagePickerController alloc] init];
    pickerVc.sourceType = UIImagePickerControllerSourceTypePhotoLibrary;
    pickerVc.delegate = self;
    [[SACommonTool currentViewController] presentViewController:pickerVc animated:YES completion:nil];
}

- (void)openCamera {
    
    if (![SAPhotoManager CameraAuthorization]) {
        return;
    }
    
    UIImagePickerController *pickerVc = [[UIImagePickerController alloc] init];
    pickerVc.sourceType = UIImagePickerControllerSourceTypeCamera;
    pickerVc.cameraCaptureMode = UIImagePickerControllerCameraCaptureModePhoto;
    pickerVc.allowsEditing = YES;
    pickerVc.delegate = self;
    [[SACommonTool currentViewController] presentViewController:pickerVc animated:YES completion:nil];
}

#pragma mark - NPActionSheetClickedDelegate
- (void)actionSheet:(SAActionSheet *)actionSheet clickedButtonAtIndex:(SAActionSheetItem *)item {
    if (item.index == 1) {
        [self openCamera];
    }
    
    if (item.index == 2) {      
        [self openAlbum];
    }
}


#pragma mark - getters

- (SAUploadManager *)uploadManager{
    if (_uploadManager == nil) {
        _uploadManager = [[SAUploadManager alloc] init];
        _uploadManager.delegate = self;
        _uploadManager.paramSource = self;
    }
    return _uploadManager;
}


- (void)dealloc{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

@end
