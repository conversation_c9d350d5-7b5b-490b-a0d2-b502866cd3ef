//
//  EsignFaceManager.h
//  EsignSDKIOS
//
//  Created by 叶鹏飞 on 2024/8/6.
//

#import <Foundation/Foundation.h>
#import "EsignSDKConfig.h"
#import "EsignFaceVerifyResult.h"
#import "EsignSDKError.h"

NS_ASSUME_NONNULL_BEGIN

@interface EsignFaceManager : NSObject

+ (instancetype)shareEsignFaceManager;


/*刷脸初始化*/
- (void)initConfig:(EsignSDKConfig *)config success:(void (^)(void))success failure:(void (^)(EsignSDKError * _Nonnull error))failure;


/*发起刷脸*/
- (void)startFaceVerify:(NSString *)faceAuthCode success:(void (^)(EsignFaceVerifyResult * _Nonnull reslut))success failure:(void (^)(EsignSDKError * _Nonnull error))failure;


@end

NS_ASSUME_NONNULL_END
