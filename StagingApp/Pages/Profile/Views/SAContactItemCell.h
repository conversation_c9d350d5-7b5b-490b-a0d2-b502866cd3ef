
#import <UIKit/UIKit.h>
#import "SAPersonalVerifyModel.h"

NS_ASSUME_NONNULL_BEGIN

@class SAContactItemCell;
@protocol NPContactItemCellDelegate <NSObject>

- (void)didSelectAddressBook: (SAContactItemCell *)cell idx:(NSInteger)index;

@end

@interface SAContactItemCell : UITableViewCell

@property (nonatomic, strong) SAPersonalVerifyModel *model;
@property (nonatomic, assign) NSInteger index;

@property (nonatomic, weak) id delegate;


@end

NS_ASSUME_NONNULL_END
