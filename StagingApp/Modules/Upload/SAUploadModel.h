
#import <Foundation/Foundation.h>

typedef NS_ENUM(NSInteger, NPFileType) {
    NPFileType_Txt    = 1,      
    NPFileType_Image  = 2,    
};

typedef NS_ENUM(NSInteger, NPUploadType) {
    NPUploadType_OSS  = 1,      
    NPUploadType_AWS  = 2,      
    NPUploadType_Ser  = 3,      
    NPUploadType_HW   = 4,      
};

extern NSString * const kFileURL;
extern NSString * const kTag;
extern NSString * const kRelativeUrl;


NS_ASSUME_NONNULL_BEGIN

@interface SAUploadModel : NSObject

@property (nonatomic, assign) NPUploadType clientType;

@property (nonatomic, strong) NSString *accessKeyId;
@property (nonatomic, strong) NSString *accessKeySecret;
@property (nonatomic, strong) NSString *securityToken;
@property (nonatomic, strong) NSString *expiration;
@property (nonatomic, strong) NSString *objectName;
@property (nonatomic, strong) NSString *bucketName;
@property (nonatomic, strong) NSString *baseUrl;
@property (nonatomic, strong) NSString *region;
@property (nonatomic, strong) NSString *urlPrefix;

@property (nonatomic, strong) NSString *objUrl;


+ (instancetype)objectWithDict:(NSDictionary *)dict ;


@end

NS_ASSUME_NONNULL_END
