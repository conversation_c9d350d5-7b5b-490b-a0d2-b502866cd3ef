
#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import "BRPickerViewMacro.h"

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, BRBorderStyle) {
     
    BRBorderStyleNone = 0,
     
    BRBorderStyleSolid,
     
    BRBorderStyleFill
};

@interface SAPickerStyle : NSObject



 
@property (nullable, nonatomic, strong) UIColor *maskColor;

 
@property (nonatomic, assign) BOOL hiddenMaskView;



 
@property (nullable, nonatomic, strong) UIColor *alertViewColor;

 
@property (nonatomic, assign) NSInteger topCornerRadius;

 
@property (nullable, nonatomic, strong) UIColor *shadowLineColor;

 
@property (nonatomic, assign) CGFloat shadowLineHeight;

 
@property (nonatomic, assign) BOOL hiddenShadowLine;

 
@property (nonatomic, assign) CGFloat paddingBottom;



 
@property (nullable, nonatomic, strong) UIColor *titleBarColor;

 
@property (nonatomic, assign) CGFloat titleBarHeight;

 
@property (nullable, nonatomic, strong) UIColor *titleLineColor;

 
@property (nonatomic, assign) <PERSON><PERSON><PERSON> hiddenTitleLine;

 
@property (nonatomic, assign) BOOL hiddenTitleBarView;



 
@property (nullable, nonatomic, strong) UIColor *titleLabelColor;

 
@property (nullable, nonatomic, strong) UIColor *titleTextColor;

 
@property (nullable, nonatomic, strong) UIFont *titleTextFont;

 
@property (nonatomic, assign) CGRect titleLabelFrame;

 
@property (nonatomic, assign) BOOL hiddenTitleLabel;



 
@property (nullable, nonatomic, strong) UIColor *cancelColor;

 
@property (nullable, nonatomic, strong) UIColor *cancelTextColor;

 
@property (nullable, nonatomic, strong) UIFont *cancelTextFont;

 
@property (nonatomic, assign) CGRect cancelBtnFrame;

 
@property (nonatomic, assign) BRBorderStyle cancelBorderStyle;

 
@property (nonatomic, assign) CGFloat cancelCornerRadius;

 
@property (nonatomic, assign) CGFloat cancelBorderWidth;

 
@property (nullable, nonatomic, strong) UIImage *cancelBtnImage;

 
@property (nullable, nonatomic, copy) NSString *cancelBtnTitle;

 
@property (nonatomic, assign) BOOL hiddenCancelBtn;



 
@property (nullable, nonatomic, strong) UIColor *doneColor;

 
@property (nullable, nonatomic, strong) UIColor *doneTextColor;

 
@property (nullable, nonatomic, strong) UIFont *doneTextFont;

 
@property (nonatomic, assign) CGRect doneBtnFrame;

 
@property (nonatomic, assign) BRBorderStyle doneBorderStyle;

 
@property (nonatomic, assign) CGFloat doneCornerRadius;

 
@property (nonatomic, assign) CGFloat doneBorderWidth;

 
@property (nullable, nonatomic, strong) UIImage *doneBtnImage;

 
@property (nullable, nonatomic, copy) NSString *doneBtnTitle;

 
@property (nonatomic, assign) BOOL hiddenDoneBtn;



 
@property (nullable, nonatomic, strong) UIColor *pickerColor;

 
@property (nullable, nonatomic, strong) UIColor *separatorColor;

 
@property (nonatomic, assign) CGFloat separatorHeight;

 
@property (nullable, nonatomic, strong) UIColor *pickerTextColor;

 
@property (nullable, nonatomic, strong) UIFont *pickerTextFont;

 
@property (nullable, nonatomic, strong) UIColor *selectRowColor;

 
@property (nullable, nonatomic, strong) UIColor *selectRowTextColor;

 
@property (nullable, nonatomic, strong) UIFont *selectRowTextFont;

 
@property (nonatomic, assign) CGFloat pickerHeight;

 
@property (nonatomic, assign) CGFloat rowHeight;
 
@property (nonatomic, assign) CGFloat columnWidth;
 
@property (nonatomic, assign) CGFloat columnSpacing;

 
@property (nonatomic, assign) NSUInteger maxTextLines;

 
@property (nonatomic, assign) BOOL clearPickerNewStyle;


 
@property(nullable, nonatomic, copy) NSString *language;



 
@property (nullable, nonatomic, strong) UIColor *dateUnitTextColor;

 
@property (nullable, nonatomic, strong) UIFont *dateUnitTextFont;

 
@property (nonatomic, assign) CGFloat dateUnitOffsetX;

 
@property (nonatomic, assign) CGFloat dateUnitOffsetY;



+ (instancetype)pickerStyleWithThemeColor:(nullable UIColor *)themeColor;

+ (instancetype)pickerStyleWithDoneTextColor:(nullable UIColor *)doneTextColor;

+ (instancetype)pickerStyleWithDoneBtnImage:(nullable UIImage *)doneBtnImage;



 
- (void)setupPickerSelectRowStyle:(UIPickerView *)pickerView titleForRow:(NSInteger)row forComponent:(NSInteger)component;

 
- (void)addSeparatorLineView:(UIView *)pickerView;

 
+ (void)br_setView:(UIView *)view roundingCorners:(UIRectCorner)corners withRadius:(CGFloat)radius;

@end

NS_ASSUME_NONNULL_END
