
#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface SAProductPeriodModel : NSObject

@property (nonatomic, assign) NSInteger period;
@property (nonatomic, strong) NSString *productCode;
@property (nonatomic, strong) NSArray <NSDictionary *>*orderFieldDetails;
@property (nonatomic, strong) NSDictionary *productInfo;
@property (nonatomic, strong) NSString *borrowProtocolUrl;

@end

@interface SASubmitProductInfoModel : NSObject

@property (nonatomic, copy)NSString *borrowProtocolUrl;
@property (nonatomic, strong)NSArray <NSDictionary *>*orderFieldDetails;
@property (nonatomic, strong)NSDictionary *productInfo;

+ (instancetype)objectWithDict:(NSDictionary *)dict;

@end

NS_ASSUME_NONNULL_END
