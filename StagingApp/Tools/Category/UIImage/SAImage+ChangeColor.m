
#import "SAImage+ChangeColor.h"

@implementation UIImage (ChangeColor)

-(UIImage*)imageWithColor:(UIColor*)color
{
    
    UIGraphicsBeginImageContextWithOptions(self.size, NO, 0.0f);
    
    [color setFill];
    
    CGRect bounds = CGRectMake(0, 0, self.size.width, self.size.height);
    UIRectFill(bounds);
    
    [self drawInRect:bounds blendMode:kCGBlendModeOverlay alpha:1.0f];
    
    [self drawInRect:bounds blendMode:kCGBlendModeDestinationIn alpha:1.0f];
    
    UIImage *img = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    return img;
}

- (UIImage *)originalImageWithColor:(UIColor *)color 
{
    UIImage *img = [self imageWithColor:color];
    img = [img imageWithRenderingMode:(UIImageRenderingModeAlwaysTemplate)];
    return img;
}

@end
