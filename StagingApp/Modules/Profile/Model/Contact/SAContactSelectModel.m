
#import "SAContactSelectModel.h"

@implementation SAContactSelectModel

- (CGFloat)rowHeigt {
    if (_rowHeigt) {
        return _rowHeigt;
    }
    
    
    CGFloat maxWid = UIScreenWidth - XX_6(40);
    CGSize maxSize = CGSizeMake(maxWid, MAXFLOAT);
    CGSize realSize = [self.name boundingRectWithSize:maxSize options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:[UIFont fontSizeOfXX_6:18]} context:nil].size;
    _rowHeigt = realSize.height;
    if (realSize.height < XX_6(40)) {
        _rowHeigt = XX_6(40);
    }
    return _rowHeigt;
}

@end
