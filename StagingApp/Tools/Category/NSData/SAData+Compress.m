
#import "SAData+Compress.h"
#import "zlib.h"

@implementation NSData (Compress)

+ (NSData *)compressData:(NSData *)data
{
    if (data.length == 0) return data;
       
       z_stream zStream;
       bzero(&zStream, sizeof(zStream));
       
       zStream.zalloc = Z_NULL;
       zStream.zfree = Z_NULL;
       zStream.opaque = Z_NULL;
       zStream.next_in = (Bytef *) data.bytes;
       zStream.avail_in = (uInt) data.length;
       zStream.total_out = 0;
       
       OSStatus status = deflateInit2(&zStream,
                                      Z_DEFAULT_COMPRESSION,
                                      Z_DEFLATED,
                                      31,
                                      MAX_MEM_LEVEL,
                                      Z_DEFAULT_STRATEGY);
       
       if (status != Z_OK) {
           return nil;
       }
       
       static NSInteger kZlibCompressChunkSize = 2048;
       NSMutableData *compressedData = [NSMutableData dataWithLength:kZlibCompressChunkSize];
       do {
           if ((status == Z_BUF_ERROR) || (zStream.total_out == compressedData.length)) {
               [compressedData increaseLengthBy:kZlibCompressChunkSize];
           }
           zStream.next_out = (Bytef *)compressedData.bytes + zStream.total_out;
           zStream.avail_out = (uInt)(compressedData.length - zStream.total_out);
           status = deflate(&zStream, Z_FINISH);
       } while ((status == Z_BUF_ERROR) || (status == Z_OK));
       
       status = deflateEnd(&zStream);
       
       if ((status != Z_OK) && (status != Z_STREAM_END)) {
           return nil;
       }
       
       compressedData.length = zStream.total_out;
       
       return compressedData;
}

+ (NSData *)uncompressGZip:(NSData *)compressedData
{
    if ([compressedData length] == 0) return compressedData;

    NSUInteger full_length = [compressedData length];
    NSUInteger half_length = [compressedData length] / 2;

    NSMutableData *decompressed = [NSMutableData dataWithLength: full_length + half_length];
    BOOL done = NO;
    int status;

    z_stream strm;
    strm.next_in = (Bytef *)[compressedData bytes];
    strm.avail_in = (unsigned int)[compressedData length];
    strm.total_out = 0;
    strm.zalloc = Z_NULL;
    strm.zfree = Z_NULL;

    if (inflateInit2(&strm, (15+32)) != Z_OK) return nil;

    while (!done) {
        
        if (strm.total_out >= [decompressed length]) {
            [decompressed increaseLengthBy: half_length];
        }
        strm.next_out = [decompressed mutableBytes] + strm.total_out;
        strm.avail_out = (unsigned int)([decompressed length] - strm.total_out);

        
        status = inflate (&strm, Z_SYNC_FLUSH);
        if (status == Z_STREAM_END) {
            done = YES;
        } else if (status != Z_OK) {
            break;
        }
    }
    if (inflateEnd (&strm) != Z_OK) return nil;

    
    if (done) {
        [decompressed setLength: strm.total_out];
        return [NSData dataWithData: decompressed];
    } else {
        return nil;
    }
}

@end
