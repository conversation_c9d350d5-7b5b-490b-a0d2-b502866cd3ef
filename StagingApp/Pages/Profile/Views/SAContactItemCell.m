
#import "SAContactItemCell.h"

#define kContactItemHeight  45

#define kBasicRelationTag   439

@interface SAContactItemCell ()

@property (nonatomic, strong) UILabel *relTitleLab;
@property (nonatomic, strong) UIView *relBoxV;

@property (nonatomic, strong) UIView *nameBoxBg;
@property (nonatomic, strong) UILabel *nameHolderLab;
@property (nonatomic, strong) UITextField *nameField;
@property (nonatomic, strong) UIImageView *nameArrow;
@property (nonatomic, strong) UIView *line1;

@property (nonatomic, strong) UIView *mobileBoxBg;
@property (nonatomic, strong) UILabel *mobileHolderLab;
@property (nonatomic, strong) UITextField *mobileField;
@property (nonatomic, strong) UIImageView *mobileArrow;
@property (nonatomic, strong) UIView *line2;

@property (nonatomic, strong) NSMutableArray <UIButton *>*btnArr;

@end

@implementation SAContactItemCell

#pragma mark - Life cycle

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier])
    {
        self.contentView.backgroundColor = [UIColor whiteColor];
        self.contentView.layer.masksToBounds = YES;
        self.contentView.layer.cornerRadius = 6;
        
        [self createUI];
        [self setConstraints];
        
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(selectAddressBook)];
        UITapGestureRecognizer *tap2 = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(selectAddressBook)];

        self.nameBoxBg.userInteractionEnabled = YES;
        self.mobileBoxBg.userInteractionEnabled= YES;
        [self.nameBoxBg addGestureRecognizer:tap];
        [self.mobileBoxBg addGestureRecognizer:tap2];
    }
    
    return self;
}

- (void)createUI
{
    [self.contentView addSubview:self.relTitleLab];
    [self.contentView addSubview:self.relBoxV];
    
    [self.contentView addSubview:self.nameBoxBg];
    [self.nameBoxBg addSubview:self.nameHolderLab];
    [self.nameBoxBg addSubview:self.nameField];
    [self.nameBoxBg addSubview:self.line1];
    
    [self.contentView addSubview:self.mobileBoxBg];
    [self.mobileBoxBg addSubview:self.mobileHolderLab];
    [self.mobileBoxBg addSubview:self.mobileField];
    [self.mobileBoxBg addSubview:self.line2];
    
}

- (void)setConstraints
{
    [self.relTitleLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.top.equalTo(self.contentView).offset(14);
    }];
    [self.relBoxV mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.equalTo(self.relTitleLab);
        make.top.equalTo(self.relTitleLab.mas_bottom).offset(5);
        make.height.mas_equalTo(50);
        make.centerX.equalTo(self.contentView);
    }];

    [self.nameBoxBg mas_makeConstraints:^(SAConstraintMaker *make) {
        make.top.equalTo(self.relBoxV.mas_bottom).offset(1);
        make.left.centerX.equalTo(self.relBoxV);
        make.height.mas_equalTo(kContactItemHeight);
    }];
    [self.nameHolderLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.height.top.equalTo(self.nameBoxBg);
    }];
    [self.nameField mas_makeConstraints:^(SAConstraintMaker *make) {
        make.right.top.bottom.equalTo(self.nameBoxBg);
        make.left.equalTo(self.nameHolderLab.mas_right);
    }];
    [self.line1 mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.right.equalTo(self.nameBoxBg);
        make.height.mas_equalTo(1);
        make.top.equalTo(self.nameBoxBg.mas_bottom);
    }];
    
    
    [self.mobileBoxBg mas_makeConstraints:^(SAConstraintMaker *make) {
        make.top.equalTo(self.nameBoxBg.mas_bottom).offset(1);
        make.left.height.centerX.equalTo(self.nameBoxBg);
    }];
    [self.mobileHolderLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.height.top.equalTo(self.mobileBoxBg);
    }];
    [self.mobileField mas_makeConstraints:^(SAConstraintMaker *make) {
        make.right.top.bottom.equalTo(self.mobileBoxBg);
        make.left.equalTo(self.mobileHolderLab.mas_right);
    }];
    [self.line2 mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.right.height.equalTo(self.line1);
        make.top.equalTo(self.mobileBoxBg.mas_bottom);
    }];
}

#pragma mark - setter

- (void)setModel:(SAPersonalVerifyModel *)model
{
    _model = model;
    
    NSMutableArray <SAPersonalInfoModel *>*inputParams = model.inputParams;
    
    SAPersonalInfoModel *relInfoModel = inputParams[0];
    self.relTitleLab.text = relInfoModel.paramName;
    NSMutableArray *btnArr = [NSMutableArray  array];
    [relInfoModel.selectVo enumerateObjectsUsingBlock:^(SAPersonalInfoCheckModel *item, NSUInteger idx, BOOL * _Nonnull stop) {
        UIButton *btn = [UIButton buttonWithType:UIButtonTypeCustom];
        [btn setTitle:item.name forState: UIControlStateNormal];
        btn.titleLabel.font = Font_XX6(12);
        btn.layer.cornerRadius = 3;
        btn.layer.masksToBounds = YES;
        [self.relBoxV addSubview:btn];
        [btnArr addObject:btn];
        
        btn.tag  = kBasicRelationTag + idx;
        [btn addTarget:self action:@selector(selectRelation:) forControlEvents:UIControlEventTouchUpInside];
        
        btn.backgroundColor = [UIColor colorWithHex:0xf6f6f6];
        [btn setTitleColor:[UIColor colorWithHex:0x444444] forState:UIControlStateNormal];
        if(relInfoModel.inputValue == item.type){
            btn.backgroundColor = kPrimaryColor;
            [btn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        }
    }];
    self.btnArr = btnArr;
    
    
    [self.btnArr mas_distributeViewsAlongAxis:MASAxisTypeHorizontal withFixedItemLength:64 leadSpacing:0 tailSpacing:0];
    
    [self.btnArr mas_makeConstraints:^(SAConstraintMaker *make) {
        make.centerY.equalTo(self.relBoxV);
        make.height.mas_equalTo(34);
    }];
    
    SAPersonalInfoModel *nameInfoModel = inputParams[1];
    self.nameHolderLab.text = nameInfoModel.paramName;
    self.nameField.text = nameInfoModel.inputValue;
    
    SAPersonalInfoModel *mobileInfoModel = inputParams[2];
    self.mobileHolderLab.text = mobileInfoModel.paramName;
    self.mobileField.text = mobileInfoModel.inputValue;

}

- (void)setFrame:(CGRect)frame
{
    frame.origin.x += 12;
    frame.size.width -= 24;
    
    [super setFrame:frame];
}

#pragma mark - 选择联系人关系

- (void)selectRelation:(UIButton *)sender
{
    NSInteger idx = sender.tag - kBasicRelationTag;
    
    NSMutableArray <SAPersonalInfoModel *>*inputParams = self.model.inputParams;
    SAPersonalInfoModel *relInfoModel = inputParams[0];
    relInfoModel.inputValue = relInfoModel.selectVo[idx].type;
    
    [self.btnArr enumerateObjectsUsingBlock:^(UIButton *btn, NSUInteger idx, BOOL * _Nonnull stop) {
        btn.backgroundColor = [UIColor colorWithHex:0xf6f6f6];
        [btn setTitleColor:[UIColor colorWithHex:0x444444] forState:UIControlStateNormal];
    }];
    sender.backgroundColor = kPrimaryColor;
    [sender setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
}

#pragma mark - 选择通讯录

- (void)selectAddressBook
{
    if(self.delegate && [self.delegate respondsToSelector:@selector(didSelectAddressBook: idx:)]){
        [self.delegate didSelectAddressBook:self idx:self.index];
    }
}

#pragma mark - getter

- (UILabel *)relTitleLab{
    FF_Fetch_UILable(_relTitleLab, [UIColor blackColor], BoldFont_XX6(16))
}

- (UIView *)relBoxV{
    FF_Fetch_UIView(_relBoxV, [UIColor clearColor])
}

- (UIView *)nameBoxBg{
    FF_Fetch_UIView(_nameBoxBg, [UIColor clearColor])
}

- (UIView *)mobileBoxBg{
    FF_Fetch_UIView(_mobileBoxBg, [UIColor clearColor])
}

- (UITextField *)nameField{
    if (_nameField == nil) {
        _nameField = [UITextField new];
        _nameField.textColor = [UIColor blackColor];
        _nameField.font = Font_XX6(14);
        _nameField.enabled = NO;
        _nameField.textAlignment =  NSTextAlignmentRight;
    }
    return _nameField;
}

- (UITextField *)mobileField{
    if (_mobileField == nil) {
        _mobileField = [UITextField new];
        _mobileField.textColor = [UIColor blackColor];
        _mobileField.font = Font_XX6(14);
        _mobileField.enabled = NO;
        _mobileField.textAlignment =  NSTextAlignmentRight;
    }
    return _mobileField;
}

- (UIView *)line1{
    FF_Fetch_UIView(_line1, [UIColor colorWithHex:0xe3e3e3])
}

- (UIView *)line2{
    FF_Fetch_UIView(_line2, [UIColor colorWithHex:0xe3e3e3])
}

- (NSMutableArray<UIButton *> *)btnArr{
    if(_btnArr == nil){
        _btnArr = [NSMutableArray array];
    }
    return _btnArr;
}

- (UILabel *)nameHolderLab{
    FF_Fetch_UILable(_nameHolderLab, [UIColor colorWithHex:0x777777], Font_XX6(13))
}

- (UILabel *)mobileHolderLab{
    FF_Fetch_UILable(_mobileHolderLab, [UIColor colorWithHex:0x777777], Font_XX6(13))
}

@end
