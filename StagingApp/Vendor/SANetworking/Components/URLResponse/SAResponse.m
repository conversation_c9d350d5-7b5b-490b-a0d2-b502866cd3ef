
#import "SAResponse.h"
#import "SAObject+QLNetworkingMethods.h"
#import "SARequest+QLNetworkingMethods.h"

@interface SAResponse ()

@property (nonatomic, assign, readwrite)    NPURLResponseStatus status;
@property (nonatomic, copy, readwrite)      NSString *contentString;
@property (nonatomic, copy, readwrite)      id content;
@property (nonatomic, copy, readwrite)      NSURLRequest *request;
@property (nonatomic, assign, readwrite)    NSInteger requestId;
@property (nonatomic, copy, readwrite)      NSData *responseData;
@property (nonatomic, assign, readwrite)    BOOL isCache;
@property (nonatomic, strong, readwrite)    NSError *error;

@end

@implementation SAResponse

#pragma mark - Life cycle

- (instancetype)initWithResponseString:(NSString *)responseString requestId:(NSNumber *)requestId request:(NSURLRequest *)request responseData:(NSData *)responseData status:(NPURLResponseStatus)status
{
    if (self = [super init])
    {
        self.contentString  = responseString;
        self.content        = [NSJSONSerialization JSONObjectWithData:responseData options:NSJSONReadingMutableContainers error:NULL];
        self.status         = status;
        self.requestId      = [requestId integerValue];
        self.request        = request;
        self.responseData   = responseData;
        self.requestParams  = request.requestParams;
        self.isCache        = NO;
        self.error          = nil;
    }
    return self;
}

- (instancetype)initWithResponseString:(NSString *)responseString requestId:(NSNumber *)requestId request:(NSURLRequest *)request responseData:(NSData *)responseData error:(NSError *)error
{
    if (self = [super init])
    {
        self.contentString  = [responseString QL_defaultValue:@""];
        self.status         = [self responseStatusWithError:error];
        self.requestId      = [requestId integerValue];
        self.request        = request;
        self.responseData   = responseData;
        self.requestParams  = request.requestParams;
        self.isCache        = NO;
        self.error          = error;
        if (responseData)
        {
            self.content = [NSJSONSerialization JSONObjectWithData:responseData options:NSJSONReadingMutableContainers error:NULL];
        }
        else
        {
            self.content = nil;
        }
    }
    return self;
}

- (instancetype)initWithData:(NSData *)data
{
    if (self = [super init])
    {
        self.contentString  = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
        self.status         = [self responseStatusWithError:nil];
        self.requestId      = 0;
        self.request        = nil;
        self.responseData   = [data copy];
        self.content        = [NSJSONSerialization JSONObjectWithData:data options:NSJSONReadingMutableContainers error:NULL];
        self.isCache        = YES;
    }
    return self;
}

#pragma mark - Private methods

- (NPURLResponseStatus)responseStatusWithError:(NSError *)error
{
    if (error)
    {
        NPURLResponseStatus result = NPURLResponseStatusErrorNoNetwork;
        
        if (error.code == NSURLErrorTimedOut) {
            result = NPURLResponseStatusErrorTimeout;
        }
        return result;
    }
    else
    {
        return NPURLResponseStatusSuccess;
    }
}

@end
