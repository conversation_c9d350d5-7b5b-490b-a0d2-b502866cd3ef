
#import "SAVerifyInfoCell.h"
 
#import "SAString+Extension.h"

#import "SAPickerTextField.h"
#import "SACenterYTextView.h"

#import "SAPersonalInfoModel.h"
#import "SADate+Extension.h"
#import <HandyFrame/UIView+LayoutMethods.h>
#import "BRPickerView.h"

#define kBasicBtnTag        864

@interface SAVerifyInfoCell ()<UIPickerViewDelegate, UIPickerViewDataSource, UITextViewDelegate, UITextFieldDelegate>

@property (nonatomic, strong) UIView *bgView;
@property (nonatomic, strong) UILabel *titleLab;
@property (nonatomic, strong) SAPickerTextField *textField;
@property (nonatomic, strong) UIImageView *pointerIcon;

@property (nonatomic, strong) UIView *btnBoxV;
@property (nonatomic, strong) NSMutableArray <UIButton *>*btnArr;




@property (nonatomic, strong) UIDatePicker *datePicker;
@property (nonatomic, strong) UIPickerView *pickerView;
@property (nonatomic, strong) UIToolbar *toolBar;

@property (nonatomic, assign) NSInteger selRow;      

@property (nonatomic, copy) NSArray <NSNumber *> *linkage3SelectIndexs;

@end

@implementation SAVerifyInfoCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier])
    {
        [self addCustomViews];
        
        self.backgroundColor =[UIColor clearColor];
        self.contentView.backgroundColor = [UIColor clearColor];
    }
    return self;
}

- (void)addCustomViews
{
    self.bgView.layer.cornerRadius = 6;
    self.bgView.layer.masksToBounds = YES;
    
    [self.contentView addSubview:self.bgView];
    [self.contentView addSubview:self.titleLab];
    [self.bgView addSubview:self.textField];
    [self.bgView addSubview:self.pointerIcon];
    self.bgView.userInteractionEnabled = YES;
    
}

- (void)layoutSubviews
{
    [super layoutSubviews];
    
    self.bgView.frame = CGRectMake(0, 0, self.contentView.width, self.contentView.height);

    CGFloat hGap = XX_6(10);
    CGFloat arrowW = 15;
    CGFloat arrowH = 20;
    
    self.titleLab.x = hGap;
    self.titleLab.centerY = self.contentView.centerY;
    self.titleLab.height = 20;

    self.pointerIcon.size = CGSizeMake(arrowW, arrowH);
    [self.pointerIcon centerYEqualToView: self.titleLab];
    self.pointerIcon.x = self.contentView.width - hGap*2;

    self.textField.x = self.titleLab.maxX + hGap;
    [self.textField centerYEqualToView: self.titleLab];
    self.textField.height = self.contentView.height;
    self.textField.width = self.contentView.width - hGap*2 - arrowW - self.titleLab.maxX;
    

    
    


}

- (void)tapArrow
{
    [self.textField becomeFirstResponder];
}



#pragma mark - UIPickerViewDataSource
- (NSInteger)numberOfComponentsInPickerView:(UIPickerView *)pickerView {
    return 1;
}

- (NSInteger)pickerView:(UIPickerView *)pickerView numberOfRowsInComponent:(NSInteger)component {
    
    if (self.personalmodel != nil) {
        return self.personalmodel.selectVo.count;
    }
    return 0;
}

#pragma mark - UIPickerViewDelegate
- (CGFloat)pickerView:(UIPickerView *)pickerView rowHeightForComponent:(NSInteger)component {
    return XX_6(30);
}

- (void)pickerView:(UIPickerView *)pickerView didSelectRow:(NSInteger)row inComponent:(NSInteger)component {
    self.selRow = row;
}

- (UIView *)pickerView:(UIPickerView *)pickerView viewForRow:(NSInteger)row forComponent:(NSInteger)component reusingView:(UIView *)view{
    
    UILabel *pickerLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, UIScreenWidth, XX_6(30))];
    pickerLabel.textAlignment = NSTextAlignmentCenter;
    pickerLabel.textColor = [UIColor colorWithHex:0x4C4C4C];
    pickerLabel.font = [UIFont fontSizeOfXX_6:18];
    
    if (self.personalmodel != nil) {
        pickerLabel.text = self.personalmodel.selectVo[row].name;
    }
    
    return pickerLabel;
}

#pragma mark - UITextFieldDelegate

- (void)textFieldDidEndEditing:(UITextField *)textField{
    if(self.personalmodel.paramType == 1){
        self.personalmodel.inputValue = self.textField.text;
    }
}

#pragma mark - UITextViewDelegate

- (void)textViewDidEndEditing:(SACenterYTextView *)textView {
    
    
    NSString *textStr = [textView.text stringByReplacingOccurrencesOfString:@"\u00a0"
                                                 withString:@" "];
    textStr = [textStr removeSpaceAndNewline];
    
    
    if (self.personalmodel != nil) {
        self.personalmodel.cellHeight = textView.realHeight;
        self.personalmodel.inputStr = textStr;
    }
        
    if (self.endEditBlock) {
        self.endEditBlock();
    }
}

- (void)textViewDidChange:(SACenterYTextView *)textView {
    
    
    NSString *textStr = [textView.text stringByReplacingOccurrencesOfString:@" " withString:@"\u00a0"];
    textView.text = textStr;
}

- (BOOL)textView:(SACenterYTextView *)textView shouldChangeTextInRange:(NSRange)range replacementText:(NSString *)text{
    if ([text isEqualToString:@""]) {
        return YES;
    }
    
    if ([text isEqualToString:@"\n"]) {
        return NO;
    }
    return textView.text.length + (text.length - range.length) <= 100;
}

#pragma mark - 选择联系人关系

- (void)selectRelation:(UIButton *)sender
{
    NSInteger idx = sender.tag - kBasicBtnTag;
    
    self.personalmodel.inputValue = self.personalmodel.selectVo[idx].type;
    
    [self.btnArr enumerateObjectsUsingBlock:^(UIButton *btn, NSUInteger idx, BOOL * _Nonnull stop) {
        btn.backgroundColor = [UIColor colorWithHex:0xf6f6f6];
        [btn setTitleColor:[UIColor colorWithHex:0x444444] forState:UIControlStateNormal];
    }];
    sender.backgroundColor = kPrimaryColor;
    [sender setTitleColor:[UIColor blackColor] forState:UIControlStateNormal];
}


#pragma mark - Actions
- (void)handleSelectAction
{
    if (self.personalmodel.paramType == 8){
        
        
        SATextPickerView *stringPickerView = [[SATextPickerView alloc]initWithPickerMode:BRTextPickerComponentMulti];
        
        SAPickerStyle *style = [SAPickerStyle new];
        style.pickerTextColor = [UIColor colorWithHex:0x333333];
        style.selectRowTextColor = [UIColor colorWithHex:0x333333];
        style.cancelBtnTitle = NSLocalizedString(@"serviceCtionDebug", nil);
        style.doneBtnTitle = NSLocalizedString(@"heckIcker", nil);
        stringPickerView.pickerStyle = style;
        
        stringPickerView.title = self.personalmodel.paramName;
        stringPickerView.dataSourceArr = [self getLinkag3DataSource];
        stringPickerView.selectIndexs = self.linkage3SelectIndexs;
        stringPickerView.multiResultBlock = ^(NSArray<SATextModel *> *resultModelArr, NSArray<NSNumber *> *indexs) {
            
            NSMutableArray *selectIndexs = [[NSMutableArray alloc]init];
            
            NSString *selectValue = @"";
            
            
            NSString *valStr = @"";
            for (SATextModel *model in resultModelArr) {
                [selectIndexs addObject:@(model.index)];
                selectValue = [NSString stringWithFormat:@"%@ %@", selectValue, model.text];
                
                valStr = [NSString stringWithFormat:@"%@;%@", valStr, model.text];
            }
            if ([selectValue hasPrefix:@" "]) {
                selectValue = [selectValue substringFromIndex:1];
            }
            if ([valStr hasPrefix:@";"]) {
                valStr = [valStr substringFromIndex:1];
            }
            self.linkage3SelectIndexs = selectIndexs;
            self.textField.text = selectValue;
            self.personalmodel.inputValue = valStr;
        };
        
        
        SAPickerStyle *customStyle = [[SAPickerStyle alloc]init];
        customStyle.selectRowTextFont = [UIFont boldSystemFontOfSize:20.0f];
        customStyle.selectRowTextColor = [UIColor blueColor];
        stringPickerView.pickerStyle = customStyle;
        
        [stringPickerView show];
    }
}

- (NSArray <SATextModel *>*)getLinkag3DataSource
{
    NSArray *totalArray = [SAAdvanceManager sharedInstance].citiesArr[0];
    
    NSMutableArray *allRegionModelArr = [[NSMutableArray alloc] init];
    NSMutableArray *proArr = [[NSMutableArray alloc] init];
    NSMutableArray *twoArr = [[NSMutableArray alloc] init];
    NSMutableArray *thirdArr = [[NSMutableArray alloc] init];
    
    
    NSArray *provinceArr = totalArray;
    for (NSInteger i = 0; i < provinceArr.count; i++) {
        
        NSDictionary *oneModel = provinceArr[i];
        
        SATextModel *model = [[SATextModel alloc]init];
        model.parentCode = @"-1";
        model.code = [NSString stringWithFormat:@"%@", oneModel[@"value"]];
        model.text = oneModel[@"text"];
        [proArr addObject:model];
        
        
        
        NSArray *cityArr = oneModel[@"children"];
        for (NSInteger m = 0; m < cityArr.count; m++) {
            NSDictionary *twoModel = cityArr[m];
            
            SATextModel *model = [[SATextModel alloc]init];
            model.parentCode = [NSString stringWithFormat:@"%@", oneModel[@"value"]];
            model.code = [NSString stringWithFormat:@"%@", twoModel[@"value"]];
            model.text = twoModel[@"text"];
            [twoArr addObject:model];
            
            
            
            NSArray *areaArr = twoModel[@"children"];
            for (NSInteger n = 0; n < areaArr.count; n++) {
                NSDictionary *thirdModel = areaArr[n];
                
                SATextModel *model = [[SATextModel alloc]init];
                model.parentCode = [NSString stringWithFormat:@"%@", twoModel[@"value"]];;
                model.code = [NSString stringWithFormat:@"%@", thirdModel[@"value"]];
                model.text = thirdModel[@"text"];
                [thirdArr addObject:model];
            }
        }
    }
    
    [allRegionModelArr addObjectsFromArray:proArr];
    [allRegionModelArr addObjectsFromArray:twoArr];
    [allRegionModelArr addObjectsFromArray:thirdArr];
    return [allRegionModelArr copy];
}




- (void)toolBarItemCancelAction {
    [self.textField resignFirstResponder];
}

- (void)toolBarItemSureAction {
    [self.textField resignFirstResponder];
    
    if (self.personalmodel != nil) {
        if (self.personalmodel.paramType ==  6) {
            
            NSString *selDate = [NSDate stringFromDate:self.datePicker.date withFormat:@"yyyy-MM-dd"];
            
            self.textField.text = selDate;
            self.personalmodel.inputValue = selDate;

        }else if(self.personalmodel.paramType ==  2){
            SAPersonalInfoCheckModel *checkModel = self.personalmodel.selectVo[self.selRow];
            self.textField.text = checkModel.name;
            self.personalmodel.inputValue = [NSString stringWithFormat:@"%@", checkModel.type];
            self.personalmodel.checkModel = checkModel;
            
        }else if(self.personalmodel.paramType ==  1){
            self.personalmodel.inputValue = self.textField.text;
        }
    }
}

#pragma mark - setters
- (void)setPersonalmodel:(SAPersonalInfoModel *)model {
    _personalmodel = model;
    
    self.titleLab.text =  model.paramName;
        
    
    self.titleLab.width = model.titleW + 10;
    
    if (model != nil) {
        self.textField.placeholder = model.inputDesc;
        self.textField.text = model.inputValue;
        self.personalmodel.inputStr = model.inputValue;
    }

    if (model.paramType == 1) { 
        self.textField.enabled = true;
        _textField.inputView = nil;
        self.pointerIcon.hidden = YES;
        if (model.number) {
            self.textField.keyboardType = UIKeyboardTypeNumberPad;
        } else {
            self.textField.keyboardType = UIKeyboardTypeDefault;
        }
    } else if (model.paramType == 2) { 
        self.textField.enabled = true;
        self.pointerIcon.hidden = NO;
        _textField.inputView = self.pickerView;
        [self configPickerType];
        
    } else if (model.paramType == 3) {      
        self.pointerIcon.hidden = NO;
        _textField.inputView = nil;
        _textField.enabled = NO;
    } else if(model.paramType == 6) {        
        self.textField.enabled = true;
        self.pointerIcon.hidden = NO;
        _textField.inputView = self.datePicker;
        [self configDatePicker];
        
    } else if (model.paramType == 7){ 
        self.pointerIcon.hidden = NO;
        _textField.inputView = nil;
        self.textField.enabled = false;
        [self configBankSelect];
    }
    
    else if (model.paramType == 8){
        self.pointerIcon.hidden = NO;
        _textField.inputView = nil;
        self.textField.enabled = false;
    }
    
    
    [self setNeedsLayout];
}

- (void)configSelectRelations
{
    NSMutableArray *btnArr = [NSMutableArray  array];
    [self.personalmodel.selectVo enumerateObjectsUsingBlock:^(SAPersonalInfoCheckModel *item, NSUInteger idx, BOOL * _Nonnull stop) {
        UIButton *btn = [UIButton buttonWithType:UIButtonTypeCustom];
        [btn setTitle:item.name forState: UIControlStateNormal];
        btn.titleLabel.font = Font_XX6(12);
        btn.layer.cornerRadius = 3;
        btn.layer.masksToBounds = YES;
        [self.btnBoxV addSubview:btn];
        [btnArr addObject:btn];
        
        btn.tag  = kBasicBtnTag + idx;
        [btn addTarget:self action:@selector(selectRelation:) forControlEvents:UIControlEventTouchUpInside];
        
        btn.backgroundColor = [UIColor colorWithHex:0xf6f6f6];
        [btn setTitleColor:[UIColor colorWithHex:0x444444] forState:UIControlStateNormal];
        if(self.personalmodel.inputValue == item.type){
            btn.backgroundColor = kPrimaryColor;
            [btn setTitleColor:[UIColor blackColor] forState:UIControlStateNormal];
        }
    }];
    self.btnArr = btnArr;
    
    
    [self.btnArr mas_distributeViewsAlongAxis:MASAxisTypeHorizontal withFixedItemLength:50 leadSpacing:10 tailSpacing:10];
    
    [self.btnArr mas_makeConstraints:^(SAConstraintMaker *make) {
        make.centerY.equalTo(self.btnBoxV);
        make.height.mas_equalTo(26);
    }];
}

- (void)configBankSelect {
    if (self.personalmodel != nil) {
        if (self.personalmodel.inputValue.length != 0) {     
            
            self.textField.text = self.personalmodel.checkModel.name;
            for (int i = 0; i < self.personalmodel.selectVo.count ; i++) {
                SAPersonalInfoCheckModel *checkModel = self.personalmodel.selectVo[i];
                if ([checkModel.type isEqualToString: self.personalmodel.inputValue]) {
                    self.textField.text = checkModel.name;
                    self.personalmodel.checkModel = checkModel;
                    break;
                }else{
                    [checkModel.children enumerateObjectsUsingBlock:^(SAPersonalInfoCheckModel *obj, NSUInteger idx, BOOL * _Nonnull stop) {
                        if ([obj.type isEqualToString: self.personalmodel.inputValue]) {
                            self.textField.text = obj.name;
                            self.personalmodel.checkModel = obj;
                        }
                    }];
                }
            }
        }
    }
}

- (void)configPickerType {
    if (self.personalmodel != nil) {
        if (self.personalmodel.inputValue.length != 0) {     
            for (int i = 0; i < self.personalmodel.selectVo.count ; i++) {
                SAPersonalInfoCheckModel *checkModel = self.personalmodel.selectVo[i];
                if ([checkModel.type isEqualToString: self.personalmodel.inputValue]) {
                    self.textField.text = checkModel.name;
                    self.personalmodel.checkModel = checkModel;
                    break;
                }
            }
        }
    }
}

- (void)configDatePicker{
    if (self.personalmodel != nil) {
        if (self.personalmodel.inputValue.length != 0) {     
            self.datePicker.date = [NSDate dateFromString:self.personalmodel.inputValue withFormat:@"yyyy-MM-dd"];
        }
    }
}

- (void)setFrame:(CGRect)frame
{
    frame.origin.x += 12;
    frame.size.width -= 24;
    
    frame.origin.y += 5;
    frame.size.height -= 10;
    
    [super setFrame:frame];
}

#pragma mark - getters

- (UIView *)bgView{
    FF_Fetch_UIView(_bgView, [UIColor whiteColor])
}

- (UILabel *)titleLab {
    if (_titleLab == nil) {
        _titleLab = [[UILabel alloc] init];
        _titleLab.textColor = [UIColor colorWithHex:0x333333];
        _titleLab.textAlignment = NSTextAlignmentLeft;
        _titleLab.font = [UIFont fontSizeOfXX_6:13];
        _titleLab.numberOfLines = 0;
    }
    return _titleLab;
}

- (SAPickerTextField *)textField {
    if (_textField == nil) {
        _textField = [[SAPickerTextField alloc] init];
        _textField.font = [UIFont fontSizeOfXX_6:13];
        _textField.textColor = [UIColor colorWithHex: 0x888888];
        _textField.inputAccessoryView = self.toolBar;
        _textField.delegate = self;
        _textField.textAlignment = NSTextAlignmentRight;
        _textField.autocorrectionType = UITextAutocorrectionTypeNo;
    }
    return _textField;
}

- (UIImageView *)pointerIcon {
    if (_pointerIcon == nil) {
        _pointerIcon = [[UIImageView alloc] init];
        _pointerIcon.contentMode = UIViewContentModeScaleAspectFit;
        _pointerIcon.image = [UIImage imageNamed:@"verify_list_arrow"];
        
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(tapArrow)];
        _pointerIcon.userInteractionEnabled = YES;
        [_pointerIcon addGestureRecognizer:tap];
    }
    return _pointerIcon;
}

- (UIPickerView *)pickerView {
    if (_pickerView == nil) {
        _pickerView = [[UIPickerView alloc] init];
        _pickerView.backgroundColor = [UIColor colorWithHex:0xefeff4];
        _pickerView.dataSource = self;
        _pickerView.delegate   = self;
    }
    return _pickerView;
}

- (UIDatePicker *)datePicker{
    if(_datePicker == nil){
        _datePicker = [[UIDatePicker alloc] init];
        _datePicker.datePickerMode = UIDatePickerModeDate;
        _datePicker.minimumDate = [NSDate dateFromString:@"1964-01-01" withFormat:@"yyyy-MM-dd"];
        _datePicker.maximumDate = [NSDate dateFromString:@"2028-01-01" withFormat:@"yyyy-MM-dd"];

        if (@available(iOS 13.4, *)) {
            _datePicker.preferredDatePickerStyle = UIDatePickerStyleWheels;
        }
    }
    return _datePicker;
}

- (UIToolbar *)toolBar {
    if (_toolBar == nil) {
        _toolBar = [[UIToolbar alloc]initWithFrame:CGRectMake(0, 0, UIScreenWidth, XX_6(44))];
        _toolBar.tintColor = [UIColor blueColor];
        _toolBar.backgroundColor = [UIColor grayColor];
        
        UIBarButtonItem * leftItem = [[UIBarButtonItem alloc] initWithTitle:@"取消" style:UIBarButtonItemStylePlain target:self action:@selector(toolBarItemCancelAction)];
        leftItem.tintColor = [UIColor colorWithHex:0x404040];
        
        UIBarButtonItem * rightItem = [[UIBarButtonItem alloc] initWithTitle:@"完成" style:UIBarButtonItemStylePlain target:self action:@selector(toolBarItemSureAction)];
        rightItem.tintColor = [UIColor colorWithHex:0x404040];
        
        UIBarButtonItem *space = [[UIBarButtonItem alloc] initWithBarButtonSystemItem:UIBarButtonSystemItemFlexibleSpace target:nil action:nil];
        
        _toolBar.items = @[leftItem, space, rightItem];
    }
    return _toolBar;
}

- (UIView *)btnBoxV{
    FF_Fetch_UIView(_btnBoxV, [UIColor clearColor])
}

- (NSMutableArray<UIButton *> *)btnArr{
    if(_btnArr == nil){
        _btnArr = [NSMutableArray array];
    }
    return _btnArr;
}


@end
