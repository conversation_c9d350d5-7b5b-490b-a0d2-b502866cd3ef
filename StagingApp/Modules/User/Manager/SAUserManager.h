
#import <Foundation/Foundation.h>
#import "SATabBarViewController.h"

NS_ASSUME_NONNULL_BEGIN

@class SAUserManager;

typedef void(^LoginSuccessBlock)(void);

@interface SAUserManager : NSObject

+ (instancetype)sharedInstance;

 
- (void)checkLoginProcess:(LoginSuccessBlock __nullable)loginComplete;


 
+ (void)deallocUserManger;


 
+ (NSString *)getToken;
+ (void)saveToken:(NSString *)token;

+ (NSInteger)getUserStatus;
+ (void)saveUserStatus:(NSInteger)status;

+ (NSString *)getJumpPrefix;
+ (void)saveJumpPrefix:(NSString *)jumpPrefix;

 
+ (void)loginOutClearInfo;

 
+ (BOOL)isLogin;


 
+ (void)isNeedLoginSuccess:(LoginSuccessBlock _Nullable)success;

+ (NSInteger)getBorrowStatus;
+ (void)setBorrowStatus:(NSInteger)status;

@end

NS_ASSUME_NONNULL_END
