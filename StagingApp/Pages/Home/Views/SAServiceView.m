
#import "SAServiceView.h"
#import "SACommonWebViewController.h"

@interface SAServiceView ()

@property (nonatomic, strong) UIImageView *iconImg;
@property (nonatomic, strong) UILabel *titleLab;

@end

@implementation SAServiceView

#pragma mark - Life cycle

- (void)showActivityService
{
    self.frame = CGRectMake(UIScreenWidth-kServiceViewSize-10, (self.superview.frame.size.height-kServiceViewSize)/2, kServiceViewSize, kServiceViewSize);
    self.layer.masksToBounds = YES;
    self.layer.cornerRadius = kServiceViewSize/2;
    
}

- (instancetype)init
{
    if (self = [super init])
    {
        [self createUI];
        [self setConstraints];
        
        self.titleLab.text = @"客服咨询";
        self.backgroundColor = kPrimaryColor;
        
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(jumpCustomer)];
        self.userInteractionEnabled = YES;
        [self addGestureRecognizer:tap];
        
        UIPanGestureRecognizer *pan = [[UIPanGestureRecognizer alloc] initWithTarget:self action:@selector(moveView:)];
        [self addGestureRecognizer:pan];
    }
    
    return self;
}

- (void)createUI
{
    [self addSubview:self.iconImg];
    [self addSubview:self.titleLab];
}

- (void)setConstraints
{
    [self.iconImg mas_makeConstraints:^(SAConstraintMaker *make) {
        make.width.height.mas_equalTo(25);
        make.centerX.equalTo(self);
        make.bottom.equalTo(self.mas_centerY).offset(1);
    }];
    
    [self.titleLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.centerX.equalTo(self);
        make.top.equalTo(self.iconImg.mas_bottom).offset(3);
    }];
}

#pragma mark - method

- (void)jumpCustomer
{
    if(![NSString judgeStringExist:[SAAdvanceManager sharedInstance].customerServiceUrl]) return;
    
    SACommonWebViewController *webVC = [[SACommonWebViewController alloc] init];
    webVC.url = [SAAdvanceManager sharedInstance].customerServiceUrl;
    webVC.navTitle = @"在线客服";
    webVC.hidesBottomBarWhenPushed = YES;
    [[SACommonTool currentViewController].navigationController pushViewController:webVC animated:NO];
}

- (void)moveView:(UIPanGestureRecognizer *)panGestureRecognizer
{
    
    UIView *container = [SACommonTool currentViewController].view;
    CGPoint transP = [panGestureRecognizer locationInView:container];
    NSLog(@"transP---%@", NSStringFromCGPoint(transP));
    self.frame = CGRectMake(transP.x - kServiceViewSize/2 , transP.y - kServiceViewSize/2, kServiceViewSize, kServiceViewSize);
}

#pragma mark - getter

- (UIImageView *)iconImg{
    FF_Fetch_UIImageViewWithImage(_iconImg, [UIImage imageNamed:@"customer_service"])
}

- (UILabel *)titleLab{
    FF_Fetch_UILable_CenterX(_titleLab, kHomeTextColor, Font_XX6(9))
}

@end
