
 

#import <Foundation/Foundation.h>
#import "SAService.h"

@protocol QLServiceFactoryDataSource <NSObject>

 
- (NSDictionary<NSString *,NSString *> *)servicesKindsOfServiceFactory;

@end

@interface SAServiceFactory : NSObject

@property (nonatomic, weak) id<QLServiceFactoryDataSource> dataSource;

+ (instancetype)sharedInstance;
- (SAService<QLServiceProtocol> *)serviceWithIdentifier:(NSString *)identifier;

@end
