
#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface SAResultModel : NSObject

 
@property (nullable, nonatomic, copy) NSString *key;
 
@property (nullable, nonatomic, copy) NSString *value;;
 
@property (nullable, nonatomic, copy) NSString *parentKey;
 
@property (nullable, nonatomic, copy) NSString *parentValue;
 
@property (nullable, nonatomic, copy) NSArray<SAResultModel *> *children;
 
@property (nonatomic, assign) NSInteger index;

@property (nullable, nonatomic, strong) id extras;
@property (nullable, nonatomic, copy) NSString *level;
@property (nullable, nonatomic, copy) NSString *remark;

@end

NS_ASSUME_NONNULL_END
