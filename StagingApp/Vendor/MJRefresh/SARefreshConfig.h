
#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface SARefreshConfig : NSObject

 
@property (copy, nonatomic, nullable) NSString *languageCode;

 
@property (copy, nonatomic, nullable) NSString *i18nFilename;
 
@property (nonatomic, nullable) NSBundle *i18nBundle;

 
@property (class, nonatomic, readonly) SARefreshConfig *defaultConfig;

- (instancetype)init NS_UNAVAILABLE;
+ (instancetype)new NS_UNAVAILABLE;

@end

NS_ASSUME_NONNULL_END
