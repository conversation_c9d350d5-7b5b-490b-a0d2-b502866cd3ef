
#import "SADate+Extension.h"

@implementation NSDate (Extension)

+ (NSDate *)dateFromString:(NSString *)string withFormat:(NSString *)format
{
    NSDateFormatter *inputFormatter = [[NSDateFormatter alloc] init];
    [inputFormatter setDateFormat:format];
    NSDate *date = [inputFormatter dateFromString:string];
    NSTimeZone *zone = [NSTimeZone systemTimeZone];
    NSInteger interval = [zone secondsFromGMTForDate:[NSDate date]];
    date = [date dateByAddingTimeInterval:interval];
    
    return date;
}

+ (NSString *)stringFromDate:(NSDate *)date withFormat:(NSString *)format
{
    return [date stringWithFormat:format];
}

+ (NSString *)stringForDisplayFromDate:(NSDate *)date prefixed:(BOOL)prefixed
{
     
    
    NSDate *today = [NSDate date];
    NSCalendar *calendar = [NSCalendar currentCalendar];
    NSDateComponents *offsetComponents = [calendar components:(NSCalendarUnitYear | NSCalendarUnitMonth | NSCalendarUnitDay)
                                                     fromDate:today];
    NSDate *midnight = [calendar dateFromComponents:offsetComponents];
    NSDateFormatter *displayFormatter = [[NSDateFormatter alloc] init];
    NSString *displayString = nil;
    
    
    if ([date compare:midnight] == NSOrderedDescending)
    {
        if (prefixed)
        {
            [displayFormatter setDateFormat:@"'at' h:mm a"]; 
        }
        else
        {
            [displayFormatter setDateFormat:@"h:mm a"]; 
        }
    }
    else
    {
        
        NSDateComponents *componentsToSubtract = [[NSDateComponents alloc] init];
        [componentsToSubtract setDay:-7];
        
        NSDate *lastweek = [calendar dateByAddingComponents:componentsToSubtract toDate:today options:0];
        
        if ([date compare:lastweek] == NSOrderedDescending)
        {
            [displayFormatter setDateFormat:@"EEEE"]; 
        }
        else
        {
            
            
            NSInteger thisYear = [offsetComponents year];
            NSDateComponents *dateComponents = [calendar components:(NSCalendarUnitYear | NSCalendarUnitMonth |NSCalendarUnitDay)  fromDate:date];
            NSInteger thatYear = [dateComponents year];
            
            if (thatYear >= thisYear) {
                [displayFormatter setDateFormat:@"MMM d"];
            } else {
                [displayFormatter setDateFormat:@"MMM d, yyyy"];
            }
        }
        
        if (prefixed) {
            NSString *dateFormat = [displayFormatter dateFormat];
            NSString *prefix = @"'on' ";
            [displayFormatter setDateFormat:[prefix stringByAppendingString:dateFormat]];
        }
    }
    
    
    displayString = [displayFormatter stringFromDate:date];
    
    return displayString;
}

+ (NSString *)stringForDisplayFromDate:(NSDate *)date {
    return [self stringForDisplayFromDate:date prefixed:NO];
}

- (NSString *)getFormatYearMonthDay
{
    NSString *string = [NSString stringWithFormat:@"%lu%02lu%02lu",(unsigned long)[self getYear],(unsigned long)[self getMonth],(unsigned long)[self getDay]];
    return string;
}

- (NSString *)getFormatYearMonth
{
    NSString *string = [NSString stringWithFormat:@"%lu%02lu",(unsigned long)[self getYear],(unsigned long)[self getMonth]];
    return string;
}

- (NSInteger )getWeekNumOfMonth
{
    return [[self endOfMonth] getWeekOfYear] - [[self beginningOfMonth] getWeekOfYear] + 1;
}

- (NSInteger )getWeekOfYear
{
    int i;
    NSUInteger year = [self getYear];
    
    NSDate *date = [self endOfWeek];
    
    for (i = 1;[[date dateAfterDay:-7 * i] getYear] == year;i++);
    
    return i;
}

- (long long)todayMaxTimeInterval
{
    NSString *timeString = [self stringWithFormat:@"yyyy-MM-dd"];
    timeString = [NSString stringWithFormat:@"%@ 23:59:59", timeString];
    
    NSDate *timeDate = [NSDate dateFromString:timeString withFormat:@"yyyy-MM-dd HH:mm:ss"];
    double maxSeconds = [timeDate timeIntervalSince1970];
    return (long long)maxSeconds;
}

- (long long)todayMinTimeInterval
{
    NSString *timeString = [self stringWithFormat:@"yyyy-MM-dd"];
    timeString = [NSString stringWithFormat:@"%@ 00:00:00", timeString];
    
    NSDate *timeDate = [NSDate dateFromString:timeString withFormat:@"yyyy-MM-dd HH:mm:ss"];
    double minSeconds = [timeDate timeIntervalSince1970];
    return (long long)minSeconds;
}


- (BOOL)isInTheSameDay:(NSDate *)date
{
    long long todayMaxTimeInterval = [self todayMaxTimeInterval];
    long long todayMinTimeInterval = [self todayMinTimeInterval];
    double timeInterval            = [date timeIntervalSince1970];
    return ((timeInterval <= todayMaxTimeInterval) && (timeInterval >= todayMinTimeInterval));
}

- (NSDate *)dateAfterDay:(NSUInteger)day
{
    NSCalendar *calendar = [NSCalendar currentCalendar];
    
    
    
    NSDateComponents *componentsToAdd = [[NSDateComponents alloc] init];
    
    [componentsToAdd setDay:day];
    
    NSDate *dateAfterDay = [calendar dateByAddingComponents:componentsToAdd toDate:self options:0];
    
    return dateAfterDay;
}

- (NSDate *)lastMonth
{
    NSCalendar *calendar = [NSCalendar currentCalendar];
    NSDateComponents *components = [calendar components:NSCalendarUnitWeekday | NSCalendarUnitYear | NSCalendarUnitMonth | NSCalendarUnitDay fromDate:self];
    [components setMonth:([components month] - 1)];
    return [calendar dateFromComponents:components];
}

- (NSDate *)dateafterMonth:(int)month
{
    NSCalendar *calendar = [NSCalendar currentCalendar];
    NSDateComponents *componentsToAdd = [[NSDateComponents alloc] init];
    [componentsToAdd setMonth:month];
    NSDate *dateAfterMonth = [calendar dateByAddingComponents:componentsToAdd toDate:self options:0];
    
    return dateAfterMonth;
}

- (NSUInteger)getDay
{
    NSCalendar *calendar = [NSCalendar currentCalendar];
    NSDateComponents *dayComponents = [calendar components:(NSCalendarUnitDay) fromDate:self];
    
    return [dayComponents day];
}

- (NSString *)getWeek
{
    NSString *weekStr;
    
    NSDate *date = [NSDate date];
    
    NSArray *arrWeek = [NSArray arrayWithObjects:@"日", @"一", @"二", @"三", @"四", @"五", @"六", nil];
    NSCalendar *calendar = [[NSCalendar alloc] initWithCalendarIdentifier:NSCalendarIdentifierGregorian];
    NSDateComponents *weekdayComponents = [calendar components:NSCalendarUnitWeekday fromDate:date];
    
    NSInteger week = [weekdayComponents weekday];
    
    weekStr = arrWeek[week-1];
    
    return weekStr;
}

- (NSUInteger)getMonth
{
    NSCalendar *calendar = [NSCalendar currentCalendar];
    NSDateComponents *dayComponents = [calendar components:(NSCalendarUnitMonth) fromDate:self];
    
    return [dayComponents month];
}

- (NSUInteger)getYear
{
    NSCalendar *calendar = [NSCalendar currentCalendar];
    NSDateComponents *dayComponents = [calendar components:(NSCalendarUnitYear) fromDate:self];
    
    return [dayComponents year];
}

- (NSInteger )getHour
{
    NSCalendar *calendar = [NSCalendar currentCalendar];
    NSUInteger unitFlags = NSCalendarUnitYear| NSCalendarUnitMonth | NSCalendarUnitDay |NSCalendarUnitHour |NSCalendarUnitMinute;
    NSDateComponents *components = [calendar components:unitFlags fromDate:self];
    NSInteger hour = [components hour];
    
    return hour;
}

- (NSInteger)getMinute
{
    NSCalendar *calendar = [NSCalendar currentCalendar];
    NSUInteger unitFlags = NSCalendarUnitYear | NSCalendarUnitMonth | NSCalendarUnitDay |NSCalendarUnitHour |NSCalendarUnitMinute;
    NSDateComponents *components = [calendar components:unitFlags fromDate:self];
    NSInteger minute = [components minute];
    
    return minute;
}

- (NSInteger )getHour:(NSDate *)date
{
    NSCalendar *calendar = [NSCalendar currentCalendar];
    NSUInteger unitFlags = NSCalendarUnitYear | NSCalendarUnitMonth | NSCalendarUnitDay |NSCalendarUnitHour |NSCalendarUnitMinute;
    NSDateComponents *components = [calendar components:unitFlags fromDate:date];
    NSInteger hour = [components hour];
    
    return (NSInteger)hour;
}

- (NSInteger)getMinute:(NSDate *)date
{
    NSCalendar *calendar = [NSCalendar currentCalendar];
    NSUInteger unitFlags = NSCalendarUnitYear | NSCalendarUnitMonth | NSCalendarUnitDay |NSCalendarUnitHour |NSCalendarUnitMinute;
    NSDateComponents *components = [calendar components:unitFlags fromDate:date];
    NSInteger minute = [components minute];
    
    return (NSInteger)minute;
}

- (NSUInteger)daysAgo
{
    NSCalendar *calendar = [NSCalendar currentCalendar];
    NSDateComponents *components = [calendar components:(NSCalendarUnitDay)
                                               fromDate:self
                                                 toDate:[NSDate date]
                                                options:0];
    
    return [components day];
}

- (NSUInteger)daysAgoAgainstMidnight
{
    
    NSDateFormatter *mdf = [[NSDateFormatter alloc] init];
    [mdf setDateFormat:@"yyyy-MM-dd"];
    NSDate *midnight = [mdf dateFromString:[mdf stringFromDate:self]];
    
    return (NSInteger)[midnight timeIntervalSinceNow] / (60*60*24) *-1;
}



- (NSString *)stringDaysAgo {
    return [self stringDaysAgoAgainstMidnight:YES];
}



- (NSString *)stringDaysAgoAgainstMidnight:(BOOL)flag {
    NSUInteger daysAgo = (flag) ? [self daysAgoAgainstMidnight] : [self daysAgo];
    
    NSString *text = nil;
    switch (daysAgo) {
        case 0:
            text = @"Today";
            break;
        case 1:
            text = @"Yesterday";
            break;
        default:
            text = [NSString stringWithFormat:@"%lu days ago", (unsigned long)daysAgo];
    }
    
    return text;
}

- (NSUInteger)weekday
{
    NSCalendar *calendar = [NSCalendar currentCalendar];
    NSDateComponents *weekdayComponents = [calendar components:(NSCalendarUnitWeekday) fromDate:self];
    
    return [weekdayComponents weekday];
}

- (NSUInteger)weekday:(NSDate *)date
{
    NSCalendar *calendar = [NSCalendar currentCalendar];
    NSDateComponents *comps =[calendar components:(NSCalendarUnitWeekOfMonth | NSCalendarUnitWeekday|NSCalendarUnitWeekdayOrdinal)  fromDate:date];
    NSInteger weekday = [comps weekday]; 
    return weekday;
}

static NSDateFormatter * outputFormatter;

- (NSString *)stringWithFormat:(NSString *)format
{
    if(outputFormatter == nil)
    {
        outputFormatter = [[NSDateFormatter alloc] init];
    }
    
    [outputFormatter setDateFormat:format];
    NSString *timestamp_str = [outputFormatter stringFromDate:self];
    
    return timestamp_str;
}

- (NSString *)stringWithDateStyle:(NSDateFormatterStyle)dateStyle timeStyle:(NSDateFormatterStyle)timeStyle
{
    if(outputFormatter == nil)
    {
        outputFormatter = [[NSDateFormatter alloc] init];
    }
    
    [outputFormatter setDateStyle:dateStyle];
    [outputFormatter setTimeStyle:timeStyle];
    NSString *outputString = [outputFormatter stringFromDate:self];
    
    return outputString;
}

- (NSDate *)beginningOfWeek
{
    
    
    NSCalendar *calendar = [NSCalendar currentCalendar];
    NSDate *beginningOfWeek = nil;
    BOOL ok = [calendar rangeOfUnit:NSCalendarUnitWeekOfMonth startDate:&beginningOfWeek
                           interval:NULL forDate:self];
    if (ok)
    {
        return beginningOfWeek;
    }
    
    
    
    NSDateComponents *weekdayComponents = [calendar components:NSCalendarUnitWeekday fromDate:self];
    
     
    NSDateComponents *componentsToSubtract = [[NSDateComponents alloc] init];
    [componentsToSubtract setDay: 0 - ([weekdayComponents weekday] - 1)];
    
    beginningOfWeek = nil;
    beginningOfWeek = [calendar dateByAddingComponents:componentsToSubtract toDate:self options:0];
    
    
    NSDateComponents *components = [calendar components:(NSCalendarUnitYear | NSCalendarUnitMonth | NSCalendarUnitDay)
                                               fromDate:beginningOfWeek];
    
    return [calendar dateFromComponents:components];
}

- (NSDate *)beginningOfDay
{
    NSCalendar *calendar = [NSCalendar currentCalendar];
    
    
    NSDateComponents *components = [calendar components:(NSCalendarUnitYear | NSCalendarUnitMonth | NSCalendarUnitDay)
                                               fromDate:self];
    return [calendar dateFromComponents:components];
}

- (NSDate *)beginningOfMonth
{
    NSUInteger i = -[self getDay] + 1;
    return [self dateAfterDay: i];
}

- (NSDate *)nextBeginningOfMonth
{
    return [[self beginningOfMonth] dateafterMonth:1];
}

- (NSDate *)endOfMonth
{
    return [[self nextBeginningOfMonth] dateAfterDay:-1];
}

+(NSDate *)getSinceOrMaxOfDay : (BOOL)isMax
{
    NSCalendar *calendar = [NSCalendar currentCalendar];
    NSDateComponents *components = [calendar components:NSUIntegerMax fromDate:[NSDate date]];
    components.hour =isMax ? 23 : 0;
    components.minute = isMax ? 59 : 0;
    components.second = isMax ? 59 : 0;
    NSDate * currentDateByGMT = [calendar dateFromComponents:components];
    NSDate * currentDateByGMTPlus = [currentDateByGMT dateByAddingTimeInterval:[components.timeZone secondsFromGMTForDate:currentDateByGMT]];
    
    return currentDateByGMTPlus;
}


- (NSDate *)endOfWeek {
    NSCalendar *calendar = [NSCalendar currentCalendar];
    
    NSDateComponents *weekdayComponents = [calendar components:NSCalendarUnitWeekday fromDate:self];
    NSDateComponents *componentsToAdd = [[NSDateComponents alloc] init];
    
    
    [componentsToAdd setDay:(7 - [weekdayComponents weekday])];
    NSDate *endOfWeek = [calendar dateByAddingComponents:componentsToAdd toDate:self options:0];
    
    return endOfWeek;
}

- (NSInteger)daysBetweenDate:(NSDate *)date
{
    NSCalendar *calendar = [NSCalendar currentCalendar];
    NSDateComponents *components = [calendar components:NSCalendarUnitDay fromDate:date toDate:self options:0];
    
    return [components day];
}

- (NSInteger)daysFromNow
{
    NSCalendar *calendar = [NSCalendar currentCalendar];
    NSDateComponents *components = [calendar components:NSCalendarUnitDay fromDate:[NSDate date] toDate:self options:0];
    
    return [components day];
}

- (NSInteger)monthsBetweenDate:(NSDate *)date
{
    NSCalendar *calendar = [NSCalendar currentCalendar];
    NSDateComponents *components = [calendar components:NSCalendarUnitDay fromDate:date toDate:self options:0];
    
    return [components month];
}

+ (NSInteger)getDaysInMonthFromDate:(NSDate*)date
{
    NSCalendar *c = [NSCalendar currentCalendar];
    NSRange days  = [c rangeOfUnit:NSCalendarUnitDay
                            inUnit:NSCalendarUnitMonth
                           forDate:date];
    
    return days.length;
}

+ (BOOL)date:(NSDate*)date isBetweenDate:(NSDate*)beginDate andDate:(NSDate*)endDate
{
    if ([date compare:beginDate] == NSOrderedAscending)
        return NO;
    
    if ([date compare:endDate] == NSOrderedDescending)
        return NO;
    
    return YES;
}

#pragma mark - private

+ (NSString *)getPreferredLanguage
{
    NSUserDefaults* defs = [NSUserDefaults standardUserDefaults];
    NSArray* languages = [defs objectForKey:@"AppleLanguages"];
    NSString* preferredLang = [languages objectAtIndex:0];
    return preferredLang;
}

+ (NSString *)dataWithString:(NSString *)secs
{
    NSDate *date_    = [NSDate dateWithTimeIntervalSince1970:secs.intValue];
    NSUInteger day   = [date_ weekday:date_];
    
    NSString *weDays =@"";
    switch (day) {
        case 1:
            weDays = @"周日";
            break;
        case 2:
            weDays = @"周一";
            break;
        case 3:
            weDays = @"周二";
            break;
        case 4:
            weDays = @"周三";
            break;
        case 5:
            weDays = @"周四";
            break;
        case 6:
            weDays = @"周五";
            break;
        case 7:
            weDays = @"周六";
            break;
            
        default:
            break;
    }
    
    return weDays;
}

- (NSString*)transformToFuzzyDate
{
    NSDate* nowDate = [NSDate date];
    NSUInteger timeInterval = [nowDate timeIntervalSinceDate:self];
    
    NSCalendar *greCalendar = [[NSCalendar alloc] initWithCalendarIdentifier:NSCalendarIdentifierGregorian];
    
    NSDateComponents *nowDateComponents = [greCalendar components:NSCalendarUnitYear | NSCalendarUnitMonth | NSCalendarUnitDay | NSCalendarUnitHour | NSCalendarUnitMinute | NSCalendarUnitSecond | NSCalendarUnitWeekOfMonth | NSCalendarUnitWeekday | NSCalendarUnitWeekOfYear fromDate:nowDate];
    
    NSDateComponents *selfDateComponents = [greCalendar components:NSCalendarUnitYear | NSCalendarUnitMonth | NSCalendarUnitDay | NSCalendarUnitHour | NSCalendarUnitMinute | NSCalendarUnitSecond | NSCalendarUnitWeekOfMonth | NSCalendarUnitWeekday | NSCalendarUnitWeekOfYear fromDate:self];
    
    if (timeInterval < 5 * 60)
    {
        return @"刚刚";
    }
    else if (timeInterval < 60 * 60)
    {
        NSString* dateString = [NSString stringWithFormat:@"%lu分钟前",timeInterval / 60];
        return dateString;
    }
    else if (timeInterval < 24 * 60 * 60 && nowDateComponents.day == selfDateComponents.day)
    {
        NSString* dateString = [NSString stringWithFormat:@"%lu小时前",timeInterval / (60 * 60)];
        return dateString;
    }
    else if (timeInterval < 24 * 60 * 60 && nowDateComponents.day != selfDateComponents.day)
    {
        return @"昨天";
    }
    else if (([nowDateComponents weekOfMonth] == [selfDateComponents weekOfMonth]) && ([nowDateComponents weekOfYear] == [selfDateComponents weekOfYear]))
    {
        NSArray* weekdays = @[@"星期一",@"星期二",@"星期三",@"星期四",@"星期五",@"星期六",@"星期日"];
        NSString* dateString = weekdays[selfDateComponents.weekday];
        return dateString;
    }
    else if ([self timeIntervalSince1970] == 0)
    {
        return nil;
    }
    else
    {
        NSDateFormatter* dateFormatter = [[NSDateFormatter alloc] init];
        [dateFormatter setDateFormat:@"yy-MM-dd"];
        NSString* dateString = [dateFormatter stringFromDate:self];
        return dateString;
    }
}

- (NSString*)promptDateString
{
    NSDate* nowDate = [NSDate date];
    
    NSCalendar *greCalendar = [[NSCalendar alloc] initWithCalendarIdentifier:NSCalendarIdentifierGregorian];
    
    NSDateComponents *nowDateComponents = [greCalendar components:NSCalendarUnitYear | NSCalendarUnitMonth | NSCalendarUnitDay | NSCalendarUnitHour | NSCalendarUnitMinute | NSCalendarUnitSecond | NSCalendarUnitWeekOfMonth | NSCalendarUnitWeekday | NSCalendarUnitWeekOfYear fromDate:nowDate];
    
    NSDateComponents *selfDateComponents = [greCalendar components:NSCalendarUnitYear | NSCalendarUnitMonth | NSCalendarUnitDay | NSCalendarUnitHour | NSCalendarUnitMinute | NSCalendarUnitSecond | NSCalendarUnitWeekOfMonth | NSCalendarUnitWeekday | NSCalendarUnitWeekOfYear fromDate:self];
    
    NSDateComponents *weeDateComponents = [[NSDateComponents alloc] init];
    [weeDateComponents setCalendar:[NSCalendar currentCalendar]];
    weeDateComponents.year = selfDateComponents.year;
    weeDateComponents.month = selfDateComponents.month;
    weeDateComponents.day = selfDateComponents.day;
    weeDateComponents.hour = 0;
    weeDateComponents.minute = 0;
    weeDateComponents.second = 0;
    
    NSDate* weeDate = [[weeDateComponents date] dateByAddingTimeInterval:24 * 60 * 60];
    
    NSString* lastComponents = nil;
    NSString* twoComponent = nil;
    NSInteger hour = nowDateComponents.hour;
    if (hour < 12)
    {
        twoComponent = @"上午";
    }
    else
    {
        twoComponent = @"下午";
        hour = hour - 12;
        
    }
    if (selfDateComponents.minute < 10)
    {
        lastComponents = [NSString stringWithFormat:@"%@ %li:0%li",twoComponent,(long)selfDateComponents.hour,(long)selfDateComponents.minute];
    }
    else
    {
        lastComponents = [NSString stringWithFormat:@"%@ %li:%li",twoComponent,(long)selfDateComponents.hour,(long)selfDateComponents.minute];
    }
    
    NSInteger timeInterval = [nowDate timeIntervalSinceDate:weeDate];
    
    NSString* dateString = nil;
    if (timeInterval < 24 * 60 * 60)
    {
        if (nowDateComponents.day == selfDateComponents.day) {
            
            dateString = lastComponents;
        }
        else
        {
            
            dateString = [NSString stringWithFormat:@"昨天 %@",lastComponents];
        }
    }
    else if (([nowDateComponents weekOfMonth] == [selfDateComponents weekOfMonth]) && ([nowDateComponents weekOfYear] == [selfDateComponents weekOfYear]))
    {
        
        NSArray* weekdays = @[@"temp",@"星期日",@"星期一",@"星期二",@"星期三",@"星期四",@"星期五",@"星期六"];
        NSString* weekdayString = weekdays[selfDateComponents.weekday];
        dateString = [NSString stringWithFormat:@"%@%@",weekdayString,lastComponents];
    }
    else
    {
        NSDateFormatter* dateFormatter = [[NSDateFormatter alloc] init];
        [dateFormatter setDateFormat:@"yyyy年MM月dd日"];
        dateString = [NSString stringWithFormat:@"%@ %@",[dateFormatter stringFromDate:self],lastComponents];
    }
    return dateString;
}

@end
