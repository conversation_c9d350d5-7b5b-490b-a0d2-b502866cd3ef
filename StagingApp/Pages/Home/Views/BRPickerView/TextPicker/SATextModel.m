
#import "SATextModel.h"

@implementation SATextModel

- (instancetype)initWithDictionary:(NSDictionary *)dictionary {
    if (self = [super init]) {
        self.code = dictionary[@"code"];
        self.text = dictionary[@"text"];
        self.parentCode = dictionary[@"parent_code"];
        NSArray *childrenArray = dictionary[@"children"];
        if (childrenArray) {
            NSMutableArray *tempArr = [NSMutableArray array];
            for (NSDictionary *childDict in childrenArray) {
                SATextModel *child = [[SATextModel alloc] initWithDictionary:childDict];
                [tempArr addObject:child];
            }
            self.children = [tempArr copy];
        }
    }
    return self;
}

- (BOOL)isEqual:(id)object {
    
    if (self == object) {
        return YES;
    }
    
    if (![object isKindOfClass:[SATextModel class]]) {
        return NO;
    }
    
    SATextModel *model = (SATextModel *)object;
    if (!model) {
        return NO;
    }
    
    BOOL isSameCode = (!self.code && !model.code) || [self.code isEqualToString:model.code];
    BOOL isSameText = (!self.text && !model.text) || [self.text isEqualToString:model.text];
    
    return isSameCode && isSameText;
}

- (NSUInteger)hash {
    return [self.code hash] ^ [self.text hash];
}


- (void)setValue:(id)value forUndefinedKey:(NSString *)key {
    
}

- (void)setNilValueForKey:(NSString *)key {
    
}

- (id)valueForUndefinedKey:(NSString *)key {
    return nil;
}

@end


@implementation NSArray (BRPickerView)

+ (NSArray *)br_modelArrayWithJson:(NSArray *)dataArr mapper:(nullable NSDictionary *)mapper {
    if (!mapper) {
        
        mapper = @{
            @"code": @"code",
            @"text": @"text",
            @"parentCode": @"parent_code",
            @"extras": @"extras",
            @"children": @"children"
        };
    }
    NSMutableArray *tempArr = [NSMutableArray array];
    for (NSDictionary *dic in dataArr) {
        SATextModel *model = [[SATextModel alloc]init];
        if (mapper[@"code"]) {
            model.code = [NSString stringWithFormat:@"%@", dic[mapper[@"code"]]];
        }
        if (mapper[@"text"]) {
            model.text = dic[mapper[@"text"]];
        }
        if (mapper[@"parentCode"]) {
            model.parentCode = [NSString stringWithFormat:@"%@", dic[mapper[@"parentCode"]]];
        }
        if (mapper[@"extras"]) {
            model.extras = dic[mapper[@"extras"]];
        }
        if (dic[mapper[@"children"]]) {
            model.children = [self br_modelArrayWithJson:dic[mapper[@"children"]] mapper:mapper]; 
        }
        [tempArr addObject:model];
    }

    return [tempArr copy];
}

- (NSArray *)br_getValueArr:(NSString *)propertyName {
    NSMutableArray *valueArr = [[NSMutableArray alloc]init];
    for (SATextModel *model in self) {
        id propertyValue = [model valueForKey:propertyName];
        if (propertyValue) {
            [valueArr addObject:propertyValue];
        }
    }
    return [valueArr copy];
}

- (NSString *)br_joinValue:(NSString *)propertyName separator:(NSString *)separator {
    NSArray *valueArr = [self br_getValueArr:propertyName];
    if (valueArr && valueArr.count > 0) {
        return [valueArr componentsJoinedByString:separator];
    }
    return @"";
}

- (NSString *)br_joinText:(NSString *)separator {
    NSArray *valueArr = [self br_getValueArr:@"text"];
    if (valueArr && valueArr.count > 0) {
        return [valueArr componentsJoinedByString:separator];
    }
    return @"";
}

- (NSArray<SATextModel *> *)br_buildTreeArray {
    NSMutableArray<SATextModel *> *treeModels = [NSMutableArray array];
    NSMutableDictionary<NSString *, SATextModel *> *allItemDic = [NSMutableDictionary dictionary];

    
    for (SATextModel *model in self) {
        allItemDic[model.code] = model;
    }
    
    for (SATextModel *model in self) {
        NSString *parentCode = model.parentCode;
        SATextModel *parentModel = allItemDic[parentCode];
        if (parentModel) {
            if (!parentModel.children) {
                parentModel.children = [NSArray array];
            }
            parentModel.children = [parentModel.children arrayByAddingObject:model];
        } else {
            
            [treeModels addObject:model];
        }
    }
    
    return treeModels;
}

@end
