
#import "SAVerifyIdentityViewController.h"
#import "SAUploadManager.h"
#import <SDWebImage/SDWebImage.h>
#import "SADictionary+QLNetworkingMethods.h"
#import "SAManager.h"
#import "SASubmitManager.h"
#import "SACompareUploadManager.h"
#import "SATabBarViewController.h"
#import "SAGetRegManager.h"
#import "SAGetOcrConfigApiManager.h"
#import "SAFaceSubmitApiManager.h"
#import "SAPhotoManager.h"
#import "SAGetProtocolApiManager.h"
#import <EsignSDK/EsignSDKIOS.h>
#import "SACommonWebViewController.h"

#import "SAFaceBackAlert.h"


@interface SAVerifyIdentityViewController ()<QLAPIManagerCallBackDelegate, QLAPIManagerParamSource, UITextFieldDelegate, UITextViewDelegate, NPRequestMethodProtocol, UIGestureRecognizerDelegate>

@property (nonatomic, strong) SACompareUploadManager *uploadManager;
@property (nonatomic, strong) NSDictionary *compareDict;

@property (nonatomic, strong)SAManager *ocrMgr;  

@property (nonatomic, strong) SAGetRegManager *regManager;
@property (nonatomic, strong) NSString *cardReg;

@property (nonatomic, strong) UIView *frontBg;
@property (nonatomic, strong) UILabel *frontLab1;
@property (nonatomic, strong) UILabel *frontLab2;
@property (nonatomic, strong) UIImageView *frontImg;

@property (nonatomic, strong) UIView *backBg;
@property (nonatomic, strong) UILabel *backLab1;
@property (nonatomic, strong) UILabel *backLab2;
@property (nonatomic, strong) UIImageView *backImg;

@property (nonatomic, strong) UILabel *tipsLab;
@property (nonatomic, strong) UIImageView *tipsImg;
@property (nonatomic, strong) UILabel *titleLab1;
@property (nonatomic, strong) UILabel *titleLab2;

@property (nonatomic, strong) UIView *inputBg;
@property (nonatomic, strong) UITextField *nameField;
@property (nonatomic, strong) UITextField *cardField;

@property (nonatomic, strong) UIView *footerView;

@property (nonatomic, strong) NSString *frontUrl;
@property (nonatomic, strong) NSString *backUrl;
@property (nonatomic, strong) NSString *bestFaceUrl;


@property (nonatomic, assign) NPCardType cardType;

@property (nonatomic, strong) SAGetOcrConfigApiManager *configManager;
@property (nonatomic, strong) SASubmitManager *submitOCRManager;
@property (nonatomic, strong) SAFaceSubmitApiManager *submitFaceManager;


@property (nonatomic, assign) BOOL finished;

@property (nonatomic, assign) BOOL isAgree;
@property (nonatomic, strong) UIView *bottomView;
@property (nonatomic, strong) UIImageView *agreeImg;
@property (nonatomic, strong) SAGetProtocolApiManager *protocolManager;
@property (nonatomic, strong) NSString *protocolKey;


@property (nonatomic, strong) NSString *flowId;
@property (nonatomic, strong) NSString *faceCode;
@property (nonatomic, strong) NSString *faceResult;

@property (nonatomic, strong) UIView *topTipsV;

@end

@implementation SAVerifyIdentityViewController

- (void)viewDidAppear:(BOOL)animated
{
    [super viewWillAppear:animated];
    
}

- (void)viewWillDisappear:(BOOL)animated
{
    [super viewWillDisappear:animated];
}

- (void)viewDidLoad
{
    [super viewDidLoad];
    self.navigationItem.title = @"身份认证";
    self.view.backgroundColor = [UIColor colorWithHex:0xf6f6f6];
    self.isAgree = YES;
    
    self.navigationController.interactivePopGestureRecognizer.delegate = self;
    
    [self showActivityHUD:nil];
    [self.configManager loadData];
    
    [self addCustomViews];
    [self addViewsConstrations];
}

- (void)addCustomViews
{
    [self.view addSubview:self.topTipsV];
    [self.topTipsV mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.right.top.equalTo(self.view);
        make.height.mas_equalTo(54);
    }];
    
    [self.view addSubview:self.frontBg];
    [self.frontBg addSubview:self.frontLab1];
    [self.frontBg addSubview:self.frontLab2];
    [self.frontBg addSubview:self.frontImg];
    
    [self.view addSubview:self.backBg];
    [self.backBg addSubview:self.backLab1];
    [self.backBg addSubview:self.backLab2];
    [self.backBg addSubview:self.backImg];
    
    [self.view addSubview:self.tipsLab];
    [self.view addSubview:self.tipsImg];
    [self.view addSubview:self.titleLab1];
    [self.view addSubview:self.titleLab2];

    [self.view addSubview:self.inputBg];
    [self.inputBg addSubview:self.nameField];
    [self.inputBg addSubview:self.cardField];
    
    [self.view addSubview:self.footerView];
    
    
    self.frontLab1.text = @"点击拍摄人像面";
    self.backLab1.text = @"点击拍摄国徽面";
    self.tipsLab.text = @"身份证照片拍摄说明";
    self.titleLab1.text = @"身份证信息";
    self.titleLab2.text = @"如识别有误，请重新拍摄身份证或直接修改";
    
    self.frontLab2.numberOfLines= 0;
    self.backLab2.numberOfLines= 0;

    self.frontImg.contentMode = UIViewContentModeScaleAspectFill;
    self.backImg.contentMode = UIViewContentModeScaleAspectFill;

    [self.frontImg sd_setImageWithURL:[NSURL URLWithString:self.frontUrl] placeholderImage:[UIImage imageNamed:@"idcard_front"]];
    [self.backImg sd_setImageWithURL:[NSURL URLWithString:self.backUrl] placeholderImage:[UIImage imageNamed:@"idcard_back"]];

    UITapGestureRecognizer *tapFront = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(selectFront)];
    self.frontBg.userInteractionEnabled = YES;
    [self.frontBg addGestureRecognizer:tapFront];
    
    UITapGestureRecognizer *tapBack = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(selectBack)];
    self.backBg.userInteractionEnabled = YES;
    [self.backBg addGestureRecognizer:tapBack];
}

- (void)addViewsConstrations
{
    CGFloat imgH = 160;
    [self.frontBg mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.equalTo(self.view).offset(12);
        make.right.equalTo(self.view.mas_centerX).offset(-6);
        make.top.equalTo(self.topTipsV.mas_bottom).offset(10);
        make.height.mas_equalTo(imgH);
    }];
    self.frontBg.layer.masksToBounds = YES;
    self.frontBg.layer.cornerRadius = 10;
    
    [self.frontImg mas_makeConstraints:^(SAConstraintMaker *make) {
        make.centerX.equalTo(self.frontBg);
        make.centerY.equalTo(self.frontBg).offset(-15);
        make.height.mas_equalTo(imgH - 60);
        make.width.mas_equalTo(UIScreenWidth*0.35);
    }];
    
    [self.frontLab1 mas_makeConstraints:^(SAConstraintMaker *make) {
        make.centerX.equalTo(self.frontImg);
        make.top.equalTo(self.frontImg.mas_bottom).offset(10);
    }];
    
    
    [self.backBg mas_makeConstraints:^(SAConstraintMaker *make) {
        make.top.height.equalTo(self.frontBg);
        make.right.equalTo(self.view).offset(-12);
        make.left.equalTo(self.view.mas_centerX).offset(6);
    }];
    self.backBg.layer.masksToBounds = YES;
    self.backBg.layer.cornerRadius = 10;

    [self.backImg mas_makeConstraints:^(SAConstraintMaker *make) {
        make.centerX.equalTo(self.backBg);
        make.centerY.equalTo(self.backBg).offset(-15);
        make.width.height.equalTo(self.frontImg);
    }];

    [self.backLab1 mas_makeConstraints:^(SAConstraintMaker *make) {
        make.centerX.equalTo(self.backImg);
        make.top.equalTo(self.backImg.mas_bottom).offset(10);
    }];
    
    [self.tipsLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.equalTo(self.frontBg);
        make.top.equalTo(self.frontBg.mas_bottom).offset(20);
    }];
    [self.tipsImg mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.equalTo(self.tipsLab);
        make.right.equalTo(self.backBg);
        make.top.equalTo(self.tipsLab.mas_bottom).offset(1);
        make.height.mas_equalTo(90);
    }];
    self.tipsImg.contentMode = UIViewContentModeScaleAspectFit;

    
    [self.titleLab1 mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.equalTo(self.frontBg);
        make.top.equalTo(self.tipsImg.mas_bottom).offset(10);
    }];
    [self.titleLab2 mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.equalTo(self.frontBg);
        make.top.equalTo(self.titleLab1.mas_bottom).offset(5);
    }];
    
    [self.inputBg mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.equalTo(self.frontBg);
        make.right.equalTo(self.backBg);
        make.top.equalTo(self.titleLab2.mas_bottom).offset(20);
        make.height.mas_equalTo(100);
    }];
}

#pragma mark - UIGestureRecognizerDelegate

- (BOOL)gestureRecognizerShouldBegin:(UIGestureRecognizer *)gestureRecognizer
{
    if(gestureRecognizer == self.navigationController.interactivePopGestureRecognizer){
        
        [SAFaceBackAlert showFaceAlertSureAction:^{
            [self popSelf];
        }];
        return NO;
    }
    return YES;
}

#pragma mark - TextView

-(BOOL)textView:(UITextView *)textView shouldInteractWithURL:(nonnull NSURL *)URL inRange:(NSRange)characterRange interaction:(UITextItemInteraction)interaction{

    if ([URL.scheme isEqualToString:@"name"])
    {
        NSLog(@"点击了实名认证");
        self.protocolKey = PRIVACY_POLICY_SMRZSQTYS;
        [self showActivityHUD:nil];
        [self.protocolManager loadData];

    }
    else if ([URL.scheme isEqualToString:@"face"])
    {
        NSLog(@"点击了人脸验证");
        self.protocolKey = PRIVACY_POLICY_RLYZGRXXSQ;
        [self showActivityHUD:nil];
        [self.protocolManager loadData];
    }

    return YES;
}

- (void)changeAgree
{
    self.isAgree = !self.isAgree;
    if(!self.isAgree){
        self.agreeImg.image = [UIImage imageNamed:@"verify_check_nor"];
    } else {
        self.agreeImg.image = [UIImage customImageNamed:@"verify_check_sel"];
    }
}

#pragma mark - 点击左上角返回

- (void)backAction:(id)sender
{
    if(self.finished){
        [self popSelf];
        return;
    }
    
    [SAFaceBackAlert showFaceAlertSureAction:^{
        [self popSelf];
    }];
}

#pragma mark - 选择照片

- (void)selectFront
{
    self.cardType = NPCardType_Front;
    [self choosePhoto];
}

- (void)selectBack
{
    self.cardType = NPCardType_Back;
    [self choosePhoto];
}

- (void)choosePhoto
{
    [self.nameField resignFirstResponder];
    [self.cardField resignFirstResponder];
    
    [self startOCRidentify];
}

- (void)startOCRidentify {
    @weakify(self)
    [self.ocrMgr startOCRWithType:self.cardType complete:^(NSString *ocrUrl, NSDictionary *cardInfo, NSData *ocrData, NSString *detailId) {
        @strongify(self)
        if(self.cardType == NPCardType_Front){
            self.frontUrl = ocrUrl;
            self.frontImg.image = [UIImage imageWithData:ocrData];
            
            self.inputBg.hidden = NO;
            if([NSString judgeStringExist:cardInfo[@"fullName"]]){
                self.nameField.text = cardInfo[@"fullName"];
            }
            if([NSString judgeStringExist:cardInfo[@"accountNumber"]]){
                self.cardField.text = cardInfo[@"accountNumber"];
            }
            
        }else{
            self.backUrl = ocrUrl;
            self.backImg.image = [UIImage imageWithData:ocrData];
        }
    }];
}

#pragma mark -  提交


- (void)submitAction
{
    if(!self.isAgree){
        [self textStateHUD:@"阅读并同意协议"];
        return;
    }
    
    if (![NSString judgeStringExist:self.frontUrl]) {
        [self selectFront];
        return;
    }
    if (![NSString judgeStringExist:self.backUrl]) {
        [self selectBack];
        return;
    }
    if (![NSString judgeStringExist:self.nameField.text]) {
        [self textStateHUD:NSLocalizedString(@"size_78Contact", nil)];
        return;
    }
    if (![NSString judgeStringExist:self.cardField.text]) {
        [self textStateHUD:NSLocalizedString(@"eviceMetamacros", nil)];
        return;
    }

    [self showActivityHUD:nil];
    [self.submitOCRManager loadData];
}

- (void)startLiveFace
{
    if([SAPhotoManager CameraAuthorization]){
        [self showActivityHUD:nil];
        [[EsignFaceManager shareEsignFaceManager] startFaceVerify:self.faceCode success:^(EsignFaceVerifyResult *result) {
            
            dispatch_async(dispatch_get_main_queue(), ^{
                [self hideHUDView];
                NSDictionary *resultDict = @{@"faceAuthCode": result.faceAuthCode, @"faceCode": result.faceCode, @"faceMsg": result.faceMsg, @"isSuccess": @(result.isSuccess) };
                self.faceResult = [resultDict QL_jsonString];
                
                [self showActivityHUD:nil];
                [self.submitFaceManager loadData];
            });
            
        } failure:^(EsignSDKError * _Nonnull error) {
            [self hideHUDView];
            [self textStateHUD:[NSString stringWithFormat:@"刷脸失败，失败原因: %@",error.desc]];
        }];
    }
}

- (void)initializationEsign
{
    EsignSDKConfig *config = [EsignSDKConfig new];
    config.key = [SAAdvanceManager sharedInstance].appId;
    config.license = [SAAdvanceManager sharedInstance].appKey;
    
    [self showActivityHUD:@"初始化中..."];
    [[EsignFaceManager shareEsignFaceManager] initConfig:config success:^{
        [self hideHUDView];
    } failure:^(EsignSDKError * _Nonnull error) {
        [self hideHUDView];
        [self textStateHUD:[NSString stringWithFormat:@"初始化失败，失败原因: %@",error.desc]];
    }];
}

#pragma mark - QLAPIManagerCallBackDelegate, QLAPIManagerParamSource

- (void)managerCallAPIDidSuccess:(SABaseManager *)manager{
    [self hideHUDView];
    [SATool hideWindowHUD];
    if(manager == self.configManager)
    {
        NSDictionary *dataDict = [manager fetchDataWithReformer:self.configManager];
        NSLog(@"sss---%@", dataDict);
        self.finished = [dataDict[@"finished"] boolValue];

        self.frontUrl = [NSString judgeStringExist:dataDict[@"faceUrl"]] ? dataDict[@"faceUrl"] : @"";
        self.backUrl = [NSString judgeStringExist:dataDict[@"frontUrl"]] ? dataDict[@"frontUrl"] : @"";
        [self.frontImg sd_setImageWithURL:[NSURL URLWithString:self.frontUrl] placeholderImage:[UIImage imageNamed:@"idcard_front"]];
        [self.backImg sd_setImageWithURL:[NSURL URLWithString:self.backUrl] placeholderImage:[UIImage imageNamed:@"idcard_back"]];

        NSString *name = dataDict[@"realName"];
        NSString *cardNo = dataDict[@"idCard"];
        if([NSString judgeStringExist:name] && [NSString judgeStringExist:cardNo]){
            self.inputBg.hidden = NO;
            self.nameField.text = name;
            self.cardField.text = cardNo;
        }
        
        NSDictionary *faceInfo = dataDict[@"accountInfo"];
        if([faceInfo isKindOfClass:[NSDictionary class]]){
            [SAAdvanceManager sharedInstance].appId = faceInfo[@"appId"];
            [SAAdvanceManager sharedInstance].appKey = faceInfo[@"appKey"];
            
            [self initializationEsign];
        }
        
    }
    
    if (manager == self.submitOCRManager) {
        
        NSDictionary *dataDict = [manager fetchDataWithReformer:self.submitOCRManager];
        
        self.faceCode = dataDict[@"faceToken"];
        self.flowId = dataDict[@"flowId"];
        
        if([NSString judgeStringExist:self.faceCode]){
            [self startLiveFace];

        }else{
            SACommonWebViewController *webVC = [[SACommonWebViewController alloc] init];
            webVC.url = dataDict[@"authUrl"];
            webVC.navTitle = @" 身份认证";
            webVC.hidesBottomBarWhenPushed = YES;
            [[SACommonTool currentViewController].navigationController pushViewController:webVC animated:NO];
        }
    }
    
    if(manager == self.submitFaceManager){
        
        [SATool textStateHUD:@"身份认证成功!"];
        [[SAVerifyListManager sharedInstance] handleVerifyJump];
    }
    
    if (manager == self.uploadManager){
        
        self.popToVcClassName = @"SAHomePageViewController";
        [self popSelf];
    }
    
    if(manager == self.protocolManager)
    {
        NSDictionary *dataDict = [manager fetchDataWithReformer:self.protocolManager];
        SAProtocolViewController *vc = [SAProtocolViewController new];
        vc.htmlStr = dataDict[@"text"];
        vc.navTitle = dataDict[@"title"];
        vc.companyName = dataDict[@"companyName"];
        vc.appName = dataDict[@"appName"];
        [self presentViewController:[[UINavigationController alloc] initWithRootViewController:vc] animated:YES completion:nil];
    }
}

- (void)managerCallAPIDidFailed:(SABaseManager *)manager
{
    [self hideHUDView];
    [SATool hideWindowHUD];
    
    if(manager == self.submitFaceManager){
        NSDictionary *err = [manager fetchDataWithReformer:nil] ;
        NSInteger code = [err[@"code"] integerValue];
        if (code == 9011) {
            [[SATabBarViewController sharedInstance] restTabBar];
        }else{
            NSString *failMsg  = [SAResponseHandle netWorkErrorMsg:[manager fetchDataWithReformer:nil] andErrorType:manager.errorType];
            [SATool textStateWindowHUD:failMsg finishBlock:^{

            }];
        }
    }else{
        NSString *failMsg  = [SAResponseHandle netWorkErrorMsg:[manager fetchDataWithReformer:nil] andErrorType:manager.errorType];
        [SATool textStateWindowHUD:failMsg];
    }
}

- (NSDictionary *)paramsForApi:(SABaseManager *)manager {
    if (manager == self.submitOCRManager){
        return @{
            @"frontUrl": self.backUrl,
            @"faceUrl": self.frontUrl,
            @"idCard": self.cardField.text,
            @"realName": self.nameField.text
        };
    }else if (manager == self.uploadManager){
        return @{
            @"type": @5,
            @"result": [self.compareDict QL_jsonString]
        };
    }else if (manager == self.submitFaceManager){
        return @{
            @"type": @2,
            @"flowId": self.flowId,
            @"result": self.faceResult
        };
    }
    return [NSDictionary dictionary];
}

- (NSString *)methodForApi:(SABaseManager *)manager
{
    if (manager == self.protocolManager) {
        return [NSString stringWithFormat:@"1a99fb9ad8534512af0ede2979dfd68f/%@", self.protocolKey];
    }
    return @"";
}

#pragma mark - UITextFieldDelegate


#pragma mark - getter

- (UIView *)inputBg{
    if (_inputBg == nil) {
        _inputBg = [UIView new];
        _inputBg.userInteractionEnabled = YES;
        
        UILabel *nameLab = [UILabel new];
        nameLab.font = Font_XX6(14);
        nameLab.text = NSLocalizedString(@"asicPath", nil);
        [_inputBg addSubview:nameLab];
        
        UITextField  *nameFie = [UITextField new];
        nameFie.textColor = [UIColor blackColor];
        nameFie.font = BoldFont_XX6(14);
        nameFie.placeholder = @"请填写您的姓名";
        nameFie.enabled = true;
        nameFie.textAlignment = NSTextAlignmentRight;
        _nameField = nameFie;
        [_inputBg addSubview:nameFie];
        
        UIView *line1 = [UIView new];
        line1.backgroundColor = [UIColor colorWithHex:0xdddddd];
        [_inputBg addSubview:line1];
        
        UILabel *cardLab = [UILabel new];
        cardLab.font = Font_XX6(14);
        cardLab.text = NSLocalizedString(@"textPath", nil);
        [_inputBg addSubview:cardLab];
        
        UITextField  *cardFie = [UITextField new];
        cardFie.textColor = [UIColor blackColor];
        cardFie.font = BoldFont_XX6(14);
        cardFie.placeholder = @"请填写您的身份证号";
        cardFie.enabled = true;
        cardFie.textAlignment = NSTextAlignmentRight;
        _cardField = cardFie;
        [_inputBg addSubview:cardFie];
        
        UIView *line2 = [UIView new];
        line2.backgroundColor = [UIColor colorWithHex:0xdddddd];
        [_inputBg addSubview:line2];

        [nameLab mas_makeConstraints:^(SAConstraintMaker *make) {
            make.left.equalTo(_inputBg);
            make.height.mas_equalTo(36);
            make.width.mas_equalTo(100);
        }];
        [nameFie mas_makeConstraints:^(SAConstraintMaker *make) {
            make.right.equalTo(_inputBg);
            make.centerY.equalTo(nameLab);
            make.height.equalTo(nameLab);
            make.left.equalTo(nameLab.mas_right);
        }];
        [line1 mas_makeConstraints:^(SAConstraintMaker *make) {
            make.height.mas_equalTo(1);
            make.left.equalTo(nameLab);
            make.right.equalTo(nameFie);
            make.top.equalTo(nameLab.mas_bottom).offset(6);
        }];
        
        [cardLab mas_makeConstraints:^(SAConstraintMaker *make) {
            make.left.height.width.equalTo(nameLab);
            make.top.equalTo(line1.mas_bottom).offset(12);
        }];
        [cardFie mas_makeConstraints:^(SAConstraintMaker *make) {
            make.right.height.equalTo(nameFie);
            make.centerY.equalTo(cardLab);
            make.left.equalTo(cardLab.mas_right);
        }];
        [line2 mas_makeConstraints:^(SAConstraintMaker *make) {
            make.left.right.height.equalTo(line1);
            make.top.equalTo(cardLab.mas_bottom).offset(6);
        }];
    }
    return _inputBg;
}

- (UIView *)footerView {
    if (_footerView == nil) {
        _footerView = [[UIView alloc] init];
        _footerView.frame = CGRectMake(0, UIScreenHeight-XX_6(90)-kTopHeight, UIScreenWidth, XX_6(90));
        SAGradientButton *nextBtn = [SAGradientButton buttonWithType:UIButtonTypeCustom];
        [nextBtn gradientBackgroundColors:@[kButtonStartColor, kButtonEndColor] locations:nil startPoint:CGPointMake(0, 0.5) endPoint:CGPointMake(1, 0.5)];
        [nextBtn setTitle:@"已完成，下一步" forState:UIControlStateNormal];
        [nextBtn setTitleColor:kHomeTextColor forState:UIControlStateNormal];
        nextBtn.layer.cornerRadius  = 7;
        nextBtn.layer.masksToBounds = YES;
        [nextBtn addTarget:self action:@selector(submitAction) forControlEvents:UIControlEventTouchUpInside];
        [_footerView addSubview:nextBtn];
        [nextBtn mas_makeConstraints:^(SAConstraintMaker *make) {
            make.top.centerX.equalTo(_footerView);
            make.left.equalTo(_footerView).offset(25);
            make.height.mas_equalTo(44);
        }];
        
    }
    return _footerView;
}

- (SAManager *)ocrMgr {
    if (_ocrMgr == nil) {
        _ocrMgr = [[SAManager alloc] init];
    }
    return _ocrMgr;
}

- (SASubmitManager *)submitOCRManager{
    if (_submitOCRManager == nil) {
        _submitOCRManager = [[SASubmitManager alloc] init];
        _submitOCRManager.delegate = self;
        _submitOCRManager.paramSource = self;
    }
    return _submitOCRManager;
}

- (SACompareUploadManager *)uploadManager{
    if (_uploadManager == nil) {
        _uploadManager = [[SACompareUploadManager alloc] init];
        _uploadManager.delegate = self;
        _uploadManager.paramSource = self;
    }
    return _uploadManager;
}

- (SAGetRegManager *)regManager{
    if (_regManager == nil) {
        _regManager = [[SAGetRegManager alloc] init];
        _regManager.delegate = self;
        _regManager.paramSource = self;
    }
    return _regManager;
}

- (UIView *)frontBg{
    FF_Fetch_UIView(_frontBg, [UIColor whiteColor])
}

- (UILabel *)frontLab1{
    FF_Fetch_UILable(_frontLab1, [UIColor colorWithHex:0x333333], Font_XX6(13))
}

- (UILabel *)frontLab2{
    FF_Fetch_UILable(_frontLab2, [UIColor colorWithHex:0x777777], Font_XX6(10))
}

- (UIImageView *)frontImg{
    if(_frontImg == nil){
        _frontImg = [UIImageView new];
    }
    return _frontImg;
}

- (UIView *)backBg{
    FF_Fetch_UIView(_backBg, [UIColor whiteColor])
}

- (UILabel *)backLab1{
    FF_Fetch_UILable(_backLab1, [UIColor colorWithHex:0x333333],  Font_XX6(13))
}

- (UILabel *)backLab2{
    FF_Fetch_UILable(_backLab2, [UIColor colorWithHex:0x777777], Font_XX6(10))
}

- (UIImageView *)backImg{
    if(_backImg == nil){
        _backImg = [UIImageView new];
    }
    return _backImg;
}

- (SAGetOcrConfigApiManager *)configManager{
    if(_configManager == nil){
        _configManager = [SAGetOcrConfigApiManager new];
        _configManager.delegate = self;
        _configManager.paramSource = self;
    }
    return _configManager;
}

- (SAFaceSubmitApiManager *)submitFaceManager
{
    if(_submitFaceManager == nil){
        _submitFaceManager = [SAFaceSubmitApiManager new];
        _submitFaceManager.delegate = self;
        _submitFaceManager.paramSource = self;
    }
    return _submitFaceManager;
}

- (SAGetProtocolApiManager *)protocolManager {
    if (_protocolManager == nil) {
        _protocolManager = [[SAGetProtocolApiManager alloc] init];
        _protocolManager.delegate = self;
        _protocolManager.paramSource = self;
        _protocolManager.methodSource = self;
    }
    return _protocolManager;
}

- (UILabel *)titleLab1{
    FF_Fetch_UILable(_titleLab1, [UIColor blackColor], BoldFont_XX6(20))
}

- (UILabel *)titleLab2{
    FF_Fetch_UILable(_titleLab2, [UIColor redColor], Font_XX6(14))
}

- (UILabel *)tipsLab{
    FF_Fetch_UILable(_tipsLab, [UIColor colorWithHex:0x333333], Font_XX6(14))
}

- (UIImageView *)tipsImg{
    FF_Fetch_UIImageViewWithImage(_tipsImg, [UIImage imageNamed:@"i_center_tip"])
}

- (UIView *)topTipsV{
    if (_topTipsV == nil) {
        _topTipsV = [UIView new];
        _topTipsV.backgroundColor = [UIColor colorWithHex:0xFFF6F1];
        
        UIImageView *icon = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"warning"]];
        [_topTipsV addSubview:icon];
        
        UILabel *lab = [UILabel new];
        lab.textColor = [UIColor colorWithHex:0xEA9947];
        lab.text = @"仅用于身份验证、额度评估、信贷相关业务审核使用，您的信息将严格保密";
        lab.font = Font_XX6(11);
        lab.numberOfLines = 0;
        [_topTipsV addSubview:lab];
        
        [icon mas_makeConstraints:^(SAConstraintMaker *make) {
            make.top.left.equalTo(_topTipsV).offset(12);
            make.width.height.mas_equalTo(16);
        }];
        [lab mas_makeConstraints:^(SAConstraintMaker *make) {
            make.left.equalTo(icon.mas_right).offset(4);
            make.top.equalTo(_topTipsV).offset(12);
            make.right.equalTo(_topTipsV).offset(-12);
        }];
    }
    return _topTipsV;
}

@end
