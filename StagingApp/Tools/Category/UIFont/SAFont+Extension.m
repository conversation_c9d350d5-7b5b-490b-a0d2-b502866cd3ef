
#import "SAFont+Extension.h"

@implementation UIFont (Extension)

+ (UIFont *)fontSizeOfXX_6:(CGFloat)font {
    return [self systemFontOfSize:ceil(XX_6(font))];
}

+ (UIFont *)fontRegularSizeOfXX_6:(CGFloat)font {
    return [UIFont fontWithName:@"PingFangSC-Regular" size:ceil(XX_6(font))];
}

+ (UIFont *)fontSemiboldSizeOfXX_6:(CGFloat)font {
    UIFont *ft = [UIFont fontWithName:@"PingFangSC-Semibold" size:ceil(XX_6(font))];
    if (ft == nil) {
        ft = [UIFont fontBlodSizeOfXX_6:font];
    }
    return ft;
}

+ (UIFont *)fontBlodSizeOfXX_6:(CGFloat)font {
    return [self boldSystemFontOfSize:ceil(XX_6(font))];
    
}


@end
