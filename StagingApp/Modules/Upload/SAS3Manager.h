
#import <Foundation/Foundation.h>
#import "SAUploadModel.h"

@class SANLUploadManager;

@class SANLUploadManager;



NS_ASSUME_NONNULL_BEGIN

@interface SAS3Manager : NSObject

+ (instancetype)sharedInstance;

- (void)setup;

- (void)uploadObjectAsync:(NSData *)fileData
                  success:(NPUploadSuccess)successBlock
                   failed:(NPUploadFailed)failBlock;


@end

NS_ASSUME_NONNULL_END
