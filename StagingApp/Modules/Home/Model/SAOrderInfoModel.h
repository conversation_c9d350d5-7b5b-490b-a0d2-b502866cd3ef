
#import <Foundation/Foundation.h>
#import "SAAuditProgressItemModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface SAOrderInfoModel : NSObject

@property (nonatomic, copy)NSString *bankName;
@property (nonatomic, copy)NSString *bankNo;
@property (nonatomic, copy)NSString *borrowDate;
@property (nonatomic, copy)NSString *borrowProtocolUrl;
@property (nonatomic, copy)NSString *borrowerName;
@property (nonatomic, copy)NSString *currentPeriod;
@property (nonatomic, copy)NSString *ktpNo;
@property (nonatomic, copy)NSString *orderAmount;
@property (nonatomic, assign)BOOL overdue;
@property (nonatomic, assign)NSInteger overdueDays;
@property (nonatomic, copy)NSString *overdueFee;
@property (nonatomic, copy)NSString *overdueFeeRate;
@property (nonatomic, assign)NSInteger periodLength;
@property (nonatomic, copy)NSString *productCode;
@property (nonatomic, copy)NSString *receivedCapital;
@property (nonatomic, copy)NSString *remainpayAmount;
@property (nonatomic, copy)NSString *repaySchedule;
@property (nonatomic, copy)NSString *repaymentAmount;
@property (nonatomic, copy)NSString *repaymentDate;
@property (nonatomic, assign)NSInteger repaymentDays;
@property (nonatomic, copy)NSString *toApplyDate;
@property (nonatomic, assign)NSInteger toApplyDays;
@property (nonatomic, assign)NSInteger totalPeriod;
@property (nonatomic, copy)NSString *tradeNo;
@property (nonatomic, copy)NSString *unit;

@property (nonatomic, strong)NSArray <NSString *>*pendingBillNos;

@property (nonatomic, strong)NSArray <NSDictionary *>*borrowDetailData;

@property (nonatomic, strong)NSNumber *payType;     

+ (instancetype)objectWithDict:(NSDictionary *)dict;


 
@property (nonatomic, strong)NSArray <SAAuditProgressItemModel *>*auditProgressItems;

 
@property (nonatomic, assign)CGFloat contentHeigt;


@property (nonatomic, assign)CGFloat repaymentHeadCellHeight;        



+ (instancetype)objectWithDict:(NSDictionary *)dict;

@end

NS_ASSUME_NONNULL_END
