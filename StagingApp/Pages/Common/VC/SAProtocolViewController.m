

#import "SAProtocolViewController.h"
#import <WebKit/WebKit.h>
#import "SAScope.h"

@interface SAProtocolViewController ()<WKNavigationDelegate, WKUIDelegate>

@property (nonatomic, strong) WKWebView *webVBox;

@end

@implementation SAProtocolViewController

- (void)viewWillAppear:(BOOL)animated
{
    [super viewWillAppear:animated];
    
    [UIApplication sharedApplication].statusBarStyle = UIStatusBarStyleDefault;
    [self.navigationController setNavigationBarHidden:NO animated:YES];
}

- (void)viewDidLoad
{
    [super viewDidLoad];
    
    self.navigationItem.title = self.navTitle;
    [self.view addSubview:self.webVBox];
    
    [self addBackButton];
        
    self.htmlStr = [self.htmlStr stringByReplacingOccurrencesOfString:@"{{ appName }}" withString:self.appName];
    self.htmlStr = [self.htmlStr stringByReplacingOccurrencesOfString:@"{{ companyName }}" withString:self.companyName];

    [self.webVBox loadHTMLString:self.htmlStr baseURL:nil];
}

- (void)addBackButton
{
    UIButton *backButton = [[UIButton alloc] initWithFrame:CGRectMake(0, 0, 20, 20)];
    [backButton setBackgroundColor:[UIColor clearColor]];
    [backButton setImage:[[UIImage imageNamed:@"close"] imageWithRenderingMode:UIImageRenderingModeAutomatic] forState:UIControlStateNormal];
    [backButton setImage:[[UIImage imageNamed:@"close"] imageWithRenderingMode:UIImageRenderingModeAutomatic] forState:UIControlStateHighlighted];
    [backButton addTarget:self action:@selector(goBack) forControlEvents:UIControlEventTouchUpInside];
    backButton.contentHorizontalAlignment = UIControlContentHorizontalAlignmentLeft;
    self.navigationItem.leftBarButtonItem = [[UIBarButtonItem alloc] initWithCustomView:backButton];
    self.navigationItem.leftBarButtonItem.style = UIBarButtonItemStylePlain;
}

- (void)viewWillLayoutSubviews
{
    [super viewWillLayoutSubviews];
    self.webVBox.frame = self.view.bounds;
}

- (void)goBack
{
    
    [self.navigationController dismissViewControllerAnimated:YES completion:nil];
}

#pragma mark - WKNavigationDelegate
- (void)webView:(WKWebView *)webView didStartProvisionalNavigation:(WKNavigation *)navigation
{
    [SATool showActivityHUD:nil];
}
- (void)webView:(WKWebView *)webView didCommitNavigation:(WKNavigation *)navigation
{
}

- (void)webView:(WKWebView *)webView didFinishNavigation:(WKNavigation *)navigation
{
    [ webView evaluateJavaScript:@"document.getElementsByTagName('body')[0].style.webkitTextSizeAdjust= '200%'" completionHandler:nil];
    [ webView evaluateJavaScript:@"document.getElementsByTagName('body')[0].style.padding= '0 10px'" completionHandler:nil];
}

- (void)webView:(WKWebView *)webView didFailNavigation:(WKNavigation *)navigation withError:(NSError *)error
{
    [SATool hideHUDView];
}
- (void)webView:(WKWebView *)webView didReceiveServerRedirectForProvisionalNavigation:(WKNavigation *)navigation
{
    
}
- (void)webView:(WKWebView *)webView decidePolicyForNavigationResponse:(WKNavigationResponse *)navigationResponse decisionHandler:(void (^)(WKNavigationResponsePolicy))decisionHandler
{
    
    decisionHandler(WKNavigationResponsePolicyAllow);
}

- (void)webView:(WKWebView *)webView decidePolicyForNavigationAction:(WKNavigationAction *)navigationAction decisionHandler:(void (^)(WKNavigationActionPolicy))decisionHandler
{
    decisionHandler(WKNavigationActionPolicyAllow);
}

#pragma mark - getters
- (WKWebView *)webVBox
{
    if (_webVBox == nil) {
        _webVBox = [[WKWebView alloc] init];
        _webVBox.navigationDelegate = self;
        _webVBox.UIDelegate = self;
        _webVBox.opaque = NO;

    }
    return _webVBox;
}

@end

