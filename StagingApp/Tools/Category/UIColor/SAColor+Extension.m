
#import "SAColor+Extension.h"

@implementation UIColor (Extension)

+ (instancetype)colorWithHex:(uint32_t)hex {
    uint8_t r = (hex & 0xff0000) >> 16;
    uint8_t g = (hex & 0x00ff00) >> 8;
    uint8_t b = hex & 0x0000ff;
    
    return [self colorWithRed:r green:g blue:b];
}

+ (instancetype)colorWithHex:(uint32_t)hex alpha:(CGFloat)alpha{
    return [[self colorWithHex:hex] colorWithAlphaComponent:alpha];
}

+ (instancetype)colorWithRed:(uint8_t)red green:(uint8_t)green blue:(uint8_t)blue {
    return [UIColor colorWithRed:red / 255.0 green:green / 255.0 blue:blue / 255.0 alpha:1.0];
}

+ (UIColor*)gradientFromColor:(UIColor*)c1 toColor:(UIColor*)c2 withHeight:(int)height {
    
    CGSize size = CGSizeMake(1, height);
    UIGraphicsBeginImageContextWithOptions(size, NO, 0);
    CGContextRef context = UIGraphicsGetCurrentContext();
    CGColorSpaceRef colorspace = CGColorSpaceCreateDeviceRGB();
    
    NSArray* colors = [NSArray arrayWithObjects:(id)c1.CGColor, (id)c2.CGColor, nil];
    CGGradientRef gradient = CGGradientCreateWithColors(colorspace, (__bridge CFArrayRef)colors, NULL);
    CGContextDrawLinearGradient(context, gradient, CGPointMake(0, 0), CGPointMake(0, size.height), 0);
    
    UIImage *image = UIGraphicsGetImageFromCurrentImageContext();
    
    CGGradientRelease(gradient);
    CGColorSpaceRelease(colorspace);
    UIGraphicsEndImageContext();
    
    return [UIColor colorWithPatternImage:image];
    
}





#pragma mark - 配置通用颜色

 
+ (instancetype)shareMainColor {
    return [self colorWithHex:0x183C78];
}


 
+ (instancetype)shareOverdueBgColor {
    return [self colorWithHex:0xFF8180];
}


 
+ (instancetype)shareLoginBeginColor {
    return [self colorWithHex:0x44D3AC];
}
+ (instancetype)shareLoginEndColor {
    return [self colorWithHex:0x00B487];
}

 
+ (instancetype)shareRepaymentBeginColor {
    return [self colorWithHex:0x44D3AC];
}
+ (instancetype)shareRepaymentEndColor {
    return [self colorWithHex:0x00B487];
}

 
+ (instancetype)shareRenewalBeginColor {
    return [self colorWithHex:0xFFDC3F];
}
+ (instancetype)shareRenewalEndColor {
    return [self colorWithHex:0xFFB970];
}


 
+ (instancetype)shareNormalVaCodeBeginColor {
    return [self colorWithHex:0x44D3AC];
}
+ (instancetype)shareNormalVaCodeEndColor {
    return [self colorWithHex:0x00B487];
}

 
+ (instancetype)shareNormalVoucherBeginColor {
    return [self colorWithHex:0xFFDC3F];
}
+ (instancetype)shareNormalVoucherEndColor {
    return [self colorWithHex:0xFFB970];
}


 
+ (instancetype)shareOverdueVaCodeBeginColor {
    return [self colorWithHex:0xFF9F9C];
}
+ (instancetype)shareOverdueVaCodeEndColor {
    return [self colorWithHex:0xFF7A79];
}

 
+ (instancetype)shareOverdueVoucherBeginColor {
    return [self colorWithHex:0xFFDC3F];
}
+ (instancetype)shareOverdueVoucherEndColor {
    return [self colorWithHex:0xFFB970];
}


 
+ (instancetype)shareSubmitBeginColor {
    return [self colorWithHex:0x44D3AC];
}
+ (instancetype)shareSubmitEndColor {
    return [self colorWithHex:0x00B487];
}

@end
