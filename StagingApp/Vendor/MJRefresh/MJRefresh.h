
#import <Foundation/Foundation.h>

#if __has_include(<MJRefresh/MJRefresh.h>)
FOUNDATION_EXPORT double MJRefreshVersionNumber;
FOUNDATION_EXPORT const unsigned char MJRefreshVersionString[];

#import <MJRefresh/SAScrollView+MJRefresh.h>
#import <MJRefresh/SAScrollView+MJExtension.h>
#import <MJRefresh/SAView+MJExtension.h>

#import <MJRefresh/SARefreshNormalHeader.h>
#import <MJRefresh/SARefreshGifHeader.h>

#import <MJRefresh/SARefreshBackNormalFooter.h>
#import <MJRefresh/SARefreshBackGifFooter.h>
#import <MJRefresh/SARefreshAutoNormalFooter.h>
#import <MJRefresh/SARefreshAutoGifFooter.h>

#import <MJRefresh/SARefreshNormalTrailer.h>
#import <MJRefresh/SARefreshConfig.h>
#import <MJRefresh/SABundle+MJRefresh.h>
#import <MJRefresh/SARefreshConst.h>
#else
#import "SAScrollView+MJRefresh.h"
#import "SAScrollView+MJExtension.h"
#import "SAView+MJExtension.h"

#import "SARefreshNormalHeader.h"
#import "SARefreshGifHeader.h"

#import "SARefreshBackNormalFooter.h"
#import "SARefreshBackGifFooter.h"
#import "SARefreshAutoNormalFooter.h"
#import "SARefreshAutoGifFooter.h"

#import "SARefreshNormalTrailer.h"
#import "SARefreshConfig.h"
#import "SABundle+MJRefresh.h"
#import "SARefreshConst.h"
#endif
