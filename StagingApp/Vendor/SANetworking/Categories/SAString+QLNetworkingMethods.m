
#import "SAString+QLNetworkingMethods.h"
#include <CommonCrypto/CommonDigest.h>
#import "SAObject+QLNetworkingMethods.h"

@implementation NSString (SANetworkingMethods)

- (NSString *)QL_md5
{
    NSData* inputData = [self dataUsingEncoding:NSUTF8StringEncoding];
    unsigned char outputData[CC_MD5_DIGEST_LENGTH];
    CC_MD5([inputData bytes], (unsigned int)[inputData length], outputData);
    
    NSMutableString* hashStr = [NSMutableString string];
    int i = 0;
    for (i = 0; i < CC_MD5_DIGEST_LENGTH; ++i)
        [hashStr appendFormat:@"%02x", outputData[i]];
    
    return hashStr;
}

+ (NSString *)QL_dictToString:(NSDictionary *)parameters deleteSpace:(BOOL)deleteSpace
{
    NSString *parametersString = nil;
    if ([parameters count])
    {
        NSData *jsonData = [NSString QL_toJSONData:parameters];
        if (jsonData)
        {
            parametersString = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
        }
        else
        {
            parametersString = @"";
        }
    }
    else
    {
        parametersString = @"";
    }
	if (deleteSpace)
	{
		parametersString = [parametersString stringByReplacingOccurrencesOfString:@" " withString:@""];
		parametersString = [parametersString stringByReplacingOccurrencesOfString:@"\n" withString:@""];
	}
	
	return parametersString;
}

+ (NSData *)QL_toJSONData:(id)theData
{
    NSError *error = nil;
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:theData options:NSJSONWritingPrettyPrinted error:&error];
    
    if ([jsonData length] > 0 && error == nil)
    {
        return jsonData;
    }
    return nil;
}

+ (BOOL)judgeStringValid:(NSString *)string
{
    if (string && string != nil)
    {
        if ([string isKindOfClass:[NSNull class]])
        {
            return NO;
        }
        if ([string isKindOfClass:[NSString class]])
        {
            if ([string isEqualToString:@"<null>"] ||
                [string isEqualToString:@"(null)"])
            {
                return NO;
            }
        }
        
        NSString *tempStr = [NSString stringWithFormat:@"%@", string];
        if ([tempStr length] > 0)
        {
            return YES;
        }
    }
    
    return NO;
}

@end
