
#import "SAShapeLayer+Extension.h"

@implementation CAShapeLayer (Extension)



+ (CAShapeLayer *)drawLineFromPoint:(CGPoint)fromPoint toPoint:(CGPoint)toPoint lineLength:(int)lineLength lineSpacing:(int)lineSpacing lineColor:(UIColor *)lineColor
{
    CAShapeLayer *shapeLayer = [CAShapeLayer layer];
    
    shapeLayer.fillColor = [UIColor clearColor].CGColor;
    shapeLayer.strokeColor = lineColor.CGColor;
    [shapeLayer setLineDashPattern:[NSArray arrayWithObjects:[NSNumber numberWithInt:lineLength], [NSNumber numberWithInt:lineSpacing], nil]];
    
    CGMutablePathRef path = CGPathCreateMutable();
    CGPathMoveToPoint(path, NULL, fromPoint.x, fromPoint.y);
    CGPathAddLineToPoint(path, NULL,toPoint.x, toPoint.y);
    [shapeLayer setPath:path];
    CGPathRelease(path);
    
    return shapeLayer;
}


@end
