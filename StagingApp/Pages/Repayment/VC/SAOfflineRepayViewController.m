
#import "SAOfflineRepayViewController.h"
#import "SAOfflineRepayInfoApiManager.h"
#import "SACommonWebViewController.h"

@interface SAOfflineRepayViewController ()<QLAPIManagerParamSource, QLAPIManagerCallBackDelegate>

@property (nonatomic, strong) SAOfflineRepayInfoApiManager *infoManager;
@property (nonatomic, strong) NSDictionary *dataDict;

@end

@implementation SAOfflineRepayViewController

- (void)viewDidLoad
{
    [super viewDidLoad];
    
    
    self.navigationItem.title = @"我要还款";

    [self showActivityHUD:nil];
    [self.infoManager loadData];
}

- (void)setupSubviews
{
    UIImageView *icon = [[UIImageView alloc] init];
    icon.contentMode = UIViewContentModeScaleAspectFill;
    [self.view addSubview:icon];
    
    UILabel *titLab = [UILabel new];
    titLab.font = Font_XX6(15);
    titLab.textColor = [UIColor colorWithHex:0x333333];
    [self.view addSubview:titLab];
    
    UILabel *conLab = [UILabel new];
    conLab.font = Font_XX6(15);
    conLab.textColor = [UIColor colorWithHex:0x333333];
    [self.view addSubview:conLab];

    
    UIButton *customBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [customBtn setTitle:@"联系官方客服" forState:UIControlStateNormal];
    [customBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    [customBtn setBackgroundColor:kPrimaryColor];
    [customBtn addTarget:self action:@selector(jumpCustomer) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:customBtn];
    
    [icon sd_setImageWithURL:[NSURL URLWithString:self.dataDict[@"headerPicture"]]];
    titLab.text = self.dataDict[@"reminderTitle"];
    conLab.text = self.dataDict[@"reminderContent"];
    
    [icon mas_makeConstraints:^(SAConstraintMaker *make) {
        make.centerX.equalTo(self.view);
        make.top.equalTo(self.view).offset(20);
        make.left.equalTo(self.view).offset(20);
        make.height.mas_equalTo(220);
    }];
    
    [titLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.equalTo(self.view).offset(30);
        make.top.equalTo(icon.mas_bottom).offset(30);
    }];
    
    conLab.numberOfLines = 0;
    [conLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.equalTo(titLab);
        make.top.equalTo(titLab.mas_bottom).offset(8);
        make.centerX.equalTo(self.view);
    }];
    
    [customBtn mas_makeConstraints:^(SAConstraintMaker *make) {
        make.centerX.equalTo(self.view);
        make.top.equalTo(conLab.mas_bottom).offset(40);
        make.width.mas_equalTo(160);
        make.height.mas_equalTo(48);
    }];
    customBtn.layer.masksToBounds = YES;
    customBtn.layer.cornerRadius = 6;
}


- (void)jumpCustomer
{
    SACommonWebViewController *webVC = [[SACommonWebViewController alloc] init];
    webVC.url = self.dataDict[@"customerServiceUrl"];
    webVC.navTitle = @"联系客服";
    webVC.hidesBottomBarWhenPushed = YES;
    [[SACommonTool currentViewController].navigationController pushViewController:webVC animated:NO];
}

#pragma mark QLAPIManagerCallBackDelegate

- (void)managerCallAPIDidSuccess:(SABaseManager *)manager
{
    [self hideHUDView];
    
    if (manager == self.infoManager) {
        NSDictionary *dataDict = [manager fetchDataWithReformer:self.infoManager];
        self.dataDict = dataDict;
        
        [self setupSubviews];
    }
}

- (void)managerCallAPIDidFailed:(SABaseManager *)manager
{
    [self hideHUDView];
    
    NSString *failMsg  = [SAResponseHandle netWorkErrorMsg:[manager fetchDataWithReformer:nil] andErrorType:manager.errorType];
    [SATool textStateHUD:failMsg];
}

#pragma mark QLAPIManagerParamSource

- (NSDictionary *)paramsForApi:(SABaseManager *)manager
{
    return [NSDictionary dictionary];
}

#pragma mark - getter

- (SAOfflineRepayInfoApiManager *)infoManager{
    if(_infoManager == nil){
        _infoManager = [SAOfflineRepayInfoApiManager new];
        _infoManager.delegate = self;
        _infoManager.paramSource = self;
    }
    return _infoManager;
}


@end
