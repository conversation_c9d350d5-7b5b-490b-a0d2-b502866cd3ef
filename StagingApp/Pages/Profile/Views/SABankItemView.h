
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@class SABankItemView;
@protocol NPBankItemViewDelegate <NSObject>

- (void)bankItemEndEditing:(SABankItemView *)itemView;

@end

@interface SABankItemView : UIView

@property (nonatomic, strong) NSString *titleText;
@property (nonatomic, strong) NSString *fieldHolder;


@property (nonatomic, strong) NSString *itemValue;
@property (nonatomic, strong) NSString *itemKey;

@property (nonatomic, assign) BOOL isArrow;
@property (nonatomic, assign) BOOL isVCode;
@property (nonatomic, assign) BOOL showLine;
@property (nonatomic, assign) BOOL isNumber;
@property (nonatomic, assign) NSInteger digitsNum;



@property (nonatomic, strong) NSString *mobile;
@property (nonatomic, strong) NSString *bankNo;
@property (nonatomic, strong) NSString *flowId;
@property (nonatomic, strong) NSString *bankName;
@property (nonatomic, strong) NSString *bankCode;


@property (nonatomic, weak) id<NPBankItemViewDelegate> delegate;


@end

NS_ASSUME_NONNULL_END
