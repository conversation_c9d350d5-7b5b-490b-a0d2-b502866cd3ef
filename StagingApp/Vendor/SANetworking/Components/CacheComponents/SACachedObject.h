
#import <Foundation/Foundation.h>

@interface SACachedObject : NSObject

@property (nonatomic, copy, readonly)   NSData *content;
@property (nonatomic, copy, readonly)   NSDate *lastUpdateTime;

@property (nonatomic, assign, readonly) BOOL isOutdated;
@property (nonatomic, assign, readonly) BOOL isEmpty;

- (instancetype)initWithContent:(NSData *)content;
- (void)updateContent:(NSData *)content;

@end
