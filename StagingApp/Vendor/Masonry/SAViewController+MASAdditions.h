
#import "MASUtilities.h"
#import "SAConstraintMaker.h"
#import "SAViewAttribute.h"

#ifdef MAS_VIEW_CONTROLLER

@interface MAS_VIEW_CONTROLLER (MASAdditions)

 
@property (nonatomic, strong, readonly) SAViewAttribute *mas_topLayoutGuide;
@property (nonatomic, strong, readonly) SAViewAttribute *mas_bottomLayoutGuide;
@property (nonatomic, strong, readonly) SAViewAttribute *mas_topLayoutGuideTop;
@property (nonatomic, strong, readonly) SAViewAttribute *mas_topLayoutGuideBottom;
@property (nonatomic, strong, readonly) SAViewAttribute *mas_bottomLayoutGuideTop;
@property (nonatomic, strong, readonly) SAViewAttribute *mas_bottomLayoutGuideBottom;


@end

#endif
