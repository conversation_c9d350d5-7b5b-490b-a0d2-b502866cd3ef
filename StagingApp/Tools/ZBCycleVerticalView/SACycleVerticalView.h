
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN
typedef NS_ENUM(NSInteger, ZBCycleVerticalViewScrollDirection) {
    ZBCycleVerticalViewScrollDirectionUp = 0,
    ZBCycleVerticalViewScrollDirectionDown
};

typedef void(^ClickBlock)(NSInteger index);

@interface SACycleVerticalView : UIView

@property (assign, nonatomic) ZBCycleVerticalViewScrollDirection direction;
@property (strong, nonatomic) NSArray *dataArray;  
@property (copy, nonatomic) ClickBlock block;

- (void)startAnimation;

- (void)stopAnimation;

@end

NS_ASSUME_NONNULL_END
