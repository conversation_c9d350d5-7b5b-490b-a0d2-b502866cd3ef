//
//  SARepayCashierModel.h
//  StagingApp
//
//  Created by Hardeen on 2024/10/25.
//  Copyright © 2024 Facebook. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@class SACashierCardModel;
@interface SARepayCashierModel : NSObject

@property (nonatomic, assign) NSInteger amount;
@property (nonatomic, assign) NSInteger orderType;
@property (nonatomic, strong) NSString *payOrderNo;
@property (nonatomic, strong) NSArray <SACashierCardModel *>*bankRoList;

+ (instancetype)objectWithDict:(NSDictionary *)dict;


@end

@interface SACashierCardModel : NSObject

@property (nonatomic, strong) NSString *bankCardType;
@property (nonatomic, strong) NSString *bankCode;
@property (nonatomic, strong) NSString *bankName;
@property (nonatomic, strong) NSString *holderName;
@property (nonatomic, strong) NSString *cardNo;
@property (nonatomic, strong) NSString *bindId;
@property (nonatomic, strong) NSString *bankLogo;
@property (nonatomic, strong) NSString *phone;

@end

NS_ASSUME_NONNULL_END
