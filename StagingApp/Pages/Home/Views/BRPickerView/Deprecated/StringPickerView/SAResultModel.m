
#import "SAResultModel.h"

@implementation SAResultModel

- (BOOL)isEqual:(id)object {
    
    if (self == object) {
        return YES;
    }
    
    if (![object isKindOfClass:[SAResultModel class]]) {
        return NO;
    }
    
    SAResultModel *model = (SAResultModel *)object;
    if (!model) {
        return NO;
    }
    
    BOOL isSameKey = (!self.key && !model.key) || [self.key isEqualToString:model.key];
    BOOL isSameValue = (!self.value && !model.value) || [self.value isEqualToString:model.value];
    
    return isSameKey && isSameValue;
}

- (NSUInteger)hash {
    return [self.key hash] ^ [self.value hash];
}

@end
