
#import "SAHomeModel.h"


@implementation SAHomeModel

+ (void)load {
    
    [SAHomeModel mj_setupObjectClassInArray:^NSDictionary *{
        return @{
                 @"borrowPurposeList" : @"SABorrowPurposeTypeModel",
                 @"borrowCashList" : @"SABorrowCashModel",
                 @"slideshowVOList" : @"SABannerModel",
                 @"borrowStepVOList": @"NPHomeStepModel"
                };
    }];
}

+ (instancetype)objectWithDict:(NSDictionary *)dict {
    return [self mj_objectWithKeyValues:dict];
}

- (CGFloat)cellHeight {
    return UIScreenHeight - kTabBarHeight;

    
}

@end
