
#import <Foundation/Foundation.h>

@interface NSDate (Extension)

+ (NSDate *)dateFromString:(NSString *)string withFormat:(NSString *)format;
+ (NSString *)stringFromDate:(NSDate *)date withFormat:(NSString *)format;
+ (NSString *)stringForDisplayFromDate:(NSDate *)date prefixed:(BOOL)prefixed;
+ (NSString *)stringForDisplayFromDate:(NSDate *)date;

+ (NSInteger)getDaysInMonthFromDate:(NSDate*)date;
- (NSString *)getFormatYearMonthDay;
- (NSString *)getFormatYearMonth;

- (NSInteger )getWeekNumOfMonth;

- (NSInteger )getWeekOfYear;
- (long long)todayMaxTimeInterval;
- (long long)todayMinTimeInterval;
- (BOOL)isInTheSameDay:(NSDate *)date;


- (NSDate *)dateAfterDay:(NSUInteger)day;
- (NSDate *)lastMonth;
- (NSDate *)dateafterMonth:(int)month;

- (NSUInteger)getDay;
- (NSString *)getWeek;
- (NSUInteger)getMonth;
- (NSUInteger)getYear;
- (NSInteger )getHour;
- (NSInteger)getMinute;
- (NSInteger)getHour:(NSDate *)date;
- (NSInteger)getMinute:(NSDate *)date;

- (NSUInteger)daysAgo;
- (NSUInteger)daysAgoAgainstMidnight;
- (NSString *)stringDaysAgo;
- (NSString *)stringDaysAgoAgainstMidnight:(BOOL)flag;

- (NSUInteger)weekday;
- (NSUInteger)weekday:(NSDate *)date;
+ (NSString *)dataWithString:(NSString *)secs;

- (NSString *)stringWithFormat:(NSString *)format;

- (NSString *)stringWithDateStyle:(NSDateFormatterStyle)dateStyle timeStyle:(NSDateFormatterStyle)timeStyle;

- (NSDate *)beginningOfWeek;
- (NSDate *)beginningOfDay;
- (NSDate *)beginningOfMonth;
- (NSDate *)nextBeginningOfMonth;
- (NSDate *)endOfMonth;
- (NSDate *)endOfWeek;

- (NSInteger)daysBetweenDate:(NSDate *)date;
- (NSInteger)daysFromNow;

- (NSInteger)monthsBetweenDate:(NSDate *)date;

+ (NSDate *)getSinceOrMaxOfDay:(BOOL)isMax;

- (NSString*)transformToFuzzyDate;

- (NSString*)promptDateString;

+ (BOOL)date:(NSDate*)date isBetweenDate:(NSDate*)beginDate andDate:(NSDate*)endDate;

@end
