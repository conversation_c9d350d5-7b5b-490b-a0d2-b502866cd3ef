
#import "SARepayListVC.h"
#import "SAGetRepayBillListApi.h"
#import "SATabedSlideView.h"
#import "SARepaymentCell.h"
#import <MJExtension.h>
#import "SAOrderDetailViewController.h"
#import "SACommonNoDataView.h"
#import "SANormalRefresh.h"
#import "SAGetBorrowAgainApi.h"
#import "SAConfirmmBankViewController.h"

@interface SARepayListVC ()<QLAPIManagerParamSource, QLAPIManagerCallBackDelegate, UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, strong) SAGetRepayBillListApi *repayApi;
@property (nonatomic, strong) SACommonNoDataView *noDataV;

@property (nonatomic, strong) SAGetBorrowAgainApi *againApi;

@property (nonatomic, strong) UITableView *tableV;
@property (nonatomic, strong) NSArray *dataList;

@end

@implementation SARepayListVC

#pragma mark - Life cycle

- (instancetype)init
{
    if (self = [super init])
    {
        self.backImgName = @"";
    }

    return self;
}

- (void)viewDidAppear:(BOOL)animated
{
    [super viewDidAppear:animated];
    
    self.navigationController.interactivePopGestureRecognizer.enabled = NO;
}

- (void)viewDidDisappear:(BOOL)animated
{
    [super viewDidDisappear:animated];
    
    self.navigationController.interactivePopGestureRecognizer.enabled = YES;
}

- (void)viewDidLoad
{
    [super viewDidLoad];
    
    self.navigationItem.title = @"待还款";

    self.view.backgroundColor = [UIColor colorWithHex:0xf6f6f6];
    [self createUI];
    
}

- (void)viewWillAppear:(BOOL)animated
{
    [super viewWillAppear:animated];
    
    [self showActivityHUD:nil];
    [self.repayApi loadData];
}

- (void)createUI
{
    [self.view addSubview:self.tableV];
    [self.tableV mas_makeConstraints:^(SAConstraintMaker *make) {
        make.edges.equalTo(self.view);
    }];
}

#pragma mark - Method

- (void)borrowAgain
{
    [self showActivityHUD:nil];
    [self.againApi loadData];
}

#pragma mark - QLAPIManagerCallBackDelegate

- (void)managerCallAPIDidSuccess:(SABaseManager *)manager
{
    [self hideHUDView];
    [self endLoading];
    
    if (manager == self.repayApi)
    {
        NSDictionary *dataDict = [manager fetchDataWithReformer:self.repayApi];
        self.dataList = [SARepayListModel mj_objectArrayWithKeyValuesArray: dataDict[@"repaymentBillList"]];
        
        if(!self.dataList || self.dataList.count == 0){
            [self.tableV addSubview: self.noDataV];
            [self.noDataV mas_makeConstraints:^(SAConstraintMaker *make) {
                make.width.height.mas_equalTo(64);
                make.centerX.centerY.equalTo(self.tableV);
                make.centerY.equalTo(self.tableV).offset(-80);
            }];
        }else{
            [self.noDataV removeFromSuperview];
        }
        [self.tableV reloadData];
    }
    
    if (manager == self.againApi)
    {
        NSDictionary *dataDict = [manager fetchDataWithReformer:self.againApi];
        
        NSString *jumpUrl = dataDict[@"jumpUrl"];
        if ([jumpUrl isEqualToString:@"APP/CLViewControllerUserDataList"])
        {
            [SAVerifyListManager sharedInstance].popVCName = NSStringFromClass([[SACommonTool currentViewController] class]);
            [[SAVerifyListManager sharedInstance] handleVerifyJump];
        }
        else if([jumpUrl isEqualToString:@"APP/Product/CardList"])
        {
            SAConfirmmBankViewController *vc = [SAConfirmmBankViewController new];
            vc.hidesBottomBarWhenPushed = YES;
            [[SACommonTool currentViewController].navigationController pushViewController:vc animated:YES];
        }
    }
}

- (void)managerCallAPIDidFailed:(SABaseManager *)manager
{
    [self hideHUDView];
    [self endLoading];
    
    NSString *failMsg  = [SAResponseHandle netWorkErrorMsg:[manager fetchDataWithReformer:nil] andErrorType:manager.errorType];
    [self textStateHUD:failMsg];
}

- (void)endLoading {
    
    if (self.tableV.mj_header.isRefreshing) {
        [self.tableV.mj_header endRefreshing];
    }
}

#pragma mark - QLAPIManagerParamSource

- (NSDictionary *)paramsForApi:(SABaseManager *)manager
{
    return [NSDictionary dictionary];
}

#pragma mark - UITableViewDataSource

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return 1;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    NSLog(@"当前数量----%ld", self.dataList.count);
    return self.dataList.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    static NSString *repayCID = @"Repayment-Cell";
    SARepaymentCell *cell = [tableView dequeueReusableCellWithIdentifier:repayCID];
    if (cell == nil) {
        cell = [[SARepaymentCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:repayCID];
    }
    cell.backgroundColor = [UIColor clearColor];
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    
    SARepayListModel *model = self.dataList[indexPath.row];
    cell.model = model;
    
    return  cell;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath
{
    return 345;
}


- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section
{
        return 100;
}


#pragma mark - getter

- (UITableView *)tableV {
    if (_tableV == nil) {
        _tableV = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
        _tableV.dataSource      = self;
        _tableV.delegate        = self;
        if (@available(iOS 11.0, *)) {
            _tableV.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
        }
        _tableV.backgroundColor = [UIColor clearColor];
        _tableV.separatorStyle = UITableViewCellSeparatorStyleNone;
        __weak typeof(self) weakSelf = self;
        _tableV.mj_header = [SAJGRefreshNormalHeader headerWithRefreshingBlock:^{
            [weakSelf.repayApi loadData];
        }];
    }
    return _tableV;
}

- (SAGetRepayBillListApi *)repayApi{
    if (_repayApi == nil) {
        _repayApi = [SAGetRepayBillListApi new];
        _repayApi.delegate = self;
        _repayApi.paramSource = self;
    }
    return _repayApi;
}

- (NSArray *)dataList{
    if (_dataList == nil) {
        _dataList = [NSArray array];
    }
    return _dataList;
}

- (SACommonNoDataView *)noDataV {
    if (_noDataV == nil) {
        _noDataV = [[SACommonNoDataView alloc] initWithImg:@"noData_icon" title:@"暂无待还款账单"];
    }
    return _noDataV;
}

- (SAGetBorrowAgainApi *)againApi{
    if(_againApi == nil){
        _againApi = [SAGetBorrowAgainApi new];
        _againApi.delegate = self;
        _againApi.paramSource = self;
    }
    return _againApi;
}

@end
