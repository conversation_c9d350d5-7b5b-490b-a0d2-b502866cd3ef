
#import <UIKit/UIKit.h>
#import "SAPickerStyle.h"

NS_ASSUME_NONNULL_BEGIN

typedef void(^BRPickerAlertCancelBlock)(void);
typedef void(^BRPickerAlertDoneBlock)(void);

@interface SAPickerAlertView : UIView

 
@property (nullable, nonatomic, copy) NSString *title;

 
@property (nullable, nonatomic, strong) SAPickerStyle *pickerStyle;

 
@property (nullable, nonatomic, strong) UIView *pickerHeaderView;

 
@property (nullable, nonatomic, strong) UIView *pickerFooterView;

 
@property (nullable, nonatomic, copy) BRPickerAlertCancelBlock cancelBlock;

@property (nullable, nonatomic, copy) BRPickerAlertDoneBlock doneBlock;

 
@property (nullable, nonatomic, strong) UIView *alertView;

 
@property (nullable, nonatomic, strong) UIView *keyView;


- (void)reloadData;

- (void)addPickerToView:(nullable UIView *)view NS_REQUIRES_SUPER;

- (void)removePickerFromView:(nullable UIView *)view;

- (void)addSubViewToPicker:(UIView *)customView;

- (void)addSubViewToTitleBar:(UIView *)customView;


@end

NS_ASSUME_NONNULL_END
