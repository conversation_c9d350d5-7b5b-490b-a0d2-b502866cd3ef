
#import "SAHomeNormalCell.h"
#import "SATabBarViewController.h"
#import "SALoginViewController.h"
#import "SAVerifyListManager.h"
#import "SAGetBorrowAgainApi.h"
#import "SAConfirmmBankViewController.h"

#define kBtnTipsWidth   140
#define kBtnTipsHeight  20

@interface SAHomeNormalCell()<QLAPIManagerCallBackDelegate, QLAPIManagerParamSource>

@property (nonatomic, strong) UILabel *titleLab;
@property (nonatomic, strong) UILabel *amountTipsLab;
@property (nonatomic, strong) UILabel *amountLab;

@property (nonatomic, strong) UIImageView *dotLine;
@property (nonatomic, strong) UIView *midContentV;


@property (nonatomic, strong) UIButton *loanBtn;

@property (nonatomic, strong) UILabel *bigTipsLab;
@property (nonatomic, strong) UILabel *smallTipsLab;

@property (nonatomic, strong) UILabel *btnTipsLab;

@property (nonatomic, strong) SAGetBorrowAgainApi *againApi;

@end

@implementation SAHomeNormalCell

#pragma mark - Life cycle

- (void)renderCoreBg
{
    [self addCoreSubviews];
    [self setCoreConstraints];
    
    self.titleLab.text = @"最高可借额度(元)";
    self.amountLab.text = self.model.maxAmount;
    self.amountTipsLab.text = self.model.tips; 

    self.bigTipsLab.text = self.model.bigTips;
    self.smallTipsLab.text = self.model.tips;
    [self.loanBtn setTitle:self.model.buttonTxt forState:UIControlStateNormal];
    
    self.btnTipsLab.text = @"新用户下款率高达90%";
//    self.btnTipsLab.backgroundColor = [UIColor colorWithHex:0xfcf3e0];
//    
//    CGRect bounds = CGRectMake(0, 0, kBtnTipsWidth, kBtnTipsHeight);
//    UIBezierPath *maskPath = [UIBezierPath
//        bezierPathWithRoundedRect:bounds
//        byRoundingCorners:(UIRectCornerTopLeft | UIRectCornerBottomRight)
//        cornerRadii:CGSizeMake(5, 5)
//    ];
//    CAShapeLayer *maskLayer = [CAShapeLayer layer];
//    maskLayer.frame = bounds;
//    maskLayer.path = maskPath.CGPath;
//    self.btnTipsLab.layer.mask = maskLayer;
}

- (void)addCoreSubviews
{
    [self.coreBg addSubview:self.titleLab];
    [self.coreBg addSubview:self.amountTipsLab];
    [self.coreBg addSubview:self.amountLab];
    [self.coreBg addSubview:self.dotLine];
    [self.coreBg addSubview:self.midContentV];
    [self.coreBg addSubview:self.loanBtn];
    [self.coreBg addSubview:self.btnTipsLab];
}

- (void)setCoreConstraints
{
    CGFloat verGap = 15;
    
    [self.titleLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.centerX.equalTo(self.coreBg);
        make.top.equalTo(self.coreBg).offset(verGap+5);
    }];
    [self.amountLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.centerX.equalTo(self.coreBg);
        make.top.equalTo(self.titleLab.mas_bottom).offset(5);
    }];
    [self.amountTipsLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.centerX.equalTo(self.coreBg);
        make.top.equalTo(self.amountLab.mas_bottom).offset(5);
    }];
    
    [self.dotLine mas_makeConstraints:^(SAConstraintMaker *make) {
        make.top.equalTo(self.amountTipsLab.mas_bottom).offset(20);
        make.centerX.equalTo(self.coreBg);
        make.left.equalTo(self.coreBg).offset(30);
        make.height.mas_equalTo(6);
    }];
    
    [self.midContentV mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.right.equalTo(self.coreBg);
        make.top.equalTo(self.dotLine.mas_bottom).offset(1);
        make.height.mas_equalTo(76);
    }];

    
  
    [self.loanBtn mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.equalTo(self.coreBg).offset(30);
        make.centerX.equalTo(self.coreBg);
        make.top.equalTo(self.midContentV.mas_bottom).offset(verGap*3);
        make.height.mas_equalTo(48);
    }];
    self.loanBtn.layer.masksToBounds = YES;
    self.loanBtn.layer.cornerRadius = 8;
    
    
    [self.btnTipsLab mas_makeConstraints:^(SAConstraintMaker *make) {
//        make.height.mas_equalTo(kBtnTipsHeight);
//        make.width.mas_equalTo(kBtnTipsWidth);
//        make.centerY.equalTo(self.loanBtn.mas_top);
//        make.right.equalTo(self.loanBtn);
        make.centerX.equalTo(self.coreBg);
        make.bottom.equalTo(self.loanBtn.mas_top).offset(-10);
    }];
    
}

#pragma mark - action

- (void)loanAction{
    
    if ([SAUserManager isLogin])
    {
        if ([self.model.buttonJumpUrl isEqualToString:@"APP/CLViewControllerUserDataList"])
        {
            [SAVerifyListManager sharedInstance].popVCName = NSStringFromClass([[SACommonTool currentViewController] class]);
            [[SAVerifyListManager sharedInstance] handleVerifyJump];
        }
        else if([self.model.buttonJumpUrl isEqualToString:@"APP/Product/CardList"])
        {
            
            [SATool showWindowHUD:nil];
            [self.againApi loadData];
        }
    }
    else
    {
        UINavigationController *navVC = [[UINavigationController alloc] initWithRootViewController:[[SALoginViewController alloc] init]];
        navVC.modalPresentationStyle = UIModalPresentationFullScreen;
        [[SACommonTool currentViewController].navigationController presentViewController:navVC animated:YES completion:nil];
    }
}

#pragma mark - QLAPIManagerCallBackDelegate

- (void)managerCallAPIDidSuccess:(SABaseManager *)manager
{
    [SATool hideWindowHUD];
    if (manager == self.againApi)
    {
        NSDictionary *dataDict = [manager fetchDataWithReformer:self.againApi];
        
        NSString *jumpUrl = dataDict[@"jumpUrl"];
        if ([NSString judgeStringExist:jumpUrl] && [jumpUrl isEqualToString:@"APP/CLViewControllerUserDataList"])
        {
            [SAVerifyListManager sharedInstance].popVCName = NSStringFromClass([[SACommonTool currentViewController] class]);
            [[SAVerifyListManager sharedInstance] handleVerifyJump];
        }
        else
        {
            [SATool textStateHUD:@"操作成功!"];
            [[NSNotificationCenter defaultCenter] postNotificationName:@"RefreshHomeData" object:nil];
        }
    }
}

- (void)managerCallAPIDidFailed:(SABaseManager *)manager
{
    [SATool hideWindowHUD];
    
    NSString *failMsg  = [SAResponseHandle netWorkErrorMsg:[manager fetchDataWithReformer:nil] andErrorType:manager.errorType];
    [SATool textStateWindowHUD:failMsg];
}

#pragma mark - QLAPIManagerParamSource

- (NSDictionary *)paramsForApi:(SABaseManager *)manager
{
    return [NSDictionary dictionary];
}

#pragma mark - setter


#pragma mark - getter


- (UILabel *)titleLab{
    FF_Fetch_UILable_CenterX(_titleLab, kHomeTextColor, BoldFont_XX6(14))
}

- (UILabel *)amountTipsLab{
    FF_Fetch_UILable_CenterX(_amountTipsLab, kHomeTextColor, Font_XX6(12))
}

- (UILabel *)amountLab{
    FF_Fetch_UILable_CenterX(_amountLab,kHomeTextColor, BoldFont_XX6(36))
}

- (UILabel *)bigTipsLab{
    FF_Fetch_UILable_CenterX(_bigTipsLab, kHomeTextColor, Font_XX6(13))
}

- (UIButton *)loanBtn
{
    if (_loanBtn == nil) {
        _loanBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_loanBtn setTitleColor:kHomeTextColor forState:UIControlStateNormal];
        [_loanBtn addTarget:self action:@selector(loanAction) forControlEvents:UIControlEventTouchUpInside];
        _loanBtn.titleLabel.font = BoldFont_XX6(17);
        _loanBtn.backgroundColor = kPrimaryColor;
    }
    
    return _loanBtn;
}

- (UILabel *)smallTipsLab
{
    FF_Fetch_UILable_CenterX(_smallTipsLab, [UIColor colorWithHex:0x666666], Font_XX6(10))
}

- (UILabel *)btnTipsLab{
    FF_Fetch_UILable_CenterX(_btnTipsLab, [UIColor redColor], Font_XX6(10))
}

- (SAGetBorrowAgainApi *)againApi{
    if(_againApi == nil){
        _againApi = [SAGetBorrowAgainApi new];
        _againApi.delegate = self;
        _againApi.paramSource = self;
    }
    return _againApi;
}

- (UIView *)midContentV{
    if (_midContentV == nil) {
        _midContentV = [UIView new];
        
        
        NSArray *dataList = @[
            @{@"title": @"安全合规", @"icon": @"home_tip1"},
            @{@"title": @"息费透明", @"icon": @"home_tip2"},
            @{@"title": @"期限灵活", @"icon": @"home_tip3"},
        ];
        
        CGFloat singleW = (UIScreenWidth-15*2)/3;
        [dataList enumerateObjectsUsingBlock:^(NSDictionary *item, NSUInteger idx, BOOL * _Nonnull stop) {
            UIView *view = [UIView new];
            [_midContentV addSubview:view];
            
            UIImageView *icon = [[UIImageView alloc] initWithImage:[UIImage imageNamed:item[@"icon"]]];
            [view addSubview:icon];
            
            UILabel *titL = [UILabel new];
            titL.text = item[@"title"];
            titL.font = BoldFont_XX6(11);
            titL.textColor = [UIColor colorWithHex:0x333333];
            [view addSubview:titL];
            
            [view mas_makeConstraints:^(SAConstraintMaker *make) {
                make.left.equalTo(_midContentV).offset(idx * singleW);
                make.width.mas_equalTo(singleW);
                make.top.bottom.equalTo(_midContentV);
            }];

            [icon mas_makeConstraints:^(SAConstraintMaker *make) {
                make.width.height.mas_equalTo(30);
                make.centerX.centerY.equalTo(view);
            }];
            [titL mas_makeConstraints:^(SAConstraintMaker *make) {
                make.centerX.equalTo(view);
                make.top.equalTo(icon.mas_bottom).offset(5);
            }];
        }];
    }
    return _midContentV;
}

- (UIImageView *)dotLine{
    FF_Fetch_UIImageViewWithImage(_dotLine, [UIImage imageNamed:@"dot_line"])
}

@end
