
#import <UIKit/UIKit.h>


NS_ASSUME_NONNULL_BEGIN
@class SAActionSheet;
@class SAActionSheetItem;
@protocol NPActionSheetClickedDelegate <NSObject>

@required
 
- (void)actionSheet:(SAActionSheet *)actionSheet clickedButtonAtIndex:(SAActionSheetItem *)item;

@end

@interface SAActionSheetItem : NSObject

@property (nonatomic, readonly, copy) NSString *title;
@property (nonatomic, readonly, assign) NSInteger index;

+ (SAActionSheetItem *)itemWithTitle:(NSString *)title index:(NSInteger)index;

@end

NS_ASSUME_NONNULL_END





NS_ASSUME_NONNULL_BEGIN

typedef void(^ClickedCompleteBlock)(SAActionSheetItem *item);

@interface SAActionSheet : UIView

@property (nonatomic, copy)ClickedCompleteBlock clickedCompleteBlock;

 
- (instancetype)initWithDelegate:(id<NPActionSheetClickedDelegate>)delegate
                     cancleTitle:(NSString *)cancleTitle
                     otherTitles:(NSString *)otherTitles,... NS_REQUIRES_NIL_TERMINATION;

- (void)show;

@end

NS_ASSUME_NONNULL_END
