
#import "SABannerView.h"
#import <TYCyclePagerView/TYPageControl.h>
#import "SACommonWebViewController.h"
#import "SABannerCell.h"


@interface SABannerView ()<TYCyclePagerViewDelegate, TYCyclePagerViewDataSource>

@property (nonatomic, strong) TYPageControl *pageControl;

@end


@implementation SABannerView

#pragma mark - Life cycle

- (instancetype)init
{
    if (self = [super init])
    {
        self.backgroundColor = [UIColor clearColor];
        [self createUI];
        [self addPageControl];
        
    }
    
    return self;
}

- (void)createUI
{
    [self addSubview:self.pagerView];
    [self.pagerView mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.right.top.bottom.equalTo(self);
    }];
}

- (void)addPageControl 
{
    TYPageControl *pageControl = [[TYPageControl alloc]init];
    
    pageControl.currentPageIndicatorSize = CGSizeMake(6, 6);
    pageControl.pageIndicatorSize = CGSizeMake(12, 6);
    pageControl.currentPageIndicatorTintColor = [UIColor redColor];
    pageControl.pageIndicatorTintColor = [UIColor grayColor];
    [_pagerView addSubview:pageControl];
    _pageControl = pageControl;
}

- (void)layoutSubviews
{
    [super layoutSubviews];
    
    self.pageControl.frame = CGRectMake(0, CGRectGetHeight(self.pagerView.frame) - 26, CGRectGetWidth(self.pagerView.frame), 26);
}

#pragma mark - TYCyclePagerViewDelegate, TYCyclePagerViewDataSource

- (NSInteger)numberOfItemsInPagerView:(TYCyclePagerView *)pageView
{
    return self.dataList.count;
}

- (UICollectionViewCell *)pagerView:(TYCyclePagerView *)pagerView cellForItemAtIndex:(NSInteger)index
{
    SABannerCell *cell = [pagerView dequeueReusableCellWithReuseIdentifier:@"SABannerCell" forIndex:index];
    [cell.imgV sd_setImageWithURL:[NSURL URLWithString:self.dataList[index].imgUrl]];
    return cell;
}

- (TYCyclePagerViewLayout *)layoutForPagerView:(TYCyclePagerView *)pageView 
{
    TYCyclePagerViewLayout *layout = [[TYCyclePagerViewLayout alloc] init];
    layout.itemSize = CGSizeMake(UIScreenWidth-2*self.hGap, kBannerHeight);
    
    return layout;
}

- (void)pagerView:(TYCyclePagerView *)pageView didScrollFromIndex:(NSInteger)fromIndex toIndex:(NSInteger)toIndex 
{
    _pageControl.currentPage = toIndex;
    
}

- (void)pagerView:(TYCyclePagerView *)pageView didSelectedItemCell:(__kindof UICollectionViewCell *)cell atIndex:(NSInteger)index
{
    if(![NSString judgeStringExist:self.dataList[index].jumpUrl]) return;
    
    SACommonWebViewController *webVC = [[SACommonWebViewController alloc] init];
    webVC.url = self.dataList[index].jumpUrl;
    webVC.navTitle = self.dataList[index].title;
    webVC.hidesBottomBarWhenPushed = YES;
    [[SACommonTool currentViewController].navigationController pushViewController:webVC animated:NO];
}

#pragma mark - setter

- (void)setDataList:(NSArray<SABannerModel *> *)dataList
{
    _dataList = dataList;
    
    _pageControl.numberOfPages = dataList.count;
    [self.pagerView reloadData];
    
    [self.pagerView setNeedUpdateLayout];
}

#pragma mark - getter

- (TYCyclePagerView *)pagerView{
    if(_pagerView == nil){
        _pagerView = [[TYCyclePagerView alloc] init];
        _pagerView.isInfiniteLoop = YES;
        _pagerView.delegate = self;
        _pagerView.dataSource = self;
        _pagerView.autoScrollInterval = 3;
        [_pagerView registerClass:[SABannerCell class] forCellWithReuseIdentifier:@"SABannerCell"];
    }
    return _pagerView;
}


@end
