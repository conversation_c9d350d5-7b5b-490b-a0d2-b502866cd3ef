//
//  EsignSDKError.h
//  EsignSDKIOS
//
//  Created by 叶鹏飞 on 2024/8/8.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN


@interface EsignSDKError : NSObject

/**
 错误发生的地方, 具体的发生地方由上面定义的 WBFaceErrorDomainXXXX 指定
 */
@property (nonatomic, readonly, copy) NSString *domain;
/**
 每个domain中有相应的错误代码, 具体的错误代码见
 */
@property (nonatomic, readonly, assign) NSInteger code; // 错误代码
@property (nonatomic, readonly, copy) NSString *desc; // 获取本地化描述
@property (nonatomic, readonly, copy) NSString *reason; // 错误出现的真实原因原因

+ (instancetype)errorWithDomain:(NSString *)domain code:(NSInteger)code desc:(NSString *)desc;
+ (instancetype)errorWithDomain:(NSString *)domain code:(NSInteger)code desc:(NSString *)desc reason:(NSString *)reason;

@end

NS_ASSUME_NONNULL_END
