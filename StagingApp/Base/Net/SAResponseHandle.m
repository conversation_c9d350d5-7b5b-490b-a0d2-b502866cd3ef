
#import "SAResponseHandle.h"
#import "QLNetworkEnum.h"
#import "SADictionary+QLNetworkingMethods.h"

@implementation SAResponseHandle

+ (BOOL)processRespDictCodeZero:(id)data
{
    NSDictionary *respDict = data;
    if([data isKindOfClass:[NSString class]]){
        NSString *decodeStr = [SAObjC decryptString:data privateKey:COMMON_CRYPTO_RSA_PRIVATE];
        respDict = [NSDictionary QL_stringToDict: decodeStr];
    }
    if (nil != [respDict objectForKey:@"code"] && ![[respDict objectForKey:@"code"] isEqual:[NSNull null]])
    {
        NSString *code = [respDict objectForKey:@"code"];
        if ([code integerValue] == 0)
        {
            return YES;
        }
    }
    
    return NO;
}

+ (NSString *)netWorkErrorMsg:(NSDictionary *)dict andErrorType:(NSUInteger)errorType
{
    if (QLAPIManagerErrorTypeDefault == errorType)
    {
        return NSLocalizedString(@"ocationNameFramework", nil);
    }
    if (![dict isKindOfClass:[NSDictionary class]])
    {
        return NSLocalizedString(@"citiesFootList", nil);
    }
    
    
    
    NSString *msg = [dict objectForKey:@"msg"];
    if (msg == nil) {
        switch (errorType) {
                
            case QLAPIManagerErrorTypeNoContent:
                msg = NSLocalizedString(@"changeVerticalLayout", nil);
                break;
                
            case QLAPIManagerErrorTypeParamsError:
                
                msg = NSLocalizedString(@"radientInfo", nil);
                break;
            case QLAPIManagerErrorTypeTimeout:
            {
                msg = NSLocalizedString(@"productPurpose", nil);
            }
                
                break;
            case QLAPIManagerErrorTypeNoNetWork:
                msg = NSLocalizedString(@"otherEviceTool", nil);
                break;
            case QLAPIManagerErrorTypeSuccess:
                msg = NSLocalizedString(@"cardsRoduct", nil);
                break;
            default:
                msg = NSLocalizedString(@"networkingRotocol", nil);
                break;
        }
    }
    
    return msg;
}

@end
