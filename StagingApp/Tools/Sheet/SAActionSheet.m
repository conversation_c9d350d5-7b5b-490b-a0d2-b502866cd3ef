
#import "SAActionSheet.h"

@interface SAActionSheetItem()

@property (nonatomic, readwrite, copy) NSString *title;
@property (nonatomic, readwrite, assign) NSInteger index;

@end

@implementation SAActionSheetItem

+ (SAActionSheetItem *)itemWithTitle:(NSString *)title index:(NSInteger)index {
    SAActionSheetItem *item = [[SAActionSheetItem alloc] initWithTitle:title index:index];
    return item;
}

- (instancetype)initWithTitle:(NSString *)title index:(NSInteger)index {
    self = [super init];
    if(self) {
        _title = [title copy];
        _index = index;
    }
    return self;
}

@end


static CGFloat BtnHeight = 46.0;
static CGFloat CancleMargin = 8.0;

#define ZMActionSheetColor(r, g, b) [UIColor colorWithRed:(r/255.0) green:(g/255.0) blue:(b/255.0) alpha:1.0]
#define ZMActionSheetBGColor ZMActionSheetColor(237,240,242) 
#define ZMActionSheetSeparatorColor ZMActionSheetColor(226, 226, 226) 
#define ZMActionSheetNormalImage [self imageWithColor:ZMActionSheetColor(255,255,255)] 
#define ZMActionSheetHighImage [self imageWithColor:ZMActionSheetColor(242,242,242)] 

#define ZMActionSheetScreenWidth [UIScreen mainScreen].bounds.size.width
#define ZMActionSheetScreenHeight [UIScreen mainScreen].bounds.size.height

@interface SAActionSheet ()

@property (nonatomic, strong) UIView *sheetView;
@property (nonatomic, weak) id <NPActionSheetClickedDelegate> delegate;
@property (nonatomic, strong) NSMutableArray *items;

@end

@implementation SAActionSheet

- (instancetype)initWithDelegate:(id<NPActionSheetClickedDelegate>)delegate cancleTitle:(NSString *)cancleTitle otherTitles:(NSString *)otherTitles, ... {
    self = [super init];
    if (self) {
        
        if (delegate) {
            _delegate = delegate;
        }
        
        self.frame = [UIScreen mainScreen].bounds;
        self.backgroundColor = [UIColor blackColor];
        [[UIApplication sharedApplication].keyWindow addSubview:self];
        self.alpha = 0.0;
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(coverClick)];
        [self addGestureRecognizer:tap];
        
        
        _sheetView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, ZMActionSheetScreenWidth, 0)];
        _sheetView.backgroundColor = ZMActionSheetColor(237,240,242);
        _sheetView.alpha = 0.9;
        [[UIApplication sharedApplication].keyWindow addSubview:_sheetView];
        _sheetView.hidden = YES;
        
        
        int tag = 0;
        _items = [NSMutableArray array];
        
        SAActionSheetItem *cancleItem = [SAActionSheetItem itemWithTitle:cancleTitle index:0];
        [_items addObject:cancleItem];
        
        tag ++;
        
        NSString* curStr;
        va_list list;
        if(otherTitles)
        {
            SAActionSheetItem *item = [SAActionSheetItem itemWithTitle:otherTitles index:tag];
            [_items addObject:item];
            tag ++;
            
            va_start(list, otherTitles);
            while ((curStr = va_arg(list, NSString*))) {
                SAActionSheetItem *item = [SAActionSheetItem itemWithTitle:curStr index:tag];
                [_items addObject:item];
                tag ++;
            }
            va_end(list);
        }
        CGRect sheetViewF = _sheetView.frame;
        sheetViewF.size.height = BtnHeight * _items.count + CancleMargin;
        _sheetView.frame = sheetViewF;
        
        [self setupBtnWithTitles];
    }
    return self;
}

- (void)show {
    self.sheetView.hidden = NO;
    
    CGRect sheetViewF = self.sheetView.frame;
    sheetViewF.origin.y = ZMActionSheetScreenHeight;
    self.sheetView.frame = sheetViewF;
    
    CGRect newSheetViewF = self.sheetView.frame;
    newSheetViewF.origin.y = ZMActionSheetScreenHeight - self.sheetView.frame.size.height;
    
    [UIView animateWithDuration:0.3 animations:^{
        
        self.sheetView.frame = newSheetViewF;
        
        self.alpha = 0.7;
    }];
}

 
- (void)setupBtnWithTitles {
    
    for (SAActionSheetItem *item in _items) {
        UIButton *btn = nil;
        if (item.index == 0) {
            btn = [[UIButton alloc] initWithFrame:CGRectMake(0, _sheetView.frame.size.height - BtnHeight, ZMActionSheetScreenWidth, BtnHeight)];
        } else {
            btn = [[UIButton alloc] initWithFrame:CGRectMake(0, BtnHeight * (item.index - 1) , ZMActionSheetScreenWidth, BtnHeight)];
            
            UIView *line = [[UIView alloc] initWithFrame:CGRectMake(0, 0, ZMActionSheetScreenWidth, 0.5)];
            line.backgroundColor = ZMActionSheetSeparatorColor;
            [btn addSubview:line];
        }
        btn.tag = item.index;
        [btn setBackgroundImage:ZMActionSheetNormalImage forState:UIControlStateNormal];
        [btn setBackgroundImage:ZMActionSheetHighImage forState:UIControlStateHighlighted];
        [btn setTitle:item.title forState:UIControlStateNormal];
        btn.titleLabel.font = [UIFont fontWithName:@"STHeitiSC-Light" size:17];
        [btn setTitleColor:[UIColor blackColor] forState:UIControlStateNormal];
        [btn addTarget:self action:@selector(sheetBtnClick:) forControlEvents:UIControlEventTouchUpInside];
        [self.sheetView addSubview:btn];
    }
}

 
- (void)coverClick{
    CGRect sheetViewF = self.sheetView.frame;
    sheetViewF.origin.y = ZMActionSheetScreenHeight;
    
    [UIView animateWithDuration:0.2 animations:^{
        self.sheetView.frame = sheetViewF;
        self.alpha = 0.0;
    } completion:^(BOOL finished) {
        [self removeFromSuperview];
        [self.sheetView removeFromSuperview];
    }];
}

- (void)sheetBtnClick:(UIButton *)btn{
    SAActionSheetItem *item = _items[btn.tag];
    if (item.index == 0) {
        [self coverClick];
        return;
    }
    
    if ([self.delegate respondsToSelector:@selector(actionSheet:clickedButtonAtIndex:)]) {
        [self.delegate actionSheet:self clickedButtonAtIndex:item];
    }
    
    if (self.clickedCompleteBlock) {
        self.clickedCompleteBlock(item);
    }
    
    [self coverClick];
}

 
- (UIImage*)imageWithColor:(UIColor*)color
{
    CGRect rect=CGRectMake(0.0f, 0.0f, 1.0f, 1.0f);
    UIGraphicsBeginImageContext(rect.size);
    CGContextRef context = UIGraphicsGetCurrentContext();
    CGContextSetFillColorWithColor(context, [color CGColor]);
    CGContextFillRect(context, rect);
    UIImage *theImage = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    return theImage;
}

@end
