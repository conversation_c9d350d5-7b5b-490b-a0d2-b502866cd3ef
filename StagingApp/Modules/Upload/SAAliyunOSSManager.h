
#import <Foundation/Foundation.h>

#import "SABaseManager.h"
#import "SAUploadModel.h"


NS_ASSUME_NONNULL_BEGIN

typedef void(^YJBUploadSuccess)(NSDictionary *dict);
typedef void(^YJBUploadFailed)(NSError *error);


@interface SAAliyunOSSManager : NSObject

+ (SAAliyunOSSManager *)sharedInstance;

 
- (void)launchUpdateToken;

 
- (void)setupEnvironment;

- (void)uploadObjectAsync:(NSData *)fileData
               apiManager:(NSString *)apiManagerName
                apiParams:(NSDictionary *)params
                  success:(YJBUploadSuccess)successBlock
                   failed:(YJBUploadFailed)failBlock;

- (void)uploadObjectAsync:(NSData *)fileData
                  success:(YJBUploadSuccess)successBlock
                   failed:(YJBUploadFailed)failBlock;

- (void)uploadObjectAsync:(NSData *)fileData
                      tag:(NSNumber *)tagNumber
                  success:(YJBUploadSuccess)successBlock
                   failed:(YJBUploadFailed)failBlock;

@end

NS_ASSUME_NONNULL_END
