
#import <Foundation/Foundation.h>
#import "SAService.h"
#import "SALoggerConfiguration.h"
#import "SAResponse.h"

@interface SALogger : NSObject

@property (nonatomic, strong, readonly) SALoggerConfiguration *configParams;

+ (void)logDebugInfoWithRequest:(NSURLRequest *)request
                        apiName:(NSString *)apiName
                        service:(SAService *)service
                  requestParams:(id)requestParams
                     httpMethod:(NSString *)httpMethod;

+ (void)logDebugInfoWithResponse:(NSHTTPURLResponse *)response
                  responseString:(NSString *)responseString
                         request:(NSURLRequest *)request
                           error:(NSError *)error;

+ (void)logDebugInfoWithCachedResponse:(SAResponse *)response methodName:(NSString *)methodName serviceIdentifier:(SAService *)service;

+ (instancetype)sharedInstance;
- (void)logWithActionCode:(NSString *)actionCode params:(NSDictionary *)params;

@end
