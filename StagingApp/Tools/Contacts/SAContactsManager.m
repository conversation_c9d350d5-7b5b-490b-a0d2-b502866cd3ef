
 

#import "SAContactsManager.h"

#import <AddressBook/AddressBook.h>
#import <AddressBookUI/AddressBookUI.h>

#import <Contacts/Contacts.h>
#import <ContactsUI/ContactsUI.h>

typedef enum : NSUInteger {
    MyContactsAuthorityTypeEnable,      
    MyContactsAuthorityTypeRefuse,      
    MyContactsAuthorityTypeNoRequest,   
} MyContactsAuthorityType;

@interface SAContactsManager ()<ABPeoplePickerNavigationControllerDelegate, CNContactPickerDelegate>

@property (nonatomic, copy)SelectContactsCompleteBlock complete;
@property (nonatomic, copy)CancelSelectContactsBlock cancel;
@property (nonatomic, copy)GainAllContactsBlock gainBlock;


@end

@implementation SAContactsManager

- (void)startSelectContacts:(SelectContactsCompleteBlock)completeBlock withCancel:(CancelSelectContactsBlock)cancelBlock {
    
    self.complete = completeBlock;
    self.cancel = cancelBlock;
    
    switch ([SAContactsManager checkContactsAuthority]) {
        case MyContactsAuthorityTypeRefuse:
            [self jumpOpenAuthorization];
            break;
        case MyContactsAuthorityTypeNoRequest:
        {
            __weak typeof(self) weakSelf = self;
            [self requestAuthorization:^(BOOL result) {
                if (result) {
                    [weakSelf presentContactsVc];
                }
            }];
            break;
        }
        default:
            [self presentContactsVc];
            break;
    }
}

- (void)fetchUploadContactsParams:(GainAllContactsBlock)gainBlock {
    self.gainBlock = gainBlock;
    
    switch ([SAContactsManager checkContactsAuthority]) {
        case MyContactsAuthorityTypeRefuse:
            gainBlock(@[]);
            [self jumpOpenAuthorization];
            break;
        case MyContactsAuthorityTypeNoRequest:
        {
            __weak typeof(self) weakSelf = self;
            [self requestAuthorization:^(BOOL result) {
                if (result) {
                    [weakSelf getContactsInfo];
                }
            }];
            break;
        }
        default:
            [self getContactsInfo];
            break;
    }
}


- (void)getContactsInfo {
    if (@available(iOS 9, *)) {

        [self contactsInfos];

    } else {
    
        [self addressBookInfos];
    }
}

- (void)addressBookInfos {
    
    ABAddressBookRef addressBook = ABAddressBookCreate();
    
    
    CFArrayRef peopleArray = ABAddressBookCopyArrayOfAllPeople(addressBook);
    
    NSMutableArray *mutArr = [NSMutableArray array];
    
    CFIndex peopleCount = CFArrayGetCount(peopleArray);
    for (CFIndex i = 0; i < peopleCount; i++) {
        ABRecordRef person = CFArrayGetValueAtIndex(peopleArray, i);
        
        
        CFStringRef firstName = ABRecordCopyValue(person, kABPersonFirstNameProperty);
        CFStringRef lastName = ABRecordCopyValue(person, kABPersonLastNameProperty);
        NSString *nameStr = [NSString stringWithFormat:@"%@%@", lastName, firstName];
        
        
        NSString *phoneStr = @"";
        ABMultiValueRef phones = ABRecordCopyValue(person, kABPersonPhoneProperty);
        CFIndex phoneCount = ABMultiValueGetCount(phones);
        for (CFIndex i = 0; i < phoneCount; i++) {
            CFStringRef phoneValue = ABMultiValueCopyValueAtIndex(phones, i);
            phoneStr = [NSString stringWithFormat:@"%@", (__bridge NSString *)phoneValue];
            CFRelease(phoneValue);
            if (phoneStr.length != 0) {
                break;
            }
        }
        NSDictionary *dict = @{@"name": nameStr,@"phone": phoneStr};
        [mutArr addObject:dict];
        CFRelease(firstName);
        CFRelease(lastName);
        CFRelease(phones);
    }
    CFRelease(addressBook);
    CFRelease(peopleArray);
    
    if (self.gainBlock) {
        self.gainBlock(mutArr);
    }
    
}

- (void)contactsInfos {
    
    CNContactStore *contactStore = [[CNContactStore alloc] init];
    
    
    NSArray *keys = @[CNContactGivenNameKey, CNContactFamilyNameKey, CNContactPhoneNumbersKey];
    CNContactFetchRequest *request = [[CNContactFetchRequest alloc] initWithKeysToFetch:keys];
    
    
    dispatch_async(dispatch_get_global_queue(0, 0), ^{
        __block NSMutableArray *mutArr = [NSMutableArray array];
        [contactStore enumerateContactsWithFetchRequest:request error:nil usingBlock:^(CNContact * _Nonnull contact, BOOL * _Nonnull stop) {
            
            NSString *nameStr = [NSString stringWithFormat:@"%@ %@", contact.familyName, contact.givenName];
            
            
            NSString *phoneStr;
            NSArray *phones = contact.phoneNumbers;
            for (CNLabeledValue *labelValue in phones) {
                CNPhoneNumber *phoneNumber = labelValue.value;
                phoneStr = phoneNumber.stringValue;
                if (phoneStr.length != 0) {
                    NSDictionary *dict = @{@"name": nameStr,@"phone": phoneStr.length == 0 ? @"" : phoneStr};
                    [mutArr addObject:dict];
                }
            }
        }];
        
    
    
        if (self.gainBlock) {
            self.gainBlock(mutArr);
        }
    });
  
}

- (void)presentContactsVc {
    if (@available(iOS 9, *)) {
        
        
        dispatch_async(dispatch_get_main_queue(), ^{
            CNContactPickerViewController *pc = [[CNContactPickerViewController alloc]
                                                 init];
            pc.delegate = self;
            pc.displayedPropertyKeys = @[CNContactPhoneNumbersKey];
            
            [[SACommonTool currentViewController] presentViewController:pc animated:YES completion:nil];
        });
        
    } else {
        
        ABPeoplePickerNavigationController *ppnc = [[ABPeoplePickerNavigationController alloc] init];
        ppnc.displayedProperties = @[@(kABPersonPhoneProperty)];
        ppnc.peoplePickerDelegate = self;
        
        [SAContactsManager checkContactsAuthority];
        
        
        [[SACommonTool currentViewController] presentViewController:ppnc animated:YES completion:nil];
    }
}

- (void)requestAuthorization:(void (^)(BOOL result))requestBlock{
    
    if (@available(iOS 9, *)) {
        
        CNContactStore *contactStore = [[CNContactStore alloc] init];
        [contactStore requestAccessForEntityType:CNEntityTypeContacts completionHandler:^(BOOL granted, NSError * _Nullable error) {
            
            if(error){
                [SATool hideWindowHUD];
            }else{
                if (requestBlock) {
                    requestBlock(granted);
                }
            }
        }];
        
    } else {
        
        ABAddressBookRef addressBook = ABAddressBookCreate();
        
        
        ABAddressBookRequestAccessWithCompletion(addressBook, ^(bool granted, CFErrorRef error) {
            
            if (requestBlock) {
                requestBlock(granted);
            }
        });
    }
}

- (void)jumpOpenAuthorization {
    
    UIAlertController *alertVc = [UIAlertController alertControllerWithTitle:NSLocalizedString(@"ixedFoot", nil)
                                                                     message:NSLocalizedString(@"waitingMacroOntact", nil) preferredStyle:UIAlertControllerStyleAlert];
    UIAlertAction *cancel = [UIAlertAction actionWithTitle:NSLocalizedString(@"serviceCtionDebug", nil) style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
    }];
    
    UIAlertAction *sure = [UIAlertAction actionWithTitle:NSLocalizedString(@"handleRotocolMasonry", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        
        if (@available(iOS 10.0, *)) {
            NSURL *url = [NSURL URLWithString:UIApplicationOpenSettingsURLString];
            [[UIApplication sharedApplication] openURL:url options:@{}
                                     completionHandler:^(BOOL success) {
                                     }];
        } else {
            
            NSString *schemesStr = [NSString stringWithFormat:@"prefs:root=Privacy&path=CONTACTS/%@", [SADeviceTool bundleId]];
            NSURL *url = [NSURL URLWithString:schemesStr];
            if ([[UIApplication sharedApplication] canOpenURL:url]) {
                [[UIApplication sharedApplication] openURL: url];
            }
        }
        
    }];
    [alertVc addAction:cancel];
    [alertVc addAction:sure];
    
        [[SACommonTool currentViewController] presentViewController:alertVc animated:YES completion:nil];
}

+ (BOOL)contactsIsEnable {
    if ([self checkContactsAuthority] == MyContactsAuthorityTypeEnable) {
        return YES;
    } else {
        return NO;
    }
}

+ (MyContactsAuthorityType)checkContactsAuthority {
    
    if (@available(iOS 9, *)) {
        
        CNAuthorizationStatus status = [CNContactStore authorizationStatusForEntityType:CNEntityTypeContacts];
        switch (status) {
            case CNAuthorizationStatusAuthorized:
                return MyContactsAuthorityTypeEnable;
                return YES;
            case CNAuthorizationStatusDenied:
                return MyContactsAuthorityTypeRefuse;;
                return NO;
            case CNAuthorizationStatusRestricted:
                return MyContactsAuthorityTypeRefuse;;
                return NO;
            case CNAuthorizationStatusNotDetermined:
                return MyContactsAuthorityTypeNoRequest;
        }
        
        return NO;
    } else {
        
        ABAuthorizationStatus status = ABAddressBookGetAuthorizationStatus();
        switch (status) {
            case kABAuthorizationStatusAuthorized:
                
                return MyContactsAuthorityTypeEnable;
            case kABAuthorizationStatusDenied:
                
                return MyContactsAuthorityTypeRefuse;
            case kABAuthorizationStatusNotDetermined:
                
                return MyContactsAuthorityTypeNoRequest;;
            case kABAuthorizationStatusRestricted:
                
                return MyContactsAuthorityTypeRefuse;;
            default:
                return MyContactsAuthorityTypeRefuse;;
        }
    }
}

#pragma mark - IOS 9.0及以上
#pragma mark CNContactPickerDelegate
 

- (void)contactPicker:(CNContactPickerViewController *)picker didSelectContactProperty:(CNContactProperty *)contactProperty  API_AVAILABLE(ios(9.0)){
    
    CNContact *contact = contactProperty.contact;
    
    NSString *nameStr = [CNContactFormatter stringFromContact:contact style:CNContactFormatterStyleFullName];
    
    CNPhoneNumber *phoneValue= contactProperty.value;
    NSString *phoneStr = @"";
    if ([phoneValue isKindOfClass:[CNPhoneNumber class]]) {
        phoneStr = phoneValue.stringValue;
    }
    NSString *phone = [phoneStr stringByReplacingOccurrencesOfString:@" " withString:@""];
    if (self.complete) {
        self.complete(nameStr, phone);
    }
}

- (void)contactPickerDidCancel:(CNContactPickerViewController *)picker  API_AVAILABLE(ios(9.0)){
    if (self.cancel) {
        self.cancel();
    }
}

#pragma mark - IOS 9.0以下
#pragma mark ABPeoplePickerNavigationControllerDelegate
 

 
- (void)peoplePickerNavigationController:(ABPeoplePickerNavigationController *)peoplePicker didSelectPerson:(ABRecordRef)person property:(ABPropertyID)property identifier:(ABMultiValueIdentifier)identifier {
    
    
    
    CFStringRef name = ABRecordCopyValue(person, kABPersonFirstNameProperty);
    NSString *naStr = (__bridge NSString *)name;
    CFStringRef surName = ABRecordCopyValue(person, kABPersonLastNameProperty);
    NSString *surStr = (__bridge NSString *)surName;
    NSString *nameStr = [NSString stringWithFormat:@"%@ %@", surStr, naStr];
    CFRelease(name);
    CFRelease(surName);
    
    ABMultiValueRef phone = ABRecordCopyValue(person, kABPersonPhoneProperty);
    long index = ABMultiValueGetIndexForIdentifier(phone,identifier);
    CFStringRef phoneRef = ABMultiValueCopyValueAtIndex(phone, index);
    NSString *phoneNO = [NSString stringWithFormat:@"%@",(__bridge NSString *)phoneRef];
    CFRelease(phoneRef);
    CFRelease(phone);
    phoneNO = [phoneNO stringByReplacingOccurrencesOfString:@" " withString:@""];
    
    if (self.complete) {
        self.complete(nameStr, phoneNO);
    }
    
    
    
    
    
    
    
    
    
    
}


- (void)peoplePickerNavigationControllerDidCancel:(ABPeoplePickerNavigationController *)peoplePicker {
    if (self.cancel) {
        self.cancel();
    }
}

#pragma mark - getters

@end
