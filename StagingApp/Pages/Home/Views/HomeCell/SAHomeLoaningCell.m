
#import "SAHomeLoaningCell.h"

@interface SAHomeLoaningCell()

@property (nonatomic, strong) UILabel *statusLab;
@property (nonatomic, strong) UILabel *tipsLab;
@property (nonatomic, strong) UILabel *descLab;

@property (nonatomic, strong) UIImageView *dotLine;
@property (nonatomic, strong) UIView *midContentV;

@end

@implementation SAHomeLoaningCell

#pragma mark - Life cycle

- (void)renderCoreBg
{
    [self addCoreSubviews];
    [self setCoreConstraints];
    [self.coreBg mas_updateConstraints:^(SAConstraintMaker *make) {
        make.height.mas_equalTo(265);
    }];

    self.statusLab.text = self.model.tips ;
    self.tipsLab.text =  self.model.bigTips;
    self.descLab.text = self.model.miniTips;
}

- (void)addCoreSubviews
{
    [self.coreBg addSubview:self.statusLab];
    [self.coreBg addSubview:self.tipsLab];
    [self.coreBg addSubview:self.descLab];
    [self.coreBg addSubview:self.dotLine];
    [self.coreBg addSubview:self.midContentV];
}

- (void)setCoreConstraints
{
    CGFloat verGap = 15;
    
    [self.statusLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.centerX.equalTo(self.coreBg);
        make.top.equalTo(self.coreBg).offset(verGap+5);
    }];
    [self.tipsLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.centerX.equalTo(self.coreBg);
        make.top.equalTo(self.statusLab.mas_bottom).offset(15);
    }];
    [self.descLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.centerX.equalTo(self.coreBg);
        make.top.equalTo(self.tipsLab.mas_bottom).offset(15);
    }];
 
    [self.dotLine mas_makeConstraints:^(SAConstraintMaker *make) {
        make.top.equalTo(self.descLab.mas_bottom).offset(32);
        make.centerX.equalTo(self.coreBg);
        make.left.equalTo(self.coreBg).offset(30);
        make.height.mas_equalTo(6);
    }];
    
    [self.midContentV mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.right.equalTo(self.coreBg);
        make.top.equalTo(self.dotLine.mas_bottom).offset(10);
        make.height.mas_equalTo(76);
    }];
}

#pragma mark - getter


- (UILabel *)statusLab{
    FF_Fetch_UILable_CenterX(_statusLab, kHomeTextColor, Font_XX6(13))
}

- (UILabel *)tipsLab{
    FF_Fetch_UILable_NumZERO_CenterX(_tipsLab, kHomeTextColor, BoldFont_XX6(20) , 0)
}

- (UILabel *)descLab{
    FF_Fetch_UILable_CenterX(_descLab, [UIColor colorWithHex:0x999999], Font_XX6(11))
}

- (UIView *)midContentV{
    if (_midContentV == nil) {
        _midContentV = [UIView new];
        
        
        NSArray *dataList = @[
            @{@"title": @"安全合规", @"icon": @"home_tip1"},
            @{@"title": @"息费透明", @"icon": @"home_tip2"},
            @{@"title": @"期限灵活", @"icon": @"home_tip3"},
        ];
        
        CGFloat singleW = (UIScreenWidth-15*2)/3;
        [dataList enumerateObjectsUsingBlock:^(NSDictionary *item, NSUInteger idx, BOOL * _Nonnull stop) {
            UIView *view = [UIView new];
            [_midContentV addSubview:view];
            
            UIImageView *icon = [[UIImageView alloc] initWithImage:[UIImage imageNamed:item[@"icon"]]];
            [view addSubview:icon];
            
            UILabel *titL = [UILabel new];
            titL.text = item[@"title"];
            titL.font = BoldFont_XX6(11);
            titL.textColor = [UIColor colorWithHex:0x333333];
            [view addSubview:titL];
            
            [view mas_makeConstraints:^(SAConstraintMaker *make) {
                make.left.equalTo(_midContentV).offset(idx * singleW);
                make.width.mas_equalTo(singleW);
                make.top.bottom.equalTo(_midContentV);
            }];

            [icon mas_makeConstraints:^(SAConstraintMaker *make) {
                make.width.height.mas_equalTo(32);
                make.centerX.centerY.equalTo(view);
            }];
            [titL mas_makeConstraints:^(SAConstraintMaker *make) {
                make.centerX.equalTo(view);
                make.top.equalTo(icon.mas_bottom).offset(5);
            }];
        }];
    }
    return _midContentV;
}

- (UIImageView *)dotLine{
    FF_Fetch_UIImageViewWithImage(_dotLine, [UIImage imageNamed:@"dot_line"])
}

@end
