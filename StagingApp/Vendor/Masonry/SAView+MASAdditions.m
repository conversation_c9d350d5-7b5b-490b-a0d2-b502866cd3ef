
#import "SAView+MASAdditions.h"
#import <objc/runtime.h>

@implementation MAS_VIEW (MASAdditions)

- (NSArray *)mas_makeConstraints:(void(^)(SAConstraintMaker *))block {
    self.translatesAutoresizingMaskIntoConstraints = NO;
    SAConstraintMaker *constraintMaker = [[SAConstraintMaker alloc] initWithView:self];
    block(constraintMaker);
    return [constraintMaker install];
}

- (NSArray *)mas_updateConstraints:(void(^)(SAConstraintMaker *))block {
    self.translatesAutoresizingMaskIntoConstraints = NO;
    SAConstraintMaker *constraintMaker = [[SAConstraintMaker alloc] initWithView:self];
    constraintMaker.updateExisting = YES;
    block(constraintMaker);
    return [constraintMaker install];
}

- (NSArray *)mas_remakeConstraints:(void(^)(SAConstraintMaker *make))block {
    self.translatesAutoresizingMaskIntoConstraints = NO;
    SAConstraintMaker *constraintMaker = [[SAConstraintMaker alloc] initWithView:self];
    constraintMaker.removeExisting = YES;
    block(constraintMaker);
    return [constraintMaker install];
}

#pragma mark - NSLayoutAttribute properties

- (SAViewAttribute *)mas_left {
    return [[SAViewAttribute alloc] initWithView:self layoutAttribute:NSLayoutAttributeLeft];
}

- (SAViewAttribute *)mas_top {
    return [[SAViewAttribute alloc] initWithView:self layoutAttribute:NSLayoutAttributeTop];
}

- (SAViewAttribute *)mas_right {
    return [[SAViewAttribute alloc] initWithView:self layoutAttribute:NSLayoutAttributeRight];
}

- (SAViewAttribute *)mas_bottom {
    return [[SAViewAttribute alloc] initWithView:self layoutAttribute:NSLayoutAttributeBottom];
}

- (SAViewAttribute *)mas_leading {
    return [[SAViewAttribute alloc] initWithView:self layoutAttribute:NSLayoutAttributeLeading];
}

- (SAViewAttribute *)mas_trailing {
    return [[SAViewAttribute alloc] initWithView:self layoutAttribute:NSLayoutAttributeTrailing];
}

- (SAViewAttribute *)mas_width {
    return [[SAViewAttribute alloc] initWithView:self layoutAttribute:NSLayoutAttributeWidth];
}

- (SAViewAttribute *)mas_height {
    return [[SAViewAttribute alloc] initWithView:self layoutAttribute:NSLayoutAttributeHeight];
}

- (SAViewAttribute *)mas_centerX {
    return [[SAViewAttribute alloc] initWithView:self layoutAttribute:NSLayoutAttributeCenterX];
}

- (SAViewAttribute *)mas_centerY {
    return [[SAViewAttribute alloc] initWithView:self layoutAttribute:NSLayoutAttributeCenterY];
}

- (SAViewAttribute *)mas_baseline {
    return [[SAViewAttribute alloc] initWithView:self layoutAttribute:NSLayoutAttributeBaseline];
}

- (SAViewAttribute *(^)(NSLayoutAttribute))mas_attribute
{
    return ^(NSLayoutAttribute attr) {
        return [[SAViewAttribute alloc] initWithView:self layoutAttribute:attr];
    };
}

#if (__IPHONE_OS_VERSION_MIN_REQUIRED >= 80000) || (__TV_OS_VERSION_MIN_REQUIRED >= 9000) || (__MAC_OS_X_VERSION_MIN_REQUIRED >= 101100)

- (SAViewAttribute *)mas_firstBaseline {
    return [[SAViewAttribute alloc] initWithView:self layoutAttribute:NSLayoutAttributeFirstBaseline];
}
- (SAViewAttribute *)mas_lastBaseline {
    return [[SAViewAttribute alloc] initWithView:self layoutAttribute:NSLayoutAttributeLastBaseline];
}

#endif

#if (__IPHONE_OS_VERSION_MIN_REQUIRED >= 80000) || (__TV_OS_VERSION_MIN_REQUIRED >= 9000)

- (SAViewAttribute *)mas_leftMargin {
    return [[SAViewAttribute alloc] initWithView:self layoutAttribute:NSLayoutAttributeLeftMargin];
}

- (SAViewAttribute *)mas_rightMargin {
    return [[SAViewAttribute alloc] initWithView:self layoutAttribute:NSLayoutAttributeRightMargin];
}

- (SAViewAttribute *)mas_topMargin {
    return [[SAViewAttribute alloc] initWithView:self layoutAttribute:NSLayoutAttributeTopMargin];
}

- (SAViewAttribute *)mas_bottomMargin {
    return [[SAViewAttribute alloc] initWithView:self layoutAttribute:NSLayoutAttributeBottomMargin];
}

- (SAViewAttribute *)mas_leadingMargin {
    return [[SAViewAttribute alloc] initWithView:self layoutAttribute:NSLayoutAttributeLeadingMargin];
}

- (SAViewAttribute *)mas_trailingMargin {
    return [[SAViewAttribute alloc] initWithView:self layoutAttribute:NSLayoutAttributeTrailingMargin];
}

- (SAViewAttribute *)mas_centerXWithinMargins {
    return [[SAViewAttribute alloc] initWithView:self layoutAttribute:NSLayoutAttributeCenterXWithinMargins];
}

- (SAViewAttribute *)mas_centerYWithinMargins {
    return [[SAViewAttribute alloc] initWithView:self layoutAttribute:NSLayoutAttributeCenterYWithinMargins];
}

#endif

#if (__IPHONE_OS_VERSION_MAX_ALLOWED >= 110000) || (__TV_OS_VERSION_MAX_ALLOWED >= 110000)

- (SAViewAttribute *)mas_safeAreaLayoutGuide {
    return [[SAViewAttribute alloc] initWithView:self item:self.safeAreaLayoutGuide layoutAttribute:NSLayoutAttributeBottom];
}
- (SAViewAttribute *)mas_safeAreaLayoutGuideTop {
    return [[SAViewAttribute alloc] initWithView:self item:self.safeAreaLayoutGuide layoutAttribute:NSLayoutAttributeTop];
}
- (SAViewAttribute *)mas_safeAreaLayoutGuideBottom {
    return [[SAViewAttribute alloc] initWithView:self item:self.safeAreaLayoutGuide layoutAttribute:NSLayoutAttributeBottom];
}
- (SAViewAttribute *)mas_safeAreaLayoutGuideLeft {
    return [[SAViewAttribute alloc] initWithView:self item:self.safeAreaLayoutGuide layoutAttribute:NSLayoutAttributeLeft];
}
- (SAViewAttribute *)mas_safeAreaLayoutGuideRight {
    return [[SAViewAttribute alloc] initWithView:self item:self.safeAreaLayoutGuide layoutAttribute:NSLayoutAttributeRight];
}

#endif

#pragma mark - associated properties

- (id)mas_key {
    return objc_getAssociatedObject(self, @selector(mas_key));
}

- (void)setMas_key:(id)key {
    objc_setAssociatedObject(self, @selector(mas_key), key, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
}

#pragma mark - heirachy

- (instancetype)mas_closestCommonSuperview:(MAS_VIEW *)view {
    MAS_VIEW *closestCommonSuperview = nil;

    MAS_VIEW *secondViewSuperview = view;
    while (!closestCommonSuperview && secondViewSuperview) {
        MAS_VIEW *firstViewSuperview = self;
        while (!closestCommonSuperview && firstViewSuperview) {
            if (secondViewSuperview == firstViewSuperview) {
                closestCommonSuperview = secondViewSuperview;
            }
            firstViewSuperview = firstViewSuperview.superview;
        }
        secondViewSuperview = secondViewSuperview.superview;
    }
    return closestCommonSuperview;
}

@end
