
#import "SABaseView.h"
#import "SAResultModel.h"

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, BRStringPickerMode) {
     
    BRStringPickerComponentSingle,
     
    BRStringPickerComponentMulti,
     
    BRStringPickerComponentLinkage
};

typedef void(^BRStringResultModelBlock)(SAResultModel * _Nullable resultModel);

typedef void(^BRStringResultModelArrayBlock)(NSArray <SAResultModel *> * _Nullable resultModelArr);

@interface SAStringPickerView : SABaseView

 
@property (nonatomic, assign) BRStringPickerMode pickerMode;

 
@property (nullable, nonatomic, copy) NSArray *dataSourceArr;
 
@property (nullable, nonatomic, copy) NSString *plistName;

 
@property (nonatomic, assign) NSInteger selectIndex;
@property (nullable, nonatomic, copy) NSString *selectValue;

 
@property (nullable, nonatomic, copy) NSArray <NSNumber *> *selectIndexs;
@property (nullable, nonatomic, copy) NSArray <NSString *> *selectValues;

 
@property (nullable, nonatomic, copy) BRStringResultModelBlock resultModelBlock;
 
@property (nullable, nonatomic, copy) BRStringResultModelArrayBlock resultModelArrayBlock;

 
@property (nullable, nonatomic, copy) BRStringResultModelBlock changeModelBlock;
 
@property (nullable, nonatomic, copy) BRStringResultModelArrayBlock changeModelArrayBlock;

 
@property (nonatomic, readonly, assign, getter=isRolling) BOOL rolling;

 
@property (nonatomic, assign) NSInteger numberOfComponents;

 
@property (nonatomic, assign) BOOL selectRowAnimated;

- (instancetype)initWithPickerMode:(BRStringPickerMode)pickerMode;

- (void)show;

- (void)dismiss;








 
+ (void)showPickerWithTitle:(nullable NSString *)title
              dataSourceArr:(nullable NSArray *)dataSourceArr
                selectIndex:(NSInteger)selectIndex
                resultBlock:(nullable BRStringResultModelBlock)resultBlock;

 
+ (void)showPickerWithTitle:(nullable NSString *)title
              dataSourceArr:(nullable NSArray *)dataSourceArr
                selectIndex:(NSInteger)selectIndex
               isAutoSelect:(BOOL)isAutoSelect
                resultBlock:(nullable BRStringResultModelBlock)resultBlock;

 
+ (void)showMultiPickerWithTitle:(nullable NSString *)title
                   dataSourceArr:(nullable NSArray *)dataSourceArr
                    selectIndexs:(nullable NSArray <NSNumber *> *)selectIndexs
                     resultBlock:(nullable BRStringResultModelArrayBlock)resultBlock;

 
+ (void)showMultiPickerWithTitle:(nullable NSString *)title
                   dataSourceArr:(nullable NSArray *)dataSourceArr
                    selectIndexs:(nullable NSArray <NSNumber *> *)selectIndexs
                    isAutoSelect:(BOOL)isAutoSelect
                     resultBlock:(nullable BRStringResultModelArrayBlock)resultBlock;

 
+ (void)showLinkagePickerWithTitle:(nullable NSString *)title
                     dataSourceArr:(nullable NSArray *)dataSourceArr
                      selectIndexs:(nullable NSArray <NSNumber *> *)selectIndexs
                      isAutoSelect:(BOOL)isAutoSelect
                       resultBlock:(nullable BRStringResultModelArrayBlock)resultBlock;


@end

NS_ASSUME_NONNULL_END
