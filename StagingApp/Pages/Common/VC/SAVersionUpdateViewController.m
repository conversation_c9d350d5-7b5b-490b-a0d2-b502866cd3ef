
#import "SAVersionUpdateViewController.h"

#import "SAGradientView.h"
#import "SAAppCodeManager.h"

@interface SAVersionUpdateViewController ()

@property (nonatomic, weak)UIViewController *fromController;

@property (nonatomic, strong)UIView *maskView;          
@property (nonatomic, strong)UIView *alertView;         
@property (nonatomic, strong)UIImageView *topImgView;
@property (nonatomic, strong)UIView *contentView;       
@property (nonatomic, strong)UILabel *titleLab;         
@property (nonatomic, strong)UILabel *contentLabel;     
@property (nonatomic, strong)SAGradientButton *updateBtn;       
@property (nonatomic, strong)UIButton *cancelBtn;       

@property (nonatomic, strong)NSDictionary *parameter;       

@property (nonatomic, strong)SAAppCodeManager *codeMgr;

@property (nonatomic, assign)BOOL forceUpdate;      

@end

static UIWindow *alertWindow;

@implementation SAVersionUpdateViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self createViews];
    
    [self setupConstraints];
}
#pragma mark - Views Network
- (void)createViews {
    
    [self.view addSubview:self.maskView];
    [self.view addSubview:self.alertView];
    
    [self.alertView addSubview:self.contentView];
    [self.alertView addSubview:self.topImgView];
    [self.alertView addSubview:self.cancelBtn];
    
    [self.contentView addSubview:self.titleLab];
    [self.contentView addSubview:self.contentLabel];
    [self.contentView addSubview:self.updateBtn];
    
    NSString *updateStr = self.parameter[@"forceUpdateDesc"];
    if ([updateStr isKindOfClass:[NSNull class]] || updateStr.length == 0) {
        updateStr = NSLocalizedString(@"asiceUery", nil);
    }
    self.contentLabel.text = updateStr;
}

- (void)setupConstraints {
    
    CGFloat widt = [UIScreen mainScreen].bounds.size.width;
    CGFloat heig = [UIScreen mainScreen].bounds.size.height;
    
    self.maskView.frame = CGRectMake(0, 0, widt, heig);
    
    self.cancelBtn.frame = CGRectMake(widt - XX_6(120), XX_6(20), XX_6(40), XX_6(40));
    
    self.topImgView.frame = CGRectMake(0, 0, widt - XX_6(80), XX_6(147));
    self.contentView.frame = CGRectMake(0, self.topImgView.height - XX_6(15), widt - XX_6(80), 0);
    
    self.titleLab.frame = CGRectMake(XX_6(30), XX_6(30), 140, XX_6(14));
    
    CGFloat contentLabW = self.contentView.width - XX_6(60);
    
    CGFloat contentLabH =  [self.contentLabel.text boundingRectWithSize:CGSizeMake(contentLabW, CGFLOAT_MAX) options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:[UIFont fontSizeOfXX_6:16]} context:nil].size.height + XX_6(5);
    if (contentLabH > XX_6(100)) {
        contentLabH = XX_6(100);
    }
    self.contentLabel.frame = CGRectMake(XX_6(30), self.titleLab.maxY + XX_6(20), contentLabW, contentLabH);
    
    self.updateBtn.frame = CGRectMake(XX_6(30), self.contentLabel.maxY + XX_6(30), contentLabW, XX_6(40));
    self.updateBtn.layer.cornerRadius = XX_6(20);
    self.updateBtn.layer.masksToBounds = YES;
    
    self.contentView.height = self.updateBtn.maxY + XX_6(30);
    self.contentView.layer.cornerRadius = XX_6(7);
    self.contentView.layer.masksToBounds = YES;
    
    CGFloat alertH = self.topImgView.height + self.contentView.height - XX_6(10);
    self.alertView.frame = CGRectMake(XX_6(40), (heig - alertH) * 0.5 - XX_6(30), widt - XX_6(80), alertH);
}

#pragma mark - Public
+ (instancetype)showVersionUpdateAlertWithAppleId:(NSDictionary *)dict withFromVc:(UIViewController *_Nullable)fromVc {
    
    SAVersionUpdateViewController *updateVc = [[SAVersionUpdateViewController alloc] init];
    updateVc.fromController = fromVc;
    if (dict == nil) {
        [updateVc requestUpdateInfo];
    } else {
        updateVc.parameter = dict;
        updateVc.forceUpdate = [dict[@"forceUpdate"] boolValue];
    }
    
    return updateVc;
}

- (void)presentFromView {
    
    if (alertWindow == nil) {
        alertWindow = [[UIWindow alloc] initWithFrame:[UIScreen mainScreen].bounds];
        alertWindow.backgroundColor = [UIColor clearColor];
        alertWindow.windowLevel = UIWindowLevelAlert - 1;
        
    }
    
    alertWindow.rootViewController = self;
    
    
    self.alertView.transform = CGAffineTransformMakeScale(1.1, 1.1);
    self.maskView.alpha = 0.0;
    [UIView animateWithDuration:0.25 animations:^{
        self.alertView.transform = CGAffineTransformMakeScale(1.0, 1.0);
        self.maskView.alpha = 1.0;
    }];
    
    [alertWindow makeKeyAndVisible];
}

 
- (void)dismissUpdateAlert {
    
    self.maskView.alpha = 0.0;
    [self.maskView removeFromSuperview];
    
    alertWindow.hidden = YES;
    self.fromController = nil;
    alertWindow = nil;
}

#pragma mark - Actions
- (void)clickUpdateButton:(UIButton *)btn {
    btn.userInteractionEnabled = NO;
    
    if (!self.forceUpdate) {
        [self dismissUpdateAlert];
    }
    
    NSURL *url = [NSURL URLWithString:self.parameter[@"forceUpdateUrl"]];
    if ([[UIApplication sharedApplication] canOpenURL:url] && [NSString judgeStringExist:self.parameter[@"forceUpdateUrl"]]) {
        [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:^(BOOL success) {
            
        }];
    }
    
    btn.userInteractionEnabled = YES;
}

- (void)requestUpdateInfo {
    __weak typeof(self) weakSelf = self;
    [self.codeMgr requestAppCodeSuccess:^(NSDictionary * _Nonnull dict) {
        weakSelf.codeMgr = nil;
        weakSelf.forceUpdate = [dict[@"forceUpdate"] boolValue];
        weakSelf.parameter = dict;
    } tapRetry:^{
        [weakSelf requestUpdateInfo];
    }];
}

#pragma mark - setters
- (void)setForceUpdate:(BOOL)forceUpdate {
    _forceUpdate = forceUpdate;
    self.cancelBtn.hidden = forceUpdate;
}

#pragma mark - getters
- (UIView *)maskView {
    if (_maskView == nil) {
        
        _maskView = [[UIView alloc] init];
        _maskView.backgroundColor = [UIColor colorWithRed:((float)((0x333333 & 0xFF0000) >> 16))/255.0 green:((float)((0x333333 & 0xFF00) >> 8))/255.0 blue:((float)(0x333333 & 0xFF))/255.0 alpha:0.5];
        _maskView.alpha = 0.0;
        _maskView.userInteractionEnabled = YES;
        [_maskView addGestureRecognizer:[[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(dismissUpdateAlert)]];
    }
    return _maskView;
}

- (UIView *)alertView {
    if (_alertView == nil) {
        _alertView = [[UIView alloc] init];
        _alertView.backgroundColor = [UIColor clearColor];
    }
    return _alertView;
}

- (UIImageView *)topImgView {
    if (_topImgView == nil) {
        _topImgView = [[UIImageView alloc] init];
        _topImgView.image = [UIImage imageNamed:@"common_update_topImg"];
        _topImgView.contentMode = UIViewContentModeScaleAspectFit;
    }
    return _topImgView;
}

- (UIButton *)cancelBtn {
    if (_cancelBtn == nil) {
        _cancelBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_cancelBtn setImage:[UIImage imageNamed:@"common_Update_Close"] forState:UIControlStateNormal];
        [_cancelBtn setImage:[UIImage imageNamed:@"common_Update_Close"] forState:UIControlStateHighlighted];
        [_cancelBtn addTarget:self action:@selector(dismissUpdateAlert) forControlEvents:UIControlEventTouchUpInside];
    }
    return _cancelBtn;
}

- (UIView *)contentView {
    if (_contentView == nil) {
        _contentView = [[UIView alloc] init];
        _contentView.backgroundColor = [UIColor whiteColor];
    }
    return _contentView;
}

- (UILabel *)titleLab {
    if (_titleLab == nil) {
        _titleLab = [[UILabel alloc] init];
        _titleLab.font = [UIFont fontSizeOfXX_6:14];
        _titleLab.textColor = [UIColor colorWithHex:0xa0a0a0];
        _titleLab.textAlignment = NSTextAlignmentLeft;
        _titleLab.text = NSLocalizedString(@"erifyAuto_e", nil);
    }
    return _titleLab;
}

- (UILabel *)contentLabel {
    if (_contentLabel == nil) {
        _contentLabel = [[UILabel alloc] init];
        _contentLabel.textColor = [UIColor colorWithHex:0x626262];
        _contentLabel.textAlignment = NSTextAlignmentLeft;
        _contentLabel.font = [UIFont fontSizeOfXX_6:16];
        _contentLabel.numberOfLines = 0;
    }
    return _contentLabel;
}

- (SAGradientButton *)updateBtn {
    if (_updateBtn == nil) {
        _updateBtn = [SAGradientButton buttonWithType:UIButtonTypeCustom];
        _updateBtn.titleLabel.font = [UIFont fontSizeOfXX_6:16];
        [_updateBtn setTitle:NSLocalizedString(@"cardsSuccessErvice", nil) forState:UIControlStateNormal];
        [_updateBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        [_updateBtn gradientBackgroundColors:@[[UIColor colorWithHex:0x458eff], [UIColor colorWithHex:0x3886ff]] locations:nil startPoint:CGPointMake(0, 0.5) endPoint:CGPointMake(1.0, 0.5)];
        [_updateBtn addTarget:self action:@selector(clickUpdateButton:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _updateBtn;
}

#pragma mark - getter
- (SAAppCodeManager *)codeMgr {
    if (_codeMgr == nil) {
        _codeMgr = [[SAAppCodeManager alloc] init];
    }
    return _codeMgr;
}

@end
