
#if __has_include(<MJRefresh/SARefreshComponent.h>)
#import <MJRefresh/SARefreshComponent.h>
#else
#import "SARefreshComponent.h"
#endif

NS_ASSUME_NONNULL_BEGIN

@interface SARefreshHeader : SARefreshComponent
 
+ (instancetype)headerWithRefreshingBlock:(MJRefreshComponentAction)refreshingBlock;
 
+ (instancetype)headerWithRefreshingTarget:(id)target refreshingAction:(SEL)action;

 
@property (copy, nonatomic) NSString *lastUpdatedTimeKey;
 
@property (strong, nonatomic, readonly, nullable) NSDate *lastUpdatedTime;

 
@property (assign, nonatomic) CGFloat ignoredScrollViewContentInsetTop;

 
@property (nonatomic) BOOL isCollectionViewAnimationBug;
@end

NS_ASSUME_NONNULL_END
