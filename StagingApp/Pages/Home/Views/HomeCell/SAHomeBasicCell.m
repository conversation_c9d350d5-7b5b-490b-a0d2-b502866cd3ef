
#import "SAHomeBasicCell.h"
#import <JhtMarquee/JhtHorizontalMarquee.h>
#import "SAMarqueeView.h"
#import "SACommonWebViewController.h"

#import "SABannerView.h"

#define kHorizonGap     14

@interface SAHomeBasicCell ()<UUMarqueeViewDelegate>

@property (nonatomic, strong) UIImageView *homeBgImg;

@property (nonatomic, strong) UIView *topView;
@property (nonatomic, strong) UILabel *appNameLab;
@property (nonatomic, strong) UILabel *nameTipsLab;
@property (nonatomic, strong) UIImageView *serviceIcon;
@property (nonatomic, strong) UIView *cycleBgV;
@property (nonatomic, strong) UILabel *noticeLab;
@property (nonatomic, strong) SAMarqueeView *cycleV;


@property (nonatomic, strong) UILabel *platformTipsLab;
@property (nonatomic, strong) UIImageView *advantageImg;

@property (nonatomic, strong) SABannerView *bannerView;



@end

@implementation SAHomeBasicCell

#pragma mark - Life cycle

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier])
    {
        self.backgroundColor = [UIColor clearColor];
        [self createUI];
        
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(jumpService)];
        self.serviceIcon.userInteractionEnabled = YES;
        [self.serviceIcon addGestureRecognizer:tap];
    }
    
    return self;
}

- (void)createUI
{
    [self.contentView addSubview:self.homeBgImg];
    [self.homeBgImg mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.right.top.bottom.equalTo(self.contentView);
    }];
    
    [self.contentView addSubview:self.topView];
    
    [self.contentView addSubview:self.coreBg];
    [self.coreBg mas_makeConstraints:^(SAConstraintMaker *make) {
        make.height.mas_equalTo(350);
        make.top.equalTo(self.topView.mas_bottom).offset(5);
        make.left.equalTo(self.contentView).offset(kHorizonGap);
        make.right.equalTo(self.contentView).offset(-kHorizonGap);
    }];
    self.coreBg.layer.masksToBounds = YES;
    self.coreBg.layer.cornerRadius = 10;
    
    [self.contentView addSubview:self.platformTipsLab];
    [self.contentView addSubview:self.advantageImg];
    
    [self.platformTipsLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.centerX.equalTo(self.contentView);
        make.top.equalTo(self.coreBg.mas_bottom).offset(10);
    }];
    [self.advantageImg mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.equalTo(self.contentView).offset(kHorizonGap);
        make.centerX.equalTo(self.contentView);
        make.top.equalTo(self.platformTipsLab.mas_bottom).offset(3);
        make.height.mas_equalTo(150);
    }];
    self.advantageImg.contentMode = UIViewContentModeScaleAspectFit;
    
    
    [self.contentView addSubview:self.bannerView];
    self.bannerView.hGap = kHorizonGap;
    [self.bannerView mas_makeConstraints:^(SAConstraintMaker *make) {
        make.top.equalTo(self.advantageImg.mas_bottom).offset(5);
        make.left.equalTo(self.advantageImg);
        make.centerX.equalTo(self.contentView);
        make.height.mas_equalTo(kBannerHeight);
    }];
}

#pragma mark - 事件


- (void)jumpService
{
    if(![NSString judgeStringExist:[SAAdvanceManager sharedInstance].customerServiceUrl]) return;
    
    SACommonWebViewController *webVC = [[SACommonWebViewController alloc] init];
    webVC.url = [SAAdvanceManager sharedInstance].customerServiceUrl;
    webVC.navTitle = @"在线客服";
    webVC.hidesBottomBarWhenPushed = YES;
    [[SACommonTool currentViewController].navigationController pushViewController:webVC animated:NO];
}

#pragma mark - setter

- (void)setModel:(SAHomeModel *)model
{
    _model = model;
    
    [self.cycleV reloadData];
    
    [self layoutIfNeeded];
    
    NSMutableParagraphStyle *style = [NSMutableParagraphStyle new];
    style.lineSpacing  = 3;
    style.alignment = NSTextAlignmentCenter;
    style.lineBreakMode = NSLineBreakByCharWrapping;
    
    self.platformTipsLab.text = @"平台优势";
    self.noticeLab.text = @"公告";
    
    self.bannerView.dataList = model.slideshowVOList;
    
    [self.cycleV reloadData];
    
}

- (void)renderCoreBg
{
    
}

#pragma mark - UUMarqueeViewDelegate

- (NSUInteger)numberOfVisibleItemsForMarqueeView:(SAMarqueeView*)marqueeView {

    return 1;
}

- (NSUInteger)numberOfDataForMarqueeView:(SAMarqueeView*)marqueeView {
    
    return self.model.homeMessages.count;
}

- (void)createItemView:(UIView*)itemView forMarqueeView:(SAMarqueeView*)marqueeView {
    
    UILabel *content = [[UILabel alloc] initWithFrame:itemView.bounds];
    content.font = [UIFont systemFontOfSize:11.0f];
    content.tag = 1001;
    content.textColor = [UIColor blackColor];
    [itemView addSubview:content];
}

- (void)updateItemView:(UIView*)itemView atIndex:(NSUInteger)index forMarqueeView:(SAMarqueeView*)marqueeView {
    
    UILabel *content = [itemView viewWithTag:1001];
    content.text = self.model.homeMessages[index];
}

- (CGFloat)itemViewWidthAtIndex:(NSUInteger)index forMarqueeView:(SAMarqueeView*)marqueeView {
    
    UILabel *content = [[UILabel alloc] init];
    content.font = [UIFont systemFontOfSize:10.0f];
    content.text = self.model.homeMessages[index];
    return content.intrinsicContentSize.width;
}

- (void)didTouchItemViewAtIndex:(NSUInteger)index forMarqueeView:(SAMarqueeView*)marqueeView {
    
    NSLog(@"Touch at index %lu", (unsigned long)index);
}

#pragma mark - getter

- (UIImageView *)homeBgImg{
    FF_Fetch_UIImageViewWithImage(_homeBgImg, [UIImage customImageNamed:@"home_main_bg"])
}

- (UIView *)topView{
    if(_topView == nil){
        _topView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, UIScreenWidth, 150)];
        
        
        [_topView addSubview:self.appNameLab];
        [_topView addSubview:self.nameTipsLab];
        [self.appNameLab mas_makeConstraints:^(SAConstraintMaker *make) {
            make.left.equalTo(_topView).offset(15);
            make.top.equalTo(_topView).offset(kNavBarHeight+5);
        }];
        [self.nameTipsLab mas_makeConstraints:^(SAConstraintMaker *make) {
            make.left.equalTo(self.appNameLab.mas_right).offset(5);
            make.bottom.equalTo(self.appNameLab).offset(-2);
        }];
        
        [_topView addSubview:self.serviceIcon];
        [self.serviceIcon mas_makeConstraints:^(SAConstraintMaker *make) {
            make.centerY.equalTo(self.appNameLab);
            make.right.equalTo(_topView).offset(-18);
            make.width.height.mas_equalTo(36);
        }];
        
        [_topView addSubview:self.cycleBgV];
        [self.cycleBgV addSubview:self.noticeLab];
        [self.cycleBgV addSubview:self.cycleV];
        [self.cycleBgV mas_makeConstraints:^(SAConstraintMaker *make) {
            make.left.equalTo(self.appNameLab);
            make.right.equalTo(self.serviceIcon);
            make.height.mas_equalTo(34);
            make.top.equalTo(self.appNameLab.mas_bottom).offset(20);
        }];
        self.cycleBgV.layer.masksToBounds = YES;
        self.cycleBgV.layer.cornerRadius= 6;
        
        [self.noticeLab mas_makeConstraints:^(SAConstraintMaker *make) {
            make.left.equalTo(self.cycleBgV).offset(10);
            make.centerY.equalTo(self.cycleBgV);
            make.width.mas_equalTo(30);
            make.height.mas_equalTo(20);
        }];
        self.noticeLab.backgroundColor = [UIColor colorWithHex:0xFF4500];
        self.noticeLab.layer.masksToBounds = YES;
        self.noticeLab.layer.cornerRadius = 3;
        
        [self.cycleV mas_makeConstraints:^(SAConstraintMaker *make) {
            make.centerY.equalTo(self.noticeLab);
            make.left.equalTo(self.noticeLab.mas_right).offset(8);
            make.right.equalTo(self.cycleBgV).offset(-12);
            make.height.mas_equalTo(30);
        }];
        

        
        
        self.appNameLab.text = [[NSBundle mainBundle] objectForInfoDictionaryKey:@"CFBundleDisplayName"];
        self.nameTipsLab.text = [NSString stringWithFormat:@"安心借,放心用"];
        
    }
    return _topView;
}

- (UIImageView *)serviceIcon{
    FF_Fetch_UIImageViewWithImage(_serviceIcon, [UIImage imageNamed:@"customer_service_home"])
}

- (UILabel *)appNameLab{
    FF_Fetch_UILable(_appNameLab, kPrimaryColor, BoldFont_XX6(21))
}

- (UILabel *)nameTipsLab{
    FF_Fetch_UILable(_nameTipsLab, kPrimaryColor, Font_XX6(11))
}


- (UIView *)cycleBgV{
    FF_Fetch_UIView(_cycleBgV, [UIColor colorWithHex:0xFBF4EF])
}

- (UILabel *)noticeLab{
    FF_Fetch_UILable_CenterX(_noticeLab, [UIColor whiteColor], BoldFont_XX6(9))
}

- (SAMarqueeView *)cycleV{
    if (!_cycleV) {
        _cycleV = [[SAMarqueeView alloc] init];
        _cycleV.delegate = self;
        _cycleV.timeIntervalPerScroll = 2.0f;
        _cycleV.timeDurationPerScroll = 1.0f;
    }
    
    return _cycleV;
}

- (UIView *)coreBg{
    FF_Fetch_UIView(_coreBg, kMinorColor)
}

- (UILabel *)platformTipsLab{
    FF_Fetch_UILable_CenterX(_platformTipsLab, [UIColor colorWithHex:0x222222], BoldFont_XX6(15))
}

- (UIImageView *)advantageImg{
    FF_Fetch_UIImageViewWithImage(_advantageImg, [UIImage imageNamed:@"home_bottom_tip"])
}


- (SABannerView *)bannerView{
    if(_bannerView == nil){
        _bannerView = [SABannerView new];
    }
    return _bannerView;
}

@end
