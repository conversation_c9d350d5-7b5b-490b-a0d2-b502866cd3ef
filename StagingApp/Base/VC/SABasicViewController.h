
#import <UIKit/UIKit.h>
#import "SADeviceTool.h"

NS_ASSUME_NONNULL_BEGIN

@interface SABasicViewController : UIViewController

@property (nonatomic, strong)UIViewController *popToVc;      
@property (nonatomic, copy)NSString *popToVcClassName;        

@property (nonatomic, strong) NSString *backImgName;
@property (nonatomic, strong) UIView *barLineView;

- (void)popSelf;
- (void)backAction:(id)sender;


#pragma mark - 指示器和文本弹框
- (void)showActivityHUD:(NSString * __nullable)text;
- (void)hideHUDView;

- (void)textStateHUD:(NSString * __nullable)text;
- (void)textStateHUD:(NSString * __nullable)text finishBlock:(void (^)(void))finishBlock;

@end

NS_ASSUME_NONNULL_END
