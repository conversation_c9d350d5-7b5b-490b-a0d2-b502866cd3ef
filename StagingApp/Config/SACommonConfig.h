 

#ifndef SACommonConfig_h
#define SACommonConfig_h


#if STAGING_ONE

#define kPrimaryColor           [UIColor colorWithHex:0xD7C691]
#define kMinorColor             [UIColor colorWithHex:0xFBF6E6]

#define kHomeTextColor          [UIColor colorWithHex:0x584A2C]

#define kButtonStartColor       [UIColor colorWithHex:0xE9C599]
#define kButtonEndColor         [UIColor colorWithHex:0xdf9644]

#elif STAGING_TWO

#define kPrimaryColor           [UIColor colorWithHex:0xD7C691]
#define kMinorColor             [UIColor colorWithHex:0xFBF6E6]

#define kHomeTextColor          [UIColor colorWithHex:0x584A2C]

#define kButtonStartColor       [UIColor colorWithHex:0xE9C599]
#define kButtonEndColor         [UIColor colorWithHex:0xdf9644]

#else

#define kPrimaryColor           [UIColor colorWithHex:0xD7C691]
#define kMinorColor             [UIColor colorWithHex:0xFBF6E6]

#define kHomeTextColor          [UIColor colorWithHex:0x584A2C]

#define kButtonStartColor       [UIColor colorWithHex:0xE9C599]
#define kButtonEndColor         [UIColor colorWithHex:0xdf9644]


#endif




#define isPad                   ([[UIDevice currentDevice] userInterfaceIdiom] == UIUserInterfaceIdiomPad)
#define kiPhone4                ([UIScreen instancesRespondToSelector:@selector(currentMode)] ? CGSizeEqualToSize(CGSizeMake(640, 960), [[UIScreen mainScreen] currentMode].size) && !isPad : NO)
#define kiPhone5                ([UIScreen instancesRespondToSelector:@selector(currentMode)] ? CGSizeEqualToSize(CGSizeMake(640, 1136), [[UIScreen mainScreen] currentMode].size) && !isPad : NO)
#define kiPhone6 ([UIScreen instancesRespondToSelector:@selector(currentMode)] ? CGSizeEqualToSize(CGSizeMake(750, 1334), [[UIScreen mainScreen] currentMode].size) && !isPad : NO)
#define kiPhone6Plus            ([UIScreen instancesRespondToSelector:@selector(currentMode)] ? CGSizeEqualToSize(CGSizeMake(1242, 2208), [[UIScreen mainScreen] currentMode].size) && !isPad : NO)
#define IS_IPHONE_X             ([UIScreen instancesRespondToSelector:@selector(currentMode)] ? CGSizeEqualToSize(CGSizeMake(1125, 2436), [[UIScreen mainScreen] currentMode].size) && !isPad : NO)
#define IS_IPHONE_Xr            ([UIScreen instancesRespondToSelector:@selector(currentMode)] ? CGSizeEqualToSize(CGSizeMake(828, 1792), [[UIScreen mainScreen] currentMode].size) && !isPad : NO)
#define IS_IPHONE_Xs_Max        ([UIScreen instancesRespondToSelector:@selector(currentMode)] ? CGSizeEqualToSize(CGSizeMake(1242, 2688), [[UIScreen mainScreen] currentMode].size)&& !isPad : NO)

#define NP_SHARE_INSTANCE            \
+ (instancetype)sharedInstance   \
{                                \
static dispatch_once_t once; \
static id instance;          \
dispatch_once(&once, ^{      \
instance = [self new];   \
});                          \
return instance;             \
}


#ifdef __OPTIMIZE__
# define NSLog(...) {}
#else
# define NSLog(...) NSLog(__VA_ARGS__)
#endif


#define IS_PhoneXAll            (IS_IPHONE_X || IS_IPHONE_Xr || IS_IPHONE_Xs_Max)


#define kStatusBarHeight [[UIApplication sharedApplication] statusBarFrame].size.height
#define kNavBarHeight 44.0
#define kTopHeight (kStatusBarHeight + kNavBarHeight)
#define kTabBarHeight ([[UIApplication sharedApplication] statusBarFrame].size.height>20?83:49)


#define UIScreenHeight          [UIScreen mainScreen].bounds.size.height
#define UIScreenWidth           [UIScreen mainScreen].bounds.size.width

#define XX_6(value)     (1.0 * (value) * UIScreenWidth / 375.0)



#define Font_XX6(value)         [UIFont fontSizeOfXX_6:(value)]
#define BoldFont_XX6(value)     [UIFont fontSemiboldSizeOfXX_6:(value)]



#define kOrangeColor            [UIColor colorWithHex:0xFF9900]
#define kRedColor               [UIColor colorWithHex:0xEE3D11]

#define DECODE_DES_KEY          @"fiVB4VJEp@mEDpd4Ph^jpNFA2thvY)XS"

#define ONLINE_API_KEY          @"ONLINE_API_KEY"

//#define COMMON_CRYPTO_RSA_PUBLIC   @"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDDBevKRvqBtQT7koZ7Wj5g3bBpCmfTV0zhi9ubTxtUIrW1E6YSIXk1htru13IUT0qWsy/k8mO/2oApiEH4K0VyNeLdEhkrLDU/bBcAIJ6hl10KskV6YPedYqy/Zj+Z7AyXhb2gsssUaxhl/skT27Dd3V4QXsmjpH2rK4qu/xk7RwIDAQAB"
//
//
//#define COMMON_CRYPTO_RSA_PRIVATE   @"MIICXQIBAAKBgQDDBevKRvqBtQT7koZ7Wj5g3bBpCmfTV0zhi9ubTxtUIrW1E6YSIXk1htru13IUT0qWsy/k8mO/2oApiEH4K0VyNeLdEhkrLDU/bBcAIJ6hl10KskV6YPedYqy/Zj+Z7AyXhb2gsssUaxhl/skT27Dd3V4QXsmjpH2rK4qu/xk7RwIDAQABAoGAJCof7HW3FIB1+RTV3WABu0LA6OmmETnaJuUhhy5nOfXpzjdjj28no/Zq+Ol43S1K/qEh24nbV4N0Sr9axGN5z/2Jtm/B59zLSXVSdZWPYxHt0HSpRn2ovbr9KaS37mZ7vlIyh1fY89lkUqFF7M/NaXQVUBXW2G4QTVkifhSBCuECQQD9XF2RxMXBOuCCUwr+iEChl53Q/3RH4EC+7of/UPR6Dc6dS1eDUOD2rgTjYvjvPY+TCvAix4YxrYQvzqddYvjRAkEAxQ38s0cyUjPkrr5l/2pZxTLDrYXo94JXcTtkFTshQVUniDnMxOIpdnnstjEq6uMMiWsoQcKpjVbaTGUR6U74lwJBALEPI7UDNtBbPRrWvhAzWDeVpYyxeanhZl4IhLJA+RlzHLmANaBnnU/HPVNLj3xiITw5oKgtl/KPuJlQalYxHIECQQDDMw/YJ/bZJz6pO0KeuSMkDR15juUwCZXFPSfvQBu5NDls4JTPk5mvGyg5cospErEgj13ZhpOexyKH+ra7ftcRAkBMOk8oLWaAk4nYUQm2c/deV3qFyoiqpS0VCKV9+vJY0+MfAA0ceAqond7RlGF4eozegMb4UPQW4M1ZvCxnl5D9"


#define COMMON_CRYPTO_RSA_PUBLIC   @"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCgzWUFJf0tW3qOZxrFbvaalTW0xcp6T0MMfxKhGNrARvfEYaJD+yr4OELP6EPltemqzN2zI7UY9AzArYdfyRUPXcLAOXwLLngogkvMpKREW/BFbD7wI6vbOpK+6HNv589EfDcKxlSrAZqWMleufCuwl3q7AAUVGqoUYHvuZHwuVwIDAQAB"

#define COMMON_CRYPTO_RSA_PRIVATE   @"MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAKDNZQUl/S1beo5nGsVu9pqVNbTFynpPQwx/EqEY2sBG98RhokP7Kvg4Qs/oQ+W16arM3bMjtRj0DMCth1/JFQ9dwsA5fAsueCiCS8ykpERb8EVsPvAjq9s6kr7oc2/nz0R8NwrGVKsBmpYyV658K7CXersABRUaqhRge+5kfC5XAgMBAAECgYBEB2ErkmzP2Zm50SjkShOORn3YIq2MnSfSi5gIC4nQOrXroRqTBBmjtbmeV7cR4aofllhcx0iAbA9RHJjfDyUXFHmvbKdnHRgoeXLF1ff578sjfh0ExFKnokRqH8NEfBBV404vMJpyisUgXCnEyH1PvVapPe9JHa4zAnvTSvEwAQJBAOH7stuwAgAbCCCJCTv9ujNAt7Di4SVMaNGBIpMVy1LdAWytBdunkc/qSXoSLcsEyFq+CMi0/5QKFJbW/15FvAECQQC2KUtkWE8XI8YVr6OCq+VUbDtN/yoSTa7AC5GMJI+Xd/pOaqidP/OHomvEt3f97zqSsdBnNbkJgLqpW3U2cUpXAkEAv1UcWmTrTKuWdfWQm/p3bG2fGWT+u1W2aausWlxZig8U5a6ZByEZk7AKBhDeNMYX3LyJM2YL/ouKYywliuwAAQJAYpV+o9PXGeLWdS4VA8cb2dCpV9DcaAN6q5yXLI0s2QCpin7WuiO+HI2eXVwdqGQsAvAQpYrBlY8Bdl501P4DCQJBALNBZvJwpmUhT7J9YejTAK4DofEyVj6qEYBSf7/2Kme1oY5pnBOHqLoRPCs2C2opFap+exOYRtXJqZcYyZhOj/I="
#define NSLocalizedString(key, comment) [NSBundle.mainBundle localizedStringForKey:(key) value:@"" table:nil]


#define PRIVACY_POLICY_YSXY             @"yszc" 
#define PRIVACY_POLICY_ZCXY             @"zcxy" 
#define PRIVACY_POLICY_SMRZSQTYS        @"smrzsqtys"    
#define PRIVACY_POLICY_RLYZGRXXSQ       @"rlyzgrxxsq"    
#define PRIVACY_POLICY_GRXXSQS          @"grxxsqs"   
#define PRIVACY_POLICY_SJCXSQSMS        @"sjcxsqsms" 
#define PRIVACY_POLICY_MGGRXXSQS        @"mggrxxsqs"    
#define PRIVACY_POLICY_JKXZJFXTS        @"jkxzjfxts"    
#define PRIVACY_POLICY_SJSYFZXCXSQTK    @"sjsyfzxcxsqtk"    
#define PRIVACY_POLICY_ZHWTKKSQS        @"zhwtkksqs"    




#define FF_Fetch_UILable(_lable, _aColor, _aFont)      \
if (!(_lable)) {                                 \
(_lable)           = [[UILabel alloc] init]; \
(_lable).textColor = (_aColor);              \
(_lable).font      = (_aFont);               \
}                                                \
return (_lable);

#define FF_Fetch_UILable_Alignment(_lable, _aColor, _aFont, _aTextAlignment)   \
if (!(_lable)) {                                 \
(_lable)           = [[UILabel alloc] init]; \
(_lable).textColor = (_aColor);              \
(_lable).font      = (_aFont);               \
(_lable).textAlignment  = (_aTextAlignment);       \
}                                                \
return (_lable);


#define FF_Fetch_UILable_CenterX(_lable, _aColor, _aFont)  \
if (!(_lable)) {                                     \
(_lable)               = [[UILabel alloc] init]; \
(_lable).textColor     = (_aColor);              \
(_lable).font          = (_aFont);               \
(_lable).textAlignment = NSTextAlignmentCenter;  \
}                                                    \
return (_lable);

#define FF_Fetch_UILable_NumZERO_CenterX(_lable, _aColor, _aFont, _num) \
if (!(_lable)) {                                                  \
(_lable)               = [[UILabel alloc] init];              \
(_lable).textColor     = (_aColor);                           \
(_lable).numberOfLines = _num;                                \
(_lable).font          = (_aFont);                            \
(_lable).textAlignment = NSTextAlignmentCenter;               \
}                                                                 \
return (_lable);

#define FF_Fetch_UILable_RightX(_lable, _aColor, _aFont)   \
if (!(_lable)) {                                     \
(_lable)               = [[UILabel alloc] init]; \
(_lable).textColor     = (_aColor);              \
(_lable).font          = (_aFont);               \
(_lable).textAlignment = NSTextAlignmentRight;   \
}                                                    \
return (_lable);

#define FF_Fetch_UIView(_view, _aColor)                    \
if (!(_view)) {                                      \
(_view)                 = [[UIView alloc] init]; \
(_view).backgroundColor = (_aColor);             \
}                                                    \
return (_view);

#define FF_Fetch_UIImageViewWithColor(_imgV, _aColor)           \
if (!(_imgV)) {                                           \
(_imgV)                 = [[UIImageView alloc] init]; \
(_imgV).backgroundColor = (_aColor);                  \
}                                                         \
return (_imgV);

#define FF_Fetch_UIImageViewWithImage(_imgV, _aImg)                              \
if (!(_imgV)) {                                                            \
(_imgV)                 = [[UIImageView alloc] initWithImage:(_aImg)]; \
(_imgV).backgroundColor = [UIColor clearColor];                        \
}                                                                          \
return (_imgV);

#define FF_Fetch_UITextField(_textField, _aColor, _aFont)               \
if (!(_textField)) {                                              \
(_textField)               = [[UITextField alloc] init];      \
(_textField).textColor     = (_aColor);                       \
_textField.clearButtonMode = UITextFieldViewModeWhileEditing; \
(_textField).font          = (_aFont);                        \
}                                                                 \
return (_textField);

#endif  
