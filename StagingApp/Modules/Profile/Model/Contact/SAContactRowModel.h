
#import <Foundation/Foundation.h>
#import "SAContactSelectModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface SAContactRowModel : NSObject

@property (nonatomic, copy)NSString *paramName;
@property (nonatomic, copy)NSString *param;
@property (nonatomic, assign)NSInteger paramType;
@property (nonatomic, copy)NSString *inputDesc;
@property (nonatomic, copy)NSString *inputValue;

@property (nonatomic, assign)BOOL requied;
@property (nonatomic, assign)BOOL readOnly;
@property (nonatomic, assign)BOOL show;
@property (nonatomic, assign)BOOL number;

@property (nonatomic, strong)NSArray <SAContactSelectModel *>*selectVo;

@property (nonatomic, assign)CGFloat titleWidt;      
@property (nonatomic, assign)CGFloat cellHeight;         

@end

NS_ASSUME_NONNULL_END
