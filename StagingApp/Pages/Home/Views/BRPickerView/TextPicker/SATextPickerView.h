
#import "SAPickerAlertView.h"
#import "SATextModel.h"

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, BRTextPickerMode) {
     
    BRTextPickerComponentSingle,
     
    BRTextPickerComponentMulti,
     
    BRTextPickerComponentCascade
};

typedef void(^BRSingleResultBlock)(SATextModel * _Nullable model, NSInteger index);

typedef void(^BRMultiResultBlock)(NSArray <SATextModel *> * _Nullable models, NSArray<NSNumber *> * _Nullable indexs);

@interface SATextPickerView : SAPickerAlertView

 
@property (nonatomic, assign) BRTextPickerMode pickerMode;

 
@property (nullable, nonatomic, copy) NSArray *dataSourceArr;
 
@property (nullable, nonatomic, copy) NSString *fileName;

 
@property (nonatomic, assign) NSInteger selectIndex;
 
@property (nullable, nonatomic, copy) NSArray <NSNumber *> *selectIndexs;

 
@property (nullable, nonatomic, copy) BRSingleResultBlock singleChangeBlock;
 
@property (nullable, nonatomic, copy) BRMultiResultBlock multiChangeBlock;

 
@property (nullable, nonatomic, copy) BRSingleResultBlock singleResultBlock;
 
@property (nullable, nonatomic, copy) BRMultiResultBlock multiResultBlock;

 
@property (nonatomic, readonly, assign, getter=isRolling) BOOL rolling;

 
@property (nonatomic, assign) NSUInteger showColumnNum;

 
@property (nonatomic, assign) BOOL selectRowAnimated;

- (instancetype)initWithPickerMode:(BRTextPickerMode)pickerMode;

- (void)show;

- (void)dismiss;








 
+ (void)showPickerWithTitle:(nullable NSString *)title
              dataSourceArr:(nullable NSArray *)dataSourceArr
                selectIndex:(NSInteger)selectIndex
                 resultBlock:(nullable BRSingleResultBlock)resultBlock;

 
+ (void)showMultiPickerWithTitle:(nullable NSString *)title
                   dataSourceArr:(nullable NSArray *)dataSourceArr
                    selectIndexs:(nullable NSArray <NSNumber *> *)selectIndexs
                     resultBlock:(nullable BRMultiResultBlock)resultBlock;

 
+ (void)showCascadePickerWithTitle:(nullable NSString *)title
                     dataSourceArr:(nullable NSArray *)dataSourceArr
                      selectIndexs:(nullable NSArray <NSNumber *> *)selectIndexs
                       resultBlock:(nullable BRMultiResultBlock)resultBlock;

@end

NS_ASSUME_NONNULL_END
