
#import <Foundation/Foundation.h>

@interface NSString (Size)

 
- (CGFloat)widthWithFont:(UIFont *)font;

 
- (CGFloat)widthWithFont:(UIFont *)font maxWidth:(CGFloat)maxWidth;

 
- (CGFloat)heightWithFixWidth:(CGFloat)width font:(UIFont *)font;

 
- (CGSize)textSizeWithFont:(UIFont *)font;

 
- (CGSize)textSizeWithFont:(UIFont *)font constrainedToSize:(CGSize)size;


 
- (CGSize)textDrawAtPoint:(CGPoint)point withFont:(UIFont *)font;

- (CGSize)textDrawInRect:(CGRect)rect withFont:(UIFont *)font;

- (CGSize)textDrawInRect:(CGRect)rect withFont:(UIFont *)font lineBreakMode:(NSLineBreakMode)lineBreakMode;

- (CGSize)textDrawInRect:(CGRect)rect withFont:(UIFont *)font lineBreakMode:(NSLineBreakMode)lineBreakMode alignment:(NSTextAlignment)textAlignment;

@end
