
#import "SAPickerAlertView.h"
#import "SADate+BRPickerView.h"

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, BRDatePickerMode) {
    
     
    BRDatePickerModeDate,
     
    BRDatePickerModeDateAndTime,
     
    BRDatePickerModeTime,
     
    BRDatePickerModeCountDownTimer,
    
    
     
    BRDatePickerModeYMDHMS,
     
    BRDatePickerModeYMDHM,
     
    BRDatePickerModeYMDH,
     
    BRDatePickerModeMDHM,
     
    BRDatePickerModeYMD,
     
    BRDatePickerModeYM,
     
    BRDatePickerModeY,
     
    BRDatePickerModeMD,
     
    BRDatePickerModeHMS,
     
    BRDatePickerModeHM,
     
    BRDatePickerModeMS,
    
     
    BRDatePickerModeYQ,
     
    BRDatePickerModeYMW,
     
    BRDatePickerModeYW
};

typedef NS_ENUM(NSInteger, BRShowUnitType) {
     
    BRShowUnitTypeAll,
     
    BRShowUnitTypeOnlyCenter,
     
    BRShowUnitTypeNone
};

typedef void (^BRDateResultBlock)(NSDate * _Nullable selectDate, NSString * _Nullable selectValue);

typedef void (^BRDateResultRangeBlock)(NSDate * _Nullable selectStartDate, NSDate * _Nullable selectEndDate, NSString * _Nullable selectValue);

@interface SADatePickerView : SAPickerAlertView

 
@property (nonatomic, assign) BRDatePickerMode pickerMode;

 
@property (nullable, nonatomic, strong) NSDate *selectDate;
@property (nullable, nonatomic, copy) NSString *selectValue;

 
@property (nullable, nonatomic, strong) NSDate *minDate;
 
@property (nullable, nonatomic, strong) NSDate *maxDate;

 
@property (nonatomic, assign) BOOL isAutoSelect;

 
@property (nullable, nonatomic, copy) BRDateResultBlock resultBlock;
 
@property (nullable, nonatomic, copy) BRDateResultRangeBlock resultRangeBlock;

 
@property (nullable, nonatomic, copy) BRDateResultBlock changeBlock;
 
@property (nullable, nonatomic, copy) BRDateResultRangeBlock changeRangeBlock;

 
@property (nonatomic, readonly, assign, getter=isRolling) BOOL rolling;

 
@property (nonatomic, assign) BRShowUnitType showUnitType;

 
@property (nonatomic, assign, getter=isShowWeek) BOOL showWeek;

 
@property (nonatomic, assign, getter=isShowToday) BOOL showToday;

 
@property (nonatomic, assign, getter=isAddToNow) BOOL addToNow;

 
@property (nullable, nonatomic, copy) NSString *firstRowContent;

 
@property (nullable, nonatomic, copy) NSString *lastRowContent;

 
@property (nonatomic, assign, getter=isDescending) BOOL descending;

 
@property (nonatomic, assign, getter=isNumberFullName) BOOL numberFullName;

 
@property (nonatomic, assign, getter=isTwelveHourMode) BOOL twelveHourMode;

 
@property (nonatomic, assign) NSInteger minuteInterval;

 
@property (nonatomic, assign) NSInteger secondInterval;

 
@property (nonatomic, assign) NSTimeInterval countDownDuration;

 
@property (nonatomic, copy) NSArray <NSString *> *monthNames;

 
@property (nonatomic, assign, getter=isShortMonthName) BOOL shortMonthName;

 
@property (nonatomic, copy) NSDictionary *customUnit;

 
@property (nonatomic, assign, getter=isShowAMAndPM) BOOL showAMAndPM;

 
@property (nullable, nonatomic, copy) NSTimeZone *timeZone;

 
@property (nullable, nonatomic, copy) NSCalendar *calendar;

 
@property (nullable, nonatomic, copy) NSArray <NSDate *> *nonSelectableDates;

 
@property (nullable, nonatomic, copy) BRDateResultBlock nonSelectableBlock;

- (instancetype)initWithPickerMode:(BRDatePickerMode)pickerMode;

- (void)show;

- (void)dismiss;








 
+ (void)showDatePickerWithMode:(BRDatePickerMode)mode
                         title:(nullable NSString *)title
                   selectValue:(nullable NSString *)selectValue
                   resultBlock:(nullable BRDateResultBlock)resultBlock;

 
+ (void)showDatePickerWithMode:(BRDatePickerMode)mode
                         title:(nullable NSString *)title
                   selectValue:(nullable NSString *)selectValue
                  isAutoSelect:(BOOL)isAutoSelect
                   resultBlock:(nullable BRDateResultBlock)resultBlock;

 
+ (void)showDatePickerWithMode:(BRDatePickerMode)mode
                         title:(nullable NSString *)title
                   selectValue:(nullable NSString *)selectValue
                       minDate:(nullable NSDate *)minDate
                       maxDate:(nullable NSDate *)maxDate
                  isAutoSelect:(BOOL)isAutoSelect
                   resultBlock:(nullable BRDateResultBlock)resultBlock;

 
+ (void)showDatePickerWithMode:(BRDatePickerMode)mode
                         title:(nullable NSString *)title
                   selectValue:(nullable NSString *)selectValue
                       minDate:(nullable NSDate *)minDate
                       maxDate:(nullable NSDate *)maxDate
                  isAutoSelect:(BOOL)isAutoSelect
                   resultBlock:(nullable BRDateResultBlock)resultBlock
              resultRangeBlock:(nullable BRDateResultRangeBlock)resultRangeBlock;

@end

NS_ASSUME_NONNULL_END
