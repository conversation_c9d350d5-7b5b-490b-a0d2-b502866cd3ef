
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface UIColor (Extension)

 
+ (instancetype)colorWithHex:(uint32_t)hex;

 
+ (instancetype)colorWithHex:(uint32_t)hex alpha:(CGFloat)alpha;


 
+ (instancetype)colorWithRed:(uint8_t)red green:(uint8_t)green blue:(uint8_t)blue;

 
+ (UIColor*)gradientFromColor:(UIColor*)c1 toColor:(UIColor*)c2 withHeight:(int)height;



#pragma mark - 配置通用颜色

 
+ (instancetype)shareMainColor;


 
+ (instancetype)shareOverdueBgColor;


 
+ (instancetype)shareLoginBeginColor;
+ (instancetype)shareLoginEndColor;

 
+ (instancetype)shareRepaymentBeginColor;
+ (instancetype)shareRepaymentEndColor;

 
+ (instancetype)shareRenewalBeginColor;
+ (instancetype)shareRenewalEndColor;


 
+ (instancetype)shareNormalVaCodeBeginColor;
+ (instancetype)shareNormalVaCodeEndColor;

 
+ (instancetype)shareNormalVoucherBeginColor;
+ (instancetype)shareNormalVoucherEndColor;


 
+ (instancetype)shareOverdueVaCodeBeginColor;
+ (instancetype)shareOverdueVaCodeEndColor;

 
+ (instancetype)shareOverdueVoucherBeginColor;
+ (instancetype)shareOverdueVoucherEndColor;


 
+ (instancetype)shareSubmitBeginColor;
+ (instancetype)shareSubmitEndColor;


@end

NS_ASSUME_NONNULL_END
