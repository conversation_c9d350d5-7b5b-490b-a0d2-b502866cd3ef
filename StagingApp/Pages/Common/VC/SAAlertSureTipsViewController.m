
#import "SAAlertSureTipsViewController.h"

#import "SAGradientView.h"

@interface SAAlertSureTipsViewController ()


@property (nonatomic, strong)UIView *maskView;          
@property (nonatomic, strong)UIView *alertView;         
@property (nonatomic, strong)UILabel *tips;
@property (nonatomic, strong)SAGradientButton *sureBtn;         

@property (nonatomic, copy)SureBlock completeBlock;

@end

static UIWindow *alertWindow;

@implementation SAAlertSureTipsViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
}
#pragma mark - Views Network
- (void)createViews {
    [self.view addSubview:self.maskView];
    [self.view addSubview:self.alertView];
    
    [self.alertView addSubview:self.tips];
    [self.alertView addSubview:self.sureBtn];
}

- (void)renderViewWithData:(NSString *)tipTitle {
    
    [self createViews];
    
    self.maskView.frame = CGRectMake(0, 0, UIScreenWidth, UIScreenHeight);
    
    CGFloat maxW = UIScreenWidth - XX_6(90);
    CGSize maxSize = CGSizeMake(maxW, MAXFLOAT);
    CGSize tipSize = [tipTitle boundingRectWithSize:maxSize options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:[UIFont fontSizeOfXX_6:14]} context:nil].size;
    
    self.tips.frame = CGRectMake(XX_6(15), XX_6(15), maxW, tipSize.height);
    self.sureBtn.frame = CGRectMake(XX_6(20), self.tips.maxY + XX_6(30), UIScreenWidth - XX_6(100), XX_6(45));
    self.sureBtn.layer.cornerRadius = XX_6(22.5);
    self.sureBtn.layer.masksToBounds = YES;
    
    CGFloat alertW = UIScreenWidth - XX_6(60);
    
    self.alertView.frame = CGRectMake(0, 0, alertW, self.sureBtn.maxY + XX_6(10));
    self.alertView.layer.cornerRadius = XX_6(10);
    self.alertView.layer.masksToBounds = YES;
    self.alertView.center = self.view.center;
    
    self.tips.text = tipTitle;
}

#pragma mark - show、present、dismiss
+ (instancetype)showSureTips:(NSString * __nullable)tips andSureAction:(SureBlock __nullable)sure {
    
    SAAlertSureTipsViewController *alertVc = [[SAAlertSureTipsViewController alloc] init];
    alertVc.completeBlock = sure;
    
    [alertVc createViews];
    
    [alertVc renderViewWithData:tips];
    
    [alertVc presentFromView];
    
    return alertVc;
}


- (void)presentFromView {
    
    if (alertWindow == nil) {
        alertWindow = [[UIWindow alloc] initWithFrame:[UIScreen mainScreen].bounds];
        alertWindow.backgroundColor = [UIColor clearColor];
        alertWindow.windowLevel = UIWindowLevelAlert - 1;
        
    }
    
    alertWindow.rootViewController = self;
    
    
    self.alertView.transform = CGAffineTransformMakeScale(1.1, 1.1);
    self.maskView.alpha = 0.0;
    [UIView animateWithDuration:0.25 animations:^{
        self.alertView.transform = CGAffineTransformMakeScale(1.0, 1.0);
        self.maskView.alpha = 1.0;
    }];
    
    [alertWindow makeKeyAndVisible];
}

 
- (void)dismissUpdateAlert {
    
    self.maskView.alpha = 0.0;
    [self.maskView removeFromSuperview];
    
    alertWindow.hidden = YES;
    
    alertWindow = nil;
}

- (void)clickSureButton:(UIButton *)btn {
    
    [self dismissUpdateAlert];
    
    if (self.completeBlock) {
        self.completeBlock();
    }
}

#pragma mark - getters
- (UIView *)maskView {
    if (_maskView == nil) {
        
        _maskView = [[UIView alloc] init];
        _maskView.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.7];
        _maskView.alpha = 0.0;
    }
    return _maskView;
}

- (UIView *)alertView {
    if (_alertView == nil) {
        _alertView = [[UIView alloc] init];
        _alertView.backgroundColor = [UIColor whiteColor];
    }
    return _alertView;
}

- (UILabel *)tips {
    if (_tips == nil) {
        _tips = [[UILabel alloc] init];
        _tips.font = [UIFont fontSizeOfXX_6:14];
        _tips.textAlignment = NSTextAlignmentCenter;
        _tips.textColor = [UIColor colorWithHex:0x4C4C4C];
        _tips.numberOfLines = 0;
    }
    return _tips;
}

- (SAGradientButton *)sureBtn {
    if (_sureBtn == nil) {
        _sureBtn = [SAGradientButton buttonWithType:UIButtonTypeCustom];
        [_sureBtn setTitle:NSLocalizedString(@"money_alert_btn_title", nil) forState:UIControlStateNormal];
        [_sureBtn setTitleColor:[UIColor colorWithHex:0xffffff] forState:UIControlStateNormal];
        _sureBtn.titleLabel.font = [UIFont fontSizeOfXX_6:16];
        _sureBtn.titleLabel.textAlignment = NSTextAlignmentCenter;
        [_sureBtn addTarget:self action:@selector(clickSureButton:) forControlEvents:UIControlEventTouchUpInside];
        [_sureBtn gradientBackgroundColors:@[[UIColor colorWithHex:0x3188F6], [UIColor colorWithHex:0x68A6FC]] locations:nil startPoint:CGPointMake(0, 0.5) endPoint:CGPointMake(1.0, 0.5)];
    }
    return _sureBtn;
}

- (void)dealloc {
}

@end
