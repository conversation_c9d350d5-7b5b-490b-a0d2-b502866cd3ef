
#import "SABaseView.h"
#import "SAAddressModel.h"

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, BRAddressPickerMode) {
     
    BRAddressPickerModeArea,
     
    BRAddressPickerModeCity,
     
    BRAddressPickerModeProvince
};

typedef void(^BRAddressResultBlock)(SAProvinceModel * _Nullable province, SACityModel * _Nullable city, SAAreaModel * _Nullable area);

@interface Pointer : SABaseView

 
@property (nonatomic, assign) BRAddressPickerMode pickerMode;

 
@property (nullable, nonatomic, copy) NSArray <NSNumber *> *selectIndexs;
 
@property (nullable, nonatomic, copy) NSArray <NSString *> *selectValues;

 
@property (nullable, nonatomic, copy) BRAddressResultBlock resultBlock;

 
@property (nullable, nonatomic, copy) BRAddressResultBlock changeBlock;

 
@property (nonatomic, readonly, assign, getter=isRolling) BOOL rolling;

 
@property (nullable, nonatomic, copy) NSArray *dataSourceArr;

- (instancetype)initWithPickerMode:(BRAddressPickerMode)pickerMode;

- (void)show;

- (void)dismiss;








 
+ (void)showAddressPickerWithSelectIndexs:(nullable NSArray <NSNumber *> *)selectIndexs
                              resultBlock:(nullable BRAddressResultBlock)resultBlock;

 
+ (void)showAddressPickerWithMode:(BRAddressPickerMode)mode
                     selectIndexs:(nullable NSArray <NSNumber *> *)selectIndexs
                     isAutoSelect:(BOOL)isAutoSelect
                      resultBlock:(nullable BRAddressResultBlock)resultBlock;

 
+ (void)showAddressPickerWithMode:(BRAddressPickerMode)mode
                       dataSource:(nullable NSArray *)dataSource
                     selectIndexs:(nullable NSArray <NSNumber *> *)selectIndexs
                     isAutoSelect:(BOOL)isAutoSelect
                      resultBlock:(nullable BRAddressResultBlock)resultBlock;


@end

NS_ASSUME_NONNULL_END
