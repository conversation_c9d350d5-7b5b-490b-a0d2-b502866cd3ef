
#import "SAMyCardsListViewController.h"
#import "SAGetMyCardsApi.h"
#import "SACardListCell.h"
#import "SACommonNoDataView.h"
#import "SAVerifyBankCardViewController.h"
#import <MJExtension.h>

@interface SAMyCardsListViewController ()<QLAPIManagerCallBackDelegate, QLAPIManagerParamSource, NPRequestMethodProtocol, UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, strong) SAGetMyCardsApi *myCardApi;

@property (nonatomic, strong) SACommonNoDataView *noDataV;

@property (nonatomic, strong) UITableView *tableV;
@property (nonatomic, strong) NSArray *dataList;

@property (nonatomic, strong) UIButton *bindBtn;

@end

@implementation SAMyCardsListViewController

#pragma mark - Life cycle

- (void)viewWillAppear:(BOOL)animated
{
    [super viewWillAppear: animated];
    
    [self showActivityHUD:nil];
    [self.myCardApi loadData];
}

- (void)viewDidLoad
{
    [super viewDidLoad];
    self.navigationItem.title = @"我的银行卡";
    
    [self createBindButton];
    [self createUI];
}

- (void)createUI
{
    [self.view addSubview:self.tableV];
    [self.view addSubview:self.bindBtn];
    
    [self.tableV mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.top.right.bottom.equalTo(self.view);
    }];
}

- (void)createBindButton
{
    UIButton *bindBtn = [[UIButton alloc] initWithFrame:CGRectMake(0, 0, 76, 24)];
    [bindBtn setTitleColor:kPrimaryColor forState:UIControlStateNormal];
    [bindBtn setTitle:@"重新绑定" forState:UIControlStateNormal];
    bindBtn.layer.cornerRadius = 4;
    bindBtn.layer.masksToBounds = YES;
    bindBtn.titleLabel.font = Font_XX6(13);
    bindBtn.layer.borderWidth = 1;
    bindBtn.layer.borderColor = kPrimaryColor.CGColor;
    bindBtn.titleLabel.textAlignment = NSTextAlignmentCenter;
    [bindBtn addTarget:self action:@selector(jumpBindAction) forControlEvents:UIControlEventTouchUpInside];
    self.navigationItem.rightBarButtonItem = [[UIBarButtonItem alloc] initWithCustomView:bindBtn];
    self.navigationItem.rightBarButtonItem.style = UIBarButtonItemStylePlain;
}

#pragma mark - Method

- (void)jumpBindAction
{
    SAVerifyBankCardViewController *vc = [SAVerifyBankCardViewController new];
    vc.hidesBottomBarWhenPushed = YES;
    [[SACommonTool currentViewController].navigationController pushViewController:vc animated:NO];
}

#pragma mark - QLAPIManagerCallBackDelegate

- (void)managerCallAPIDidSuccess:(SABaseManager *)manager
{
    [self hideHUDView];
    if (manager == self.myCardApi)
    {
        NSDictionary *dataDict = [manager fetchDataWithReformer:self.myCardApi];
        self.dataList = [SAMyBankItemModel mj_objectArrayWithKeyValuesArray: dataDict[@"bankRoList"]];
        
        if(!self.dataList || self.dataList.count == 0){
            [self.tableV addSubview: self.noDataV];
            [self.noDataV mas_makeConstraints:^(SAConstraintMaker *make) {
                make.width.height.mas_equalTo(64);
                make.centerX.centerY.equalTo(self.tableV);
                make.centerY.equalTo(self.tableV).offset(-80);
            }];
        }else{
            [self.noDataV removeFromSuperview];
        }
        [self.tableV reloadData];
    }
}

- (void)managerCallAPIDidFailed:(SABaseManager *)manager
{
    [self hideHUDView];
    
    NSString *failMsg  = [SAResponseHandle netWorkErrorMsg:[manager fetchDataWithReformer:nil] andErrorType:manager.errorType];
    [self textStateHUD:failMsg];
}

#pragma mark - QLAPIManagerParamSource

- (NSDictionary *)paramsForApi:(SABaseManager *)manager
{
    return [NSDictionary dictionary];
}

- (NSString *)methodForApi:(SABaseManager *)manager
{
    if(manager == self.myCardApi){
//        return @"pay/bindCardList/1";
        return @"d90057463da140068619ac898222b955/1";
    }
    return @"";
}

#pragma mark - UITableViewDataSource

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return self.dataList.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    static NSString *repayCID = @"NPCardListCell-Cell";
    SACardListCell *cell = [tableView dequeueReusableCellWithIdentifier:repayCID];
    if (cell == nil) {
        cell = [[SACardListCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:repayCID];
    }
    cell.backgroundColor = [UIColor whiteColor];
    
    SAMyBankItemModel *model = self.dataList[indexPath.row];
    cell.model = model;
    
    return  cell;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath
{
    return 135;
}


#pragma mark - getter

- (SAGetMyCardsApi *)myCardApi{
    if(_myCardApi == nil){
        _myCardApi = [SAGetMyCardsApi new];
        _myCardApi.delegate = self;
        _myCardApi.paramSource = self;
        _myCardApi.methodSource = self;
    }
    return _myCardApi;
}

- (UITableView *)tableV {
    if (_tableV == nil) {
        _tableV = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
        _tableV.dataSource      = self;
        _tableV.delegate        = self;
        if (@available(iOS 11.0, *)) {
            _tableV.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
        }
        _tableV.backgroundColor = [UIColor clearColor];
        _tableV.separatorStyle = UITableViewCellSeparatorStyleNone;
    }
    return _tableV;
}

- (NSArray *)dataList{
    if (_dataList == nil) {
        _dataList = [NSArray array];
    }
    return _dataList;
}

- (SACommonNoDataView *)noDataV {
    if (_noDataV == nil) {
        _noDataV = [[SACommonNoDataView alloc] initWithImg:@"noData_icon" title:@"暂无绑定的银行卡"];
    }
    return _noDataV;
}

- (UIButton *)bindBtn{
    if (_bindBtn == nil) {
        _bindBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_bindBtn addTarget:self action:@selector(jumpBindAction) forControlEvents:UIControlEventTouchUpInside];
        [_bindBtn setTitle:@"解绑银行卡" forState:UIControlStateNormal];
        [_bindBtn setTitleColor:kPrimaryColor forState:UIControlStateNormal];
    }
    
    return _bindBtn;
}

@end
