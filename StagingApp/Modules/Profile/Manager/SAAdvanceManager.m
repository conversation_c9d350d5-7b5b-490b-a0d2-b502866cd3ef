
#import "SAAdvanceManager.h"

#import "SAGetLiveConfigManager.h"
#import "SAString+Extension.h"

#define kDESKey     @"fiVB4VJEp@mEDpd4Ph^jpNFA2thvY)XS"

@interface SAAdvanceManager ()<QLAPIManagerCallBackDelegate, QLAPIManagerParamSource>

@property (nonatomic, strong) SAGetLiveConfigManager *configManager;

@end

@implementation SAAdvanceManager

+ (instancetype)sharedInstance
{
    static dispatch_once_t once;
    static id instance;
    dispatch_once(&once, ^{
        instance = [self new];
    });
    return instance;
}

- (void)startUp
{
    [self.configManager loadData];
}

- (void)setAdvanceInfo: (NSDictionary *)dict{
    
    if (![NSString judgeStringExist:dict[@"accessKey"]]) return;
    if (![NSString judgeStringExist:dict[@"secretKey"]]) return;
    if (![NSString judgeStringExist:dict[@"accessToken"]]) return;
        
    self.accessKey = [NSString decryptUseDES:dict[@"accessKey"] key:kDESKey];
    self.secretKey = [NSString decryptUseDES:dict[@"secretKey"] key:kDESKey];
    self.accessToken = [NSString decryptUseDES:dict[@"accessToken"] key:kDESKey];
    
    NSLog(@"---access--%@---\n----secret---%@", self.accessKey, self.secretKey);
    
    if ([NSString judgeStringExist:dict[@"livenessApi"]]) {
        self.liveApi = dict[@"livenessApi"];
    }
    if ([NSString judgeStringExist:dict[@"ocrApi"]]) {
        self.ocrApi = dict[@"ocrApi"];
    }
    if ([NSString judgeStringExist:dict[@"faceApi"]]) {
        self.faceApi = dict[@"faceApi"];
    }
    
}

#pragma mark - QLAPIManagerCallBackDelegate, QLAPIManagerParamSource

- (void)managerCallAPIDidSuccess:(SABaseManager *)manager{
    if (manager == self.configManager) {
        NSDictionary *dataDict = [manager fetchDataWithReformer:self.configManager];
        [self setAdvanceInfo:dataDict[@"advanceInfo"]];
    }
}

- (void)managerCallAPIDidFailed:(SABaseManager *)manager{
    
}

- (NSDictionary *)paramsForApi:(SABaseManager *)manager {
    return [NSDictionary dictionary];
}


#pragma mark - getter

- (SAGetLiveConfigManager *)configManager{
    if (_configManager == nil) {
        _configManager = [[SAGetLiveConfigManager  alloc] init];
        _configManager.delegate = self;
        _configManager.paramSource = self;
    }
    return _configManager;
}

@end

