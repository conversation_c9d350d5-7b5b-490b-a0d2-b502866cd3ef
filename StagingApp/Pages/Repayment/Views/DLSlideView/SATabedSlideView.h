
#import <UIKit/UIKit.h>
#import "DLSlideTabbarProtocol.h"

@interface SATabedbarItem : NSObject
@property (nonatomic, strong) NSString *title;
@property(nonatomic, strong) UIImage *image;
@property(nonatomic, strong) UIImage *selectedImage;

+ (SATabedbarItem *)itemWithTitle:(NSString *)title image:(UIImage *)image selectedImage:(UIImage *)selectedImage;
@end

@class SATabedSlideView;

@protocol DLTabedSlideViewDelegate <NSObject>
- (NSInteger)numberOfTabsInDLTabedSlideView:(SATabedSlideView *)sender;
- (UIViewController *)SATabedSlideView:(SATabedSlideView *)sender controllerAt:(NSInteger)index;
@optional
- (void)SATabedSlideView:(SATabedSlideView *)sender didSelectedAt:(NSInteger)index;
@end

@interface SATabedSlideView : UIView<DLSlideTabbarDelegate>
@property(nonatomic, weak) UIViewController *baseViewController;
@property(nonatomic, assign) NSInteger selectedIndex;


@property (nonatomic, strong) UIColor *tabItemNormalColor;
@property (nonatomic, strong) UIColor *tabItemSelectedColor;
@property(nonatomic, strong) UIImage *tabbarBackgroundImage;
@property(nonatomic, strong) UIColor *tabbarTrackColor;
@property(nonatomic, strong) NSArray *tabbarItems;
@property(nonatomic, assign) float tabbarHeight;
@property(nonatomic, assign) float tabbarBottomSpacing;

@property(nonatomic, assign) NSInteger cacheCount;

- (void)buildTabbar;



@property(nonatomic, weak)IBOutlet id<DLTabedSlideViewDelegate>delegate;

@end
