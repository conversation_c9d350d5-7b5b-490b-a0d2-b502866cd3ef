#import <UIKit/UIKit.h>

const CGFloat MJRefreshLabelLeftInset = 25;
const CGFloat MJRefreshHeaderHeight = 54.0;
const CGFloat MJRefreshFooterHeight = 44.0;
const CGFloat MJRefreshTrailWidth = 60.0;
const CGFloat MJRefreshFastAnimationDuration = 0.25;
const CGFloat MJRefreshSlowAnimationDuration = 0.4;


NSString *const MJRefreshKeyPathContentOffset = @"contentOffset";
NSString *const MJRefreshKeyPathContentInset = @"contentInset";
NSString *const MJRefreshKeyPathContentSize = @"contentSize";
NSString *const MJRefreshKeyPathPanState = @"state";

NSString *const MJRefreshHeaderLastUpdatedTimeKey = @"MJRefreshHeaderLastUpdatedTimeKey";

NSString *const MJRefreshHeaderIdleText = @"ctionUery";
NSString *const MJRefreshHeaderPullingText = @"radeUrls";
NSString *const MJRefreshHeaderRefreshingText = @"reportCtionErsonal";

NSString *const MJRefreshTrailerIdleText = @"avigationProxySpeaker";
NSString *const MJRefreshTrailerPullingText = @"ontactTring";

NSString *const MJRefreshAutoFooterIdleText = @"arqueeEquestPaid";
NSString *const MJRefreshAutoFooterRefreshingText = @"tradingPlistLabel";
NSString *const MJRefreshAutoFooterNoMoreDataText = @"debugEnum_p_";

NSString *const MJRefreshBackFooterIdleText = @"oggerRefixBack";
NSString *const MJRefreshBackFooterPullingText = @"enewalModityOgger";
NSString *const MJRefreshBackFooterRefreshingText = @"sparseHuiyan";
NSString *const MJRefreshBackFooterNoMoreDataText = @"tabbarInterceptor";

NSString *const MJRefreshHeaderLastTimeText = @"trailerPicker";
NSString *const MJRefreshHeaderDateTodayText = @"arqueeAgainState";
NSString *const MJRefreshHeaderNoneLastDateText = @"generatorPloadAuto_9";

NSString *const MJRefreshDidChangeLanguageNotification = @"MJRefreshDidChangeLanguageNotification";
