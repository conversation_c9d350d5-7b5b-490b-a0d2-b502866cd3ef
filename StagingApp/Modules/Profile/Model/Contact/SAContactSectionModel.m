
#import "SAContactSectionModel.h"
#import <MJExtension.h>

@implementation SAContactSectionModel

+ (void)load {
    
    [SAContactSectionModel mj_setupObjectClassInArray:^NSDictionary *{
        return @{
                 @"inputParams" : @"SAContactRowModel"
                 };
    }];
}

+ (instancetype)objectWithDict:(NSDictionary * _Nullable)dict {
    if (dict == nil || ![dict isKindOfClass:[NSDictionary class]]) {
        return [[self alloc] init];
    }
    return [self mj_objectWithKeyValues:dict];
}


@end
