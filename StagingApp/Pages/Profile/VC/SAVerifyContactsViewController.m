
#import "SAVerifyContactsViewController.h"
 

#import "SAGetContactInfoApiManager.h"
#import "SASaveContactInfoApiManager.h"

#import "SAContactSectionModel.h"

#import "SAContactsManager.h"
#import "SAContactItemCell.h"
#import "SAPersonalVerifyModel.h"
#import "SAArray+QLNetworkingMethods.h"
#import "SAUploadInfoManager.h"
#import "SAGetProtocolApiManager.h"
#import "SAFaceBackAlert.h"

@interface SAVerifyContactsViewController ()<QLAPIManagerCallBackDelegate, QLAPIManagerParamSource, UITableViewDelegate, UITableViewDataSource, UITextViewDelegate, NPRequestMethodProtocol, NPContactItemCellDelegate, UIGestureRecognizerDelegate>

@property (nonatomic, strong) UIView *headerView;
@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) UIView *footerView;

@property (nonatomic, strong) SAGetContactInfoApiManager *contactInfoApiMgr;
@property (nonatomic, strong) SASaveContactInfoApiManager *saveApiMgr;

@property (nonatomic, strong) NSMutableArray <SAPersonalVerifyModel *>*dataArray;

@property (nonatomic, strong) SAContactsManager *contactsMgr;
@property (nonatomic, strong) NSMutableArray *submitData;

@property (nonatomic, strong) NSArray *contactsArray;
@property (nonatomic, strong) NSString *contactsUrl;
@property (nonatomic, strong) SAUploadInfoManager *uploadConManager; 
@property (nonatomic, assign) BOOL uploadSucc;


@property (nonatomic, strong) NSMutableArray *phoneArr;

@property (nonatomic, assign) BOOL finished;

@property (nonatomic, assign) BOOL isAgree;
@property (nonatomic, strong) UIView *bottomView;
@property (nonatomic, strong) UIImageView *agreeImg;
@property (nonatomic, strong) SAGetProtocolApiManager *protocolManager;
@property (nonatomic, strong) NSString *protocolKey;

@property (nonatomic, strong) UIView *topTipsV;

@end

@implementation SAVerifyContactsViewController

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
}

- (void)viewDidAppear:(BOOL)animated
{
    [super viewWillAppear:animated];
    
}

- (void)viewWillDisappear:(BOOL)animated
{
    [super viewWillDisappear:animated];
    
//    self.navigationController.interactivePopGestureRecognizer.enabled = YES;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.navigationItem.title = @"联系人信息";
    self.isAgree = YES;
    self.view.backgroundColor = [UIColor colorWithHex:0xf6f6f6];

    self.navigationController.interactivePopGestureRecognizer.delegate = self;

    [self addCustomViews];
    [self createFrames];
    
    [self showActivityHUD:nil];
    [self.contactInfoApiMgr loadData];
}

- (void)addCustomViews 
{
    [self.view addSubview:self.topTipsV];
    [self.topTipsV mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.right.top.equalTo(self.view);
        make.height.mas_equalTo(54);
    }];

    [self.view addSubview:self.headerView];
    [self.view addSubview:self.tableView];
    [self.view addSubview:self.footerView];
}

- (void)createFrames
{
        [self.tableView mas_makeConstraints:^(SAConstraintMaker *make) {
    
    
            make.bottom.mas_equalTo(self.footerView.mas_top);
            make.left.right.equalTo(self.view);
            make.top.equalTo(self.topTipsV.mas_bottom).offset(10);
        }];
}

#pragma mark - UIGestureRecognizerDelegate

- (BOOL)gestureRecognizerShouldBegin:(UIGestureRecognizer *)gestureRecognizer
{
    if(gestureRecognizer == self.navigationController.interactivePopGestureRecognizer){
        
        [SAFaceBackAlert showFaceAlertSureAction:^{
            [self popSelf];
        }];
        return NO;
    }
    return YES;
}

#pragma mark - TextView

-(BOOL)textView:(UITextView *)textView shouldInteractWithURL:(nonnull NSURL *)URL inRange:(NSRange)characterRange interaction:(UITextItemInteraction)interaction{

    if ([URL.scheme isEqualToString:@"yhxx"])
    {
        NSLog(@"点击了信息授权");
        self.protocolKey = PRIVACY_POLICY_GRXXSQS;
        [self showActivityHUD:nil];
        [self.protocolManager loadData];

    }
    else if ([URL.scheme isEqualToString:@"sjcx"])
    {
        NSLog(@"点击了数据查询");
        self.protocolKey = PRIVACY_POLICY_SJCXSQSMS;
        [self showActivityHUD:nil];
        [self.protocolManager loadData];
    }
    else if ([URL.scheme isEqualToString:@"jkxz"])
    {
        NSLog(@"点击了借款须知");
        self.protocolKey = PRIVACY_POLICY_JKXZJFXTS;
        [self showActivityHUD:nil];
        [self.protocolManager loadData];
    }
    else if ([URL.scheme isEqualToString:@"mgxx"])
    {
        NSLog(@"点击了敏感信息");
        self.protocolKey = PRIVACY_POLICY_MGGRXXSQS;
        [self showActivityHUD:nil];
        [self.protocolManager loadData];
    }
    else if ([URL.scheme isEqualToString:@"zxcx"])
    {
        NSLog(@"点击了征信查询");
        self.protocolKey = PRIVACY_POLICY_SJSYFZXCXSQTK;
        [self showActivityHUD:nil];
        [self.protocolManager loadData];
    }

    return YES;
}

- (void)changeAgree
{
    self.isAgree = !self.isAgree;
    if(!self.isAgree){
        self.agreeImg.image = [UIImage imageNamed:@"verify_check_nor"];
    } else {
        self.agreeImg.image = [UIImage customImageNamed:@"verify_check_sel"];
    }
}

#pragma mark - 点击左上角返回

- (void)backAction:(id)sender
{
    if(self.finished){
        [self popSelf];
        return;
    }
    [SAFaceBackAlert showFaceAlertSureAction:^{
        [self popSelf];
    }];
}

#pragma mark - 上传通讯录

- (void)uploadContactsBook
{
    __weak typeof(self) weakSelf = self;
    [self.contactsMgr fetchUploadContactsParams:^(NSArray *arr) {
        if(arr.count == 0){
            return;
        }
        weakSelf.contactsArray = arr;
        [weakSelf uploadContactOSS];
    }];
}

- (void)uploadContactOSS
{
    NSString *jsonStr = [self.contactsArray QL_jsonString];
    NSData *conData = [jsonStr dataUsingEncoding:NSUTF8StringEncoding];
    
    [[SANLUploadManager sharedInstance] uploadObjectAsync:conData
                                               fileType:NPFileType_Txt
                                                success:^(NSString *path) {
        if (path) {
            NSString  *dataUrl = path;
            self.contactsUrl = dataUrl;
            [self.uploadConManager loadData];
        }
    }
    failed:^(NSError * _Nonnull error) {
        [SATool hideWindowHUD];
    }];
}

#pragma mark - 协议
#pragma mark UITableViewDataSource
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return self.dataArray.count;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section 
{
    return 1;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath 
{
    static NSString * identify = @"ContactCellId";
    SAContactItemCell *cell = [tableView dequeueReusableCellWithIdentifier:identify];
    if (cell == nil) {
        cell = [[SAContactItemCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:identify];
    }
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    cell.backgroundColor = [UIColor clearColor];
    
    SAPersonalVerifyModel *model = self.dataArray[indexPath.section];
    cell.model = model;
    cell.index = indexPath.section;
    cell.delegate = self;
    
    return cell;
}

#pragma mark NPContactItemCellDelegate

- (void)didSelectAddressBook:(SAContactItemCell *)cell idx:(NSInteger)index
{
    __weak typeof(self) weakSelf = self;
    [self.contactsMgr startSelectContacts:^(NSString * _Nonnull nameStr, NSString * _Nonnull phoneStr) {
                                
        weakSelf.dataArray[index].inputParams[1].inputValue = nameStr;
        weakSelf.dataArray[index].inputParams[2].inputValue = phoneStr;
        [weakSelf.tableView reloadData];
        
        if(!weakSelf.uploadSucc){
            [weakSelf uploadContactsBook];
        }
        
    } withCancel:^{
        
    }];
}

#pragma mark - UITableViewDelegate

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath 
{
    if(self.dataArray.count == 0) return CGFLOAT_MIN;
    return XX_6(190);
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section{
    return XX_6(44);
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section{
    if(self.dataArray.count == 0) return nil;
    UIView *view = [UIView new];
    view.backgroundColor = [UIColor clearColor];
    
    
    UILabel *lab = [UILabel new];
    lab.textColor = [UIColor blackColor];
    lab.font = Font_XX6(13);
    [view addSubview:lab];
    [lab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.centerY.equalTo(view);
        make.left.equalTo(view).offset(12);
    }];
    lab.text = self.dataArray[section].name;
    return  view;
}

#pragma mark QLAPIManagerCallBackDelegate

- (void)managerCallAPIDidSuccess:(SABaseManager *)manager {
    [self hideHUDView];
    
    if (manager == self.contactInfoApiMgr) {
        NSDictionary *dataDict = [manager fetchDataWithReformer:self.contactInfoApiMgr];
        self.finished = [dataDict[@"finished"] boolValue];

        self.dataArray = (NSMutableArray *)[SAPersonalVerifyModel arrayTransfromToModels:dataDict[@"userInputInfoVOs"]];
        [self.tableView reloadData];
    } else if (manager == self.saveApiMgr) {

        [[SAVerifyListManager sharedInstance] handleVerifyJump];
    }
    
    if (manager == self.uploadConManager)
    {
        self.uploadConManager = nil;
        self.uploadSucc = YES;
    }
    
    if(manager == self.protocolManager)
       {
           NSDictionary *dataDict = [manager fetchDataWithReformer:self.protocolManager];
           SAProtocolViewController *vc = [SAProtocolViewController new];
           vc.htmlStr = dataDict[@"text"];
           vc.navTitle = dataDict[@"title"];
           vc.companyName = dataDict[@"companyName"];
           vc.appName = dataDict[@"appName"];
           [self presentViewController:[[UINavigationController alloc] initWithRootViewController:vc] animated:YES completion:nil];
       }
}

- (void)managerCallAPIDidFailed:(SABaseManager *)manager {
    [self hideHUDView];
    if (manager == self.contactInfoApiMgr) {
        NSString *failMsg  = [SAResponseHandle netWorkErrorMsg:[manager fetchDataWithReformer:nil] andErrorType:manager.errorType];
        [self textStateHUD:failMsg];
    } else if (manager == self.saveApiMgr) {
        NSString *failMsg  = [SAResponseHandle netWorkErrorMsg:[manager fetchDataWithReformer:nil] andErrorType:manager.errorType];
        [self textStateHUD:failMsg];
    }
    
    
    if (manager == self.uploadConManager)
    {
        [self.uploadConManager loadData];
    }
}

#pragma mark QLAPIManagerParamSource
- (NSDictionary *)paramsForApi:(SABaseManager *)manager {
    if (manager == self.saveApiMgr) {
        return @{
                 @"contactVos":self.submitData
                 };
    }
    if (manager == self.uploadConManager){
        return @{
            @"dataUrl": self.contactsUrl,
            @"sourceType": @3,
            @"type": @3,
            @"dataType": @2 
        };
    }
    return [NSDictionary dictionary];
}

- (NSString *)methodForApi:(SABaseManager *)manager
{
    if (manager == self.protocolManager) {
        return [NSString stringWithFormat:@"1a99fb9ad8534512af0ede2979dfd68f/%@", self.protocolKey];
    }
    return @"";
}

#pragma mark - Actions

- (void)submitAction
{
    if ([self checkParams]) {
        [self showActivityHUD:nil];
        [self.saveApiMgr loadData];
    }
}

- (BOOL)checkParams {
    [self.submitData removeAllObjects];
    
    NSInteger count =  self.dataArray.count;
    for (int i = 0; i < count; i++) {
        SAContactSectionModel *sectionM = self.dataArray[i];
        
        NSMutableDictionary *dictM = [NSMutableDictionary dictionary];
        for (int j = 0 ; j < sectionM.inputParams.count ; j++) {
            SAContactRowModel *rowM = sectionM.inputParams[j];
            if (rowM.inputValue.length == 0 && rowM.requied == YES) {
                [self textStateHUD:[NSString stringWithFormat:@"%@ %@", rowM.inputDesc, rowM.paramName]];
                return NO;
            }
            if (j == 0) {
                dictM[@"relation"] = rowM.inputValue;;
            } else if (j == 1) {
                dictM[@"friendName"] = rowM.inputValue;
            } else if (j == 2) {
                dictM[@"friendMobile"] = rowM.inputValue;
            }
        }
        
        dictM[@"type"] = [NSNumber numberWithInt:i];
        [self.submitData addObject:dictM];
    }
    return YES;
}

#pragma mark - getters

- (UITableView *)tableView {
    if (_tableView == nil) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
        _tableView.contentSize = CGSizeMake(UIScreenWidth, UIScreenHeight-XX_6(95));
        _tableView.backgroundColor = [UIColor clearColor];
        _tableView.dataSource      = self;
        _tableView.delegate        = self;
        if (@available(iOS 11.0, *)) {
            _tableView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
        } else {
            self.automaticallyAdjustsScrollViewInsets = NO;
        }
        _tableView.showsVerticalScrollIndicator = NO;
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;

        _tableView.estimatedRowHeight = 0;
        _tableView.estimatedSectionFooterHeight = 0;
        _tableView.estimatedSectionHeaderHeight = 0;
        
        _tableView.layer.cornerRadius = XX_6(15);
        _tableView.layer.masksToBounds = YES;
        
        
    }
    return _tableView;
}

- (UIView *)headerView{
    FF_Fetch_UIView(_headerView, [UIColor colorWithHex:0xfcaca3]);
}

- (UIView *)footerView {
    if (_footerView == nil) {
        _footerView = [[UIView alloc] init];
        _footerView.frame = CGRectMake(0, UIScreenHeight-XX_6(90)-kTopHeight, UIScreenWidth, XX_6(90));
        
        SAGradientButton *nextBtn = [SAGradientButton buttonWithType:UIButtonTypeCustom];
        [nextBtn gradientBackgroundColors:@[kButtonStartColor, kButtonEndColor] locations:nil startPoint:CGPointMake(0, 0.5) endPoint:CGPointMake(1, 0.5)];
        [nextBtn setTitle:@"已完成，下一步" forState:UIControlStateNormal];
        [nextBtn setTitleColor:kHomeTextColor forState:UIControlStateNormal];
        nextBtn.layer.cornerRadius  = 7;
        nextBtn.layer.masksToBounds = YES;
        [nextBtn addTarget:self action:@selector(submitAction) forControlEvents:UIControlEventTouchUpInside];
        [_footerView addSubview:nextBtn];
        [nextBtn mas_makeConstraints:^(SAConstraintMaker *make) {
            make.centerX.centerY.equalTo(_footerView);
            make.left.equalTo(_footerView).offset(25);
            make.height.mas_equalTo(44);
        }];
        
    }
    return _footerView;
}

- (SAGetContactInfoApiManager *)contactInfoApiMgr {
    if (_contactInfoApiMgr == nil) {
        _contactInfoApiMgr = [[SAGetContactInfoApiManager alloc] init];
        _contactInfoApiMgr.delegate = self;
        _contactInfoApiMgr.paramSource = self;
    }
    return _contactInfoApiMgr;
}

- (SASaveContactInfoApiManager *)saveApiMgr {
    if (_saveApiMgr == nil) {
        _saveApiMgr = [[SASaveContactInfoApiManager alloc] init];
        _saveApiMgr.delegate = self;
        _saveApiMgr.paramSource = self;
    }
    return _saveApiMgr;
}

- (NSMutableArray *)dataArray {
    if (_dataArray == nil) {
        _dataArray = [NSMutableArray array];
    }
    return _dataArray;
}

- (SAContactsManager *)contactsMgr {
    if (_contactsMgr == nil) {
        _contactsMgr = [[SAContactsManager alloc] init];
    }
    return _contactsMgr;
}

- (NSMutableArray *)submitData {
    if (_submitData == nil) {
        _submitData = [NSMutableArray array];
    }
    return _submitData;
}

- (NSMutableArray *)phoneArr{
    if(_phoneArr == nil){
        _phoneArr = [NSMutableArray array];
    }
    return _phoneArr;
}

- (SAUploadInfoManager *)uploadConManager {
    if (_uploadConManager == nil) {
        _uploadConManager = [[SAUploadInfoManager alloc] init];
        _uploadConManager.delegate = self;
        _uploadConManager.paramSource = self;
    }
    return _uploadConManager;
}

- (SAGetProtocolApiManager *)protocolManager {
    if (_protocolManager == nil) {
        _protocolManager = [[SAGetProtocolApiManager alloc] init];
        _protocolManager.delegate = self;
        _protocolManager.paramSource = self;
        _protocolManager.methodSource = self;
    }
    return _protocolManager;
}

- (UIView *)topTipsV{
    if (_topTipsV == nil) {
        _topTipsV = [UIView new];
        _topTipsV.backgroundColor = [UIColor colorWithHex:0xFFF6F1];
        
        UIImageView *icon = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"warning"]];
        [_topTipsV addSubview:icon];
        
        UILabel *lab = [UILabel new];
        lab.textColor = [UIColor colorWithHex:0xEA9947];
        lab.text = @"仅用于身份验证、额度评估、信贷相关业务审核使用，您的信息将严格保密";
        lab.font = Font_XX6(11);
        lab.numberOfLines = 0;
        [_topTipsV addSubview:lab];
        
        [icon mas_makeConstraints:^(SAConstraintMaker *make) {
            make.top.left.equalTo(_topTipsV).offset(12);
            make.width.height.mas_equalTo(16);
        }];
        [lab mas_makeConstraints:^(SAConstraintMaker *make) {
            make.left.equalTo(icon.mas_right).offset(4);
            make.top.equalTo(_topTipsV).offset(12);
            make.right.equalTo(_topTipsV).offset(-12);
        }];
    }
    return _topTipsV;
}

@end
