
#import "SAWifiManager.h"
#import "SAString+Extension.h"
#import <SystemConfiguration/CaptiveNetwork.h>

@implementation SAWifiManager


- (id)fetchSSIDInfo
{
    NSArray *ifs = (__bridge_transfer id)CNCopySupportedInterfaces();
    id info = nil;
    for (NSString *ifnam in ifs) {
        info = (__bridge_transfer id)CNCopyCurrentNetworkInfo((__bridge CFStringRef)ifnam);
        if (info && [info count]) {
            break;
        }
    }
    return info;
}

- (NSString *)fetchSSIDName
{
    NSString *ssidName = nil;
    NSDictionary *info = [self fetchSSIDInfo];
    if (info) {
        if (info[@"SSID"]) {
            ssidName = info[@"SSID"];
        }
    }
    return ssidName;
}

- (NSDictionary *)fetchUploadWifiParameter {
    
    id ssidInfo = [self fetchSSIDInfo];
    if ([ssidInfo isKindOfClass:[NSDictionary class]] && ssidInfo != nil) {
        
        NSString *ssid = [ssidInfo objectForKey:@"SSID"];
        if (![ssid isNotEmptyStr]) {
            ssid = @"";
        }
        
        NSString *bssid = [ssidInfo objectForKey:@"BSSID"];
        if (![bssid isNotEmptyStr]) {
            bssid = @"";
        }
        return @{@"currentWifi":@{@"bssid":bssid,@"ssid":ssid},
                 @"nearbyWifis":@[]};
        
    } else {
        return @{@"currentWifi":@{},
                 @"nearbyWifis":@[]
                 };
    }
}

@end
