
#import "SABundle+BRPickerView.h"
#import "SAPickerAlertView.h"

BRSYNTH_DUMMY_CLASS(NSBundle_BRPickerView)

@implementation NSBundle (BRPickerView)

#pragma mark - 获取 BRPickerView.bundle
+ (instancetype)br_pickerBundle {
    static NSBundle *pickerBundle = nil;
    if (!pickerBundle) {
        
        NSBundle *bundle = [NSBundle bundleForClass:[SAPickerAlertView class]];
        NSString *bundlePath = [bundle pathForResource:@"Panding" ofType:@"bundle"];
        pickerBundle = [NSBundle bundleWithPath:bundlePath];
    }
    return pickerBundle;
}

#pragma mark - 获取国际化后的文本
+ (NSString *)br_localizedStringForKey:(NSString *)key language:(NSString *)language {
    return [self br_localizedStringForKey:key value:nil language:language];
}

+ (NSString *)br_localizedStringForKey:(NSString *)key value:(NSString *)value language:(NSString *)language {
    static NSBundle *bundle = nil;
    if (!bundle) {
        
        if (!language) {
            
            language = [NSLocale preferredLanguages].firstObject;
        }
        
        if ([language hasPrefix:@"en"]) {
            language = @"en";
        } else if ([language hasPrefix:@"zh"]) {
            if ([language rangeOfString:@"Hans"].location != NSNotFound) {
                language = @"zh-Hans"; 
            } else { 
                language = @"zh-Hant"; 
            }
        } else {
            language = @"en";
        }
        
        
        bundle = [NSBundle bundleWithPath:[[self br_pickerBundle] pathForResource:language ofType:@"lproj"]];
    }
    value = [bundle localizedStringForKey:key value:value table:nil];
    
    return [[NSBundle mainBundle] localizedStringForKey:key value:value table:nil];
}

@end
