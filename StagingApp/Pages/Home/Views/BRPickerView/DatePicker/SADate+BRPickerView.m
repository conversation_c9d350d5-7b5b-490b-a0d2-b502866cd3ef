
#import "SADate+BRPickerView.h"
#import "BRPickerViewMacro.h"

BRSYNTH_DUMMY_CLASS(NSDate_BRPickerView)

static const NSCalendarUnit unitFlags = (NSCalendarUnitYear | NSCalendarUnitMonth | NSCalendarUnitDay | NSCalendarUnitWeekOfYear | NSCalendarUnitWeekOfMonth |  NSCalendarUnitHour | NSCalendarUnitMinute | NSCalendarUnitSecond | NSCalendarUnitWeekday | NSCalendarUnitWeekdayOrdinal | NSCalendarUnitQuarter);

@implementation NSDate (BRPickerView)

static NSCalendar *_sharedCalendar = nil;
static NSTimeZone *_timeZone = nil;

#pragma mark - 设置日历对象
+ (void)br_setCalendar:(NSCalendar *)calendar {
    _sharedCalendar = calendar;
}

#pragma mark - 获取日历对象
+ (NSCalendar *)br_getCalendar {
    if (!_sharedCalendar) {
        
        _sharedCalendar = [[NSCalendar alloc]initWithCalendarIdentifier:NSCalendarIdentifierGregorian];
    }
    return _sharedCalendar;
}

#pragma mark - 设置时区
+ (void)br_setTimeZone:(NSTimeZone *)timeZone {
    _timeZone = timeZone;
    
    [self br_getCalendar].timeZone = timeZone;
}

#pragma mark - 获取当前时区
+ (NSTimeZone *)br_getTimeZone {
    if (!_timeZone) {
        
        NSTimeZone *localTimeZone = [NSTimeZone localTimeZone];
        
        NSInteger interval = [localTimeZone secondsFromGMTForDate:[NSDate date]];
        
        
        _timeZone = [NSTimeZone timeZoneForSecondsFromGMT:interval];
    }
    return _timeZone;
}

#pragma mark - NSDate 转 NSDateComponents
+ (NSDateComponents *)br_componentsFromDate:(NSDate *)date {
    
    NSCalendar *calendar = [self br_getCalendar];
    
    return [calendar components:unitFlags fromDate:date];
}

#pragma mark - NSDateComponents 转 NSDate
+ (NSDate *)br_dateFromComponents:(NSDateComponents *)components {
    
    NSCalendar *calendar = [self br_getCalendar];
    return [calendar dateFromComponents:components];
}

#pragma mark - 获取指定日期的年份
- (NSInteger)br_year {
    return [NSDate br_componentsFromDate:self].year;
}

#pragma mark - 获取指定日期的月份
- (NSInteger)br_month {
    return [NSDate br_componentsFromDate:self].month;
}

#pragma mark - 获取指定日期的天
- (NSInteger)br_day {
    return [NSDate br_componentsFromDate:self].day;
}

#pragma mark - 获取指定日期的小时
- (NSInteger)br_hour {
    return [NSDate br_componentsFromDate:self].hour;
}

#pragma mark - 获取指定日期的分钟
- (NSInteger)br_minute {
    return [NSDate br_componentsFromDate:self].minute;
}

#pragma mark - 获取指定日期的秒
- (NSInteger)br_second {
    return [NSDate br_componentsFromDate:self].second;
}

#pragma mark - 获取指定日期的星期
- (NSInteger)br_weekday {
    return [NSDate br_componentsFromDate:self].weekday;
}

#pragma mark - 获取指定日期的月周
- (NSInteger)br_monthWeek {
    return [NSDate br_componentsFromDate:self].weekOfMonth;
}

#pragma mark - 获取指定日期的年周
- (NSInteger)br_yearWeek {
    return [NSDate br_componentsFromDate:self].weekOfYear;
}

#pragma mark - 获取指定日期的季度
- (NSInteger)br_quarter {
    NSInteger quarter = 1;
    NSInteger month = self.br_month;
    if (month > 3) quarter = 2;
    if (month > 6) quarter = 3;
    if (month > 9) quarter = 4;
    
    return quarter;
}

#pragma mark - 获取指定日期的星期
- (NSString *)br_weekdayString {
    switch (self.br_weekday - 1) {
        case 0:
        {
            return @"周日";
        }
            break;
        case 1:
        {
            return @"周一";
        }
            break;
        case 2:
        {
            return @"周二";
        }
            break;
        case 3:
        {
            return @"周三";
        }
            break;
        case 4:
        {
            return @"周四";
        }
            break;
        case 5:
        {
            return @"周五";
        }
            break;
        case 6:
        {
            return @"周六";
        }
            break;
            
        default:
            break;
    }
    
    return @"";
}

#pragma mark - 创建date（通过 NSCalendar 类来创建日期）
+ (NSDate *)br_setYear:(NSInteger)year month:(NSInteger)month day:(NSInteger)day hour:(NSInteger)hour minute:(NSInteger)minute second:(NSInteger)second {
    return [self br_setYear:year month:month day:day hour:hour minute:minute second:second weekOfMonth:0 weekOfYear:0 quarter:0];
}

+ (NSDate *)br_setYear:(NSInteger)year month:(NSInteger)month day:(NSInteger)day
                  hour:(NSInteger)hour minute:(NSInteger)minute second:(NSInteger)second
           weekOfMonth:(NSInteger)weekOfMonth weekOfYear:(NSInteger)weekOfYear quarter:(NSInteger)quarter {
    NSDateComponents *components = [self br_componentsFromDate:[NSDate date]];
    if (year > 0) {
        
        components = [[NSDateComponents alloc]init];
        components.year = year;
    }
    if (month > 0) {
        components.month = month;
    }
    if (day > 0) {
        components.day = day;
    }
    if (hour >= 0) {
        components.hour = hour;
    }
    if (minute >= 0) {
        components.minute = minute;
    }
    if (second >= 0) {
        components.second = second;
    }
    if (weekOfMonth > 0) {
        components.weekOfMonth = weekOfMonth;
    }
    if (weekOfYear > 0) {
        components.weekOfYear = weekOfYear;
    }
    if (quarter > 0) {
        components.quarter = quarter;
    }
    
    return [self br_dateFromComponents:components];
}

+ (NSDate *)br_setYear:(NSInteger)year month:(NSInteger)month day:(NSInteger)day hour:(NSInteger)hour minute:(NSInteger)minute {
    return [self br_setYear:year month:month day:day hour:hour minute:minute second:0];
}

+ (NSDate *)br_setYear:(NSInteger)year month:(NSInteger)month day:(NSInteger)day hour:(NSInteger)hour {
    return [self br_setYear:year month:month day:day hour:hour minute:0 second:0];
}

+ (NSDate *)br_setYear:(NSInteger)year month:(NSInteger)month day:(NSInteger)day {
    return [self br_setYear:year month:month day:day hour:0 minute:0 second:0];
}

+ (NSDate *)br_setYear:(NSInteger)year month:(NSInteger)month {
    return [self br_setYear:year month:month day:0 hour:0 minute:0 second:0];
}

+ (NSDate *)br_setYear:(NSInteger)year {
    return [self br_setYear:year month:0 day:0 hour:0 minute:0 second:0];
}

+ (NSDate *)br_setMonth:(NSInteger)month day:(NSInteger)day hour:(NSInteger)hour minute:(NSInteger)minute {
    return [self br_setYear:0 month:month day:day hour:hour minute:minute second:0];
}

+ (NSDate *)br_setMonth:(NSInteger)month day:(NSInteger)day {
    return [self br_setYear:0 month:month day:day hour:0 minute:0 second:0];
}

+ (NSDate *)br_setHour:(NSInteger)hour minute:(NSInteger)minute second:(NSInteger)second {
    return [self br_setYear:0 month:0 day:0 hour:hour minute:minute second:second];
}

+ (NSDate *)br_setHour:(NSInteger)hour minute:(NSInteger)minute {
    return [self br_setYear:0 month:0 day:0 hour:hour minute:minute second:0];
}

+ (NSDate *)br_setMinute:(NSInteger)minute second:(NSInteger)second {
    return [self br_setYear:0 month:0 day:0 hour:0 minute:minute second:second];
}

+ (NSDate *)br_setYear:(NSInteger)year month:(NSInteger)month weekOfMonth:(NSInteger)weekOfMonth {
    return [self br_setYear:year month:month day:0 hour:0 minute:0 second:0 weekOfMonth:weekOfMonth weekOfYear:0 quarter:0];
}

+ (NSDate *)br_setYear:(NSInteger)year weekOfYear:(NSInteger)weekOfYear {
    return [self br_setYear:year month:0 day:0 hour:0 minute:0 second:0 weekOfMonth:0 weekOfYear:weekOfYear quarter:0];
}

+ (NSDate *)br_setYear:(NSInteger)year quarter:(NSInteger)quarter {
    return [self br_setYear:year month:0 day:0 hour:0 minute:0 second:0 weekOfMonth:0 weekOfYear:0 quarter:quarter];
}

- (NSDate *)br_setTwelveHour:(NSInteger)hour {
    NSDateComponents *components = [NSDate br_componentsFromDate:self];
    if (hour >= 0) {
        components.hour = hour;
    }
    return [NSDate br_dateFromComponents:components];
}

#pragma mark - 获取某个月的天数（通过年月求每月天数）
+ (NSUInteger)br_getDaysInYear:(NSInteger)year month:(NSInteger)month {
    BOOL isLeapYear = year % 4 == 0 ? (year % 100 == 0 ? (year % 400 == 0 ? YES : NO) : YES) : NO;
    switch (month) {
        case 1:
        case 3:
        case 5:
        case 7:
        case 8:
        case 10:
        case 12:
        {
            return 31;
        }
        case 4:
        case 6:
        case 9:
        case 11:
        {
            return 30;
        }
        case 2:
        {
            if (isLeapYear) {
                return 29;
            } else {
                return 28;
            }
        }
        default:
            break;
    }
    
    return 0;
}

#pragma mark - 获取某个月的周数（通过年月求该月周数）
+ (NSUInteger)br_getWeeksOfMonthInYear:(NSInteger)year month:(NSInteger)month {
    NSUInteger lastDayOfMonth = [self br_getDaysInYear:year month:month];
    NSDate *endDate = [self br_setYear:year month:month day:lastDayOfMonth];
    return endDate.br_monthWeek;
}

#pragma mark - 获取某一年的周数（通过年求该年周数）
+ (NSUInteger)br_getWeeksOfYearInYear:(NSInteger)year {
    NSDate *endDate = [self br_setYear:year month:12 day:31];
    NSInteger weeks = endDate.br_yearWeek;
    if (weeks == 1) weeks = 52;
    return weeks;
}

#pragma mark - 获取某一年的季度数（通过年求该年季度数）
+ (NSUInteger)br_getQuartersInYear:(NSInteger)year {
    NSDate *endDate = [self br_setYear:year month:12 day:31];
    return endDate.br_quarter;
}

#pragma mark - 获取 日期加上/减去某天数后的新日期
- (NSDate *)br_getNewDateToDays:(NSTimeInterval)days {
    
    return [self dateByAddingTimeInterval:60 * 60 * 24 * days];
}

#pragma mark - 获取 日期加上/减去某个月数后的新日期
- (nullable NSDate *)br_getNewDateToMonths:(NSTimeInterval)months {
    
    NSDateComponents *components = [[NSDateComponents alloc] init];
    [components setMonth:months];
    NSCalendar *calender = [[NSCalendar alloc] initWithCalendarIdentifier:NSCalendarIdentifierGregorian];
    return [calender dateByAddingComponents:components toDate:self options:0];
}

#pragma mark - NSDate 转 NSString
+ (NSString *)br_stringFromDate:(NSDate *)date dateFormat:(NSString *)dateFormat {
    return [self br_stringFromDate:date dateFormat:dateFormat language:nil];
}
#pragma mark - NSDate 转 NSString
+ (NSString *)br_stringFromDate:(NSDate *)date
                     dateFormat:(NSString *)dateFormat
                       language:(NSString *)language {
    NSDateFormatter *dateFormatter = [[NSDateFormatter alloc] init];
    
    dateFormatter.dateFormat = dateFormat;
    
    dateFormatter.timeZone = [self br_getTimeZone];
    if (!language) {
        language = [NSLocale preferredLanguages].firstObject;
    }
    dateFormatter.locale = [[NSLocale alloc]initWithLocaleIdentifier:language];
    NSString *dateString = [dateFormatter stringFromDate:date];

    return dateString;
}

#pragma mark - NSString 转 NSDate
+ (NSDate *)br_dateFromString:(NSString *)dateString dateFormat:(NSString *)dateFormat {
    return [self br_dateFromString:dateString dateFormat:dateFormat language:nil];
}
#pragma mark - NSString 转 NSDate
+ (NSDate *)br_dateFromString:(NSString *)dateString
                   dateFormat:(NSString *)dateFormat
                     language:(NSString *)language {
    NSDateFormatter *dateFormatter = [[NSDateFormatter alloc] init];
    
    dateFormatter.dateFormat = dateFormat;
    if (!language) {
        language = [NSLocale preferredLanguages].firstObject;
    }
    
    dateFormatter.timeZone = [self br_getTimeZone];
    dateFormatter.locale = [[NSLocale alloc]initWithLocaleIdentifier:language];
    
    dateFormatter.lenient = YES;
    
    return [dateFormatter dateFromString:dateString];
}

@end
