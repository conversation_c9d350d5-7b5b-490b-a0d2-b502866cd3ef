 

#import "AppDelegate.h"

#import <IQKeyboardManager.h>
#import "SAServiceFactory.h"
#import "SANetworkConst.h"
#import "SAAppCodeManager.h"
#import "SAUserManager.h"
#import "SAAdvanceManager.h"
#import <AppTrackingTransparency/AppTrackingTransparency.h>

#import "SATabBarViewController.h"
#import "SAVersionUpdateViewController.h"
#import "SALoginViewController.h"


@implementation NSURLRequest(ViewController)

+ (BOOL)allowsAnyHTTPSCertificateForHost:(NSString *)host
{
    return YES;
}

@end

@interface AppDelegate ()<QLServiceFactoryDataSource>

@property (nonatomic, strong)SAAppCodeManager *codeMgr;

@end

@implementation AppDelegate

- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions {
    
    self.window = [[UIWindow alloc] initWithFrame:[UIScreen mainScreen].bounds];
    self.window.backgroundColor = [UIColor whiteColor];
    
    if (@available(iOS 9.0, *)) {
        self.window.rootViewController = [[UIViewController alloc] init];
    }
    
    [SAServiceFactory sharedInstance].dataSource = self;
    [self preRequestProcessApplication:application options:launchOptions];
        
    [self setupThirdLibs:launchOptions application:application];

    [self.window makeKeyAndVisible];
    return YES;
}

#pragma mark - life

- (void)preRequestProcessApplication:(UIApplication *)application options:(NSDictionary *)launchOptions
{
    __weak typeof(self) weakSelf = self;
    [SATool showWindowHUD:nil];
    [self.codeMgr requestAppCodeSuccess:^(NSDictionary * _Nonnull dict) {
        [SATool hideWindowHUD];
        weakSelf.codeMgr = nil;
        [weakSelf setupWindowRootVC:dict application:application options:launchOptions];
    } tapRetry:^{
        [weakSelf preRequestProcessApplication:application options:launchOptions];
    }];
    
    
}

- (void)setupWindowRootVC:(NSDictionary *)dict application:(UIApplication *)application options:(NSDictionary *)launchOptions
{
    self.window.rootViewController = [SATabBarViewController sharedInstance];
        
    if([SAUserManager isLogin]){
        [[SANLUploadManager sharedInstance] setup];
    }
    
    NSString *updateUrl = dict[@"forceUpdateUrl"];
    if ([NSString judgeStringExist:updateUrl]) {
        SAVersionUpdateViewController *vc = [SAVersionUpdateViewController showVersionUpdateAlertWithAppleId:dict withFromVc:[SACommonTool currentViewController]];
        [vc presentFromView];
    }
}

- (void)setupThirdLibs:(NSDictionary *)launchOptions application: (UIApplication *)application {
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        
         
        [IQKeyboardManager sharedManager].toolbarDoneBarButtonItemText  = NSLocalizedString(@"abedOnfrimSign", nil);
        [IQKeyboardManager sharedManager].shouldResignOnTouchOutside    = YES;
        [IQKeyboardManager sharedManager].enableAutoToolbar = YES;
        [IQKeyboardManager sharedManager].previousNextDisplayMode       = IQPreviousNextDisplayModeAlwaysHide;
    });
}

- (void)applicationDidBecomeActive:(UIApplication *)application
{
    if (@available(iOS 14, *)) {
      [ATTrackingManager requestTrackingAuthorizationWithCompletionHandler:^(ATTrackingManagerAuthorizationStatus status) {
        NSLog(@"Status: %lu", (unsigned long)status);
      }];
    }
}

#pragma mark - QLServiceFactoryDataSource
- (NSDictionary<NSString *,NSString *> *)servicesKindsOfServiceFactory {
    
    return @{
             kNPBasicServiceKey: @"SABasicService",
             };
}

#pragma mark - getter
- (SAAppCodeManager *)codeMgr {
    if (_codeMgr == nil) {
        _codeMgr = [[SAAppCodeManager alloc] init];
    }
    return _codeMgr;
}

@end
