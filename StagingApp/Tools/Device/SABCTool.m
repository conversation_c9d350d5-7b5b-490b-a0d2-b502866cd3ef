


#import "SABCTool.h"
#import <AdSupport/AdSupport.h>
#import "SAKeyChainTool.h"


#define kUUID @"kYLUUID"


@implementation SABCTool

+ (NSString *)getDeviceID {
    
        
        NSString *idfa = [[[ASIdentifierManager sharedManager] advertisingIdentifier] UUIDString];
        
        return idfa;
}

+ (NSString *)getGuestID
{
    NSString * strUUID = (NSString *)[SAKeyChainTool searchForKey:kUUID];
    if ([strUUID isEqualToString: @""] || !strUUID) {
      NSString *idfa = [[[UIDevice currentDevice] identifierForVendor] UUIDString];
      if ([idfa isEqualToString:@"00000000-0000-0000-0000-000000000000"]) {
        idfa = [self getUUID];
      }
      [SAKeyChainTool saveData: idfa forKey: kUUID];
      return idfa;
    }
    return strUUID;
}

+ (NSString *)getUUID {
    return [NSUUID UUID].UUIDString;
}

 
+ (NSString *)getIDFV {
    return [[[UIDevice currentDevice] identifierForVendor] UUIDString];
}

@end
