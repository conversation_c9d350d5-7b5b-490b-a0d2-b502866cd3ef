
#import "SATool.h"
#import "AppDelegate.h"
#import "SACommonTool.h"
#import "SACustomHudImg.h"

@implementation SATool

#pragma mark - VC hud

 
+ (void)showActivityHUD:(nullable NSString *)text
{
    [self hideHUDView];
    [self showCurrentVCHud:text];
    return;
}

 
+ (void)hideHUDView
{
    dispatch_async(dispatch_get_main_queue(), ^{
        [self hideCurrentVCHud];
    });
}

 
+ (void)textStateHUD:(nullable NSString *)text
{
    if (text == nil || [text length] == 0)
    {
    }
    
    [self textStateCurrentVCHud:text];
    return;
}

 
+ (void)showCurrentVCHud:(nullable NSString *)text {
    if ([text isKindOfClass:[NSNull class]]) {
        text = @"null";
    }
    
    dispatch_async(dispatch_get_main_queue(), ^{

        UIViewController *vc = [SACommonTool currentViewController];
        MBProgressHUD *hud = [self createIndicatorHUD:vc.view];
        hud.label.text = text;
        hud.margin = 30;
    });
}

 
+ (void)hideCurrentVCHud
{
    dispatch_async(dispatch_get_main_queue(), ^{
        UIViewController *vc = [SACommonTool currentViewController];
        [MBProgressHUD hideHUDForView:vc.view animated:YES];
    });
}


 
+ (void)textStateCurrentVCHud:(nullable NSString *)text
{
    if ([text isKindOfClass:[NSNull class]]) {
        text = @"null";
    }
    dispatch_async(dispatch_get_main_queue(), ^{
        UIViewController *vc = [SACommonTool currentViewController];
        MBProgressHUD *hud = [self createTextHUD:vc.view];
        hud.label.text = text;
        hud.margin = 40;
        
        [hud hideAnimated:YES afterDelay:2.6];
    });
}

#pragma mark - Window

+ (void)showWindowHUD:(nullable NSString *)text {
    [self hideWindowHUD];
    if ([text isKindOfClass:[NSNull class]]) {
        text = @"null";
    }
    dispatch_async(dispatch_get_main_queue(), ^{
        
        MBProgressHUD *hud = [self createImageHUD:nil];
        hud.label.text = text;
        hud.margin = 30;
    });
}

+ (void)hideWindowHUD {
    
    [self hideHUDView];
    dispatch_async(dispatch_get_main_queue(), ^{
        
        [MBProgressHUD hideHUDForView:[UIApplication sharedApplication].keyWindow animated:YES];
    });
    
}

+ (void)textStateWindowHUD:(nullable NSString *)text {
    if (![NSString judgeStringExist:text]) {
        return;
    }
    dispatch_async(dispatch_get_main_queue(), ^{
        
        MBProgressHUD *hud = [self createTextHUD:nil];
        
        [hud hideAnimated:YES afterDelay:2.6];
        hud.label.text = text;
        hud.margin = 30;
    
    });
}

+ (void)textStateWindowHUD:(nullable NSString *)text finishBlock:(void (^)(void))finishBlock {
    dispatch_async(dispatch_get_main_queue(), ^{
        
        MBProgressHUD *hud = [self createTextHUD:nil];
        
        [hud hideAnimated:YES afterDelay:2.6];
        hud.label.text = text;
        hud.completionBlock = finishBlock;
        hud.margin = 30;
    });
}


 
+ (MBProgressHUD *)createTextHUD:(UIView *)view {
    
    if (view == nil) view = (UIView *)[UIApplication sharedApplication].keyWindow;
    
    MBProgressHUD *hud = [MBProgressHUD showHUDAddedTo:view animated:YES];
    
    hud.label.numberOfLines = 0;
    hud.label.font = [UIFont systemFontOfSize:13.0];
    hud.contentColor = [UIColor whiteColor];
    hud.bezelView.color = [[UIColor blackColor] colorWithAlphaComponent:0.7];
    hud.bezelView.style = MBProgressHUDBackgroundStyleSolidColor;

    
    hud.removeFromSuperViewOnHide = YES;
    
    hud.mode = MBProgressHUDModeText;
    
    return hud;
}


 
+ (MBProgressHUD *)createIndicatorHUD:(UIView *)view {
    
    if (view == nil) view = (UIView *)[UIApplication sharedApplication].keyWindow;
    
    MBProgressHUD *hud = [self createTextHUD:view];
    
    hud.mode = MBProgressHUDModeIndeterminate;
    hud.bezelView.style = MBProgressHUDBackgroundStyleSolidColor;

    return hud;
}

 
+ (MBProgressHUD *)createImageHUD:(UIView *)view {
    
    if (view == nil) view = (UIView *)[UIApplication sharedApplication].keyWindow;
    
    MBProgressHUD *hud = [self createTextHUD:view];
    
    hud.mode = MBProgressHUDModeCustomView;
    hud.bezelView.style = MBProgressHUDBackgroundStyleSolidColor;

    NSString *path = [[NSBundle mainBundle] pathForResource:@"loading" ofType:@"gif"];
    NSData * data = [NSData dataWithContentsOfFile:path];
    UIImage *image = [UIImage sd_imageWithGIFData:data];
    SACustomHudImg *customV = [[SACustomHudImg alloc] initWithImage:image];
    hud.customView = customV;
    return hud;
}

@end
