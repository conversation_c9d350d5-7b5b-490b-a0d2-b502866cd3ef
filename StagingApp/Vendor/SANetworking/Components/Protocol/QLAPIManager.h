 

#import <Foundation/Foundation.h>
#import "QLNetworkEnum.h"

@protocol QLAPIManager <NSObject>

@required
- (NSString *)methodName;
- (NSString *)serviceType;
- (QLAPIManagerRequestType)requestType;
- (BOOL)shouldCache;

@optional
- (NSInteger)timeOutSecond;
- (void)cleanData;
- (NSDictionary *)reformParams:(NSDictionary *)params;
- (NSInteger)loadDataWithParams:(NSDictionary *)params;
- (BOOL)shouldLoadFromNative;

- (id)requestMimeType;
- (id)fileName;

@end
