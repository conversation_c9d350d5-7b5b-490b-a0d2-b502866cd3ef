
#import <UIKit/UIKit.h>

@class SAMarqueeView;

typedef NS_ENUM(NSUInteger, UUMarqueeViewDirection) {
    UUMarqueeViewDirectionUpward,   
    UUMarqueeViewDirectionLeftward  
};

#pragma mark - UUMarqueeViewDelegate
@protocol UUMarqueeViewDelegate <NSObject>
- (NSUInteger)numberOfDataForMarqueeView:(SAMarqueeView*)marqueeView;
- (void)createItemView:(UIView*)itemView forMarqueeView:(SAMarqueeView*)marqueeView;
- (void)updateItemView:(UIView*)itemView atIndex:(NSUInteger)index forMarqueeView:(SAMarqueeView*)marqueeView;
@optional
- (NSUInteger)numberOfVisibleItemsForMarqueeView:(SAMarqueeView*)marqueeView;   
- (CGFloat)itemViewWidthAtIndex:(NSUInteger)index forMarqueeView:(SAMarqueeView*)marqueeView;   
- (CGFloat)itemViewHeightAtIndex:(NSUInteger)index forMarqueeView:(SAMarqueeView*)marqueeView;   
- (void)didTouchItemViewAtIndex:(NSUInteger)index forMarqueeView:(SAMarqueeView*)marqueeView;
@end

#pragma mark - SAMarqueeView
@interface SAMarqueeView : UIView
@property (nonatomic, weak) id<UUMarqueeViewDelegate> delegate;
@property (nonatomic, assign) NSTimeInterval timeIntervalPerScroll;
@property (nonatomic, assign) NSTimeInterval timeDurationPerScroll; 
@property (nonatomic, assign) BOOL useDynamicHeight;    
@property (nonatomic, assign) float scrollSpeed;    
@property (nonatomic, assign) float itemSpacing;    
@property (nonatomic, assign) BOOL stopWhenLessData;    
@property (nonatomic, assign) BOOL clipsToBounds;
@property (nonatomic, assign, getter=isTouchEnabled) BOOL touchEnabled;
@property (nonatomic, assign) UUMarqueeViewDirection direction;
- (instancetype)initWithDirection:(UUMarqueeViewDirection)direction;
- (instancetype)initWithFrame:(CGRect)frame direction:(UUMarqueeViewDirection)direction;
- (void)reloadData;
- (void)start;
- (void)pause;
@end

#pragma mark - UUMarqueeViewTouchResponder(Private)
@protocol UUMarqueeViewTouchResponder <NSObject>
- (void)touchesBegan;
- (void)touchesEndedAtPoint:(CGPoint)point;
- (void)touchesCancelled;
@end

#pragma mark - SAMarqueeViewTouchReceiver(Private)
@interface SAMarqueeViewTouchReceiver : UIView
@property (nonatomic, weak) id<UUMarqueeViewTouchResponder> touchDelegate;
@end

#pragma mark - SAMarqueeItemView(Private)
@interface SAMarqueeItemView : UIView   
@property (nonatomic, assign) BOOL didFinishCreate;
@property (nonatomic, assign) CGFloat width;    
@property (nonatomic, assign) CGFloat height;   
- (void)clear;
@end
