
#import "SARequestHandle.h"
#import "SANetworkConst.h"
#import "SADeviceTool.h"
#import "SAUserManager.h"
#import "SAString+Extension.h"
#import "SAMutableDictionary+Extension.h"
#import "SAString+QLNetworkingMethods.h"
#import "SADictionary+QLNetworkingMethods.h"
#import "SAAdvanceManager.h"

@implementation SARequestHandle

+ (NSString *)basicParamsHeadString:(NSDictionary *)requestParams deleteSpace:(BOOL)deleteSpace {
    
    NSMutableDictionary *basicHeadParamsDict = [NSMutableDictionary dictionary];
    
    NSString *paramsJsonStr = @"";
    NSString *appKeyStr = kNPAppKey;
    NSString *appVersionStr = [SADeviceTool appVersion];
    NSString *appClientStr = kNPAppClient;
    NSString *buildStr = [SADeviceTool buildVersion];
    NSNumber *timestampNum  = [NSNumber numberWithInteger:(NSInteger)[[NSDate date] timeIntervalSince1970]];
    NSString *guestIdStr = [SADeviceTool deviceGuestID];
    NSString *tokenStr = [SAUserManager getToken];
    
    [basicHeadParamsDict safetySetValue:appKeyStr forKey:@"appKey"];
    [basicHeadParamsDict safetySetValue:appVersionStr forKey:@"appVersion"];
    [basicHeadParamsDict safetySetValue:kNPChannelName forKey:@"channel"];
    [basicHeadParamsDict safetySetValue:appClientStr forKey:@"appClient"];
    
    NSString *netStr = [SADeviceTool isEnableWIFI] ? @"wifi" : @"GPRS";
    [basicHeadParamsDict setObject:netStr forKey:@"net"];
    [basicHeadParamsDict safetySetValue:buildStr forKey:@"versionCode"];
    
    [basicHeadParamsDict safetySetValue:timestampNum forKey:@"timestamp"];
    
    [basicHeadParamsDict safetySetValue:guestIdStr forKey:@"guestId"];
    [basicHeadParamsDict safetySetValue:tokenStr forKey:@"token"];
    
    [basicHeadParamsDict safetySetValue:@"0000002" forKey:@"appCode"];
    [basicHeadParamsDict safetySetValue:[SADeviceTool deviceUUID] forKey:@"clientId"];
    [basicHeadParamsDict safetySetValue:[[NSUUID UUID] UUIDString] forKey:@"openId"];
    [basicHeadParamsDict safetySetValue:[SADeviceTool deviceModelName] forKey:@"from"];
    [basicHeadParamsDict safetySetValue:@"RSA" forKey:@"cryptoType"];
    
    
    
    paramsJsonStr = [paramsJsonStr appendNoNullString:appKeyStr];
    paramsJsonStr = [paramsJsonStr appendNoNullString:appVersionStr];
    paramsJsonStr = [paramsJsonStr appendNoNullString:appClientStr];
    paramsJsonStr = [paramsJsonStr appendNoNullString:buildStr];
    paramsJsonStr = [paramsJsonStr appendNoNullString:[timestampNum stringValue]];
    paramsJsonStr = [paramsJsonStr appendNoNullString:guestIdStr];
    paramsJsonStr = [paramsJsonStr appendNoNullString:tokenStr];
    NSString *requestParamsStr = [NSString QL_dictToString:requestParams deleteSpace:deleteSpace];
    
    NSString *appSecretStr = kNPAppSecretKey;
    if (appSecretStr == nil) {
        appSecretStr = @"";
    }
    
    
    NSString *appSignString = [NSString stringWithFormat:@"%@%@%@", paramsJsonStr, requestParamsStr, appSecretStr];
    appSignString = [[appSignString QL_md5] QL_md5];
    [basicHeadParamsDict safetySetValue:appSignString forKey:@"appSign"];
    
    NSString *basicParamsHeadString = [[NSString alloc] initWithData:[NSString QL_toJSONData:basicHeadParamsDict] encoding:NSUTF8StringEncoding];
    
    basicParamsHeadString = [basicParamsHeadString stringByReplacingOccurrencesOfString:@"\n" withString:@""];
    basicParamsHeadString = [basicParamsHeadString stringByReplacingOccurrencesOfString:@" " withString:@""];
    
    return basicParamsHeadString;
}

@end
