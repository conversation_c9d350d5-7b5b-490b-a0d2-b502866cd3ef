

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, NPCardType) {
    NPCardType_Front = 2,      
    NPCardType_Back  = 1,      
};

typedef void(^OCRSuccessBlock)(NSString *ocrUrl, NSDictionary *cardInfo, NSData *ocrData, NSString *detailId);

@interface SAManager : NSObject

@property (nonatomic, strong)NSData *imgData;       

- (void)startOCRWithType:(NPCardType)type complete:(OCRSuccessBlock)success;

@end

NS_ASSUME_NONNULL_END
