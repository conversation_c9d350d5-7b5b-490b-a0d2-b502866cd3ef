{
    "name": "custom",

    "statusBarStyle":   "dark",
    "navbarTintColor":  "0x333333",
    "baseNavBarColor":  "0x0",
    "authNavBarColor":  "0x22252A",

    "imageBgColor":     "0xffffff",
    "faceNormalColor": "0x33FFFFFF",
    "faceSatisfyColor": "0x5065FF",
    "faceErrorColor": "0xFF6034",

    "guideLabelColor":  "0x363C62",
    "guideLabelErrorColor" : "0xFF6034",
    "customTipsColor":  "0x545874",
    "bottomTipsBgColor":  "0xFAFAFA",
    "bottomTipsTextColor": "0x8E92AE",

    "backButtonImage": "backbutton@light",
    "authBackButtonImage": "backbutton@dark",
    "authBodyImage_651": "wbcf_auth_face_651",
    "authCheckboxUnSelectImage_651": "wbcf_checkbox_unselect_651",
    "authCheckboxSelectImage_651": "wbcf_checkbox_select_651",
    "authCheckboxTipsColor": "0x8E92AE",
    "authCheckboxLinkColor": "0x5065FF",
    "authAgreeButtonNormalColor": "0xB7C0FF",
    "authAgreeButtonHighlightColor": "0x5065FF",
    "authAgreeTextNormalColor":"0xFFFFFF",
    "authAgreeTextHighlightColor":"0xFFFFFF",
    "authTipBackgroundColor_651":"0xF9FAFF",
    "authTipTitileTextColor_651":"0x363C62",
    "authTipDetailTextColor_651":"0x545874",
    
    "alertTitleColor":"0x000000",
    "alertMessageColor":"0x000000",
    "alertLeftBtnColor":"0x5065FF",
    "alertRightBtnColor":"0x5065FF",
    "alertBackgroundColor":"0xFFFFFF",
}

