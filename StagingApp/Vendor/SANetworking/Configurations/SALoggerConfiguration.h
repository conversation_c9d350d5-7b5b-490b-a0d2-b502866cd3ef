
#import <Foundation/Foundation.h>

@interface SALoggerConfiguration : NSObject

 
@property (nonatomic, strong) NSString *channelID;

 
@property (nonatomic, strong) NSString *appKey;

 
@property (nonatomic, strong) NSString *logAppName;

 
@property (nonatomic, assign) NSString *serviceType;

 
@property (nonatomic, strong) NSString *sendLogMethod;

 
@property (nonatomic, strong) NSString *sendActionMethod;

 
@property (nonatomic, strong) NSString *sendLogKey;

 
@property (nonatomic, strong) NSString *sendActionKey;


@end
