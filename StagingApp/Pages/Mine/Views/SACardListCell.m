
#import "SACardListCell.h"

@interface SACardListCell ()

@property (nonatomic, strong) UIImageView *bankLogo;
@property (nonatomic, strong) UILabel *nameLab;
@property (nonatomic, strong) UILabel *typeLab;
@property (nonatomic, strong) UILabel *noLab;
@property (nonatomic, strong) UIImageView *unionImg;


@end

@implementation SACardListCell

#pragma mark - Life cycle

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier])
    {
        [self createUI];
        [self setConstraints];
        
        self.contentView.layer.masksToBounds = YES;
        self.contentView.layer.cornerRadius = 10;
    }
    
    return self;
}

- (void)createUI
{
    SAGradientView *view = [SAGradientView new];
    [view gradientBackgroundColors:@[[UIColor colorWithHex:0x70A8F7], [UIColor colorWithHex:0x598CF3]] locations:nil startPoint:CGPointMake(0, 0.5) endPoint:CGPointMake(1.0, 0.5)];
    [self.contentView addSubview:view];
    [view mas_makeConstraints:^(SAConstraintMaker *make) {
        make.edges.equalTo(self.contentView);
    }];
    
    [self.contentView addSubview:self.bankLogo];
    [self.contentView addSubview:self.nameLab];
    [self.contentView addSubview:self.typeLab];
    [self.contentView addSubview:self.noLab];
//    [self.contentView addSubview:self.unionImg];
}

- (void)setConstraints
{
    [self.bankLogo mas_makeConstraints:^(SAConstraintMaker *make) {
        make.width.height.mas_equalTo(40);
        make.left.top.equalTo(self.contentView).offset(14);
    }];
    [self.nameLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.equalTo(self.bankLogo.mas_right).offset(10);
        make.bottom.equalTo(self.bankLogo.mas_centerY).offset(4);
    }];
    [self.typeLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.equalTo(self.nameLab);
        make.top.equalTo(self.nameLab.mas_bottom).offset(5);
    }];
    [self.noLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.equalTo(self.nameLab);
        make.bottom.equalTo(self.contentView).offset(-20);
    }];
    
//    [self.unionImg mas_makeConstraints:^(SAConstraintMaker *make) {
//        make.right.equalTo(self.contentView).offset(-20);
//        make.bottom.equalTo(self.contentView).offset(-20);
//        make.width.mas_equalTo(46);
//        make.height.mas_equalTo(38);
//    }];
}

#pragma mark - setter

- (void)setModel:(SAMyBankItemModel *)model
{
    _model = model;
    [self.bankLogo sd_setImageWithURL:[NSURL URLWithString:model.bankLogo]];
    self.nameLab.text = model.bankName;
    self.typeLab.text = [NSString stringWithFormat:@"%@ | %@", model.bankCardType, model.holderName];
    self.noLab.text = model.cardNo;
}

- (void)setFrame:(CGRect)frame
{
    frame.origin.x += 12;
    frame.size.width -= 24;
    
    frame.origin.y += 5;
    frame.size.height -= 10;
    
    [super setFrame:frame];
}

#pragma mark - getter

- (UILabel *)nameLab{
    FF_Fetch_UILable(_nameLab, [UIColor whiteColor], BoldFont_XX6(17))
}

- (UILabel *)typeLab{
    FF_Fetch_UILable(_typeLab, [UIColor whiteColor], Font_XX6(12))
}

- (UILabel *)noLab{
    FF_Fetch_UILable(_noLab, [UIColor whiteColor], BoldFont_XX6(18))
}

- (UIImageView *)unionImg{
    FF_Fetch_UIImageViewWithImage(_unionImg, [UIImage imageNamed:@"yinlian"])
}

- (UIImageView *)bankLogo{
    FF_Fetch_UIImageViewWithColor(_bankLogo, [UIColor clearColor])
}

@end
