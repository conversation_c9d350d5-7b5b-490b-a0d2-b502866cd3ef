
#import "SAPersonalVerifyModel.h"
#import <MJExtension.h>

@implementation SAPersonalVerifyModel

+ (void)load {
    
    [SAPersonalVerifyModel mj_setupObjectClassInArray:^NSDictionary *{
        return @{
                 @"inputParams" : @"SAPersonalInfoModel"
                 };
    }];
}

+ (instancetype)objectWithDict:(NSDictionary *)dict {
    return [self mj_objectWithKeyValues:dict];
}

+ (id)arrayTransfromToModels:(NSArray *)array{
    if(array.count == 0) return nil;

    NSMutableArray *modelsArr = [NSMutableArray array];
    for (int i=0; i<array.count; i++) {
        SAPersonalVerifyModel *model = [SAPersonalVerifyModel objectWithDict:array[i]];
        [modelsArr addObject:model];
    }
    return modelsArr;
}

- (CGFloat)personalBaseInfoTableH {
    if (self.inputParams.count < 5) {
        return 0.0;
    }
    CGFloat heigt = 0;
    for (int i = 1; i < 5; i++) {
        SAPersonalInfoModel *model = self.inputParams[i];
        if (model.cellHeight < XX_6(60)) {
            heigt += XX_6(60);
        } else {
            heigt += model.cellHeight;
        }
    }
    heigt += XX_6(95);
    return heigt;
}

- (CGFloat)personalOtherInfoTableH {
    CGFloat heigt = 0;
    for (int i = 5; i < self.inputParams.count; i++) {
        SAPersonalInfoModel *model = self.inputParams[i];
        if (model.show) {
            if (model.cellHeight < XX_6(60)) {
                heigt += XX_6(60);
            } else {
                heigt += model.cellHeight;
            }
        }
    }
    heigt += XX_6(95);
    return heigt;
}


- (NSArray *)showIndexArray {
    if (_showIndexArray.count != 0) {
        return _showIndexArray;
    }
    NSMutableArray *mutArr = [NSMutableArray array];
    for (int i = 5; i < self.inputParams.count; i++) {
        SAPersonalInfoModel *model = self.inputParams[i];
        if (model.show) {
            [mutArr addObject:[NSNumber numberWithInt:i]];
        }
    }
    return mutArr;
}

@end
