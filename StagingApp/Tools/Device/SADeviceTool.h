
#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface SADeviceTool : NSObject

 
+ (NSString *)deviceModelName;

 
+ (NSString *)deviceSystemVersion;

 
+ (NSString *)appVersion;

 
+ (NSString *)buildVersion;

 
+ (NSString *)appName;

 
+ (NSString *)bundleId;

 
+ (NSString *)deviceUUID;
+ (NSString *)deviceGuestID;

+ (BOOL)isEnableWIFI;
+ (BOOL)isEnable3G;
+ (BOOL)isLink;


 
+ (NSString *)languages;

 
+ (BOOL)isIndonesian;

+ (NSInteger)getSIMState;

@end

NS_ASSUME_NONNULL_END
