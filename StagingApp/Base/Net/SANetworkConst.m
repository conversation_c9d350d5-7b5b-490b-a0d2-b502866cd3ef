
#import "SANetworkConst.h"


#if DEBUG_MODE

BOOL const kNPServiceIsOnline = NO;
NSString * const kNPAppKey = @"7449618";
NSString * const kNPAppSecretKey = @"MKxHmMqGsMOAYNzIq0NRQGTsl5SiC5vJBdcJ";

#else

BOOL const kNPServiceIsOnline = YES;
NSString * const kNPAppKey = @"8835458";
NSString * const kNPAppSecretKey = @"yabilqiBtmqHGY0PCsl1i4U9prSozQfwXGN6";

#endif

NSString * const kNPBasicServiceKey = @"SABasicService";


NSString * const kNPAppClient = @"iphone";

NSString * const kNPChannelName = @"AppStore";


NSString * const kNPCodeUserTokenInvalid = @"700";
NSString * const kNPNeedLogin = @"";
NSString * const kNPForceUpdate = @"600";
