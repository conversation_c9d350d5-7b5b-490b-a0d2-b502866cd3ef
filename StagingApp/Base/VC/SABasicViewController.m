
#import "SABasicViewController.h"
#import <MBProgressHUD.h>
#import "SAToast+UIView.h"
#import "SACustomHudImg.h"

#import "SAOrderDetailViewController.h"

@interface SABasicViewController ()<UIGestureRecognizerDelegate>

@end

@implementation SABasicViewController

- (UIStatusBarStyle)preferredStatusBarStyle{
    return UIStatusBarStyleDefault;
}

- (void)viewWillAppear:(BOOL)animated 
{
    [super viewWillAppear:animated];
    
    [self.navigationController setNavigationBarHidden:NO animated:YES];
    self.barLineView.hidden = YES;
    [self findHairlineImageViewUnder:self.navigationController.navigationBar].hidden = YES;
    
    [self setupNavigationBar];

    if ([self.navigationController respondsToSelector:@selector(interactivePopGestureRecognizer)])
    {
        self.navigationController.interactivePopGestureRecognizer.delegate = self;
        self.navigationController.interactivePopGestureRecognizer.enabled  = YES;
    }
}

- (void)viewWillDisappear:(BOOL)animated
{
    [super viewWillDisappear:animated];
    [self hideHUDView];
    
}

- (instancetype)init
{
    if (self = [super init])
    {
        self.backImgName = @"nav_back_gray";
    }
    
    return self;
}

- (void)viewDidLoad
{
    [super viewDidLoad];
    
    self.view.backgroundColor = [UIColor whiteColor];
    self.edgesForExtendedLayout = UIRectEdgeNone;
    
    [self addBackButton];

    
    if (@available(iOS 15.0, *)) {
        [[UITableView appearance] setSectionHeaderTopPadding:0];
    }
}

- (void)addBackButton 
{
    UIButton *backButton = [[UIButton alloc] initWithFrame:CGRectMake(0, 0, 40, 40)];
    [backButton setBackgroundColor:[UIColor clearColor]];
    [backButton setImage:[[UIImage imageNamed:self.backImgName] imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal] forState:UIControlStateNormal];
    [backButton setImage:[[UIImage imageNamed:self.backImgName] imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal] forState:UIControlStateHighlighted];
    [backButton addTarget:self action:@selector(backAction:) forControlEvents:UIControlEventTouchUpInside];
    backButton.contentHorizontalAlignment =UIControlContentHorizontalAlignmentLeft;
    if ([NSString judgeStringExist:self.backImgName]) {
        self.navigationItem.leftBarButtonItem = [[UIBarButtonItem alloc] initWithCustomView:backButton];
        self.navigationItem.leftBarButtonItem.style = UIBarButtonItemStylePlain;
    }
}

- (void)setupNavigationBar
{
    
    
    UIImage *bgImg = [UIImage imageNamed:@"bg_white"];

    if(@available(iOS 15.0,*)){
        UINavigationBarAppearance * appearance = [[UINavigationBarAppearance alloc] init];
        [appearance setBackgroundImage:bgImg];
        [appearance setShadowImage:[[UIImage alloc] init]];
        [appearance setTitleTextAttributes:@{NSForegroundColorAttributeName:[UIColor blackColor],NSFontAttributeName:[UIFont systemFontOfSize:18 weight:UIFontWeightBold]}];
        self.navigationController.navigationBar.standardAppearance = appearance;
        self.navigationController.navigationBar.scrollEdgeAppearance = appearance;
    }else{
        [self.navigationController.navigationBar setBackgroundImage:bgImg forBarMetrics:UIBarMetricsDefault];
        [self.navigationController.navigationBar setTitleTextAttributes:@{NSForegroundColorAttributeName:[UIColor blackColor],NSFontAttributeName:[UIFont systemFontOfSize:18 weight:UIFontWeightBold]}];
        
        [self.navigationController.navigationBar setTintColor:UIColor.blackColor];
    }
    
}

- (UIImageView *)findHairlineImageViewUnder:(UIView *)view {
    if ([view isKindOfClass:UIImageView.class] && view.bounds.size.height <= 1.0) {
        return (UIImageView *)view;
    }
    for (UIView *subview in view.subviews) {
        UIImageView *imageView = [self findHairlineImageViewUnder:subview];
        if (imageView) {
            return imageView;
        }
    }
    return nil;
}


#pragma mark - Action
- (void)backAction:(id)sender
{
    [self.navigationController popViewControllerAnimated:YES];
}

- (void)popSelf {
    
    if (self.popToVc != nil) {
        if ([self.navigationController.viewControllers containsObject:self.popToVc]) {
            [self.navigationController popToViewController:self.popToVc animated:YES];
            return;
        }
    }
    
    if (self.popToVcClassName.length != 0) {
        for (id tempVC in self.navigationController.viewControllers) {
            if ([tempVC isKindOfClass:NSClassFromString(self.popToVcClassName)]) {
                UIViewController *destinationVC = (UIViewController *)tempVC;
                [self.navigationController popToViewController:destinationVC animated:YES];
                return;
            }
        }
    }
    
    [self.navigationController popViewControllerAnimated:YES];
}

#pragma mark - 指示器和文本弹框
- (void)showActivityHUD:(NSString *)text {
    [self hideHUDView];
    if ([text isKindOfClass:[NSNull class]]) {
        text = @"null";
    }
    dispatch_async(dispatch_get_main_queue(), ^{
        
        MBProgressHUD *hud = [self createImageHUD:self.view];
        hud.label.text = text;
        hud.margin = 30;
    });
}

- (void)hideHUDView {
    
    dispatch_async(dispatch_get_main_queue(), ^{
        [MBProgressHUD hideHUDForView:self.view animated:YES];
    });
}

- (void)textStateHUD:(NSString *)text {
    
    if ([text isKindOfClass:[NSNull class]]) {
        text = @"null";
    }
    
    [self.view makeToast:text duration:1.8 position:@"center"];
}

- (void)textStateHUD:(NSString *)text finishBlock:(void (^)(void))finishBlock {
    
    if ([text isKindOfClass:[NSNull class]]) {
        text = @"null";
    }
    
    dispatch_async(dispatch_get_main_queue(), ^{
        
        MBProgressHUD *hud = [self createTextHUD:self.view];
        
        [hud hideAnimated:YES afterDelay:1.8];
        hud.label.text = text;
        hud.completionBlock = finishBlock;
        
    });
}

 
- (MBProgressHUD *)createTextHUD:(UIView *)view {
    
    if (view == nil) view = (UIView *)[UIApplication sharedApplication].keyWindow;
    
    MBProgressHUD *hud = [MBProgressHUD showHUDAddedTo:view animated:YES];
    
    hud.label.numberOfLines = 0;
    hud.label.font = [UIFont systemFontOfSize:14.0];
    hud.contentColor = [UIColor whiteColor];
    hud.bezelView.color = [[UIColor blackColor] colorWithAlphaComponent:0.7];
    hud.bezelView.style = MBProgressHUDBackgroundStyleSolidColor;
    
    
    hud.removeFromSuperViewOnHide = YES;
    
    hud.mode = MBProgressHUDModeText;
    
    return hud;
}


 
- (MBProgressHUD *)createIndicatorHUD:(UIView *)view {
    
    if (view == nil) view = (UIView *)[UIApplication sharedApplication].keyWindow;
    
    MBProgressHUD *hud = [self createTextHUD:view];
    
    hud.mode = MBProgressHUDModeIndeterminate;
    hud.bezelView.style = MBProgressHUDBackgroundStyleSolidColor;

    return hud;
}

 
- (MBProgressHUD *)createImageHUD:(UIView *)view {
    
    if (view == nil) view = (UIView *)[UIApplication sharedApplication].keyWindow;
    
    MBProgressHUD *hud = [self createTextHUD:view];
    
    hud.mode = MBProgressHUDModeCustomView;
    hud.bezelView.style = MBProgressHUDBackgroundStyleSolidColor;

    NSString *path = [[NSBundle mainBundle] pathForResource:@"loading" ofType:@"gif"];
    NSData * data = [NSData dataWithContentsOfFile:path];
    UIImage *image = [UIImage sd_imageWithGIFData:data];
    SACustomHudImg *customV = [[SACustomHudImg alloc] initWithImage:image];
    hud.customView = customV;
    return hud;
}

@end
