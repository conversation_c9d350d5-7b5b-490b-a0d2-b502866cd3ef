
#import "SASubmitManager.h"

@implementation SASubmitManager
- (instancetype)init
{
    if (self = [super init])
    {
        self.validator = self;
    }
    
    return self;
}

#pragma mark - CTAPIManager
- (NSString *)methodName
{
    return @"b139bd3ecc6b4f7898cf84a2e72ee2b9";
}

- (NSString *)serviceType
{
    return kNPBasicServiceKey;
}

- (QLAPIManagerRequestType)requestType
{
    return QLAPIManagerRequestTypePost;
}

- (BOOL)shouldCache
{
    return NO;
}

#pragma mark - YJBAPIManagerDataReformer

- (id)manager:(SABaseManager *)manager reformData:(NSDictionary *)data
{
    if ([manager isKindOfClass:[self class]])
    {
        NSDictionary *dataDic  = [data objectForKey:@"data"];
        return dataDic;
    }
    return nil;
}

#pragma mark - YJBAPIManagerValidator
- (BOOL)manager:(SABaseManager *)manager isCorrectWithParamsData:(NSDictionary *)data
{
    return YES;
}

- (BOOL)manager:(SABaseManager *)manager isCorrectWithCallBackData:(id)data
{
    return [SAResponseHandle processRespDictCodeZero:data];
}
@end
