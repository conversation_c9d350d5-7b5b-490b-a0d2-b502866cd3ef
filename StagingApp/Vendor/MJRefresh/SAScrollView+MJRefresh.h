
#import <UIKit/UIKit.h>
#if __has_include(<MJRefresh/SARefreshConst.h>)
#import <MJRefresh/SARefreshConst.h>
#else
#import "SARefreshConst.h"
#endif

@class SARefreshHeader, SARefreshFooter, SARefreshTrailer;

NS_ASSUME_NONNULL_BEGIN

@interface UIScrollView (MJRefresh)
 
@property (strong, nonatomic, nullable) SARefreshHeader *mj_header;
@property (strong, nonatomic, nullable) SARefreshHeader *header MJRefreshDeprecated("使用mj_header");
 
@property (strong, nonatomic, nullable) SARefreshFooter *mj_footer;
@property (strong, nonatomic, nullable) SARefreshFooter *footer MJRefreshDeprecated("使用mj_footer");

 
@property (strong, nonatomic, nullable) SARefreshTrailer *mj_trailer;

#pragma mark - other
- (NSInteger)mj_totalDataCount;

@end

NS_ASSUME_NONNULL_END
