
#import "SAConfirmmBankViewController.h"
#import "SAGetMyCardsApi.h"
#import "SACardListCell.h"
#import "SACommonNoDataView.h"
#import "SAVerifyBankCardViewController.h"
#import <MJExtension.h>
#import "SAOrderConfirmApiManager.h"

@interface SAConfirmmBankViewController ()<QLAPIManagerCallBackDelegate, QLAPIManagerParamSource, NPRequestMethodProtocol, UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, strong) SAGetMyCardsApi *myCardApi;

@property (nonatomic, strong) SACommonNoDataView *noDataV;

@property (nonatomic, strong) UITableView *tableV;
@property (nonatomic, strong) NSArray *dataList;

@property (nonatomic, strong) UIButton *bindBtn;
@property (nonatomic, strong) UILabel *tipsLab;

@property (nonatomic, strong) SAOrderConfirmApiManager *orderConfirmApiMgr;

@end

@implementation SAConfirmmBankViewController

#pragma mark - Life cycle

- (void)viewDidLoad
{
    [super viewDidLoad];
    self.navigationItem.title = @"确认收款银行卡";
    
    [self createUI];
    
    [self showActivityHUD:nil];
    [self.myCardApi loadData];
}

- (void)createUI
{
    [self.view addSubview:self.tableV];
    [self.view addSubview:self.bindBtn];
    [self.view addSubview:self.tipsLab];
    
    [self.tableV mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.top.right.equalTo(self.view);
        make.bottom.equalTo(self.tipsLab.mas_top).offset(-10);
    }];
    [self.tipsLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.centerX.equalTo(self.view);
        make.bottom.equalTo(self.bindBtn.mas_top).offset(-8);
    }];
    [self.bindBtn mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.equalTo(self.view).offset(30);
        make.centerX.equalTo(self.view);
        make.bottom.equalTo(self.view).offset(-35);
        make.height.mas_equalTo(44);
    }];
    self.bindBtn.layer.masksToBounds = YES;
    self.bindBtn.layer.cornerRadius = 22;
    
    self.tipsLab.text = @"如需更换银行卡，请到我的-我的银行卡中操作";
}

#pragma mark - Method

- (void)confirmAction
{
    [self showActivityHUD:nil];
    [self.orderConfirmApiMgr loadData];
}

#pragma mark - QLAPIManagerCallBackDelegate

- (void)managerCallAPIDidSuccess:(SABaseManager *)manager
{
    [self hideHUDView];
    if (manager == self.myCardApi)
    {
        NSDictionary *dataDict = [manager fetchDataWithReformer:self.myCardApi];
        self.dataList = [SAMyBankItemModel mj_objectArrayWithKeyValuesArray: dataDict[@"bankRoList"]];
        
        if(!self.dataList || self.dataList.count == 0){
            [self.tableV addSubview: self.noDataV];
            [self.noDataV mas_makeConstraints:^(SAConstraintMaker *make) {
                make.width.height.mas_equalTo(64);
                make.centerX.centerY.equalTo(self.tableV);
                make.centerY.equalTo(self.tableV).offset(-80);
            }];
        }else{
            [self.noDataV removeFromSuperview];
        }
        [self.tableV reloadData];
    }
    
    if(manager ==  self.orderConfirmApiMgr)
    {
        [self popSelf];
    }
}

- (void)managerCallAPIDidFailed:(SABaseManager *)manager
{
    [self hideHUDView];
    
    NSString *failMsg  = [SAResponseHandle netWorkErrorMsg:[manager fetchDataWithReformer:nil] andErrorType:manager.errorType];
    [self textStateHUD:failMsg];
}

#pragma mark - QLAPIManagerParamSource

- (NSDictionary *)paramsForApi:(SABaseManager *)manager
{
    if (manager == self.orderConfirmApiMgr){
        return @{@"productCode": @""};
    }
    return [NSDictionary dictionary];
}

- (NSString *)methodForApi:(SABaseManager *)manager
{
    if(manager == self.myCardApi){
//        return @"pay/bindCardList/1";
        return @"d90057463da140068619ac898222b955/1";
    }
    return @"";
}

#pragma mark - UITableViewDataSource

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return self.dataList.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    static NSString *repayCID = @"NPCardListCell-Cell";
    SACardListCell *cell = [tableView dequeueReusableCellWithIdentifier:repayCID];
    if (cell == nil) {
        cell = [[SACardListCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:repayCID];
    }
    cell.backgroundColor = [UIColor whiteColor];
    
    SAMyBankItemModel *model = self.dataList[indexPath.row];
    cell.model = model;
    
    return  cell;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath
{
    return 125;
}

#pragma mark - getter

- (SAGetMyCardsApi *)myCardApi{
    if(_myCardApi == nil){
        _myCardApi = [SAGetMyCardsApi new];
        _myCardApi.delegate = self;
        _myCardApi.paramSource = self;
        _myCardApi.methodSource = self;
    }
    return _myCardApi;
}

- (UITableView *)tableV {
    if (_tableV == nil) {
        _tableV = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
        _tableV.dataSource      = self;
        _tableV.delegate        = self;
        if (@available(iOS 11.0, *)) {
            _tableV.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
        }
        _tableV.backgroundColor = [UIColor clearColor];
        _tableV.separatorStyle = UITableViewCellSeparatorStyleNone;
    }
    return _tableV;
}

- (NSArray *)dataList{
    if (_dataList == nil) {
        _dataList = [NSArray array];
    }
    return _dataList;
}

- (SACommonNoDataView *)noDataV {
    if (_noDataV == nil) {
        _noDataV = [[SACommonNoDataView alloc] initWithImg:@"noData_icon" title:@"暂无绑定的银行卡"];
    }
    return _noDataV;
}

- (UIButton *)bindBtn{
    if (_bindBtn == nil) {
        _bindBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_bindBtn addTarget:self action:@selector(confirmAction) forControlEvents:UIControlEventTouchUpInside];
        [_bindBtn setTitle:@"确认" forState:UIControlStateNormal];
        _bindBtn.backgroundColor = kPrimaryColor;
        [_bindBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    }
    
    return _bindBtn;
}

- (UILabel *)tipsLab{
    FF_Fetch_UILable_CenterX(_tipsLab, [UIColor colorWithHex:0x999999], Font_XX6(13))
}

- (SAOrderConfirmApiManager *)orderConfirmApiMgr {
    if (_orderConfirmApiMgr == nil) {
        _orderConfirmApiMgr = [[SAOrderConfirmApiManager alloc] init];
        _orderConfirmApiMgr.delegate = self;
        _orderConfirmApiMgr.paramSource = self;
    }
    return _orderConfirmApiMgr;
}

@end
