
#import "SARequest+QLNetworkingMethods.h"
#import <objc/runtime.h>

static void *SANetworkingRequestParams;

@implementation NSURLRequest (SANetworkingMethods)

- (void)setRequestParams:(NSDictionary *)requestParams
{
    objc_setAssociatedObject(self, &SANetworkingRequestParams, requestParams, OBJC_ASSOCIATION_COPY);
}

- (NSDictionary *)requestParams
{
    return objc_getAssociatedObject(self, &SANetworkingRequestParams);
}

@end
