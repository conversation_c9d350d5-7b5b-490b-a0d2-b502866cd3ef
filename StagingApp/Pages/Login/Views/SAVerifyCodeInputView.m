
#import "SAVerifyCodeInputView.h"

@interface SAVerifyCodeInputView ()<UITextFieldDelegate>

@property (nonatomic, strong)UITextField *textField;
@property (nonatomic, strong)UIButton *receiveBtn;      
@property (nonatomic, strong)NSMutableArray <SACursorLabel *>*labels;

@property (nonatomic, weak) SACursorLabel *currentLabel;

@end

@implementation SAVerifyCodeInputView

- (instancetype)init {
    if (self = [super init]) {
        [self addCustomViews];
    }
    return self;
}

- (void)addCustomViews {
    [self addSubview:self.textField];
    [self addSubview:self.receiveBtn];
    for (int i = 0; i < 6; i++) {
        SACursorLabel *lab = [[SACursorLabel alloc] init];
        lab.textAlignment = NSTextAlignmentCenter;
        lab.textColor = [UIColor colorWithHex:0x5EA1FB];
        lab.font = [UIFont fontSizeOfXX_6:17];
        lab.layer.borderColor = [UIColor colorWithHex:0xE3E3E3].CGColor;
        lab.layer.borderWidth = XX_6(1.0);
        lab.layer.cornerRadius = XX_6(5);
        lab.layer.masksToBounds = YES;
        [self addSubview:lab];
        [self.labels addObject:lab];
        
        CGFloat wid = XX_6(40);
        CGFloat heig = XX_6(50);
        CGFloat margin = XX_6(52);
        lab.frame = CGRectMake(margin * i + XX_6(38), 0, wid, heig);
    }
}

- (void)layoutSubviews {
    [super layoutSubviews];
    self.textField.frame = self.bounds;
    self.receiveBtn.frame = self.bounds;
}

#pragma mark - UITextFieldDelegate




#pragma mark - Actions
- (void)textFieldEditingChanged:(UITextField *)field {
    if (field.text.length > 6) {
        field.text = [field.text substringWithRange:NSMakeRange(0, 6)];
    }

    for (int i = 0; i < 6; i++) {
        
        SACursorLabel *label = [self.labels objectAtIndex:i];

        if (i < field.text.length) {
            label.text = [field.text substringWithRange:NSMakeRange(i, 1)];
            
        } else {
            label.text = nil;
        }
    }
    
    [self cursor];

    
    if (field.text.length >= 6) {
        [self.currentLabel stopAnimating];
        [field resignFirstResponder];
    }
}

- (void)textFieldEditingEnd:(UITextField *)textField {
    [self.currentLabel stopAnimating];
}

- (void)textFieldEditingBegin:(UITextField *)textField {
    [self cursor];
}


- (void)clickReceiveBtn:(UIButton *)btn {
    [self.textField becomeFirstResponder];
}

#pragma mark 处理光标
- (void)cursor
{
    [self.currentLabel stopAnimating];
    
    NSInteger index = self.inputStr.length;
    if (index < 0) index = 0;
    if (index >= self.labels.count) index = self.labels.count - 1;
    
    SACursorLabel *label = [self.labels objectAtIndex:index];
    
    [label startAnimating];
    self.currentLabel = label;
}

- (void)becomeFocused {
    [self.textField becomeFirstResponder];
}

#pragma mark - getters
- (UITextField *)textField {
    if (_textField == nil) {
        _textField = [[UITextField alloc] init];
        _textField.delegate = self;
        _textField.font = [UIFont fontSizeOfXX_6:16];
        _textField.keyboardType = UIKeyboardTypeNumberPad;
        _textField.autocapitalizationType = UITextAutocapitalizationTypeNone;
        [_textField addTarget:self action:@selector(textFieldEditingChanged:) forControlEvents:UIControlEventEditingChanged];
        [_textField addTarget:self action:@selector(textFieldEditingEnd:) forControlEvents:UIControlEventEditingDidEnd];
        [_textField addTarget:self action:@selector(textFieldEditingBegin:) forControlEvents:UIControlEventEditingDidBegin];
        
    }
    return _textField;
}

- (UIButton *)receiveBtn {
    if (_receiveBtn == nil) {
        _receiveBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        _receiveBtn.backgroundColor = [UIColor colorWithHex:0xffffff];
        [_receiveBtn addTarget:self action:@selector(clickReceiveBtn:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _receiveBtn;
}

- (NSMutableArray *)labels {
    if (_labels == nil) {
        _labels = [NSMutableArray arrayWithCapacity:6];
    }
    return _labels;
}

- (NSString *)inputStr {
    return self.textField.text;
}

@end






@implementation SACursorLabel

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        [self setupView];
    }
    return self;
}

- (instancetype)initWithCoder:(NSCoder *)coder
{
    self = [super initWithCoder:coder];
    if (self) {
        [self setupView];
    }
    return self;
}

#pragma mark - 初始化View
- (void)setupView
{
    UIView *cursorView = [[UIView alloc] init];
    cursorView.backgroundColor = [UIColor colorWithHex:0x5EA1FB];
    cursorView.alpha = 0;
    [self addSubview:cursorView];
    _cursorView = cursorView;
}

- (void)layoutSubviews
{
    [super layoutSubviews];
    
    CGFloat h = XX_6(30);
    CGFloat w = 2;
    CGFloat x = self.width * 0.5;
    CGFloat y = self.height * 0.5;
    self.cursorView.frame = CGRectMake(0, 0, w, h);
    self.cursorView.center = CGPointMake(x, y);
}

- (void)startAnimating
{
    if (self.text.length > 0) return;
    
    CABasicAnimation *oa = [CABasicAnimation animationWithKeyPath:@"opacity"];
    oa.fromValue = [NSNumber numberWithFloat:0];
    oa.toValue = [NSNumber numberWithFloat:1];
    oa.duration = 1;
    oa.repeatCount = MAXFLOAT;
    oa.removedOnCompletion = NO;
    oa.fillMode = kCAFillModeForwards;
    oa.timingFunction = [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionEaseIn];
    [self.cursorView.layer addAnimation:oa forKey:@"opacity"];
    
    self.layer.borderColor = [UIColor colorWithHex:0x5EA1FB].CGColor;
}

- (void)stopAnimating
{
    [self.cursorView.layer removeAnimationForKey:@"opacity"];
    self.layer.borderColor = [UIColor colorWithHex:0xE3E3E3].CGColor;
}

@end
