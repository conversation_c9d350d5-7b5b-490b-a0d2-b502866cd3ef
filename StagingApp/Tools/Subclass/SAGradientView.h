

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface SAGradientView : UIView

 
@property(nonatomic, strong, nullable) NSArray <UIColor *>*colors;

 
@property(nonatomic, strong, nullable) NSArray<NSNumber *> *locations;

 
@property (nonatomic, assign)CGPoint startPoint;
@property (nonatomic, assign)CGPoint endPoint;

- (void)gradientBackgroundColors:(NSArray<UIColor *> *_Nullable)colors locations:(NSArray<NSNumber *> *_Nullable)locations startPoint:(CGPoint)startPoint endPoint:(CGPoint)endPoint;

 
- (void)clearGradient;

@end

@interface SAGradientLabel : UILabel

 
@property(nonatomic, strong, nullable) NSArray <UIColor *>*colors;

 
@property(nonatomic, strong, nullable) NSArray<NSNumber *> *locations;

 
@property (nonatomic, assign)CGPoint startPoint;
@property (nonatomic, assign)CGPoint endPoint;

- (void)gradientBackgroundColors:(NSArray<UIColor *> *_Nullable)colors locations:(NSArray<NSNumber *> *_Nullable)locations startPoint:(CGPoint)startPoint endPoint:(CGPoint)endPoint;

 
- (void)clearGradient;

@end

@interface SAGradientButton : UIButton

 
@property(nonatomic, strong, nullable) NSArray <UIColor *>*colors;

 
@property(nonatomic, strong, nullable) NSArray<NSNumber *> *locations;

 
@property (nonatomic, assign)CGPoint startPoint;
@property (nonatomic, assign)CGPoint endPoint;

- (void)gradientBackgroundColors:(NSArray<UIColor *> *_Nullable)colors locations:(NSArray<NSNumber *> *_Nullable)locations startPoint:(CGPoint)startPoint endPoint:(CGPoint)endPoint;

 
- (void)clearGradient;

@end

NS_ASSUME_NONNULL_END
