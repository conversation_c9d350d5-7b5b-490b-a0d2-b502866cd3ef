
#import "NPInviteAlertViewController.h"
#import "SAGradientView.h"

#define kSureBtnHeight  44
#define kInviteAlertHeight   420

@interface NPInviteAlertViewController ()


@property (nonatomic, strong)UIView *maskView;
@property (nonatomic, strong)SAGradientView *alertView;

@property (nonatomic, strong) UILabel *msgLab;
@property (nonatomic, strong) UIImageView *paobuLogo;
@property (nonatomic, strong) UILabel *tipsLab;
@property (nonatomic, strong) UIImageView *tipsImg;
@property (nonatomic, strong) UILabel *amountLab;

@property (nonatomic, strong)SAGradientButton *sureBtn;
@property (nonatomic, strong)SAGradientButton *cancelBtn;

@property (nonatomic, copy) SureBlock sureBlock;
@property (nonatomic, strong) NSString *amount;

@end

static UIWindow *alertWindow;

@implementation NPInviteAlertViewController

#pragma mark - Views Network

- (void)createViews
{
    [self.view addSubview:self.maskView];
    [self.maskView addSubview:self.alertView];
    
    [self.alertView addSubview:self.msgLab];
    [self.alertView addSubview:self.tipsLab];
    [self.alertView addSubview:self.tipsImg];
    [self.alertView addSubview:self.amountLab];
    [self.alertView addSubview:self.sureBtn];
    [self.alertView addSubview:self.cancelBtn];
    
    [self.maskView addSubview:self.paobuLogo];
    [self.maskView bringSubviewToFront:self.paobuLogo];
}

- (void)renderView
{
    [self createViews];
    
    self.maskView.frame = CGRectMake(0, 0, UIScreenWidth, UIScreenHeight);
    
    self.alertView.frame = CGRectMake(0, UIScreenHeight-kInviteAlertHeight, UIScreenWidth, kInviteAlertHeight);
    
    UIBezierPath *maskPath = [UIBezierPath
        bezierPathWithRoundedRect:self.alertView.bounds
        byRoundingCorners:(UIRectCornerTopLeft | UIRectCornerTopRight)
        cornerRadii:CGSizeMake(12, 12)
    ];
    CAShapeLayer *maskLayer = [CAShapeLayer layer];
    maskLayer.frame = self.alertView.bounds;
    maskLayer.path = maskPath.CGPath;
    self.alertView.layer.mask = maskLayer;

    
    [self.msgLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.equalTo(self.alertView).offset(30);
        make.top.equalTo(self.alertView).offset(34);
    }];
    
    [self.paobuLogo mas_makeConstraints:^(SAConstraintMaker *make) {
        make.right.equalTo(self.alertView).offset(-10);
        make.top.equalTo(self.alertView).offset(-30);
        make.width.height.mas_equalTo(150);
    }];
    
    [self.tipsLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.equalTo(self.msgLab);
        make.top.equalTo(self.msgLab.mas_bottom).offset(50);
    }];
    
    [self.tipsImg mas_makeConstraints:^(SAConstraintMaker *make) {
        make.centerY.equalTo(self.tipsLab);
        make.left.equalTo(self.tipsLab.mas_right).offset(5);
        make.width.height.mas_equalTo(20);
    }];
    
    [self.amountLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.equalTo(self.msgLab);
        make.top.equalTo(self.tipsLab.mas_bottom).offset(5);
    }];
    
    [self.sureBtn mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.equalTo(self.msgLab);
        make.top.equalTo(self.amountLab.mas_bottom).offset(66);
        make.centerX.equalTo(self.alertView);
        make.height.mas_equalTo(kSureBtnHeight);
    }];
    self.sureBtn.layer.cornerRadius = 7;
    self.sureBtn.layer.masksToBounds = YES;
    
    [self.cancelBtn mas_makeConstraints:^(SAConstraintMaker *make) {
        make.centerX.equalTo(self.alertView);
        make.height.equalTo(self.sureBtn);
        make.top.equalTo(self.sureBtn.mas_bottom).offset(5);
    }];
    

    self.msgLab.text = [NSString stringWithFormat:@"欢迎来到%@!\n现在即可申请额度", [[NSBundle mainBundle] objectForInfoDictionaryKey:@"CFBundleDisplayName"]];
    self.tipsLab.text = @"预估可借(元)";
    self.amountLab.text = self.amount;
    
}

#pragma mark - show、present、dismiss
+ (instancetype)showAmount:(NSString *)amount sureAction:(SureBlock __nullable)sure
{
    NPInviteAlertViewController *alertVc = [[NPInviteAlertViewController alloc] init];
    alertVc.sureBlock = sure;
    alertVc.amount = amount;
    [alertVc createViews];
    [alertVc renderView];
    [alertVc presentFromView];
    
    return alertVc;
}

- (void)presentFromView 
{
    if (alertWindow == nil) {
        alertWindow = [[UIWindow alloc] initWithFrame:[UIScreen mainScreen].bounds];
        alertWindow.backgroundColor = [UIColor clearColor];
        alertWindow.windowLevel = UIWindowLevelAlert - 1;
    }
    
    alertWindow.rootViewController = self;
    
    self.alertView.transform = CGAffineTransformMakeScale(1.1, 1.1);
    self.maskView.alpha = 0.0;
    [UIView animateWithDuration:0.3 animations:^{
        self.alertView.transform = CGAffineTransformMakeScale(1.0, 1.0);
        self.maskView.alpha = 1.0;
    }];
    
    [alertWindow makeKeyAndVisible];
}
 
- (void)dismissUpdateAlert {
    
    [SATool hideWindowHUD];
    [UIView animateWithDuration:0.3 animations:^{
        self.maskView.alpha = 0.0;
        [self.maskView removeFromSuperview];
        alertWindow.hidden = YES;
        alertWindow = nil;
    }];
}

- (void)clickSureButton:(UIButton *)btn 
{
    [self dismissUpdateAlert];
    
    if (self.sureBlock) {
        self.sureBlock();
    }
}

- (void)clickCancelButton
{
    [self dismissUpdateAlert];
}

#pragma mark - getters
- (UIView *)maskView {
    if (_maskView == nil) {
        
        _maskView = [[UIView alloc] init];
        _maskView.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.6];
        _maskView.alpha = 0.0;
//        
//        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(dismissUpdateAlert)];
//        _maskView.userInteractionEnabled = YES;
//        [_maskView addGestureRecognizer:tap];
    }
    return _maskView;
}

- (SAGradientView *)alertView {
    if (_alertView == nil) {
        _alertView = [[SAGradientView alloc] init];
        [_alertView gradientBackgroundColors:@[kMinorColor, [UIColor whiteColor]] locations:nil startPoint:CGPointMake(0.5, 0) endPoint:CGPointMake(0.5, 0.6)];
    }
    return _alertView;
}

- (SAGradientButton *)sureBtn {
    if (_sureBtn == nil) {
        _sureBtn = [SAGradientButton buttonWithType:UIButtonTypeCustom];
        [_sureBtn gradientBackgroundColors:@[kButtonStartColor, kButtonEndColor] locations:nil startPoint:CGPointMake(0, 0.5) endPoint:CGPointMake(1, 0.5)];
        [_sureBtn setTitle:@"立即申请" forState:UIControlStateNormal];
        [_sureBtn setTitleColor:kHomeTextColor forState:UIControlStateNormal];
        _sureBtn.titleLabel.font = [UIFont fontSizeOfXX_6:15];
        _sureBtn.titleLabel.textAlignment = NSTextAlignmentCenter;
        [_sureBtn addTarget:self action:@selector(clickSureButton:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _sureBtn;
}

- (SAGradientButton *)cancelBtn {
    if (_cancelBtn == nil) {
        _cancelBtn = [SAGradientButton buttonWithType:UIButtonTypeCustom];
        [_cancelBtn setTitle:@"暂不申请，先逛逛" forState:UIControlStateNormal];
        [_cancelBtn setTitleColor:[UIColor colorWithHex:0x777777] forState:UIControlStateNormal];
        _cancelBtn.titleLabel.font = [UIFont fontSizeOfXX_6:13];
        _cancelBtn.titleLabel.textAlignment = NSTextAlignmentCenter;
        [_cancelBtn addTarget:self action:@selector(clickCancelButton) forControlEvents:UIControlEventTouchUpInside];
    }
    return _cancelBtn;
}

- (UILabel *)msgLab {
    if (_msgLab == nil) {
        _msgLab = [[UILabel alloc] init];
        _msgLab.font = Font_XX6(15);
        _msgLab.textColor = [UIColor colorWithHex:0x333333];
        _msgLab.numberOfLines = 0;
    }
    return _msgLab;
}

- (UIImageView *)paobuLogo{
    FF_Fetch_UIImageViewWithImage(_paobuLogo, [UIImage imageNamed:@"paobu"])
}

- (UILabel *)tipsLab{
    FF_Fetch_UILable(_tipsLab, [UIColor colorWithHex:0x999999], Font_XX6(12))
}

- (UIImageView *)tipsImg{
    FF_Fetch_UIImageViewWithImage(_tipsImg, [UIImage imageNamed:@"gantanhao_grey_small"])
}

- (UILabel *)amountLab{
    FF_Fetch_UILable(_amountLab, [UIColor blackColor], BoldFont_XX6(42))
}

@end

