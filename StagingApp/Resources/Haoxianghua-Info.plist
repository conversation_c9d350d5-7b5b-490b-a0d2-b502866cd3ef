<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleAllowMixedLocalizations</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>豪享花</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array/>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>fbapi</string>
		<string>fb-messenger-share-api</string>
		<string>fbauth2</string>
		<string>fbshareextension</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
		<key>NSExceptionDomains</key>
		<dict/>
	</dict>
	<key>NSCameraUsageDescription</key>
	<string>The app wants to access your camera/album to take/get ID photos</string>
	<key>NSContactsUsageDescription</key>
	<string>The app wants to access your address book in order to review your borrowing eligibility and improve your pass rate</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>App want to access your location to review your borrowing eligibility and improve your pass rate</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>App want to access your location to review your borrowing eligibility and improve your pass rate</string>
	<key>NSLocationUsageDescription</key>
	<string>App want to access your location to review your borrowing eligibility and improve your pass rate</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>App want to access your location to review your borrowing eligibility and improve your pass rate</string>
	<key>NSMotionUsageDescription</key>
	<string>The app requires your consent to access your sports data.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>The app wants to access your camera/album to take/get ID photos</string>
	<key>NSUserTrackingUsageDescription</key>
	<string>This identifier will be used to deliver personalized advertisements to you.</string>
	<key>App Privacy - Microphone Usage Description</key>
	<string>请同意应用获取麦克风权限</string>
	<key>UIAppFonts</key>
	<array>
		<string>AntDesign.ttf</string>
		<string>Entypo.ttf</string>
		<string>EvilIcons.ttf</string>
		<string>Feather.ttf</string>
		<string>FontAwesome.ttf</string>
		<string>FontAwesome5_Brands.ttf</string>
		<string>FontAwesome5_Regular.ttf</string>
		<string>FontAwesome5_Solid.ttf</string>
		<string>Foundation.ttf</string>
		<string>Ionicons.ttf</string>
		<string>MaterialCommunityIcons.ttf</string>
		<string>MaterialIcons.ttf</string>
		<string>Octicons.ttf</string>
		<string>Roboto_medium.ttf</string>
		<string>Roboto.ttf</string>
		<string>rubicon-icon-font.ttf</string>
		<string>SimpleLineIcons.ttf</string>
		<string>Zocial.ttf</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen.storyboard</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UIUserInterfaceStyle</key>
	<string>Light</string>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<true/>
</dict>
</plist>
