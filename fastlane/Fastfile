
default_platform(:ios)

platform :ios do
  desc "正在打包中。。。"
  lane :package do
    # add actions here: https://docs.fastlane.tools/actions
    
    #01
    gym(
      # 每次打包之前clean一下
      clean: true,
      output_directory: './fastlane/release',
      output_name: 'Xiandaihua.ipa',
      scheme: "Xiandaihua",
      configuration: 'Release',
      include_bitcode: false,
      include_symbols: false,
      export_method: 'ad-hoc',
      # export_method: 'app-store',
      export_options: {
        provisioningProfiles: { 
          # "com.Rating.AnimatedFace" => "Rating AnimatedFace Store",
          "com.fenqi.xiandaihua" => "XiandaihuaAdhoc",
        }
      }
    )

    #02
    gym(
      # 每次打包之前clean一下
      clean: true,
      output_directory: './fastlane/release',
      output_name: 'Haoxianghua.ipa',
      scheme: "Haoxianghua",
      configuration: 'Release',
      include_bitcode: false,
      include_symbols: false,
      export_method: 'ad-hoc',
      export_options: {
        provisioningProfiles: { 
          "com.fenqi.haoxianghua" => "HaoxianghuaAdhoc",
        }
      }
    )

    
    notification(app_icon: './fastlane/icon.png', title: 'manager', subtitle: '已导出安装包', message: '打包成功')


  end


end
