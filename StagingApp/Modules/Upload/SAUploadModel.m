
#import "SAUploadModel.h"
#import <MJExtension.h>

NSString * const endPoint                   = @"https://oss-cn-hangzhou.aliyuncs.com";
NSString * const kFileURL                   = @"fileURL";
NSString * const kTag                       = @"tag";
NSString * const kRelativeUrl               = @"relativeUrl";

@implementation SAUploadModel

+ (instancetype)objectWithDict:(NSDictionary *)dict {
    return [self mj_objectWithKeyValues:dict];
}

@end
