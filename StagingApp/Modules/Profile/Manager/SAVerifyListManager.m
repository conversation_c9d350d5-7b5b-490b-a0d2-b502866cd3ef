

#import "SAVerifyListManager.h"
#import "SAVerifyBasicInfoViewController.h"
#import "SAVerifyContactsViewController.h"
#import "SAVerifyIdentityViewController.h"
#import "SAVerifyIdentityViewController.h"
#import "SAVerifyBankCardViewController.h"
#import "SAConfirmmBankViewController.h"

#import "SAVerifyDataListApiManager.h"
#import "SAVerifyListModel.h"
#import "SAGetCarrierUrlApiManager.h"
#import "SACommonWebViewController.h"

static NSString * const ContactsPeople  = @"APP/PROFILE/CONTACT_INFO";

static NSString * const PersonInfo      = @"APP/PROFILE/PERSONAL_INFO";
    
static NSString * const OCRVerify       = @"APP/PROFILE/FACE_OCR";

static NSString * const Carrier         = @"APP/PROFILE/CARRIER";

static NSString * const BindCardCode    = @"APP/PROFILE/BIND_CARD_CODE";

@interface SAVerifyListManager ()<QLAPIManagerCallBackDelegate, QLAPIManagerParamSource>

@property (nonatomic, strong) SAVerifyDataListApiManager *verifyListApiMgr;
@property (nonatomic, strong) SAVerifyListModel *listModel;

@property (nonatomic, strong) SAGetCarrierUrlApiManager *carrierManager;

@end

@implementation SAVerifyListManager

+ (instancetype)sharedInstance
{
    static dispatch_once_t once;
    static id instance;
    dispatch_once(&once, ^{
        instance = [self new];
    });
    return instance;
}

- (void)jumpAuthPageWithUrl:(NSString * _Nullable)urlStr topTip:(NSString *)topTip
{
    if (urlStr == nil) {
        return;
    }
    
    if ([urlStr isEqualToString:Carrier]) {
        [SATool showWindowHUD:nil];
        [self.carrierManager loadData];
        return;
    }
    
    if ([urlStr isEqualToString:ContactsPeople]) {
        SAVerifyContactsViewController *vc = [SAVerifyContactsViewController new];
        vc.hidesBottomBarWhenPushed = YES;
        vc.popToVcClassName = self.popVCName;
        [[SACommonTool currentViewController].navigationController pushViewController:vc animated:NO];
        return;
    }
    
    
    if ([urlStr isEqualToString:BindCardCode]) {
        SAVerifyBankCardViewController *vc = [SAVerifyBankCardViewController new];
        vc.isFirstBind = YES;
        vc.hidesBottomBarWhenPushed = YES;
        vc.popToVcClassName = self.popVCName;
        [[SACommonTool currentViewController].navigationController pushViewController:vc animated:NO];
        return;
    }
    
    if ([urlStr isEqualToString:PersonInfo]) {
        SAVerifyBasicInfoViewController *vc = [SAVerifyBasicInfoViewController new];
        vc.topTips = topTip;
        vc.hidesBottomBarWhenPushed = YES;
        vc.popToVcClassName = self.popVCName;
        [[SACommonTool currentViewController].navigationController pushViewController:vc animated:NO];
        return;
    }
    
    if ([urlStr isEqualToString:OCRVerify]) {
        SAVerifyIdentityViewController *vc = [SAVerifyIdentityViewController new];
        vc.hidesBottomBarWhenPushed = YES;
        vc.popToVcClassName = self.popVCName;
        [[SACommonTool currentViewController].navigationController pushViewController:vc animated:NO];
        return;
    }
}

- (void)handleVerifyJump
{
    [SATool showWindowHUD:nil];
    [self.verifyListApiMgr loadData];
}

#pragma mark QLAPIManagerCallBackDelegate
- (void)managerCallAPIDidSuccess:(SABaseManager *)manager
{
    [SATool hideWindowHUD];
    
    if (manager == self.verifyListApiMgr) 
    {
        NSDictionary *dataDict = [manager fetchDataWithReformer:self.verifyListApiMgr];
        self.listModel = [SAVerifyListModel objectWithDict:dataDict];
        
        if(![NSString judgeStringExist:self.listModel.nextJumpUrl])
        {
            [[SACommonTool currentViewController].navigationController popToRootViewControllerAnimated:YES];
        }
        else
        {
            __block NSString *topTips = @"";
            [self.listModel.authItems  enumerateObjectsUsingBlock:^(SAVerifyListItemModel *item, NSUInteger idx, BOOL * _Nonnull stop) {
                if([item.jumpUrl isEqualToString:self.listModel.nextJumpUrl]){
                    topTips = item.headerTip;
                }
            }];
            [[SAVerifyListManager sharedInstance] jumpAuthPageWithUrl:self.listModel.nextJumpUrl topTip:topTips];
        }
    }
    
    if(manager == self.carrierManager)
    {
        NSDictionary *dataDict = [manager fetchDataWithReformer:self.carrierManager];
        NSLog(@"url ---%@", dataDict);
        
        NSString *url = dataDict[@"certUrl"];
        if (![NSString judgeStringExist:url]) return;
        
        SACommonWebViewController *webVC = [[SACommonWebViewController alloc] init];
        webVC.url = url;
        webVC.fromCarrier = YES;
        webVC.navTitle = @"运营商认证";
        webVC.hidesBottomBarWhenPushed = YES;
        [[SACommonTool currentViewController].navigationController pushViewController:webVC animated:NO];
    }
}

- (void)managerCallAPIDidFailed:(SABaseManager *)manager
{
    [SATool hideWindowHUD];

    NSString *failMsg  = [SAResponseHandle netWorkErrorMsg:[manager fetchDataWithReformer:nil] andErrorType:manager.errorType];
    [SATool textStateHUD:failMsg];
}


#pragma mark QLAPIManagerParamSource
- (NSDictionary *)paramsForApi:(SABaseManager *)manager 
{
    return [NSDictionary dictionary];
}

#pragma mark - getters

- (SAVerifyDataListApiManager *)verifyListApiMgr {
    if (_verifyListApiMgr == nil) {
        _verifyListApiMgr = [[SAVerifyDataListApiManager alloc] init];
        _verifyListApiMgr.delegate = self;
        _verifyListApiMgr.paramSource = self;
    }
    
    return _verifyListApiMgr;
}

- (SAGetCarrierUrlApiManager *)carrierManager{
    if(_carrierManager == nil){
        _carrierManager = [SAGetCarrierUrlApiManager new];
        _carrierManager.delegate = self;
        _carrierManager.paramSource = self;
    }
    return _carrierManager;
}

@end
