/* 
  InfoPlist.strings
  ToyStore

  Created by <PERSON> on 2022/6/13.
  Copyright © 2022 Facebook. All rights reserved.
*/

{
    "NSCameraUsageDescription"="The app wants to access your camera/album to take/get ID photos";
    "NSContactsUsageDescription"="The app wants to access your address book in order to review your borrowing eligibility and improve your pass rate";
    "NSLocationAlwaysAndWhenInUseUsageDescription"="App want to access your location to review your borrowing eligibility and improve your pass rate";
    "NSLocationAlwaysUsageDescription"="App want to access your location to review your borrowing eligibility and improve your pass rate";
    "NSLocationUsageDescription"="App want to access your location to review your borrowing eligibility and improve your pass rate";
    "NSLocationWhenInUseUsageDescription"="App want to access your location to review your borrowing eligibility and improve your pass rate";
    "NSMotionUsageDescription"="The app requires your consent to access your sports data.";
    "NSPhotoLibraryUsageDescription"="The app wants to access your camera/album to take/get ID photos";
    "NSUserTrackingUsageDescription"="This identifier will be used to deliver personalized advertisements to you.";
}
