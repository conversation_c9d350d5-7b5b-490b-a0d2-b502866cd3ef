
#import <Foundation/Foundation.h>

#import <MJExtension.h>

NS_ASSUME_NONNULL_BEGIN

@interface SAHistoryOrderModel : NSObject

@property (nonatomic, strong) NSString *tradeNo;
@property (nonatomic, strong) NSString *repaymentDate;
@property (nonatomic, strong) NSString *orderAmount;
@property (nonatomic, strong) NSString *repaymentAmount;
@property (nonatomic, assign) NSInteger borrowDuration;



@property (nonatomic, copy)NSString *createDt;
@property (nonatomic, copy)NSString *orderNo;
@property (nonatomic, copy)NSString *realCapital;

+ (instancetype)objectWithDict:(NSDictionary *)dict;

@end

NS_ASSUME_NONNULL_END
