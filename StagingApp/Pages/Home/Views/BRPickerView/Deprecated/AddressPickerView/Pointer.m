
#import "Pointer.h"
#import "SABundle+BRPickerView.h"

@interface Pointer ()<UIPickerViewDataSource, UIPickerViewDelegate>
@property (nonatomic, strong) UIPickerView *pickerView;
@property(nonatomic, copy) NSArray *provinceModelArr;
@property(nonatomic, copy) NSArray *cityModelArr;
@property(nonatomic, copy) NSArray *areaModelArr;
@property(nonatomic, strong) SAProvinceModel *selectProvinceModel;
@property(nonatomic, strong) SACityModel *selectCityModel;
@property(nonatomic, strong) SAAreaModel *selectAreaModel;
@property(nonatomic, assign) NSInteger provinceIndex;
@property(nonatomic, assign) NSInteger cityIndex;
@property(nonatomic, assign) NSInteger areaIndex;

@property(nonatomic, assign) NSInteger rollingComponent;
@property(nonatomic, assign) NSInteger rollingRow;

@property (nonatomic, copy) NSArray <NSString *>* mSelectValues;

@end

@implementation Pointer

#pragma mark - 1.显示地址选择器
+ (void)showAddressPickerWithSelectIndexs:(NSArray <NSNumber *>*)selectIndexs
                              resultBlock:(BRAddressResultBlock)resultBlock {
    [self showAddressPickerWithMode:BRAddressPickerModeArea dataSource:nil selectIndexs:selectIndexs isAutoSelect:NO resultBlock:resultBlock];
}

#pragma mark - 2.显示地址选择器
+ (void)showAddressPickerWithMode:(BRAddressPickerMode)mode
                     selectIndexs:(NSArray <NSNumber *>*)selectIndexs
                     isAutoSelect:(BOOL)isAutoSelect
                      resultBlock:(BRAddressResultBlock)resultBlock {
    [self showAddressPickerWithMode:mode dataSource:nil selectIndexs:selectIndexs isAutoSelect:isAutoSelect resultBlock:resultBlock];
}


#pragma mark - 3.显示地址选择器
+ (void)showAddressPickerWithMode:(BRAddressPickerMode)mode
                       dataSource:(NSArray *)dataSource
                     selectIndexs:(NSArray <NSNumber *>*)selectIndexs
                     isAutoSelect:(BOOL)isAutoSelect
                      resultBlock:(BRAddressResultBlock)resultBlock {
    
    Pointer *addressPickerView = [[Pointer alloc] initWithPickerMode:mode];
    addressPickerView.dataSourceArr = dataSource;
    addressPickerView.selectIndexs = selectIndexs;
    addressPickerView.isAutoSelect = isAutoSelect;
    addressPickerView.resultBlock = resultBlock;
    
    [addressPickerView show];
}

#pragma mark - 初始化地址选择器
- (instancetype)initWithPickerMode:(BRAddressPickerMode)pickerMode {
    if (self = [super init]) {
        self.pickerMode = pickerMode;
    }
    return self;
}

#pragma mark - 处理选择器数据
- (void)handlerPickerData {
    if (self.dataSourceArr && self.dataSourceArr.count > 0) {
        id item = [self.dataSourceArr firstObject];
        
        if ([item isKindOfClass:[SAProvinceModel class]]) {
            self.provinceModelArr = self.dataSourceArr;
        } else {
            self.provinceModelArr = [self getProvinceModelArr:self.dataSourceArr];
        }
    } else {
        
        NSArray *dataSource = [self br_addressJsonArray];
        
        if (!dataSource || dataSource.count == 0) {
            return;
        }
        self.dataSourceArr = dataSource;
        self.provinceModelArr = [self getProvinceModelArr:self.dataSourceArr];
    }
    
    
    [self handlerDefaultSelectValue];
}

#pragma mark - 获取城市JSON数据
- (NSArray *)br_addressJsonArray {
    static NSArray *cityArray = nil;
    if (!cityArray) {
        
        NSBundle *containnerBundle = [NSBundle bundleForClass:[Pointer class]];
        NSString *bundlePath = [containnerBundle pathForResource:@"Pointer" ofType:@"bundle"];
        NSBundle *addressPickerBundle = [NSBundle bundleWithPath:bundlePath];
        
        
        NSString *filePath = [addressPickerBundle pathForResource:@"BRCity" ofType:@"json"];
        NSData *data = [NSData dataWithContentsOfFile:filePath];
        cityArray = [NSJSONSerialization JSONObjectWithData:data options:NSJSONReadingAllowFragments error:nil];
    }
    return cityArray;
}

#pragma mark - 获取模型数组
- (NSArray <SAProvinceModel *>*)getProvinceModelArr:(NSArray *)dataSourceArr {
    NSMutableArray *tempArr1 = [NSMutableArray array];
    for (NSDictionary *proviceDic in dataSourceArr) {
        SAProvinceModel *proviceModel = [[SAProvinceModel alloc]init];
        proviceModel.code = [proviceDic objectForKey:@"code"];
        proviceModel.name = [proviceDic objectForKey:@"name"];
        proviceModel.index = [dataSourceArr indexOfObject:proviceDic];
        NSArray *cityList = [proviceDic.allKeys containsObject:@"cityList"] ? [proviceDic objectForKey:@"cityList"] : [proviceDic objectForKey:@"citylist"];
        NSMutableArray *tempArr2 = [NSMutableArray array];
        for (NSDictionary *cityDic in cityList) {
            SACityModel *cityModel = [[SACityModel alloc]init];
            cityModel.code = [cityDic objectForKey:@"code"];
            cityModel.name = [cityDic objectForKey:@"name"];
            cityModel.index = [cityList indexOfObject:cityDic];
            NSArray *areaList = [cityDic.allKeys containsObject:@"areaList"] ? [cityDic objectForKey:@"areaList"] : [cityDic objectForKey:@"arealist"];
            NSMutableArray *tempArr3 = [NSMutableArray array];
            for (NSDictionary *areaDic in areaList) {
                SAAreaModel *areaModel = [[SAAreaModel alloc]init];
                areaModel.code = [areaDic objectForKey:@"code"];
                areaModel.name = [areaDic objectForKey:@"name"];
                areaModel.index = [areaList indexOfObject:areaDic];
                [tempArr3 addObject:areaModel];
            }
            cityModel.arealist = [tempArr3 copy];
            [tempArr2 addObject:cityModel];
        }
        proviceModel.citylist = [tempArr2 copy];
        [tempArr1 addObject:proviceModel];
    }
    return [tempArr1 copy];
}

#pragma mark - 设置默认选择的值
- (void)handlerDefaultSelectValue {
    __block NSString *selectProvinceName = nil;
    __block NSString *selectCityName = nil;
    __block NSString *selectAreaName = nil;
    
    if (self.mSelectValues.count > 0) {
        selectProvinceName = self.mSelectValues.count > 0 ? self.mSelectValues[0] : nil;
        selectCityName = self.mSelectValues.count > 1 ? self.mSelectValues[1] : nil;
        selectAreaName = self.mSelectValues.count > 2 ? self.mSelectValues[2] : nil;
    }
    
    __weak typeof(self) weakSelf = self;
    
    if (self.pickerMode == BRAddressPickerModeProvince || self.pickerMode == BRAddressPickerModeCity || self.pickerMode == BRAddressPickerModeArea) {
        if (self.selectIndexs.count > 0) {
            NSInteger provinceIndex = [self.selectIndexs[0] integerValue];
            self.provinceIndex = (provinceIndex > 0 && provinceIndex < self.provinceModelArr.count) ? provinceIndex : 0;
            self.selectProvinceModel = self.provinceModelArr.count > self.provinceIndex ? self.provinceModelArr[self.provinceIndex] : nil;
        } else {
            self.provinceIndex = 0;
            self.selectProvinceModel = self.provinceModelArr.count > 0 ? self.provinceModelArr[0] : nil;
            [self.provinceModelArr enumerateObjectsUsingBlock:^(SAProvinceModel *  _Nonnull model, NSUInteger idx, BOOL * _Nonnull stop) {
                if (selectProvinceName && [model.name isEqualToString:selectProvinceName]) {
                    weakSelf.provinceIndex = idx;
                    weakSelf.selectProvinceModel = model;
                    *stop = YES;
                }
            }];
        }
    }
    
    if (self.pickerMode == BRAddressPickerModeCity || self.pickerMode == BRAddressPickerModeArea) {
        self.cityModelArr = [self getCityModelArray:self.provinceIndex];
        if (self.selectIndexs.count > 0) {
            NSInteger cityIndex = self.selectIndexs.count > 1 ? [self.selectIndexs[1] integerValue] : 0;
            self.cityIndex = (cityIndex > 0 && cityIndex < self.cityModelArr.count) ? cityIndex : 0;
            self.selectCityModel = self.cityModelArr.count > self.cityIndex ? self.cityModelArr[self.cityIndex] : nil;
        } else {
            self.cityIndex = 0;
            self.selectCityModel = self.cityModelArr.count > 0 ? self.cityModelArr[0] : nil;
            [self.cityModelArr enumerateObjectsUsingBlock:^(SACityModel *  _Nonnull model, NSUInteger idx, BOOL * _Nonnull stop) {
                if (selectCityName && [model.name isEqualToString:selectCityName]) {
                    weakSelf.cityIndex = idx;
                    weakSelf.selectCityModel = model;
                    *stop = YES;
                }
            }];
        }
    }
    
    if (self.pickerMode == BRAddressPickerModeArea) {
        self.areaModelArr = [self getAreaModelArray:self.provinceIndex cityIndex:self.cityIndex];
        if (self.selectIndexs.count > 0) {
            NSInteger areaIndex = self.selectIndexs.count > 2 ? [self.selectIndexs[2] integerValue] : 0;
            self.areaIndex = (areaIndex > 0 && areaIndex < self.areaModelArr.count) ? areaIndex : 0;
            self.selectAreaModel = self.areaModelArr.count > self.areaIndex ? self.areaModelArr[self.areaIndex] : nil;
        } else {
            self.areaIndex = 0;
            self.selectAreaModel = self.areaModelArr.count > 0 ? self.areaModelArr[0] : nil;
            [self.areaModelArr enumerateObjectsUsingBlock:^(SAAreaModel *  _Nonnull model, NSUInteger idx, BOOL * _Nonnull stop) {
                if (selectAreaName && [model.name isEqualToString:selectAreaName]) {
                    weakSelf.areaIndex = idx;
                    weakSelf.selectAreaModel = model;
                    *stop = YES;
                }
            }];
        }
    }
}

- (NSArray *)getCityModelArray:(NSInteger)provinceIndex {
    SAProvinceModel *provinceModel = self.provinceModelArr[provinceIndex];
    
    return provinceModel.citylist;
}

- (NSArray *)getAreaModelArray:(NSInteger)provinceIndex cityIndex:(NSInteger)cityIndex {
    SAProvinceModel *provinceModel = self.provinceModelArr[provinceIndex];
    if (provinceModel.citylist && provinceModel.citylist.count > 0) {
        SACityModel *cityModel = provinceModel.citylist[cityIndex];
        
        return cityModel.arealist;
    } else {
        return nil;
    }
}

#pragma mark - 地址选择器
- (UIPickerView *)pickerView {
    if (!_pickerView) {
        CGFloat pickerHeaderViewHeight = self.pickerHeaderView ? self.pickerHeaderView.bounds.size.height : 0;
        _pickerView = [[UIPickerView alloc]initWithFrame:CGRectMake(0, self.pickerStyle.titleBarHeight + pickerHeaderViewHeight, self.keyView.bounds.size.width, self.pickerStyle.pickerHeight)];
        _pickerView.backgroundColor = self.pickerStyle.pickerColor;
        _pickerView.autoresizingMask = UIViewAutoresizingFlexibleBottomMargin | UIViewAutoresizingFlexibleRightMargin | UIViewAutoresizingFlexibleWidth;
        _pickerView.dataSource = self;
        _pickerView.delegate = self;
    }
    return _pickerView;
}

#pragma mark - UIPickerViewDataSource
- (NSInteger)numberOfComponentsInPickerView:(UIPickerView *)pickerView {
    switch (self.pickerMode) {
        case BRAddressPickerModeProvince:
            return 1;
            break;
        case BRAddressPickerModeCity:
            return 2;
            break;
        case BRAddressPickerModeArea:
            return 3;
            break;
            
        default:
            break;
    }
}

- (NSInteger)pickerView:(UIPickerView *)pickerView numberOfRowsInComponent:(NSInteger)component {
    if (component == 0) {
        
        return self.provinceModelArr.count;
    }
    if (component == 1) {
        
        return self.cityModelArr.count;
    }
    if (component == 2) {
        
        return self.areaModelArr.count;
    }
    return 0;
}

#pragma mark - UIPickerViewDelegate
- (UIView *)pickerView:(UIPickerView *)pickerView viewForRow:(NSInteger)row forComponent:(NSInteger)component reusingView:(nullable UIView *)view {
    
    UILabel *label = (UILabel *)view;
    if (!label) {
        label = [[UILabel alloc]init];
        label.backgroundColor = [UIColor clearColor];
        label.textAlignment = NSTextAlignmentCenter;
        label.font = self.pickerStyle.pickerTextFont;
        label.textColor = self.pickerStyle.pickerTextColor;
        label.numberOfLines = self.pickerStyle.maxTextLines;
        
        label.adjustsFontSizeToFitWidth = YES;
        
        label.minimumScaleFactor = 0.5f;
    }
    if (component == 0) {
        SAProvinceModel *model = self.provinceModelArr[row];
        label.text = model.name;
    } else if (component == 1) {
        SACityModel *model = self.cityModelArr[row];
        label.text = model.name;
    } else if (component == 2) {
        SAAreaModel *model = self.areaModelArr[row];
        label.text = model.name;
    }
    
    
    [self.pickerStyle setupPickerSelectRowStyle:pickerView titleForRow:row forComponent:component];
    
    
    
    NSInteger selectRow = [pickerView selectedRowInComponent:component];
    if (selectRow >= 0) {
        self.rollingComponent = component;
        self.rollingRow = selectRow;
    }
    
    return label;
}

- (BOOL)getRollingStatus:(UIView *)view {
    if ([view isKindOfClass:[UIScrollView class]]) {
        UIScrollView *scrollView = (UIScrollView *)view;
        if (scrollView.dragging || scrollView.decelerating) {
            
            return YES;
        }
    }
    
    for (UIView *subView in view.subviews) {
        if ([self getRollingStatus:subView]) {
            return YES;
        }
    }
    
    return NO;
}

- (BOOL)isRolling {
    return [self getRollingStatus:self.pickerView];
}

- (void)pickerView:(UIPickerView *)pickerView didSelectRow:(NSInteger)row inComponent:(NSInteger)component {
    if (component == 0) { 
        
        self.provinceIndex = row;
        switch (self.pickerMode) {
            case BRAddressPickerModeProvince:
            {
                self.selectProvinceModel = self.provinceModelArr.count > self.provinceIndex ? self.provinceModelArr[self.provinceIndex] : nil;
                self.selectCityModel = nil;
                self.selectAreaModel = nil;
            }
                break;
            case BRAddressPickerModeCity:
            {
                self.cityModelArr = [self getCityModelArray:self.provinceIndex];
                [self.pickerView reloadComponent:1];
                [self.pickerView selectRow:0 inComponent:1 animated:YES];
                self.selectProvinceModel = self.provinceModelArr.count > self.provinceIndex ? self.provinceModelArr[self.provinceIndex] : nil;
                self.selectCityModel = self.cityModelArr.count > 0 ? self.cityModelArr[0] : nil;
                self.selectAreaModel = nil;
            }
                break;
            case BRAddressPickerModeArea:
            {
                self.cityModelArr = [self getCityModelArray:self.provinceIndex];
                self.areaModelArr = [self getAreaModelArray:self.provinceIndex cityIndex:0];
                [self.pickerView reloadComponent:1];
                [self.pickerView selectRow:0 inComponent:1 animated:YES];
                [self.pickerView reloadComponent:2];
                [self.pickerView selectRow:0 inComponent:2 animated:YES];
                self.selectProvinceModel = self.provinceModelArr.count > self.provinceIndex ? self.provinceModelArr[self.provinceIndex] : nil;
                self.selectCityModel = self.cityModelArr.count > 0 ? self.cityModelArr[0] : nil;
                self.selectAreaModel = self.areaModelArr.count > 0 ? self.areaModelArr[0] : nil;
            }
                break;
            default:
                break;
        }
    }
    if (component == 1) { 
        
        self.cityIndex = row;
        switch (self.pickerMode) {
            case BRAddressPickerModeCity:
            {
                self.selectCityModel = self.cityModelArr.count > self.cityIndex ? self.cityModelArr[self.cityIndex] : nil;
                self.selectAreaModel = nil;
            }
                break;
            case BRAddressPickerModeArea:
            {
                self.areaModelArr = [self getAreaModelArray:self.provinceIndex cityIndex:self.cityIndex];
                [self.pickerView reloadComponent:2];
                [self.pickerView selectRow:0 inComponent:2 animated:YES];
                self.selectCityModel = self.cityModelArr.count > self.cityIndex ? self.cityModelArr[self.cityIndex] : nil;
                self.selectAreaModel = self.areaModelArr.count > 0 ? self.areaModelArr[0] : nil;
            }
                break;
            default:
                break;
        }
    }
    if (component == 2) { 
        
        self.areaIndex = row;
        if (self.pickerMode == BRAddressPickerModeArea) {
            self.selectAreaModel = self.areaModelArr.count > self.areaIndex ? self.areaModelArr[self.areaIndex] : nil;
        }
    }
    
    
    if (self.changeBlock) {
        self.changeBlock(self.selectProvinceModel, self.selectCityModel, self.selectAreaModel);
    }
    
    
    if (self.isAutoSelect) {
        if (self.resultBlock) {
            self.resultBlock(self.selectProvinceModel, self.selectCityModel, self.selectAreaModel);
        }
    }
}

- (CGFloat)pickerView:(UIPickerView *)pickerView rowHeightForComponent:(NSInteger)component {
    return self.pickerStyle.rowHeight;
}

- (CGFloat)pickerView:(UIPickerView *)pickerView widthForComponent:(NSInteger)component {
    NSInteger columnCount = [self numberOfComponentsInPickerView:pickerView];
    CGFloat columnWidth = self.pickerView.bounds.size.width / columnCount;
    if (self.pickerStyle.columnWidth > 0 && self.pickerStyle.columnWidth <= columnWidth) {
        return self.pickerStyle.columnWidth;
    }
    return columnWidth;
}

#pragma mark - 重写父类方法
- (void)reloadData {
    
    [self handlerPickerData];
    
    [self.pickerView reloadAllComponents];
    
    if (self.pickerMode == BRAddressPickerModeProvince) {
        [self.pickerView selectRow:self.provinceIndex inComponent:0 animated:YES];
    } else if (self.pickerMode == BRAddressPickerModeCity) {
        [self.pickerView selectRow:self.provinceIndex inComponent:0 animated:YES];
        [self.pickerView selectRow:self.cityIndex inComponent:1 animated:YES];
    } else if (self.pickerMode == BRAddressPickerModeArea) {
        [self.pickerView selectRow:self.provinceIndex inComponent:0 animated:YES];
        [self.pickerView selectRow:self.cityIndex inComponent:1 animated:YES];
        [self.pickerView selectRow:self.areaIndex inComponent:2 animated:YES];
    }
}

- (void)addPickerToView:(UIView *)view {
    
    if (view) {
        
        [view setNeedsLayout];
        [view layoutIfNeeded];
        
        self.frame = view.bounds;
        CGFloat pickerHeaderViewHeight = self.pickerHeaderView ? self.pickerHeaderView.bounds.size.height : 0;
        CGFloat pickerFooterViewHeight = self.pickerFooterView ? self.pickerFooterView.bounds.size.height : 0;
        self.pickerView.frame = CGRectMake(0, pickerHeaderViewHeight, view.bounds.size.width, view.bounds.size.height - pickerHeaderViewHeight - pickerFooterViewHeight);
        [self addSubview:self.pickerView];
    } else {
        
        CGFloat pickerHeaderViewHeight = self.pickerHeaderView ? self.pickerHeaderView.bounds.size.height : 0;
        self.pickerView.frame = CGRectMake(0, self.pickerStyle.titleBarHeight + pickerHeaderViewHeight, self.keyView.bounds.size.width, self.pickerStyle.pickerHeight);
        
        [self.alertView addSubview:self.pickerView];
    }
    
    
    if (self.pickerStyle.clearPickerNewStyle) {
        [self.pickerStyle addSeparatorLineView:self.pickerView];
    }
    
    
    [self reloadData];
    
    __weak typeof(self) weakSelf = self;
    
    self.doneBlock = ^{
        if (weakSelf.isRolling) {
            NSLog(@"选择器滚动还未结束");
            
            
            [weakSelf pickerView:weakSelf.pickerView didSelectRow:weakSelf.rollingRow inComponent:weakSelf.rollingComponent];
        }
    
        
        if (weakSelf.resultBlock) {
            weakSelf.resultBlock(weakSelf.selectProvinceModel, weakSelf.selectCityModel, weakSelf.selectAreaModel);
        }
    };
    
    [super addPickerToView:view];
}

#pragma mark - 重写父类方法
- (void)addSubViewToPicker:(UIView *)customView {
    [self.pickerView addSubview:customView];
}

#pragma mark - 弹出选择器视图
- (void)show {
    [self addPickerToView:nil];
}

#pragma mark - 关闭选择器视图
- (void)dismiss {
    [self removePickerFromView:nil];
}

#pragma mark - setter方法
- (void)setSelectValues:(NSArray<NSString *> *)selectValues {
    self.mSelectValues = selectValues;
}

#pragma mark - getter方法
- (NSArray *)provinceModelArr {
    if (!_provinceModelArr) {
        _provinceModelArr = [NSArray array];
    }
    return _provinceModelArr;
}

- (NSArray *)cityModelArr {
    if (!_cityModelArr) {
        _cityModelArr = [NSArray array];
    }
    return _cityModelArr;
}

- (NSArray *)areaModelArr {
    if (!_areaModelArr) {
        _areaModelArr = [NSArray array];
    }
    return _areaModelArr;
}

- (SAProvinceModel *)selectProvinceModel {
    if (!_selectProvinceModel) {
        _selectProvinceModel = [[SAProvinceModel alloc]init];
    }
    return _selectProvinceModel;
}

- (SACityModel *)selectCityModel {
    if (!_selectCityModel) {
        _selectCityModel = [[SACityModel alloc]init];
        _selectCityModel.code = @"";
        _selectCityModel.name = @"";
    }
    return _selectCityModel;
}

- (SAAreaModel *)selectAreaModel {
    if (!_selectAreaModel) {
        _selectAreaModel = [[SAAreaModel alloc]init];
        _selectAreaModel.code = @"";
        _selectAreaModel.name = @"";
    }
    return _selectAreaModel;
}

- (NSArray *)dataSourceArr {
    if (!_dataSourceArr) {
        _dataSourceArr = [NSArray array];
    }
    return _dataSourceArr;
}

- (NSArray<NSString *> *)mSelectValues {
    if (!_mSelectValues) {
        _mSelectValues = [NSArray array];
    }
    return _mSelectValues;
}

- (NSArray<NSNumber *> *)selectIndexs {
    if (!_selectIndexs) {
        _selectIndexs = [NSArray array];
    }
    return _selectIndexs;
}

@end
