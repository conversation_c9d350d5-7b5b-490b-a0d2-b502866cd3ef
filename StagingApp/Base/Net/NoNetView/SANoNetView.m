
#import "SANoNetView.h"
 

@interface SANoNetView ()

@property (nonatomic, strong) UIImageView *imgView;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *loadLabel;

@property (nonatomic, copy)   TapBlock tapBlock;

@end

@implementation SANoNetView


#pragma mark - Life cycle

- (instancetype)initWithImage:(UIImage *)image title:(NSString *)title loadTitle:(NSString *)loadTitle tapBlock:(TapBlock)tapBlock
{
    if (self = [super init])
    {
        [self addCustomViews];
        [self setConstraints];
        
        self.tapBlock           = tapBlock;
        self.imgView.image      = image;
        self.titleLabel.text    = title;
        self.loadLabel.text     = loadTitle;
        
        self.userInteractionEnabled = YES;
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(tapAction:)];
        [self addGestureRecognizer:tap];
    }
    
    return self;
}

- (void)addCustomViews
{
    [self addSubview:self.imgView];
    [self addSubview:self.titleLabel];
    [self addSubview:self.loadLabel];
}

- (void)setConstraints
{
    [self.imgView mas_makeConstraints:^(SAConstraintMaker *make) {
        make.centerX.equalTo(self);
        make.centerY.equalTo(self).offset(-XX_6(40));
        make.width.height.mas_equalTo(120);
    }];
    
    [self.titleLabel mas_makeConstraints:^(SAConstraintMaker *make) {
        make.centerX.equalTo(self);
        make.top.equalTo(self.imgView.mas_bottom).offset(XX_6(10));
    }];
    
    [self.loadLabel mas_makeConstraints:^(SAConstraintMaker *make) {
        make.centerX.equalTo(self);
        make.top.equalTo(self.titleLabel.mas_bottom).offset(XX_6(10));
    }];
}

#pragma mark - Method

- (void)tapAction:(UITapGestureRecognizer *)gesture
{
    if (self.tapBlock)
    {
        self.hidden = YES;
        self.tapBlock();
    }
}

#pragma mark - getter

- (UIImageView *)imgView
{
    if (_imgView == nil)
    {
        _imgView = [[UIImageView alloc] init];
        _imgView.contentMode = UIViewContentModeScaleAspectFit;
    }
    
    return _imgView;
}

- (UILabel *)titleLabel {
    if (_titleLabel == nil) {
        _titleLabel = [[UILabel alloc] init];
        _titleLabel.textColor = [UIColor colorWithHex:0x4c4c4c];
        _titleLabel.font = [UIFont fontSizeOfXX_6:20];
        _titleLabel.textAlignment = NSTextAlignmentCenter;
    }
    return _titleLabel;
}

- (UILabel *)loadLabel {
    if (_loadLabel == nil) {
        _loadLabel = [[UILabel alloc] init];
        _loadLabel.textColor = [UIColor colorWithHex:0x335ef1];
        _loadLabel.font = [UIFont fontSizeOfXX_6:14];
        _loadLabel.textAlignment = NSTextAlignmentCenter;
    }
    return _loadLabel;
}


@end
