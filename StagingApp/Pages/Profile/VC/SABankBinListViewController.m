
#import "SABankBinListViewController.h"

@interface SABankBinListViewController ()<UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, strong) UITableView *tableV;

@end

@implementation SABankBinListViewController

- (void)viewDidLoad
{
    [super viewDidLoad];
    self.navigationItem.title = @"所属银行";
    [self.view addSubview:self.tableV];
}

#pragma mark - UITableViewDataSource

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section{
    return  self.nameList.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath{
    static NSString *cellId = @"nameCell";
    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:cellId];
    if (cell == nil) {
        cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:cellId];
    }
    cell.accessoryType  = UITableViewCellAccessoryDisclosureIndicator;
    
    UIImageView *icon = [[UIImageView alloc] init];
    
    [cell.contentView addSubview:icon];
    [icon mas_makeConstraints:^(SAConstraintMaker *make) {
        make.width.height.mas_equalTo(24);
        make.left.equalTo(cell.contentView).offset(12);
        make.centerY.equalTo(cell.contentView);
    }];
    [cell.textLabel mas_makeConstraints:^(SAConstraintMaker *make) {
        make.centerY.equalTo(icon);
        make.left.equalTo(icon.mas_right).offset(5);
    }];
    
    SABankNameModel *mod = self.nameList[indexPath.row];
    
    [icon sd_setImageWithURL:[NSURL URLWithString:mod.bankLogo]];
    cell.textLabel.text = mod.bankName;
    cell.textLabel.textColor = [UIColor colorWithHex:0x333333];
    cell.textLabel.font = Font_XX6(14);
    
    return cell;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    return  XX_6(50);
}

#pragma mark - UITableViewDelegate

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    SABankNameModel *mod = self.nameList[indexPath.row];
    if (self.selectBlock) {
        self.selectBlock(mod);
        [self popSelf];
    }
}

#pragma mark - getter

- (UITableView *)tableV{
    if (_tableV == nil) {
        _tableV = [[UITableView alloc] initWithFrame:CGRectMake(0, 0, UIScreenWidth, UIScreenHeight-kTopHeight) style:UITableViewStylePlain];
        _tableV.contentSize = CGSizeMake(UIScreenWidth, UIScreenHeight-kTopHeight);
        _tableV.delegate = self;
        _tableV.dataSource = self;
    }
    return _tableV;
}

@end
