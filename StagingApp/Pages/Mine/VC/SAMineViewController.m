
#import "SAMineViewController.h"
#import "SAMainTopBgView.h"

#import "SAUserCenterApiManager.h"
#import "SAUserManager.h"
#import "SAUserCenterModel.h"
#import "SALogoutApiManager.h"
#import "SACommonWebViewController.h"

#import "SAGetCustomerUrlApiManager.h"
#import "SASettingViewController.h"
#import "SAVerifyBankCardViewController.h"
#import "SAServiceView.h"
#import "SAMyCardsListViewController.h"

#import "SAGetBankRealNameApi.h"
#import "SAVerifyIdentityViewController.h"
#import "SAGetBorrowAgainApi.h"
#import "SAConfirmmBankViewController.h"
#import "SAPaidListVC.h"
#import "SABannerView.h"

#define kCenterCellHeight   60
#define kItemBaseTag        198

#define kMeCellHeight       60

@interface SAMineViewController ()< QLAPIManagerCallBackDelegate, QLAPIManagerParamSource>

@property (nonatomic, strong) UIImageView *bgImg;

@property (nonatomic, strong) SAMainTopBgView *firstV;

@property (nonatomic, strong) UIView *borrowV;
@property (nonatomic, strong) UILabel *moneyLab;
@property (nonatomic, strong) UIButton *lendBtn;

@property (nonatomic, strong) SABannerView *bannerView;


@property (nonatomic, strong) UIView *contentV;
@property (nonatomic, strong) NSArray <NSDictionary *>*dataArr;


@property (nonatomic, strong) SAUserCenterApiManager *userCenterApiMgr;
@property (nonatomic, strong) SAUserCenterModel *centerModel;

@property (nonatomic, strong) SALogoutApiManager *loginOutApiMgr;

@property (nonatomic, strong) SAGetCustomerUrlApiManager *customerApiMgr;
@property (nonatomic, strong) SAGetBankRealNameApi *realNameApi;

@property (nonatomic, strong) SAGetBorrowAgainApi *againApi;

@end

@implementation SAMineViewController

- (instancetype)init
{
    if (self = [super init])
    {
        self.backImgName = @"";
    }

    return self;
}

- (void)viewDidAppear:(BOOL)animated
{
    [super viewDidAppear:animated];
    
    self.navigationController.interactivePopGestureRecognizer.enabled = NO;
}

- (void)viewDidDisappear:(BOOL)animated
{
    [super viewDidDisappear:animated];
    
    self.navigationController.interactivePopGestureRecognizer.enabled = YES;
}

- (void)viewWillAppear:(BOOL)animated
{
    [super viewWillAppear:animated];
    [self.navigationController setNavigationBarHidden:YES animated:YES];

    self.view.backgroundColor = [UIColor colorWithHex:0xf6f6f6];
    
    [self.userCenterApiMgr loadData];
    [self.customerApiMgr loadData];
}

- (void)viewDidLoad
{
    [super viewDidLoad];
    
    [self showActivityHUD:nil];
    [self addCustomViews];
    [self createFrames];
}

- (void)addCustomViews 
{
    [self.view addSubview:self.bgImg];
    [self.view addSubview:self.firstV];
  
    [self.view addSubview:self.borrowV];
    [self.view addSubview:self.contentV];
    [self.view addSubview:self.bannerView];
    
}

- (void)createFrames
{
    [self.bgImg mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.right.top.bottom.equalTo(self.view);
    }];
    [self.firstV mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.top.width.equalTo(self.view);
        make.height.mas_equalTo(XX_6(176));
    }];
    
    [self.borrowV mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.equalTo(self.view).offset(20);
        make.centerX.equalTo(self.view);
        make.top.equalTo(self.firstV.mas_bottom).offset(15);
        make.height.mas_equalTo(110);
    }];
    
    [self.contentV mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.centerX.equalTo(self.borrowV);
        make.top.equalTo(self.borrowV.mas_bottom).offset(20);
        make.height.mas_equalTo(95);
    }];
    
    self.bannerView.hGap = 20;
    [self.bannerView mas_makeConstraints:^(SAConstraintMaker *make) {
        make.top.equalTo(self.contentV.mas_bottom).offset(10);
        make.left.equalTo(self.contentV);
        make.centerX.equalTo(self.contentV);
        make.height.mas_equalTo(kBannerHeight);
    }];

}

#pragma mark - 去借款

- (void)tapBorrowAction
{
    [self showActivityHUD:nil];
    [self.againApi loadData];
}

#pragma mark - Method

- (void)tapAction: (UITapGestureRecognizer *)tap
{
    UIView *view = tap.view;
    NSInteger tag = view.tag;
    switch (tag-kItemBaseTag) {
        case 0:
            [self jumpWaitRepay];
            break;
        case 1:
            [self jumpPaidOrder];
            break;
        case 2:
            [self showActivityHUD:nil];
            [self.realNameApi loadData];
            break;
        case 3:
            [self jumpSetting];
            break;
        default:
            break;
    }
}

- (void)jumpMyCard
{
    SAMyCardsListViewController *vc = [SAMyCardsListViewController new];
    vc.hidesBottomBarWhenPushed = YES;
    [[SACommonTool currentViewController].navigationController pushViewController:vc animated:YES];
}

- (void)jumpSetting
{
    SASettingViewController *vc = [SASettingViewController new];
    vc.aboutUrl = self.centerModel.aboutUrl;
    vc.hidesBottomBarWhenPushed = YES;
    [[SACommonTool currentViewController].navigationController pushViewController:vc animated:YES];
}

- (void)jumpWaitRepay
{
    [[SATabBarViewController sharedInstance] setTabIndex:1];
}


- (void)jumpPaidOrder
{
    SAPaidListVC *vc = [SAPaidListVC new];
    vc.hidesBottomBarWhenPushed = YES;
    [[SACommonTool currentViewController].navigationController pushViewController:vc animated:YES];
}

- (void)jumpContactus
{
    if(![NSString judgeStringExist:[SAAdvanceManager sharedInstance].customerServiceUrl]) return;
    
    SACommonWebViewController *webVC = [[SACommonWebViewController alloc] init];
    webVC.url = [SAAdvanceManager sharedInstance].customerServiceUrl;
    webVC.navTitle = @"客服";
    webVC.hidesBottomBarWhenPushed = YES;
    [[SACommonTool currentViewController].navigationController pushViewController:webVC animated:NO];
}

#pragma mark QLAPIManagerCallBackDelegate

- (void)managerCallAPIDidSuccess:(SABaseManager *)manager 
{
    [self hideHUDView];
    if (manager == self.userCenterApiMgr) {
        NSDictionary *dataDict = [manager fetchDataWithReformer:self.userCenterApiMgr];
        self.centerModel = [SAUserCenterModel objectWithDict:dataDict];
        self.firstV.model = self.centerModel;
        
        self.moneyLab.text = [NSString stringWithFormat:@"¥%@", self.centerModel.maxAmount];
        [self.lendBtn setTitle:self.centerModel.buttonText forState:UIControlStateNormal];
        self.bannerView.dataList = self.centerModel.slideshowVOList;
    }
    else if (manager == self.loginOutApiMgr) {

        [[SATabBarViewController sharedInstance] setSelectedIndex:0];
        [SAUserManager loginOutClearInfo];
    }
    
    else if (manager == self.customerApiMgr)
    {
        NSDictionary *dict = [manager fetchDataWithReformer:self.customerApiMgr];
        NSLog(@"ssss---%@", dict);
        [SAAdvanceManager sharedInstance].customerServiceUrl = [NSString judgeStringExist:dict[@"customerServiceUrl"]] ? dict[@"customerServiceUrl"] : @"";
    }
    
    else if (manager == self.realNameApi)
    {
        NSDictionary *dict = [manager fetchDataWithReformer:self.realNameApi];
        BOOL isRealName = [dict[@"canBindBankCard"] boolValue];
        if(isRealName){
            [self jumpMyCard];
        }else{
            [SAVerifyListManager sharedInstance].popVCName = NSStringFromClass([[SACommonTool currentViewController] class]);
            [[SAVerifyListManager sharedInstance] handleVerifyJump];
        }
    }
    
    else if (manager == self.againApi)
    {
        NSDictionary *dataDict = [manager fetchDataWithReformer:self.againApi];
        
        NSString *jumpUrl = dataDict[@"jumpUrl"];
        if ([jumpUrl isEqualToString:@"APP/CLViewControllerUserDataList"])
        {
            [SAVerifyListManager sharedInstance].popVCName = NSStringFromClass([[SACommonTool currentViewController] class]);
            [[SAVerifyListManager sharedInstance] handleVerifyJump];
        }
        else
        {
            [self textStateHUD:@"操作成功!"];
            [[SATabBarViewController sharedInstance] setTabIndex:0];
        }
    }
}

- (void)managerCallAPIDidFailed:(SABaseManager *)manager {
    [self hideHUDView];
    NSString *failMsg  = [SAResponseHandle netWorkErrorMsg:[manager fetchDataWithReformer:nil] andErrorType:manager.errorType];
    [self textStateHUD:failMsg];
}

#pragma mark QLAPIManagerParamSource
- (NSDictionary *)paramsForApi:(SABaseManager *)manager {
    return [NSDictionary dictionary];
}

#pragma mark - getters

- (UIView *)contentV{
    if (_contentV == nil) {
        _contentV = [UIView new];
        _contentV.backgroundColor = [UIColor whiteColor];
        _contentV.layer.masksToBounds = YES;
        _contentV.layer.cornerRadius = 6;

        
        CGFloat width = (UIScreenWidth - 20*2)/4;
        [self.dataArr enumerateObjectsUsingBlock:^(NSDictionary *item, NSUInteger idx, BOOL * _Nonnull stop) {
            UIView *view = [UIView new];
            
            [_contentV addSubview:view];
            
            [view mas_makeConstraints:^(SAConstraintMaker *make) {
                make.left.equalTo(_contentV).offset(width*idx);
                make.width.mas_equalTo(width);
                make.top.height.equalTo(_contentV);
            }];
            
            UIImageView *icon = [[UIImageView alloc] initWithImage:[UIImage imageNamed:item[@"icon"]]];
            
            [view addSubview:icon];
            
            UILabel *title = [UILabel new];
            title.text = item[@"title"];
            title.textColor = [UIColor colorWithHex:0x333333];
            title.textAlignment = NSTextAlignmentCenter;
            title.font = BoldFont_XX6(13);
            [view addSubview:title];
            
            [icon mas_makeConstraints:^(SAConstraintMaker *make) {
                make.centerX.equalTo(view);
                make.height.width.mas_equalTo(30);
                make.centerY.equalTo(view).offset(-15);
            }];
            [title mas_makeConstraints:^(SAConstraintMaker *make) {
                make.centerX.equalTo(view);
                make.top.equalTo(icon.mas_bottom).offset(8);
            }];
            
            UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(tapAction:)];
            view.userInteractionEnabled = YES;
            view.tag = idx + kItemBaseTag;
            [view addGestureRecognizer:tap];
        }];
    }
    return _contentV;
}

- (UIView *)borrowV
{
    if (_borrowV == nil) {
        _borrowV = [UIView new];
        _borrowV.backgroundColor  = [UIColor blackColor];
        _borrowV.layer.masksToBounds = YES;
        _borrowV.layer.cornerRadius = 6;
        
        UILabel *topLab = [UILabel new];
        topLab.font = Font_XX6(12);
        topLab.textColor = [UIColor colorWithHex:0xcccccc];
        [_borrowV addSubview:topLab];
        
        [_borrowV addSubview:self.moneyLab];
        
        UILabel *botLab = [UILabel new];
        botLab.font = Font_XX6(12);
        botLab.textColor = [UIColor colorWithHex:0xcccccc];
        [_borrowV addSubview:botLab];
        
        [_borrowV addSubview:self.lendBtn];
        
        [self.moneyLab mas_makeConstraints:^(SAConstraintMaker *make) {
            make.left.equalTo(_borrowV).offset(20);
            make.centerY.equalTo(_borrowV);
        }];
        [topLab mas_makeConstraints:^(SAConstraintMaker *make) {
            make.left.equalTo(self.moneyLab);
            make.bottom.equalTo(self.moneyLab.mas_top).offset(-4);
        }];
        [botLab mas_makeConstraints:^(SAConstraintMaker *make) {
            make.left.equalTo(self.moneyLab);
            make.top.equalTo(self.moneyLab.mas_bottom).offset(4);
        }];
        
        [self.lendBtn mas_makeConstraints:^(SAConstraintMaker *make) {
            make.centerY.equalTo(_borrowV);
            make.right.equalTo(_borrowV).offset(-20);
            make.width.mas_equalTo(100);
            make.height.mas_equalTo(40);
        }];
        self.lendBtn.layer.cornerRadius = 6;
        self.lendBtn.layer.masksToBounds = YES;
        
        topLab.text = @"预估可借";
        botLab.text = @"只需3步轻松借款";
        
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(tapBorrowAction)];
        _borrowV.userInteractionEnabled = YES;
        [_borrowV addGestureRecognizer:tap];
        [self.lendBtn addTarget:self action:@selector(tapBorrowAction) forControlEvents:UIControlEventTouchUpInside];
    }
    
    return _borrowV;
}

- (UILabel *)moneyLab{
    FF_Fetch_UILable(_moneyLab, [UIColor whiteColor], BoldFont_XX6(24))
}

- (UIButton *)lendBtn{
    if(_lendBtn == nil){
        _lendBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_lendBtn setBackgroundColor:[UIColor yellowColor]];
        [_lendBtn setTitleColor:[UIColor blackColor] forState:UIControlStateNormal];
        [_lendBtn setTitle:@"立即借款" forState:UIControlStateNormal];
        _lendBtn.titleLabel.font = BoldFont_XX6(16);
    }
    
    return _lendBtn;
}

- (SAMainTopBgView *)firstV {
    if (_firstV == nil) {
        _firstV = [[SAMainTopBgView alloc] init];
    }
    return _firstV;
}

- (NSArray *)dataArr {
    if (_dataArr == nil) {
        _dataArr = @[
                        @{@"icon":@"mine_wait_repay",
                          @"title":@"待还账单"},
                        @{@"icon":@"mine_repayed",
                          @"title":@"已还账单"},
                        @{@"icon":@"mine_bank_card",
                          @"title":@"我的银行卡"},
                        @{@"icon":@"mine_setting",
                          @"title":@"设置"},
                     ];
    }
    return _dataArr;
}

- (SAUserCenterApiManager *)userCenterApiMgr {
    if (_userCenterApiMgr == nil) {
        _userCenterApiMgr = [[SAUserCenterApiManager alloc] init];
        _userCenterApiMgr.delegate = self;
        _userCenterApiMgr.paramSource = self;
    }
    
    return _userCenterApiMgr;
}

- (SALogoutApiManager *)loginOutApiMgr {
    if (_loginOutApiMgr == nil) {
        _loginOutApiMgr = [[SALogoutApiManager alloc] init];
        _loginOutApiMgr.delegate = self;
        _loginOutApiMgr.paramSource = self;
    }
    return _loginOutApiMgr;
}



- (SAGetCustomerUrlApiManager *)customerApiMgr
{
    if(_customerApiMgr == nil){
        _customerApiMgr = [[SAGetCustomerUrlApiManager alloc] init];
        _customerApiMgr.delegate = self;
        _customerApiMgr.paramSource = self;
        
    }
    return _customerApiMgr;
}

- (SAGetBankRealNameApi *)realNameApi{
    if (_realNameApi == nil) {
        _realNameApi = [SAGetBankRealNameApi new];
        _realNameApi.delegate = self;
        _realNameApi.paramSource = self;
    }
    return _realNameApi;
}

- (SAGetBorrowAgainApi *)againApi{
    if(_againApi == nil){
        _againApi = [SAGetBorrowAgainApi new];
        _againApi.delegate = self;
        _againApi.paramSource = self;
    }
    return _againApi;
}

- (UIImageView *)bgImg{
    FF_Fetch_UIImageViewWithImage(_bgImg, [UIImage imageNamed:@"mine_bg"])
}

- (SABannerView *)bannerView{
    if (_bannerView == nil) {
        _bannerView = [SABannerView new];
    }
    return _bannerView;
}

@end
