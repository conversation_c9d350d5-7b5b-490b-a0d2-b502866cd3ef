
#import "SAOrderDetailViewController.h"
#import "SAGetBillDetailApi.h"

#define kOrderItemHeight 33

@interface SAOrderDetailViewController ()<QLAPIManagerParamSource, QLAPIManagerCallBackDelegate>

@property (nonatomic, strong) SAGetBillDetailApi *detailApi;

@property (nonatomic, strong) UIView *topBgV;
@property (nonatomic, strong) UIView *bottomBgV;

@property (nonatomic, strong) NSArray *topList;
@property (nonatomic, strong) NSArray *bottomList;

@end

@implementation SAOrderDetailViewController

#pragma mark - Life cycle

- (void)viewDidLoad 
{
    [super viewDidLoad];
    self.navigationItem.title = @"订单详情";

    [self showActivityHUD:nil];
    [self.detailApi loadData];
    
    self.view.backgroundColor = [UIColor colorWithHex:0xf6f6f6];
}

- (void)createUI
{
    [self.view addSubview:self.topBgV];
    [self.view addSubview:self.bottomBgV];
    
    [self.topBgV mas_makeConstraints:^(SAConstraintMaker *make) {
        make.top.left.equalTo(self.view).offset(12);
        make.centerX.equalTo(self.view);
        make.height.mas_equalTo(kOrderItemHeight * self.topList.count+14);
    }];
    self.topBgV.layer.masksToBounds = YES;
    self.topBgV.layer.cornerRadius = 8;
    
    __block UILabel *lastV = nil;
    [self.topList enumerateObjectsUsingBlock:^(NSDictionary *item, NSUInteger idx, BOOL * _Nonnull stop) {
        UILabel *titLab = [UILabel new];
        titLab.textColor = [UIColor blackColor];
        titLab.font = BoldFont_XX6(13);
        [self.topBgV addSubview:titLab];
        
        UILabel *valLab = [UILabel new];
        valLab.textColor = [UIColor colorWithHex:0x666666];
        valLab.font = Font_XX6(13);
        valLab.textAlignment = NSTextAlignmentRight;
        [self.topBgV addSubview:valLab];
        
        [titLab mas_makeConstraints:^(SAConstraintMaker *make) {
            make.left.equalTo(self.topBgV).offset(12);
            make.top.equalTo(lastV ? (lastV.mas_bottom) : self.topBgV).offset(14);
        }];
        [valLab mas_makeConstraints:^(SAConstraintMaker *make) {
            make.right.equalTo(self.topBgV).offset(-12);
            make.centerY.equalTo(titLab);
        }];
        lastV = titLab;
        
        titLab.text = item[@"title"];
        valLab.text = item[@"value"];
    }];
    
    [self.bottomBgV mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.right.equalTo(self.topBgV);
        make.top.equalTo(self.topBgV.mas_bottom).offset(10);
        make.height.mas_equalTo(kOrderItemHeight * self.bottomList.count+12);
    }];
    self.bottomBgV.layer.masksToBounds = YES;
    self.bottomBgV.layer.cornerRadius = 8;

    __block UILabel *lastT = nil;
    [self.bottomList enumerateObjectsUsingBlock:^(NSDictionary *item, NSUInteger idx, BOOL * _Nonnull stop) {
        UILabel *titLab = [UILabel new];
        titLab.textColor = [UIColor blackColor];
        titLab.font = BoldFont_XX6(13);
        [self.bottomBgV addSubview:titLab];
        
        UILabel *valLab = [UILabel new];
        valLab.textColor = [UIColor colorWithHex:0x666666];
        valLab.font = Font_XX6(13);
        valLab.textAlignment = NSTextAlignmentRight;
        [self.bottomBgV addSubview:valLab];
        
        [titLab mas_makeConstraints:^(SAConstraintMaker *make) {
            make.left.equalTo(self.bottomBgV).offset(12);
            make.top.equalTo(lastT ? (lastT.mas_bottom) : self.bottomBgV).offset(14);
        }];
        [valLab mas_makeConstraints:^(SAConstraintMaker *make) {
            make.right.equalTo(self.bottomBgV).offset(-12);
            make.centerY.equalTo(titLab);
        }];
        lastT = titLab;
        
        titLab.text = item[@"title"];
        valLab.text = item[@"value"];
    }];
}

#pragma mark QLAPIManagerCallBackDelegate
- (void)managerCallAPIDidSuccess:(SABaseManager *)manager 
{
    [self hideHUDView];
    if (manager == self.detailApi)
    {
        NSDictionary *dataDict = [manager fetchDataWithReformer:self.detailApi];
        self.topList = dataDict[@"topTradeFieldDetails"];
        self.bottomList = dataDict[@"bottomTradeFieldDetails"];
        
        [self createUI];
    }
}

- (void)managerCallAPIDidFailed:(SABaseManager *)manager 
{
    [self hideHUDView];
    
    NSString *failMsg  = [SAResponseHandle netWorkErrorMsg:[manager fetchDataWithReformer:nil] andErrorType:manager.errorType];
    [SATool textStateHUD:failMsg];
}

#pragma mark QLAPIManagerParamSource
- (NSDictionary *)paramsForApi:(SABaseManager *)manager
{
    if (manager == self.detailApi) {
        return @{@"tradeNo": self.orderNo};
    }
    return [NSDictionary dictionary];
}

#pragma mark - getter

- (SAGetBillDetailApi *)detailApi {
    if (_detailApi == nil) {
        _detailApi = [[SAGetBillDetailApi alloc] init];
        _detailApi.delegate = self;
        _detailApi.paramSource = self;
    }
    return _detailApi;
}

- (UIView *)topBgV{
    FF_Fetch_UIView(_topBgV, [UIColor whiteColor])
}

- (UIView *)bottomBgV{
    FF_Fetch_UIView(_bottomBgV, [UIColor whiteColor])
}

@end
