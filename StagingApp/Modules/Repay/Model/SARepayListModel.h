
#import <Foundation/Foundation.h>

typedef NS_ENUM(NSInteger, NPOrderStatus) {
    
    NPOrderStatus_WaitRepay = 5,    
    NPOrderStatus_Overdue = 6,      
    NPOrderStatus_Finished = 7,     
};


typedef NS_ENUM(NSInteger, NPPaidStatus) {
    
    NPPaidStatus_Init = 0,    
    NPPaidStatus_PartPay = 1, 
    NPPaidStatus_Paid = 2,    
};


NS_ASSUME_NONNULL_BEGIN

@interface SARepayListModel : NSObject

@property (nonatomic, strong) NSString *billNo;
@property (nonatomic, strong) NSString *currentPeriodDesc; 
@property (nonatomic, strong) NSString *cycleStartDate; 
@property (nonatomic, strong) NSString *currentBorrowCapital; 

@property (nonatomic, strong) NSString *cycleDate; 
@property (nonatomic, strong) NSString *needPayCapital; 
@property (nonatomic, assign) NPOrderStatus orderStatus; 
@property (nonatomic, strong) NSString *orderStatusDesc;  
@property (nonatomic, assign) NPPaidStatus paidStatus; 
@property (nonatomic, strong) NSString *paidStatusDesc; 
@property (nonatomic, assign) NSInteger payType; 
@property (nonatomic, strong) NSString *pledgeNeedPayCapital;  
@property (nonatomic, assign) NPPaidStatus pledgePaidStatus;  
@property (nonatomic, strong) NSString *pledgePaidStatusDesc; 
@property (nonatomic, assign) NSInteger pledgePayType; 

@property (nonatomic, strong) NSString *pledgeRepaymentCapital; 
@property (nonatomic, strong) NSString *pledgeRepaymentDate;  
@property (nonatomic, strong) NSString *repaymentCapital; 
@property (nonatomic, strong) NSString *repaymentDate; 
@property (nonatomic, strong) NSString *tradeNo;

@property (nonatomic, strong) NSString *paidOrderStatusDesc;
@property (nonatomic, strong) NSString *pledgeOrderStatusDesc;

@property (nonatomic, assign) NSInteger overdueDays;
@property (nonatomic, assign) NSInteger repaymentDays;

@property (nonatomic, assign) NSInteger pledgeOverdueDays;
@property (nonatomic, assign) NSInteger pledgeRepaymentDays;


+ (instancetype)objectWithDict:(NSDictionary *)dict;

@end

NS_ASSUME_NONNULL_END
