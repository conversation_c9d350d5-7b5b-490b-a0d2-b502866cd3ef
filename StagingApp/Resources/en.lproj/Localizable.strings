/* 
  Localizable.strings
  ToyStore

  Created by CZM on 2019/6/6.
  Copyright © 2019 Facebook. All rights reserved.
*/


// key: value的形式展示

// 通用
"sparseDdress" ="AC Cash";
"extensionsCarrierCtion" ="Dear users, welcome to use this application.We provide instant loans for urgent needs. Instant cash is deposited directly into your bank account using just your smartphone.";
"efreshConfirmLabel" ="AC Cash";
"serviceCtionDebug" ="Cancel";
"heckIcker" ="Sure";
"footPeriod" ="Search";
"orientationHistory" ="Please enter";
"renewalOntactAgain" ="Submit";
"esultDeviceNetworking" ="Copy";
"onfirmmAvigation" ="Copy Successfully!";
"centerSparseOnfrim" ="and";
"ircleEnewal" ="Homepage";
"tabbarOntacts" ="Profile";
"makerSign" ="Records";
"commonObjectAttribute" ="Repayment";
"roductSize_5" ="Mine";
"abedOnfrimSign" ="Done";
"pickerSend" ="No network link yet!";
"recordVoucher" ="Click and try again";
"asiceAbedPload" ="Ok";
"contactDdressTrading" ="Profile";
"enewalType_nRror" ="₱";

"ubmitCommon" ="Repayment History";

//首页
"home_normal_step1"="Apply";
"home_normal_step2"="Review";
"home_normal_step3"="Withdraw";

"saveLayoutCards" ="Maximum borrowing amount";
"checkOgin" ="Cash arrives quickly";
"enewalSectionCoding" ="Failed";
"cachePlist" ="After the countdown is over, we will notify you of the result of your loan application, please be patient!";
"etworkingReport" ="Days From Re-Apply";
"centerType_fd" ="Application";
"serviceWbwfEnter" ="Get Loan";
"speakerAgainErvice" ="I have read and agree ";
"localizableSheet" ="loan terms";
"speakerAgainErvice" ="I have read and agree ";
"confirm_loan_title1"="Amount";
"confirm_loan_title2"="Period";
"progressFoot" ="please agree to the loan terms";
"tipsLaunchFooter" ="Banking Information";
"gemfilePaidMaker" ="Withdraw Channel";
"asicAvigation" ="Withdraw Account";
"sheetGemfile" ="Total Repayment Amount";
"infoWifi" ="Days From Repayment Date";
"compositeComponent" ="Repayment Date";
"waitingFfline" ="Renewal Fee";
"itemRegister_tp" ="Repayment Date After Paying Renewal Fee";
"progressSure" ="Pay Now";
"crollVoucher" ="Repay Offline";
"editItem" ="ktb";
"factoryRecordIrcle" ="Bank Account";
"compositeComponent7" ="Repayment amount";
"abedNameRder" ="Bank account for repayment";
"crollCroll" ="Upload payment confirmation image";
"centerSendDvance" ="Confirm upload";
"constraintCheckEsponse" ="Please enter bank account for repayment";
"checkCroll" ="Please upload payment voucher";
"componentMethods" ="Modify Bank Info";
"styleEviceEsign" ="Repayment";
"failPaidObject" ="Loan Terms";

// 产品
"fieldType_8x" ="Products";
"backOntact" ="Apply Date";
"basicContact" ="Apply Amount";
"stateRadeTencent" ="Loan Days";
"editOmmonQuota" ="Pending Record";
"uploadDdress" ="Borrow Again";
"roductLoan" ="recommend loan product is chosen by 95% of users";
"toolUtilitiesUbmit" ="More products";
"layoutTool" ="Repayment Date";
"pickerCompressTring" ="Repayment Amount";
"checkTable" ="Repayment Days";
"equestPload" ="Overdue Days";
"advertiseFflineName" ="No data to display";
"eviceResend" ="History";
"ueryEtworking" ="Loan Amount";
"fflineUeryLient" ="Loan Term";
"extension_0RefreshFile" ="Apply";
"otherFactoryAgain" ="Overdue";
"voucherCacheSlide" ="One-click Application";
"localizableDetail" ="Application";
"productHeader" ="Do you want to apply for all available products?";
"enterListNews" ="Application Successful!";


// 认证
"ueryEtwork" ="Data Authentication";
"itemAuto_pBind" ="%lu-Steps";
"resendSize_kAsic" ="Complete %lu steps to apply loans";
"arqueeSupport" ="Next Step";
"esultPaidQuota" ="Personal Information";
"enum_bAvigationErsonal" ="Contact Information";
"clientEsponse" ="Banking Information";
"bindBind" ="Bank Name";
"ontactsTextDevice" ="Face Counterfeiting";
"lientErviceResend" ="Click the selfie to retake";
"lideOmpareEsult" ="ID Card";
"asicPath" ="Full Name";
"ploadTility" ="Card No.";
"size_78Contact" ="Please enter your name";
"eviceMetamacros" ="Please enter your card No.";
"refreshCompositeConfiguration" ="Tips";
"ircleOntactAched" ="Please take a photo of the front of KTP, refer to the sample, Please ensure the photo is clear for application approved";
"selectOnfirmm" ="Take Photo";
"startFaceField" ="Front";
"radientResponse" ="Please enter a valid card number";
"lertMaker" ="Please select two different contact";
"erviceMaker" ="Complete the information\n& unlock the maximum amount";


// 我的
"validatorPload" ="Records";
"sureBasic" ="About Us";
"backHeader" ="Sign Out";
"additionsRealMasonry" ="Order No:";
"attributeCoding" ="Loan Amount";
"fflineOnfirmmClient" ="Processing";
"pathTilityFramework" ="Completed";
"dataRrorNormal" ="Destory Account";
"otherEsign" ="Warning";
"loadingRegister_v" ="Are you sure to delete your current account? Once deleted, all information of your current account will be deleted together, please confirm your choice again!";



/** 登录 */
"resendGeneratorOntacts" ="Sign in";
"localizableOtherTencent" ="Please enter phone number";
"loanCities" ="Please enter OTP";
"esultConst_cLaunch" ="Get OTP";
"layoutFactoryPurpose" ="Sign in";
"ddressEnterBorrow" ="Hello!\nPlease fill out the form below to get started";
"fileHandle" ="Sign out";
"oggerLientExtensions" ="Are you sure to sign out?";
"slideFoot" ="No";
"ploadFactory" ="Yes";
"screenBind" ="Please enter a valid phone number";
"type_xOmpare" ="I have read and agree ";
"personalUditEnewal" ="Terms&conditions";
"supportRade" ="Privacy Policy";
"orderBack" ="Please agree to Terms&conditions and Privacy Policy";
"auto_scBorrow" ="Please enter the complete verification OTP";
"editNetworking" ="Mobile";
"tilityRror" ="Login & Register";
"logint_top_tip1"="Enter the mobile phone number, get the OTP, and go to the next step";
"logint_top_tip2"="Enter the OTP to complete the login/registration";


/** 相机选择弹框 */
"methodsChannel" ="Album";
"efreshPickerLocalizable" ="Camera";
"itemOmpare" ="Cancel";



// 系统权限
"ixedFoot" ="Kind tips";
"const_hLabelReal" ="Can't access your album！";
"protocol_mPanel" ="Your album is not yet allowed to access, please go to Settings-Privacy-Photos to authorize access！";
"tringOntactCarrier" ="your camera is unavailable！";
"recordInput" ="Your camera is not yet allowed to access, please go to Settings-Privacy-Camera to authorize access！";
"waitingMacroOntact" ="Your address book is not yet allowed to access, please go to Settings-Privacy to authorize access！";
"uploadEviceSelect" ="Please go to Settings-Privacy to authorize access to location information！";
"handleRotocolMasonry" ="Setting Now";




///** 网络请求错误提示 */
"ocationNameFramework" ="The network is abnormal, please check whether the network is connected";
"citiesFootList" ="Error getting data, please try again later";
"changeVerticalLayout" ="Get data exception";
"radientInfo" ="Request data exception";
"productPurpose" ="The request timed out, please check the network condition";
"otherEviceTool" ="The network is abnormal, please check whether the network is connected";
"cardsRoduct" ="request succeeded";
"networkingRotocol" ="The request is abnormal, please try again later";


/***********************分割***********************************/

//  新增
"erifyAuto_e" ="New Version";
"asiceUery" ="Please update your app to the latest version!";
"cardsSuccessErvice" ="Update Now";
"confirmProxyRotocol" ="Overdue Days";
"ctionAgain" ="Overdue Fee";

"renewalOrrow" ="Loan Cycle";
"serviceProgressMain" ="Loan Rate";

"enterArqueeConfirm" ="Please give a five-star praise, which will help speed up your review. ";
"clientFooterOnfirm" ="Go to review";

"esultProtocol_35Live" ="please enter repayment amount";
"irclePlist" ="Upload Success!";
"macroTring" ="Back";
"avigationRepay" ="You are overdue";

"snewsOrrowAvigation" ="Confirm Withdrawal Account";
"reformerIxed" ="Type of certificate";
"compressRadient" ="Please select the type of certificate";
