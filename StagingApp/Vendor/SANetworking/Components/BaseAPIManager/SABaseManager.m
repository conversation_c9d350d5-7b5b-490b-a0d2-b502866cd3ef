

#import "SABaseManager.h"
#import "SANetworking.h"
#import "SACache.h"
#import "SALogger.h"


#define AXCallAPI(REQUEST_METHOD, REQUEST_ID)                                                   \
{                                                                                               \
    __weak typeof(self) weakSelf = self;                                                        \
        REQUEST_ID = [[SAApiProxy sharedInstance] call##REQUEST_METHOD##WithParams:apiParams serviceIdentifier:self.child.serviceType methodName:self.child.methodName success:^(SAResponse *response) {                                         \
        __strong typeof(weakSelf) strongSelf = weakSelf;                                        \
        [strongSelf successedOnCallingAPI:response];                                            \
    } fail:^(SAResponse *response) {                                                         \
        __strong typeof(weakSelf) strongSelf = weakSelf;                                        \
        [strongSelf failedOnCallingAPI:response withErrorType:QLAPIManagerErrorTypeDefault];    \
    }];                                                                                         \
    [self.requestIdList addObject:@(REQUEST_ID)];                                                   \
}

@interface SABaseManager ()

@property (nonatomic, strong, readwrite) id fetchedRawData;
@property (nonatomic, assign, readwrite) BOOL isLoading;
@property (nonatomic, assign) BOOL isNativeDataEmpty;

@property (nonatomic, copy, readwrite) NSString *errorMessage;
@property (nonatomic, readwrite) QLAPIManagerErrorType errorType;
@property (nonatomic, strong) NSMutableArray *requestIdList;
@property (nonatomic, strong) SACache *cache;

@end

@implementation SABaseManager

 

- (instancetype)init
{
    if (self = [super init])
    {
        _delegate       = nil;
        _validator      = nil;
        _paramSource    = nil;
        
        _fetchedRawData = nil;
        
        _errorMessage   = nil;
        _errorType = QLAPIManagerErrorTypeDefault;
        
        if ([self conformsToProtocol:@protocol(QLAPIManager)])
        {
            self.child = (id <QLAPIManager>)self;
        }
        else
        {
            self.child = (id <QLAPIManager>)self;
            NSException *exception = [[NSException alloc] initWithName:@"NPAPIBaseManager提示" reason:[NSString stringWithFormat:@"%@没有遵循QLAPIManager协议",self.child] userInfo:nil];
            @throw exception;
        }
    }
    return self;
}

- (void)dealloc
{
    [self cancelAllRequests];
    self.requestIdList = nil;
}

#pragma mark - Public methods

- (void)cancelAllRequests
{
    [[SAApiProxy sharedInstance] cancelRequestWithRequestIDList:self.requestIdList];
    [self.requestIdList removeAllObjects];
}

- (void)cancelRequestWithRequestId:(NSInteger)requestID
{
    [self removeRequestIdWithRequestID:requestID];
    [[SAApiProxy sharedInstance] cancelRequestWithRequestID:@(requestID)];
}

- (id)fetchDataWithReformer:(id<QLAPIManagerDataReformer>)reformer
{
    id resultData = nil;
    NSString *serviceIdentifier = self.child.serviceType;
    SAService *service = [[SAServiceFactory sharedInstance] serviceWithIdentifier:serviceIdentifier];
    if ([service.child respondsToSelector:@selector(handleResponseData:)]) {
        self.fetchedRawData = [service.child handleResponseData:self.fetchedRawData];
    }

    if ([reformer respondsToSelector:@selector(manager:reformData:)])
    {
        resultData = [reformer manager:self reformData:self.fetchedRawData];
    }
    else
    {
        resultData = [self.fetchedRawData mutableCopy];
    }
    return resultData;
}

- (id)fetchFailedRequstMsg:(id<QLAPIManagerDataReformer>)reformer {
    
    id resultData = nil;
    if ([reformer respondsToSelector:@selector(manager:failedReform:)])
    {
        resultData = [reformer manager:self failedReform:self.fetchedRawData];
    }
    else
    {
        resultData = [self.fetchedRawData mutableCopy];
    }
    return resultData;
}

#pragma mark - calling api

- (NSInteger)loadData
{
    NSDictionary *params = [self.paramSource paramsForApi:self];
    NSInteger requestId  = [self loadDataWithParams:params];
    return requestId;
}

- (NSInteger)loadDataWithParams:(NSDictionary *)params
{
    NSInteger requestId = 0;
    NSDictionary *apiParams = [self reformParams:params];
    
    if ([self shouldCallAPIWithParams:apiParams])
    {
        if ([self.validator manager:self isCorrectWithParamsData:apiParams])
        {
            if ([self.child shouldLoadFromNative])
            {
                [self loadDataFromNative];
            }
            
            if ([self shouldCache] && [self hasCacheWithParams:apiParams])
            {
                return 0;
            }
            
            if ([self isReachable])
            {
                self.isLoading = YES;
                
                if ([self.child respondsToSelector:@selector(timeOutSecond)])
                {
                    [SANetworkingConfigurationManager sharedInstance].apiNetworkingTimeoutSeconds = self.child.timeOutSecond;
                }
                
                switch (self.child.requestType)
                {
                    case QLAPIManagerRequestTypeGet:
                        AXCallAPI(GET, requestId);
                        break;
                    case QLAPIManagerRequestTypePost:
                        AXCallAPI(POST, requestId);
                        break;
                    case QLAPIManagerRequestTypePut:
                        AXCallAPI(PUT, requestId);
                        break;
                    case QLAPIManagerRequestTypeDelete:
                        AXCallAPI(DELETE, requestId);
                        break;
                    case QLAPIManagerRequestTypeUpload:
                        requestId = [self callUploadAPI:apiParams requestId:requestId];
                        break;
                    default:
                        break;
                }
                
                NSMutableDictionary *params = [apiParams mutableCopy];
                params[kNPAPIBaseManagerRequestID] = @(requestId);
                [self afterCallingAPIWithParams:params];
                return requestId;
                
            }
            else
            {
                [self failedOnCallingAPI:nil withErrorType:QLAPIManagerErrorTypeNoNetWork];
                return requestId;
            }
        }
        else
        {
            [self failedOnCallingAPI:nil withErrorType:QLAPIManagerErrorTypeParamsError];
            return requestId;
        }
    }
    return requestId;
}

- (NSInteger)callUploadAPI:(NSDictionary *)apiParams requestId:(NSInteger)requestId {
    if (![self valideUploadParams]) {
        [self failedOnCallingAPI:nil withErrorType:QLAPIManagerErrorTypeParamsError];
        return requestId;
    }
    __weak typeof(self) weakSelf = self;
    requestId = [[SAApiProxy sharedInstance] callUPLOADWithParams:apiParams
                                                serviceIdentifier:self.child.serviceType
                                                       methodName:self.child.methodName
                                                             data:[self.paramSource dataForUploadApi:self]
                                                         mimeType:[self.child requestMimeType]
                                                         fileName:[self.child fileName]
                                                          success:^(SAResponse *response) {
                                                              __strong typeof(weakSelf) strongSelf = weakSelf;
                                                              [strongSelf successedOnCallingAPI:response];
                                                          } fail:^(SAResponse *response) {
                                                              __strong typeof(weakSelf) strongSelf = weakSelf;
                                                              [strongSelf failedOnCallingAPI:response withErrorType:QLAPIManagerErrorTypeDefault];
                                                          }];
    [self.requestIdList addObject:@(requestId)];
    return requestId;
}

- (BOOL)valideUploadParams {
    id fileData = nil;
    if ([self.paramSource respondsToSelector:@selector(dataForUploadApi:)]) {
        fileData = [self.paramSource dataForUploadApi:self];
    }
    NSString *mimeType = nil;
    if ([self.child respondsToSelector:@selector(requestMimeType)]) {
        mimeType = [self.child requestMimeType];
    }
    NSString *fileName = nil;
    if ([self.child respondsToSelector:@selector(fileName)]) {
        fileName = [self.child fileName];
    }
    if (fileData && mimeType && fileName) {
        return YES;
    }
    return NO;
}

#pragma mark - api callbacks

- (void)successedOnCallingAPI:(SAResponse *)response
{
    self.isLoading = NO;
    self.response  = response;
    
    if ([self.child shouldLoadFromNative])
    {
        if (response.isCache == NO) {
            [[NSUserDefaults standardUserDefaults] setObject:response.responseData forKey:[self.child methodName]];
        }
    }
    
    if (response.content)
    {
        self.fetchedRawData = [response.content copy];
    }
    else
    {
        self.fetchedRawData = [response.contentString copy];
    }
    
    [self removeRequestIdWithRequestID:response.requestId];
    if ([self.validator manager:self isCorrectWithCallBackData:self.fetchedRawData])
    {
        if ([self shouldCache] && !response.isCache)
        {
            [self.cache saveCacheWithData:response.responseData serviceIdentifier:self.child.serviceType methodName:self.child.methodName requestParams:response.requestParams];
        }
        
        if ([self beforePerformSuccessWithResponse:response])
        {
            if ([self.child shouldLoadFromNative])
            {
                if (response.isCache == YES)
                {
                    [self.delegate managerCallAPIDidSuccess:self];
                }
                if (self.isNativeDataEmpty)
                {
                    [self.delegate managerCallAPIDidSuccess:self];
                }
            }
            else
            {
                [self.delegate managerCallAPIDidSuccess:self];
            }
        }
        [self afterPerformSuccessWithResponse:response];
    }
    else
    {
        [self failedOnCallingAPI:response withErrorType:QLAPIManagerErrorTypeNoContent];
    }
}

- (void)failedOnCallingAPI:(SAResponse *)response withErrorType:(QLAPIManagerErrorType)errorType
{
    NSString *serviceIdentifier = self.child.serviceType;
    SAService *service = [[SAServiceFactory sharedInstance] serviceWithIdentifier:serviceIdentifier];
    
    self.isLoading = NO;
    self.response = response;
    BOOL needCallBack = YES;
    
    if (response.content)
    {
        self.fetchedRawData = [response.content copy];
    }
    else
    {
        self.fetchedRawData = [response.contentString copy];
    }
    
    if ([service.child respondsToSelector:@selector(shouldCallBackByFailedOnCallingAPI:)]) {
        needCallBack = [service.child shouldCallBackByFailedOnCallingAPI:self.fetchedRawData];
    }
    
    if (!needCallBack) {
        return;
    }
    
    self.errorType = errorType;
    [self removeRequestIdWithRequestID:response.requestId];
    
    
    
    if ([self beforePerformFailWithResponse:response]) {
        [self.delegate managerCallAPIDidFailed:self];
    }
    [self afterPerformFailWithResponse:response];
}

#pragma mark - method for interceptor

- (BOOL)beforePerformSuccessWithResponse:(SAResponse *)response
{
    BOOL result = YES;
    
    self.errorType = QLAPIManagerErrorTypeSuccess;
    if (self != self.interceptor && [self.interceptor respondsToSelector:@selector(manager: beforePerformSuccessWithResponse:)])
    {
        result = [self.interceptor manager:self beforePerformSuccessWithResponse:response];
    }
    return result;
}

- (void)afterPerformSuccessWithResponse:(SAResponse *)response
{
    if (self != self.interceptor && [self.interceptor respondsToSelector:@selector(manager:afterPerformSuccessWithResponse:)])
    {
        [self.interceptor manager:self afterPerformSuccessWithResponse:response];
    }
}

- (BOOL)beforePerformFailWithResponse:(SAResponse *)response
{
    BOOL result = YES;
    if (self != self.interceptor && [self.interceptor respondsToSelector:@selector(manager:beforePerformFailWithResponse:)])
    {
        result = [self.interceptor manager:self beforePerformFailWithResponse:response];
    }
    return result;
}

- (void)afterPerformFailWithResponse:(SAResponse *)response
{
    if (self != self.interceptor && [self.interceptor respondsToSelector:@selector(manager:afterPerformFailWithResponse:)])
    {
        [self.interceptor manager:self afterPerformFailWithResponse:response];
    }
}

- (BOOL)shouldCallAPIWithParams:(NSDictionary *)params
{
    if (self != self.interceptor && [self.interceptor respondsToSelector:@selector(manager:shouldCallAPIWithParams:)])
    {
        return [self.interceptor manager:self shouldCallAPIWithParams:params];
    }
    else
    {
        return YES;
    }
}

- (void)afterCallingAPIWithParams:(NSDictionary *)params
{
    if (self != self.interceptor && [self.interceptor respondsToSelector:@selector(manager:afterCallingAPIWithParams:)])
    {
        [self.interceptor manager:self afterCallingAPIWithParams:params];
    }
}

#pragma mark - method for child

- (void)cleanData
{
    [self.cache clean];
    self.fetchedRawData = nil;
    self.errorMessage = nil;
    self.errorType = QLAPIManagerErrorTypeDefault;
}

- (NSDictionary *)reformParams:(NSDictionary *)params
{
    IMP childIMP = [self.child methodForSelector:@selector(reformParams:)];
    IMP selfIMP = [self methodForSelector:@selector(reformParams:)];
    
    if (childIMP == selfIMP)
    {
        return params;
    }
    else
    {
        NSDictionary *result = nil;
        result = [self.child reformParams:params];
        if (result) {
            return result;
        } else {
            return params;
        }
    }
}

- (BOOL)shouldCache
{
    return [SANetworkingConfigurationManager sharedInstance].shouldCache;
}

#pragma mark - private methods

- (void)removeRequestIdWithRequestID:(NSInteger)requestId
{
    NSNumber *requestIDToRemove = nil;
    for (NSNumber *storedRequestId in self.requestIdList)
    {
        if ([storedRequestId integerValue] == requestId) {
            requestIDToRemove = storedRequestId;
        }
    }
    if (requestIDToRemove)
    {
        [self.requestIdList removeObject:requestIDToRemove];
    }
}

- (BOOL)hasCacheWithParams:(NSDictionary *)params
{
    NSString *serviceIdentifier = self.child.serviceType;
    NSString *methodName = self.child.methodName;
    NSData *result = [self.cache fetchCachedDataWithServiceIdentifier:serviceIdentifier methodName:methodName requestParams:params];
    
    if (result == nil) return NO;
    
    __weak typeof(self) weakSelf = self;
    dispatch_async(dispatch_get_main_queue(), ^{
        
        __strong typeof (weakSelf) strongSelf = weakSelf;
        SAResponse *response = [[SAResponse alloc] initWithData:result];
        response.requestParams = params;
        [SALogger logDebugInfoWithCachedResponse:response methodName:methodName serviceIdentifier:[[SAServiceFactory sharedInstance] serviceWithIdentifier:serviceIdentifier]];
        [strongSelf successedOnCallingAPI:response];
    });
    return YES;
}

- (void)loadDataFromNative
{
    NSDictionary *result = [NSJSONSerialization JSONObjectWithData:[[NSUserDefaults standardUserDefaults] dataForKey:self.child.methodName] options:0 error:NULL];

    if (result)
    {
        self.isNativeDataEmpty = NO;
        __weak typeof(self) weakSelf = self;
        dispatch_async(dispatch_get_main_queue(), ^{
            
            __strong typeof(weakSelf) strongSelf = weakSelf;
            SAResponse *response = [[SAResponse alloc] initWithData:[NSJSONSerialization dataWithJSONObject:result options:0 error:NULL]];
            [strongSelf successedOnCallingAPI:response];
        });
    }
    else
    {
        self.isNativeDataEmpty = YES;
    }
}

#pragma mark - getters and setters

- (SACache *)cache
{
    if (_cache == nil) {
        _cache = [SACache sharedInstance];
    }
    return _cache;
}

- (NSMutableArray *)requestIdList
{
    if (_requestIdList == nil) {
        _requestIdList = [[NSMutableArray alloc] init];
    }
    return _requestIdList;
}

- (BOOL)isReachable
{
    BOOL isReachability = [SANetworkingConfigurationManager sharedInstance].isReachable;
    if (!isReachability) {
        self.errorType = QLAPIManagerErrorTypeNoNetWork;
    }
    return isReachability;
}

- (BOOL)isLoading
{
    if (self.requestIdList.count == 0) {
        _isLoading = NO;
    }
    return _isLoading;
}

- (BOOL)shouldLoadFromNative
{
    return NO;
}

@end


