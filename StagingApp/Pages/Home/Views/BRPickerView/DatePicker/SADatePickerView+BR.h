
#import "SADatePickerView.h"

NS_ASSUME_NONNULL_BEGIN

@interface SADatePickerView (BR)

 
- (NSDate *)handlerMinDate:(nullable NSDate *)minDate;

 
- (NSDate *)handlerMaxDate:(nullable NSDate *)maxDate;

 
- (NSDate *)handlerSelectDate:(nullable NSDate *)selectDate dateFormat:(NSString *)dateFormat;

 
- (NSString *)br_stringFromDate:(NSDate *)date dateFormat:(NSString *)dateFormat;

 
- (NSDate *)br_dateFromString:(NSString *)dateString dateFormat:(NSString *)dateFormat;

 
- (NSComparisonResult)br_compareDate:(NSDate *)date targetDate:(NSDate *)targetDate dateFormat:(NSString *)dateFormat;

 
- (NSArray *)getYearArr;

 
- (NSArray *)getMonthArr:(NSInteger)year;

 
- (NSArray *)getDayArr:(NSInteger)year month:(NSInteger)month;

 
- (NSArray *)getHourArr:(NSInteger)year month:(NSInteger)month day:(NSInteger)day;

 
- (NSArray *)getMinuteArr:(NSInteger)year month:(NSInteger)month day:(NSInteger)day hour:(NSInteger)hour;

 
- (NSArray *)getSecondArr:(NSInteger)year month:(NSInteger)month day:(NSInteger)day hour:(NSInteger)hour minute:(NSInteger)minute;

 
- (NSArray *)getMonthWeekArr:(NSInteger)year month:(NSInteger)month;

 
- (NSArray *)getYearWeekArr:(NSInteger)year;

  
- (NSArray *)getQuarterArr:(NSInteger)year;

 
- (void)setupPickerView:(UIView *)pickerView toView:(UIView *)view;

 
- (NSArray *)setupPickerUnitLabel:(UIPickerView *)pickerView unitArr:(NSArray *)unitArr;

- (NSString *)getYearNumber:(NSInteger)year;

- (NSString *)getMDHMSNumber:(NSInteger)number;

- (NSString *)getYearText:(NSArray *)yearArr row:(NSInteger)row;

- (NSString *)getMonthText:(NSArray *)monthArr row:(NSInteger)row;

- (NSString *)getDayText:(NSArray *)dayArr row:(NSInteger)row mSelectDate:(NSDate *)mSelectDate;

- (NSString *)getHourText:(NSArray *)hourArr row:(NSInteger)row;

- (NSString *)getMinuteText:(NSArray *)minuteArr row:(NSInteger)row;

- (NSString *)getSecondText:(NSArray *)secondArr row:(NSInteger)row;

- (NSString *)getWeekText:(NSArray *)weekArr row:(NSInteger)row;

- (NSString *)getQuarterText:(NSArray *)quarterArr row:(NSInteger)row;

- (NSString *)getAMText;

- (NSString *)getPMText;

- (NSString *)getYearUnit;

- (NSString *)getMonthUnit;

- (NSString *)getDayUnit;

- (NSString *)getHourUnit;

- (NSString *)getMinuteUnit;

- (NSString *)getSecondUnit;

- (NSString *)getWeekUnit;

- (NSString *)getQuarterUnit;

- (NSInteger)getIndexWithArray:(NSArray *)array object:(NSString *)obj;

@end

NS_ASSUME_NONNULL_END
