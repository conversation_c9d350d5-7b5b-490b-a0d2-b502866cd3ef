
#import "SAOrderInfoModel.h"
#import <MJExtension.h>

@implementation SAOrderInfoModel

+ (instancetype)objectWithDict:(NSDictionary *)dict {
    return [self mj_objectWithKeyValues:dict];
}

- (NSArray *)auditProgressItems {
    if (_auditProgressItems == nil) {
        
        NSMutableArray *mutArr = [NSMutableArray arrayWithCapacity:10];
        for (int i = 0; i < 10; i++) {
            SAAuditProgressItemModel *item = [[SAAuditProgressItemModel alloc] init];
            if (i == 0) {
                
                item.title = NSLocalizedString(@"home_auditProgress_list_borrrow_num", nil);
                item.value = self.tradeNo;
            } else if (i == 1) {
                
                item.title = NSLocalizedString(@"home_auditProgress_list_name", nil);
                item.value = self.borrowerName;
                
            } else if (i == 2) {
                
                item.title = NSLocalizedString(@"home_auditProgress_list_ktp_num", nil);
                item.value = self.ktpNo;
                
            } else if (i == 3) {
                
                item.title = NSLocalizedString(@"home_auditProgress_list_loan_time", nil);
                item.value = self.borrowDate;
                
            } else if (i == 4) {
                
                item.title = NSLocalizedString(@"home_auditProgress_list_loan_amount", nil);
                item.value = self.orderAmount;
                
            } else if (i == 5) {
                
                item.title = NSLocalizedString(@"home_auditProgress_list_loan_term", nil);
                item.value = [NSString stringWithFormat:@"%ld%@", self.periodLength, self.unit];
                
            } else if (i == 6) {
                
                item.title = NSLocalizedString(@"home_auditProgress_list_repayment_amount", nil);
                item.value = self.repaymentAmount;
                
            } else if (i == 7) {
                
                item.title = NSLocalizedString(@"home_auditProgress_list_bank", nil);
                item.value = self.bankName;
                
            } else if (i == 8) {
                
                item.title = NSLocalizedString(@"home_auditProgress_list_bank_num", nil);
                item.value = self.bankNo;
                
            } else {
                
                item.title = NSLocalizedString(@"home_auditProgress_list_protocol", nil);
                item.value = NSLocalizedString(@"home_auditProgress_list_see", nil);
            }
            
            [mutArr addObject:item];
        }
        _auditProgressItems = [NSArray arrayWithArray:mutArr];
    }
    return _auditProgressItems;
}

- (CGFloat)contentHeigt {
    if (_contentHeigt) {
        return _contentHeigt;
    }
    
    CGFloat heig = XX_6(41);
    for (SAAuditProgressItemModel *item in self.auditProgressItems) {
        heig += item.cellHeight;
    }
    
    CGFloat maxH = UIScreenHeight - kTabBarHeight - kTopHeight - XX_6(147);
    
    if (heig > maxH) {
        heig = maxH;
    }
    _contentHeigt = heig;
    return _contentHeigt;
}

- (CGFloat)repaymentHeadCellHeight {
    if (_repaymentHeadCellHeight) {
        return _repaymentHeadCellHeight;
    }
    
    if ([self.payType isEqual:@4]) {        
        
        if ([SADeviceTool isIndonesian]) {
            _repaymentHeadCellHeight = XX_6(236);
        } else {
            _repaymentHeadCellHeight = XX_6(216);
        }
        
    } else {
        if (!self.overdue) {        
            
            if ([SADeviceTool isIndonesian]) {
                _repaymentHeadCellHeight = XX_6(310);
            } else {
                _repaymentHeadCellHeight = XX_6(286);
            }
            
        } else {        
            
            _repaymentHeadCellHeight = XX_6(216);
        }
    }
    
    return _repaymentHeadCellHeight;
}


- (NSArray *)borrowDetailData {
    if (_borrowDetailData == nil) {
        
        if (self.overdue) {     
            _borrowDetailData = @[
            @{@"title":NSLocalizedString(@"borrowContract__list_idNum", nil), @"desc":self.tradeNo},
            @{@"title":NSLocalizedString(@"borrowContract__list_name", nil), @"desc":self.borrowerName},
            @{@"title":NSLocalizedString(@"borrowContract__list_KTPNum", nil), @"desc":self.ktpNo},
            @{@"title":NSLocalizedString(@"borrowContract__list_time", nil), @"desc":self.borrowDate},
            @{@"title":NSLocalizedString(@"borrowContract__list_borrowAmount", nil), @"desc":self.orderAmount},
            @{@"title":NSLocalizedString(@"borrowContract__list_term", nil), @"desc":self.borrowDate},
            @{@"title":NSLocalizedString(@"borrowContract__list_overdueRate", nil), @"desc":self.overdueFeeRate},
            @{@"title":NSLocalizedString(@"borrowContract__list_overdueDate", nil), @"desc":[NSString stringWithFormat:@"%ld", (long)self.overdueDays]},
            @{@"title":NSLocalizedString(@"borrowContract__list_overdueAmount", nil), @"desc":self.overdueFee},
            @{@"title":NSLocalizedString(@"borrowContract__list_repayAmount", nil), @"desc":self.repaymentAmount},
            @{@"title":NSLocalizedString(@"borrowContract__list_bank", nil), @"desc":self.bankName},
            @{@"title":NSLocalizedString(@"borrowContract__list_cardId", nil), @"desc":self.bankNo},
            @{@"title":NSLocalizedString(@"borrowContract__list_protocol", nil), @"desc":NSLocalizedString(@"borrowContract__list_see", nil)}
                                  ];
            
        } else {
            _borrowDetailData = @[
              @{@"title":NSLocalizedString(@"borrowContract__list_idNum", nil), @"desc":self.tradeNo},
              @{@"title":NSLocalizedString(@"borrowContract__list_name", nil), @"desc":self.borrowerName},
              @{@"title":NSLocalizedString(@"borrowContract__list_KTPNum", nil), @"desc":self.ktpNo},
              @{@"title":NSLocalizedString(@"borrowContract__list_time", nil), @"desc":self.borrowDate},
              @{@"title":NSLocalizedString(@"borrowContract__list_borrowAmount", nil), @"desc":self.orderAmount},
              @{@"title":NSLocalizedString(@"borrowContract__list_term", nil), @"desc":[NSString stringWithFormat:@"%@%@", self.borrowDate, self.unit]},
              @{@"title":NSLocalizedString(@"borrowContract__list_repayAmount", nil), @"desc":self.repaymentAmount},
              @{@"title":NSLocalizedString(@"borrowContract__list_bank", nil), @"desc":self.bankName},
              @{@"title":NSLocalizedString(@"borrowContract__list_cardId", nil), @"desc":self.bankNo},
              @{@"title":NSLocalizedString(@"borrowContract__list_protocol", nil), @"desc":NSLocalizedString(@"borrowContract__list_see", nil)}
                                  ];
        }
    }
    return _borrowDetailData;
}


@end
