//
//  NPOnlineRepayViewController.m
//  StagingApp
//
//  Created by Hardeen on 2024/10/25.
//  Copyright © 2024 Facebook. All rights reserved.
//

#import "NPOnlineRepayViewController.h"
#import "SAGetCashierInfoAPi.h"
#import "SACashierPayStatusApi.h"
#import "SACashierDoPayApi.h"
#import "SAPayStatusViewController.h"
#import "SAVerifyBankCardViewController.h"
#import "NPCashierCardCell.h"

#import "SARepayCashierModel.h"

#define kSingH      80
#define kHeaderH    30

@interface NPOnlineRepayViewController ()<QLAPIManagerParamSource, QLAPIManagerCallBackDelegate, NPRequestMethodProtocol, UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, strong) SAGetCashierInfoAPi *infoApi;
@property (nonatomic, strong) SACashierDoPayApi *doPayApi;

@property (nonatomic, strong) SARepayCashierModel *model;
@property (nonatomic, assign) NSInteger selectedIdx;


@property (nonatomic, strong) UILabel *titLab;
@property (nonatomic, strong) UILabel *amountLab;

@property (nonatomic, strong) UITableView *tableV;

@property (nonatomic, strong) SAGradientButton *payBtn;

@end

@implementation NPOnlineRepayViewController

#pragma mark - Life cycle

- (void)viewWillAppear:(BOOL)animated
{
    [super viewWillAppear:animated];
    
    [self showActivityHUD:nil];
    [self.infoApi loadData];
}

- (void)viewDidLoad
{
    [super viewDidLoad];
    
    self.navigationItem.title = @"收银台";
    self.view.backgroundColor = [UIColor colorWithHex:0xf6f6f6];
    
    self.selectedIdx = 0;
}

- (void)createUI
{
    [self.view addSubview:self.titLab];
    [self.view addSubview:self.amountLab];
    [self.view addSubview:self.tableV];
    [self.view addSubview:self.payBtn];
    
    [self.titLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.centerX.equalTo(self.view);
        make.top.equalTo(self.view).offset(30);
    }];
    [self.amountLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.centerX.equalTo(self.view);
        make.top.equalTo(self.titLab.mas_bottom).offset(5);
    }];
    
    [self.tableV mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.right.equalTo(self.view);
        make.height.mas_equalTo(kSingH *  self.model.bankRoList.count + kHeaderH);
        make.top.equalTo(self.amountLab.mas_bottom).offset(40);
    }];
    
    [self.payBtn mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.equalTo(self.view).offset(20);
        make.centerX.equalTo(self.view);
        make.top.equalTo(self.tableV.mas_bottom).offset(30);
        make.height.mas_equalTo(48);
    }];
    self.payBtn.layer.masksToBounds = YES;
    self.payBtn.layer.cornerRadius = 7;
}

#pragma mark - Action

- (void)payAction
{
    [self showActivityHUD:nil];
    [self.doPayApi loadData];
}

- (void)jumpBindCard
{
    SAVerifyBankCardViewController *vc = [SAVerifyBankCardViewController new];
    vc.orderType = [NSString stringWithFormat:@"@%ld", self.model.orderType];
    [self.navigationController pushViewController:vc animated:YES];
}

#pragma mark - QLAPIManagerCallBackDelegate

- (void)managerCallAPIDidSuccess:(SABaseManager *)manager
{
    [self hideHUDView];
    
    if(manager == self.infoApi)
    {
        NSDictionary *dataDict = [manager fetchDataWithReformer:self.infoApi];
        
        self.model = [SARepayCashierModel objectWithDict:dataDict];
        
        if(self.model.bankRoList.count == 0){
            [self jumpBindCard];
        }else{
            [self createUI];
            
            self.amountLab.text = [NSString stringWithFormat:@"%.2f", self.model.amount/100.0];
            self.titLab.text = @"支付金额";
            
            [self.tableV reloadData];
        }
        
    }
    else if (manager == self.doPayApi)
    {
        NSDictionary *dataDict = [manager fetchDataWithReformer:self.doPayApi];
        NSString *errorMsg = dataDict[@"errorMsg"];
        if ([NSString judgeStringExist:errorMsg]) {
            NSString *errorCode = dataDict[@"errorCode"];
            [SATool textStateWindowHUD:errorMsg];
            if([errorCode isEqualToString:@"400049"] || [errorCode isEqualToString:@"400032"]){
                [self jumpBindCard];
            }
        }else{
            SAPayStatusViewController *vc = [SAPayStatusViewController new];
            vc.key = self.key;
            [self.navigationController pushViewController:vc animated:YES];
        }
    }
}

- (void)managerCallAPIDidFailed:(SABaseManager *)manager
{
    [self hideHUDView];
    
    NSString *failMsg  = [SAResponseHandle netWorkErrorMsg:[manager fetchDataWithReformer:nil] andErrorType:manager.errorType];
    [SATool textStateHUD:failMsg];
//    if(manager == self.doPayApi){
//        NSDictionary *err = [manager fetchDataWithReformer:nil] ;
//        NSInteger code = [err[@"code"] integerValue];
//        if (code == 400049) {
//            [self jumpBindCard];
//        }
//    }
}

#pragma mark - QLAPIManagerParamSource

- (NSDictionary *)paramsForApi:(SABaseManager *)manager
{
    if(manager == self.doPayApi)
    {
        return @{@"tradeType": @"BINDCARD", @"orderType": @(self.model.orderType), @"bindId": self.model.bankRoList[self.selectedIdx].bindId };
    }
    return [NSDictionary dictionary];
}

- (NSString *)methodForApi:(SABaseManager *)manager
{
    if(manager == self.infoApi)
    {
        return [NSString stringWithFormat:@"029239205cd04f4b82dc09c7f291fde5/%@", self.key]; //cashier/info
    }
    if(manager == self.doPayApi)
    {
//        return [NSString stringWithFormat:@"cashier/doPay/%@", self.key];
        return [NSString stringWithFormat:@"e6e47205e436470b917283476242a850/%@", self.key];
    }
    
    return @"";
}

#pragma mark - UITableViewDataSource

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return 1;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return self.model.bankRoList.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    static NSString *repayCID = @"Repayment-Cell";
    NPCashierCardCell *cell = [tableView dequeueReusableCellWithIdentifier:repayCID];
    if (cell == nil) {
        cell = [[NPCashierCardCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:repayCID];
    }
    cell.backgroundColor = [UIColor clearColor];
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    
    SACashierCardModel *model = self.model.bankRoList[indexPath.row];
    cell.cardModel = model;
    cell.choosed = indexPath.row == self.selectedIdx;
    
    return  cell;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath
{
    return 80;
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section
{
    return kHeaderH;
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section
{
    UIView *view = [UIView new];
    UILabel *lab = [UILabel new];
    lab.text = @"支付方式";
    lab.textColor = [UIColor colorWithHex:0x666666];
    lab.font = Font_XX6(12);
    [view addSubview:lab];
    [lab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.centerY.equalTo(view);
        make.left.equalTo(view).offset(12);
    }];
    return view;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    
    self.selectedIdx = indexPath.row;
    [self.tableV reloadData];
}

#pragma mark - getter

- (UILabel *)titLab{
    FF_Fetch_UILable_CenterX(_titLab, [UIColor colorWithHex:0x666666], Font_XX6(12))
}

- (UILabel *)amountLab{
    FF_Fetch_UILable_CenterX(_amountLab, [UIColor blackColor], BoldFont_XX6(30))
}

- (UITableView *)tableV {
    if (_tableV == nil) {
        _tableV = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
        _tableV.dataSource      = self;
        _tableV.delegate        = self;
        if (@available(iOS 11.0, *)) {
            _tableV.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
        }
        _tableV.backgroundColor = [UIColor clearColor];
        _tableV.separatorStyle = UITableViewCellSeparatorStyleNone;
    }
    return _tableV;
}

- (SAGradientButton *)payBtn{
    if (_payBtn == nil) {
        _payBtn = [SAGradientButton buttonWithType:UIButtonTypeCustom];
        [_payBtn gradientBackgroundColors:@[kButtonStartColor, kButtonEndColor] locations:nil startPoint:CGPointMake(0, 0.5) endPoint:CGPointMake(1, 0.5)];
        [_payBtn addTarget:self action:@selector(payAction) forControlEvents:UIControlEventTouchUpInside];
        [_payBtn setTitle:@"确认支付" forState:UIControlStateNormal];
        [_payBtn setTitleColor:kHomeTextColor forState:UIControlStateNormal];
    }
    
    return _payBtn;
}

- (SAGetCashierInfoAPi *)infoApi{
    if (_infoApi == nil) {
        _infoApi = [[SAGetCashierInfoAPi alloc] init];
        _infoApi.delegate = self;
        _infoApi.paramSource = self;
        _infoApi.methodSource = self;
    }
    return _infoApi;
}

- (SACashierDoPayApi *)doPayApi{
    if (_doPayApi == nil) {
        _doPayApi = [[SACashierDoPayApi alloc] init];
        _doPayApi.delegate = self;
        _doPayApi.paramSource = self;
        _doPayApi.methodSource = self;
    }
    return _doPayApi;
}

@end
