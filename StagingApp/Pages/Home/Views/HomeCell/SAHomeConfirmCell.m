
#import "SAHomeConfirmCell.h"
#import "SAConfrimTradeViewController.h"

@interface SAHomeConfirmCell()

@property (nonatomic, strong) UIImageView *stepIcon;
@property (nonatomic, strong) UIButton *actionBtn;

@property (nonatomic, strong) UILabel *statusLab;
@property (nonatomic, strong) UILabel *tipsLab;

@property (nonatomic, strong) UILabel *stepTitLab;
@property (nonatomic, strong) UIView *stepBoxV;

@end

@implementation SAHomeConfirmCell

#pragma mark - Life cycle

- (void)renderCoreBg
{
    [self addCoreSubviews];
    [self setCoreConstraints];
    
    [self.coreBg mas_updateConstraints:^(SAConstraintMaker *make) {
        make.height.mas_equalTo(380);
    }];
    
    [self.actionBtn setTitle:self.model.buttonTxt forState:UIControlStateNormal];
    self.statusLab.text = self.model.tips;
    self.tipsLab.text = self.model.bigTips;
    self.stepTitLab.text = @"贷款步骤";
    
    
}

- (void)addCoreSubviews
{
    [self.coreBg addSubview:self.stepIcon];
    [self.coreBg addSubview:self.actionBtn];
    [self.coreBg addSubview:self.statusLab];
    [self.coreBg addSubview:self.tipsLab];
    [self.coreBg addSubview:self.stepTitLab];
    [self.coreBg addSubview:self.stepBoxV];
}

- (void)setCoreConstraints
{
    CGFloat verGap = 15;
    
    [self.stepIcon mas_makeConstraints:^(SAConstraintMaker *make) {
        make.top.equalTo(self.coreBg).offset(20);
        make.left.equalTo(self.coreBg).offset(30);
        make.centerX.equalTo(self.coreBg);
        make.height.mas_equalTo(50);
    }];
    self.stepIcon.contentMode = UIViewContentModeScaleAspectFill;
    
    [self.statusLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.centerX.equalTo(self.coreBg);
        make.top.equalTo(self.stepIcon.mas_bottom).offset(30);
    }];
    
    [self.tipsLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.centerX.equalTo(self.coreBg);
        make.top.equalTo(self.statusLab.mas_bottom).offset(7);
        make.left.equalTo(self.coreBg).offset(30);
    }];
    
    [self.actionBtn mas_makeConstraints:^(SAConstraintMaker *make) {
        make.centerX.equalTo(self.stepIcon);
        make.left.equalTo(self.coreBg).offset(30);
        make.top.equalTo(self.tipsLab.mas_bottom).offset(20);
        make.height.mas_equalTo(48);
    }];
    self.actionBtn.layer.masksToBounds = YES;
    self.actionBtn.layer.cornerRadius = 7;
    
    [self.stepTitLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.equalTo(self.coreBg).offset(20);
        make.top.equalTo(self.actionBtn.mas_bottom).offset(20);
    }];
    
    [self.stepBoxV mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.equalTo(self.stepTitLab);
        make.top.equalTo(self.stepTitLab.mas_bottom).offset(5);
    }];
}

#pragma mark - action

- (void)onClickAction
{
    SAConfrimTradeViewController *vc = [SAConfrimTradeViewController new];
    vc.hidesBottomBarWhenPushed = YES;
    vc.tradeNo = self.model.tradeNo;
    [[SACommonTool currentViewController].navigationController pushViewController:vc animated:YES];
}

#pragma mark - setter


#pragma mark - getter

- (UIImageView *)stepIcon{
    FF_Fetch_UIImageViewWithImage(_stepIcon, [UIImage imageNamed:@"auth_wait_for_pay_bg"])
}

- (UIButton *)actionBtn
{
    if (_actionBtn == nil) {
        _actionBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_actionBtn setTitleColor:kHomeTextColor forState:UIControlStateNormal];
        [_actionBtn addTarget:self action:@selector(onClickAction) forControlEvents:UIControlEventTouchUpInside];
        _actionBtn.titleLabel.font = BoldFont_XX6(16);
        _actionBtn.backgroundColor = kPrimaryColor;
    }
    
    return _actionBtn;
}

- (UILabel *)statusLab{
    FF_Fetch_UILable_CenterX(_statusLab, kHomeTextColor, Font_XX6(13))
}

- (UILabel *)tipsLab{
    FF_Fetch_UILable_NumZERO_CenterX(_tipsLab, kHomeTextColor, BoldFont_XX6(18) , 0)
}

- (UILabel *)stepTitLab{
    FF_Fetch_UILable(_stepTitLab, [UIColor colorWithHex:0x333333], Font_XX6(15))
}

- (UIView *)stepBoxV{
    if(_stepBoxV == nil){
        _stepBoxV = [UIView new];
        
        __block UIView *lastV = nil;
        [self.model.borrowStepVOList enumerateObjectsUsingBlock:^(NPHomeStepModel *item, NSUInteger idx, BOOL * _Nonnull stop) {
            
            UIView *view = [UIView new];
            [_stepBoxV addSubview:view];
            
            UIImageView *icon = [[UIImageView alloc] init];
            icon.image = item.finished ? [UIImage customImageNamed:@"verify_check_sel"] : [UIImage imageNamed:@"verify_check_nor"];
            [view addSubview:icon];
            
            UILabel *textLab = [UILabel new];
            textLab.textColor = kHomeTextColor;
            textLab.font = Font_XX6(11);
            textLab.text = [NSString stringWithFormat:@"%ld.%@", (idx+1), item.content];
            [view addSubview:textLab];
            
            [view mas_makeConstraints:^(SAConstraintMaker *make) {
                make.left.equalTo(_stepBoxV);
                make.height.mas_equalTo(30);
                make.top.equalTo(_stepBoxV).offset(idx*40);
            }];
            [icon mas_makeConstraints:^(SAConstraintMaker *make) {
                make.width.height.mas_equalTo(15);
                make.left.equalTo(view);
                make.centerY.equalTo(view);
            }];
            [textLab mas_makeConstraints:^(SAConstraintMaker *make) {
                make.centerY.equalTo(view);
                make.left.equalTo(icon.mas_right).offset(20);
            }];
            
            
            if(lastV){
                UIView *line = [UIView new];
                line.backgroundColor = [UIColor colorWithHex:0xcdcdcd];
                [_stepBoxV addSubview:line];
                [line mas_makeConstraints:^(SAConstraintMaker *make) {
                    make.width.mas_equalTo(1);
                    make.centerX.equalTo(icon);
                    make.top.equalTo(lastV.mas_bottom).offset(-2);
                    make.height.mas_equalTo(16);
                }];
            }
            
            lastV = view;
        }];
    }
    
    return _stepBoxV;
}

@end
