
#import "SAString+Extension.h"
#import <CommonCrypto/CommonCryptor.h>

@implementation NSString (Extension)

- (NSString * __nonnull)appendNoNullString:(NSString *)str {
    if (str.length != 0) {
        return [self stringByAppendingString:str];
    }
    return self;
}

- (BOOL)isNotEmptyStr {
    
    if (!self) {
        return NO;
    }
    
    if ([NSNull null] == (id)self) {
        return NO;
    }
    
    if ([self isKindOfClass:[NSNull class]]) {
        return NO;
    }
    
    if ([[self stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]] length]==0) {
        return NO;
    }
    
    if ([self isEqualToString:@"<null>"]) {
        return NO;
    }
    
    if ([self isEqualToString:@"(null)"]) {
        return NO;
    }
    
    return YES;
}

- (NSString *)removeSpaceAndNewline {
    
    NSString *temp = [self stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceCharacterSet]];
    
    NSString *text = [temp stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];
    
    return text;
}

+ (BOOL)judgeStringExist:(NSString *)string
{
    if (string && string != nil)
    {
        if ([string isKindOfClass:[NSNull class]])
        {
            return NO;
        }
        if ([string isKindOfClass:[NSString class]])
        {
            if ([string isEqualToString:@"<null>"] ||
                [string isEqualToString:@"(null)"] ||
                [string isEqualToString:@"null"])
            {
                return NO;
            }
        }
        
        NSString *tempStr = [NSString stringWithFormat:@"%@", string];
        if ([tempStr length] > 0)
        {
            return YES;
        }
    }
    
    return NO;
}


+ (NSString *)decryptUseDES:(NSString *)cipherText key:(NSString *)key
{    
    NSData* cipherData = [NSString convertHexStrToData:[cipherText lowercaseString]];
    
    unsigned char buffer[1024];
    memset(buffer, 0, sizeof(char));
    size_t numBytesDecrypted = 0;
    NSString *testString = key;
    NSData *testData = [testString dataUsingEncoding: NSUTF8StringEncoding];
    Byte *iv = (Byte *)[testData bytes];
    CCCryptorStatus cryptStatus = CCCrypt(kCCDecrypt,
                                          kCCAlgorithmDES,
                                          kCCOptionPKCS7Padding | kCCOptionECBMode,
                                          [key UTF8String],
                                          kCCKeySizeDES,
                                          iv,
                                          [cipherData bytes],
                                          [cipherData length],
                                          buffer,
                                          1024,
                                          &numBytesDecrypted);
    NSString* plainText = nil;
    if (cryptStatus == kCCSuccess) {
        NSData* data = [NSData dataWithBytes:buffer length:(NSUInteger)numBytesDecrypted];
        plainText = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
    }
    return plainText;
}

+ (NSData *)convertHexStrToData:(NSString *)str {
    if (!str || [str length] == 0) {
        return nil;
    }
    
    NSMutableData *hexData = [[NSMutableData alloc] initWithCapacity:8];
    NSRange range;
    if ([str length] % 2 == 0) {
        range = NSMakeRange(0, 2);
    } else {
        range = NSMakeRange(0, 1);
    }
    for (NSInteger i = range.location; i < [str length]; i += 2) {
        unsigned int anInt;
        NSString *hexCharStr = [str substringWithRange:range];
        NSScanner *scanner = [[NSScanner alloc] initWithString:hexCharStr];
        
        [scanner scanHexInt:&anInt];
        NSData *entity = [[NSData alloc] initWithBytes:&anInt length:1];
        [hexData appendData:entity];
        
        range.location += range.length;
        range.length = 2;
    }
    
    return hexData;
}

@end
