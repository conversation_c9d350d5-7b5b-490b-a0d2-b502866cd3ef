
#import "SAArray+MASAdditions.h"

#ifdef MAS_SHORTHAND

 
@interface NSArray (MASShorthandAdditions)

- (NSArray *)makeConstraints:(void(^)(SAConstraintMaker *make))block;
- (NSArray *)updateConstraints:(void(^)(SAConstraintMaker *make))block;
- (NSArray *)remakeConstraints:(void(^)(SAConstraintMaker *make))block;

@end

@implementation NSArray (MASShorthandAdditions)

- (NSArray *)makeConstraints:(void(^)(SAConstraintMaker *))block {
    return [self mas_makeConstraints:block];
}

- (NSArray *)updateConstraints:(void(^)(SAConstraintMaker *))block {
    return [self mas_updateConstraints:block];
}

- (NSArray *)remakeConstraints:(void(^)(SAConstraintMaker *))block {
    return [self mas_remakeConstraints:block];
}

@end

#endif
