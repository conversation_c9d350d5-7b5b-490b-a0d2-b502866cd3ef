
#import "SACommonTool.h"

@implementation SACommonTool

+ (UIViewController *)findBestViewController:(UIViewController*)vc
{
    if (vc.presentedViewController)
    {
        
        return [self findBestViewController:vc.presentedViewController];
    }
    else if ([vc isKindOfClass:[UISplitViewController class]])
    {
        
        UISplitViewController* svc = (UISplitViewController*) vc;
        if (svc.viewControllers.count > 0)
            return [self findBestViewController:svc.viewControllers.lastObject];
        else
            return vc;
    }
    else if ([vc isKindOfClass:[UINavigationController class]])
    {
        
        UINavigationController* svc = (UINavigationController*) vc;
        if (svc.viewControllers.count > 0)
            return [self findBestViewController:svc.topViewController];
        else
            return vc;
        
    }
    else if ([vc isKindOfClass:[UITabBarController class]])
    {
        
        UITabBarController* svc = (UITabBarController*) vc;
        if (svc.viewControllers.count > 0)
            return [self findBestViewController:svc.selectedViewController];
        else
            return vc;
    }
    else
    {
        
        return vc;
    }
}

+ (UIViewController *)currentViewController
{
    
    UIViewController* viewController = [UIApplication sharedApplication].keyWindow.rootViewController;
    return [self findBestViewController:viewController];
}

+ (UIViewController * __nullable)lastViewControllerOf:(UIViewController *)currentVC
{
    if (currentVC.navigationController.viewControllers.count == 1) return nil;
    NSInteger index = [currentVC.navigationController.viewControllers indexOfObject:currentVC];
    if (index > 0)
    {
        UIViewController *lastVC =[currentVC.navigationController.viewControllers objectAtIndex:(index-1)] ;
        return lastVC;
    }
    
    return nil;
}

+ (NSString * __nullable)lastViewControllerNameOf:(UIViewController *)currentVC
{
    if (currentVC.navigationController.viewControllers.count == 1) return nil;
    NSInteger index = [currentVC.navigationController.viewControllers indexOfObject:currentVC];
    if (index > 0)
    {
        UIViewController *lastVC =[currentVC.navigationController.viewControllers objectAtIndex:(index-1)] ;
        NSString *lastVCName = NSStringFromClass([lastVC class]);
        return lastVCName;
    }
    
    return nil;
}

+ (UIWindow *)getKeyWindow {
    return [UIApplication sharedApplication].keyWindow;
}

@end
