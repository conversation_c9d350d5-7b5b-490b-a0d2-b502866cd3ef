
#if __has_include(<MJRefresh/SARefreshAutoFooter.h>)
#import <MJRefresh/SARefreshAutoFooter.h>
#else
#import "SARefreshAutoFooter.h"
#endif

NS_ASSUME_NONNULL_BEGIN

@interface SARefreshAutoStateFooter : SARefreshAutoFooter
 
@property (assign, nonatomic) CGFloat labelLeftInset;
 
@property (weak, nonatomic, readonly) UILabel *stateLabel;

 
- (instancetype)setTitle:(NSString *)title forState:(MJRefreshState)state;

 
@property (assign, nonatomic, getter=isRefreshingTitleHidden) BOOL refreshingTitleHidden;
@end

NS_ASSUME_NONNULL_END
