
#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN
@interface NSDate (BRPickerView)

 
+ (void)br_setCalendar:(NSCalendar *)calendar;
 
+ (NSCalendar *)br_getCalendar;

 
+ (void)br_setTimeZone:(NSTimeZone *)timeZone;
 
+ (NSTimeZone *)br_getTimeZone;

@property (readonly) NSInteger br_year;         
@property (readonly) NSInteger br_month;        
@property (readonly) NSInteger br_day;          
@property (readonly) NSInteger br_hour;         
@property (readonly) NSInteger br_minute;       
@property (readonly) NSInteger br_second;       
@property (readonly) NSInteger br_weekday;      
@property (readonly) NSInteger br_monthWeek;    
@property (readonly) NSInteger br_yearWeek;     
@property (readonly) NSInteger br_quarter;      

 
@property (nullable, nonatomic, readonly, copy) NSString *br_weekdayString;

 
+ (nullable NSDate *)br_setYear:(NSInteger)year;

 
+ (nullable NSDate *)br_setYear:(NSInteger)year month:(NSInteger)month;

 
+ (nullable NSDate *)br_setYear:(NSInteger)year month:(NSInteger)month day:(NSInteger)day;

 
+ (nullable NSDate *)br_setYear:(NSInteger)year month:(NSInteger)month day:(NSInteger)day hour:(NSInteger)hour;

 
+ (nullable NSDate *)br_setYear:(NSInteger)year month:(NSInteger)month day:(NSInteger)day hour:(NSInteger)hour minute:(NSInteger)minute;

 
+ (nullable NSDate *)br_setYear:(NSInteger)year month:(NSInteger)month day:(NSInteger)day hour:(NSInteger)hour minute:(NSInteger)minute second:(NSInteger)second;

 
+ (nullable NSDate *)br_setMonth:(NSInteger)month day:(NSInteger)day hour:(NSInteger)hour minute:(NSInteger)minute;

 
+ (nullable NSDate *)br_setMonth:(NSInteger)month day:(NSInteger)day;

 
+ (nullable NSDate *)br_setHour:(NSInteger)hour minute:(NSInteger)minute second:(NSInteger)second;

 
+ (nullable NSDate *)br_setHour:(NSInteger)hour minute:(NSInteger)minute;

 
+ (nullable NSDate *)br_setMinute:(NSInteger)minute second:(NSInteger)second;

 
+ (nullable NSDate *)br_setYear:(NSInteger)year month:(NSInteger)month weekOfMonth:(NSInteger)weekOfMont;

 
+ (nullable NSDate *)br_setYear:(NSInteger)year weekOfYear:(NSInteger)weekOfYear;

 
+ (nullable NSDate *)br_setYear:(NSInteger)year quarter:(NSInteger)quarter;

 
- (NSDate *)br_setTwelveHour:(NSInteger)hour;

 
+ (NSUInteger)br_getDaysInYear:(NSInteger)year month:(NSInteger)month;

 
+ (NSUInteger)br_getWeeksOfMonthInYear:(NSInteger)year month:(NSInteger)month;

 
+ (NSUInteger)br_getWeeksOfYearInYear:(NSInteger)year;

 
+ (NSUInteger)br_getQuartersInYear:(NSInteger)year;

 
- (nullable NSDate *)br_getNewDateToDays:(NSTimeInterval)days;

 
- (nullable NSDate *)br_getNewDateToMonths:(NSTimeInterval)months;

 
+ (nullable NSString *)br_stringFromDate:(NSDate *)date dateFormat:(NSString *)dateFormat;
 
+ (nullable NSString *)br_stringFromDate:(NSDate *)date
                              dateFormat:(NSString *)dateFormat
                                language:(nullable NSString *)language;

 
+ (nullable NSDate *)br_dateFromString:(NSString *)dateString dateFormat:(NSString *)dateFormat;
 
+ (nullable NSDate *)br_dateFromString:(NSString *)dateString
                            dateFormat:(NSString *)dateFormat
                              language:(nullable NSString *)language;

@end

NS_ASSUME_NONNULL_END
