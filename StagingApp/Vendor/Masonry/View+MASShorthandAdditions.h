
#import "SAView+MASAdditions.h"

#ifdef MAS_SHORTHAND

 
@interface MAS_VIEW (MASShorthandAdditions)

@property (nonatomic, strong, readonly) SAViewAttribute *left;
@property (nonatomic, strong, readonly) SAViewAttribute *top;
@property (nonatomic, strong, readonly) SAViewAttribute *right;
@property (nonatomic, strong, readonly) SAViewAttribute *bottom;
@property (nonatomic, strong, readonly) SAViewAttribute *leading;
@property (nonatomic, strong, readonly) SAViewAttribute *trailing;
@property (nonatomic, strong, readonly) SAViewAttribute *width;
@property (nonatomic, strong, readonly) SAViewAttribute *height;
@property (nonatomic, strong, readonly) SAViewAttribute *centerX;
@property (nonatomic, strong, readonly) SAViewAttribute *centerY;
@property (nonatomic, strong, readonly) SAViewAttribute *baseline;
@property (nonatomic, strong, readonly) SAViewAttribute *(^attribute)(NSLayoutAttribute attr);

#if (__IPHONE_OS_VERSION_MIN_REQUIRED >= 80000) || (__TV_OS_VERSION_MIN_REQUIRED >= 9000) || (__MAC_OS_X_VERSION_MIN_REQUIRED >= 101100)

@property (nonatomic, strong, readonly) SAViewAttribute *firstBaseline;
@property (nonatomic, strong, readonly) SAViewAttribute *lastBaseline;

#endif

#if (__IPHONE_OS_VERSION_MIN_REQUIRED >= 80000) || (__TV_OS_VERSION_MIN_REQUIRED >= 9000)

@property (nonatomic, strong, readonly) SAViewAttribute *leftMargin;
@property (nonatomic, strong, readonly) SAViewAttribute *rightMargin;
@property (nonatomic, strong, readonly) SAViewAttribute *topMargin;
@property (nonatomic, strong, readonly) SAViewAttribute *bottomMargin;
@property (nonatomic, strong, readonly) SAViewAttribute *leadingMargin;
@property (nonatomic, strong, readonly) SAViewAttribute *trailingMargin;
@property (nonatomic, strong, readonly) SAViewAttribute *centerXWithinMargins;
@property (nonatomic, strong, readonly) SAViewAttribute *centerYWithinMargins;

#endif

#if (__IPHONE_OS_VERSION_MAX_ALLOWED >= 110000) || (__TV_OS_VERSION_MAX_ALLOWED >= 110000)

@property (nonatomic, strong, readonly) SAViewAttribute *safeAreaLayoutGuideTop API_AVAILABLE(ios(11.0),tvos(11.0));
@property (nonatomic, strong, readonly) SAViewAttribute *safeAreaLayoutGuideBottom API_AVAILABLE(ios(11.0),tvos(11.0));
@property (nonatomic, strong, readonly) SAViewAttribute *safeAreaLayoutGuideLeft API_AVAILABLE(ios(11.0),tvos(11.0));
@property (nonatomic, strong, readonly) SAViewAttribute *safeAreaLayoutGuideRight API_AVAILABLE(ios(11.0),tvos(11.0));

#endif

- (NSArray *)makeConstraints:(void(^)(SAConstraintMaker *make))block;
- (NSArray *)updateConstraints:(void(^)(SAConstraintMaker *make))block;
- (NSArray *)remakeConstraints:(void(^)(SAConstraintMaker *make))block;

@end

#define MAS_ATTR_FORWARD(attr)  \
- (SAViewAttribute *)attr {    \
    return [self mas_##attr];   \
}

@implementation MAS_VIEW (MASShorthandAdditions)

MAS_ATTR_FORWARD(top);
MAS_ATTR_FORWARD(left);
MAS_ATTR_FORWARD(bottom);
MAS_ATTR_FORWARD(right);
MAS_ATTR_FORWARD(leading);
MAS_ATTR_FORWARD(trailing);
MAS_ATTR_FORWARD(width);
MAS_ATTR_FORWARD(height);
MAS_ATTR_FORWARD(centerX);
MAS_ATTR_FORWARD(centerY);
MAS_ATTR_FORWARD(baseline);

#if (__IPHONE_OS_VERSION_MIN_REQUIRED >= 80000) || (__TV_OS_VERSION_MIN_REQUIRED >= 9000) || (__MAC_OS_X_VERSION_MIN_REQUIRED >= 101100)

MAS_ATTR_FORWARD(firstBaseline);
MAS_ATTR_FORWARD(lastBaseline);

#endif

#if (__IPHONE_OS_VERSION_MIN_REQUIRED >= 80000) || (__TV_OS_VERSION_MIN_REQUIRED >= 9000)

MAS_ATTR_FORWARD(leftMargin);
MAS_ATTR_FORWARD(rightMargin);
MAS_ATTR_FORWARD(topMargin);
MAS_ATTR_FORWARD(bottomMargin);
MAS_ATTR_FORWARD(leadingMargin);
MAS_ATTR_FORWARD(trailingMargin);
MAS_ATTR_FORWARD(centerXWithinMargins);
MAS_ATTR_FORWARD(centerYWithinMargins);

#endif

#if (__IPHONE_OS_VERSION_MAX_ALLOWED >= 110000) || (__TV_OS_VERSION_MAX_ALLOWED >= 110000)

MAS_ATTR_FORWARD(safeAreaLayoutGuideTop);
MAS_ATTR_FORWARD(safeAreaLayoutGuideBottom);
MAS_ATTR_FORWARD(safeAreaLayoutGuideLeft);
MAS_ATTR_FORWARD(safeAreaLayoutGuideRight);

#endif

- (SAViewAttribute *(^)(NSLayoutAttribute))attribute {
    return [self mas_attribute];
}

- (NSArray *)makeConstraints:(void(NS_NOESCAPE ^)(SAConstraintMaker *))block {
    return [self mas_makeConstraints:block];
}

- (NSArray *)updateConstraints:(void(NS_NOESCAPE ^)(SAConstraintMaker *))block {
    return [self mas_updateConstraints:block];
}

- (NSArray *)remakeConstraints:(void(NS_NOESCAPE ^)(SAConstraintMaker *))block {
    return [self mas_remakeConstraints:block];
}

@end

#endif
