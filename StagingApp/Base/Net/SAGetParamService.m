
#import "SAGetParamService.h"

#import "SARequestHandle.h"
#import "SANetworkConst.h"
#import "SADeviceTool.h"
#import "SAUserManager.h"
#import "SATabBarViewController.h"
#import "NPReloginAlertViewController.h"
#import "SAVersionUpdateViewController.h"
#import "SANavigationViewController.h"
#import "SALoginViewController.h"

static NSString *kNPBasicServiceOnlineUrl          =           @"http://47.110.35.200:9000";         

static NSString *kNPBasicServiceOfflineUrl         =           @"http://47.110.35.200:9000"; 


@implementation SAGetParamService

#pragma mark - QLServiceProtocal

- (BOOL)isOnline
{
    return kNPServiceIsOnline;
}

- (NSString *)offlineApiBaseUrl
{
    return kNPBasicServiceOfflineUrl;
}

- (NSString *)onlineApiBaseUrl
{
    return kNPBasicServiceOnlineUrl;
}

- (NSString *)offlineApiVersion
{
    return @"";
}

- (NSString *)onlineApiVersion
{
    return @"";
}

- (NSString *)onlinePublicKey
{
    return @"";
}

- (NSString *)offlinePublicKey
{
    return @"";
}

- (NSString *)onlinePrivateKey
{
    return @"";
}

- (NSString *)offlinePrivateKey
{
    return @"";
}

- (NSDictionary *)extraHttpHeadParmasWithMethodName:(NSString *)method requestParams:(NSDictionary *)requestParams
{
    
    NSDictionary *dict = [NSDictionary dictionary];
    
    NSDictionary *extraHeaderDict = @{
                                      @"basicParams": [SARequestHandle basicParamsHeadString:dict deleteSpace:NO],
                                      @"Content-Type": @"application/json;charset=utf-8",
                                      @"Accept-Language": [SADeviceTool languages]
                                      };
    
    return extraHeaderDict;
}

- (BOOL)shouldCallBackByFailedOnCallingAPI:(QLURLResponse *)response
{
    BOOL result = YES;
    NSString *code = [NSString stringWithFormat:@"%@", response.content[@"code"]];
    if ([code isEqualToString:kNPCodeUserTokenInvalid])
    {
        
        SANavigationViewController *navVC = [[SANavigationViewController alloc] initWithRootViewController:[[SALoginViewController alloc] init]];
        [UIApplication sharedApplication].keyWindow.rootViewController = navVC;

        return NO;
    }
    else if ([code isEqualToString:kNPForceUpdate])
    {
        
        
        SAVersionUpdateViewController *vc = [SAVersionUpdateViewController showVersionUpdateAlertWithAppleId:nil withFromVc:nil];
        [vc presentFromView];
    }
    
    return result;
}

- (void)resetPages {
    [SAUserManager loginOutClearInfo];
    [[SATabBarViewController sharedInstance] restTabBar];
}

- (void)resetPagesJumpLogin {
    [SAUserManager loginOutClearInfo];
    [[SATabBarViewController sharedInstance] restTabBar];
    [SAUserManager isNeedLoginSuccess:nil];
}


@end
