
#import "SAAppCodeManager.h"  
#import "SAAppConfigApiManager.h"
#import "SANoNetView.h"


@interface SAAppCodeManager ()<QLAPIManagerParamSource, QLAPIManagerCallBackDelegate>

@property (nonatomic, strong)SAAppConfigApiManager *codeAipMgr;
@property (nonatomic, copy)GetAppCodeSuccess successBlock;
@property (nonatomic, copy) GetAppCodeRetryBlock failBlock;

@property (nonatomic, strong)SANoNetView *noNetView;

@end

@implementation SAAppCodeManager

- (void)requestAppCodeSuccess:(GetAppCodeSuccess)success tapRetry:(GetAppCodeRetryBlock)retryBlock {

    self.successBlock = success;
    self.failBlock = retryBlock;
    
    [SATool showWindowHUD:nil];
    [self.codeAipMgr loadData];
}

- (void)tapGestureAction {
    if (self.failBlock) {
        self.failBlock();
    }
}

#pragma mark - QLAPIManagerParamSource
- (NSDictionary *)paramsForApi:(SABaseManager *)manager {
    return [NSDictionary dictionary];
}

#pragma mark - QLAPIManagerCallBackDelegate

- (void)managerCallAPIDidSuccess:(SABaseManager *)manager {
    [SATool hideWindowHUD];
    if (manager == self.codeAipMgr) {
        NSDictionary *dataDict = [manager fetchDataWithReformer:self.codeAipMgr];
        self.noNetView.hidden = YES;
        if (self.successBlock) {
            self.successBlock(dataDict);
        }
    }
}

- (void)managerCallAPIDidFailed:(SABaseManager *)manager {
    [SATool hideWindowHUD];
    if (manager == self.codeAipMgr) {
        self.noNetView.hidden = NO;
    }
    
}

#pragma mark - getters & settters

- (SANoNetView *)noNetView
{
    if (_noNetView == nil)
    {
        __weak typeof(self) weakSelf = self;
        _noNetView = [[SANoNetView alloc] initWithImage:[UIImage imageNamed:@"nonetwork_icon"]
                                                  title:NSLocalizedString(@"pickerSend", nil)
                                              loadTitle:NSLocalizedString(@"recordVoucher", nil)
                                               tapBlock:^{
            [weakSelf tapGestureAction];
        }];
        _noNetView.frame = CGRectMake(0, 0, UIScreenWidth, UIScreenHeight);
        
        [[UIApplication sharedApplication].keyWindow addSubview:_noNetView];
    }
    
    return _noNetView;
}

- (SAAppConfigApiManager *)codeAipMgr {
    if (_codeAipMgr == nil) {
        _codeAipMgr = [[SAAppConfigApiManager alloc] init];
        _codeAipMgr.delegate = self;
        _codeAipMgr.paramSource = self;
    }
    return _codeAipMgr;
}

@end
