
#import "SAConstraint.h"
#import "MASUtilities.h"

typedef NS_OPTIONS(NSInteger, MASAttribute) {
    MASAttributeLeft = 1 << NSLayoutAttributeLeft,
    MASAttributeRight = 1 << NSLayoutAttributeRight,
    MASAttributeTop = 1 << NSLayoutAttributeTop,
    MASAttributeBottom = 1 << NSLayoutAttributeBottom,
    MASAttributeLeading = 1 << NSLayoutAttributeLeading,
    MASAttributeTrailing = 1 << NSLayoutAttributeTrailing,
    MASAttributeWidth = 1 << NSLayoutAttributeWidth,
    MASAttributeHeight = 1 << NSLayoutAttributeHeight,
    MASAttributeCenterX = 1 << NSLayoutAttributeCenterX,
    MASAttributeCenterY = 1 << NSLayoutAttributeCenterY,
    MASAttributeBaseline = 1 << NSLayoutAttributeBaseline,
    
#if (__IPHONE_OS_VERSION_MIN_REQUIRED >= 80000) || (__TV_OS_VERSION_MIN_REQUIRED >= 9000) || (__MAC_OS_X_VERSION_MIN_REQUIRED >= 101100)
    
    MASAttributeFirstBaseline = 1 << NSLayoutAttributeFirstBaseline,
    MASAttributeLastBaseline = 1 << NSLayoutAttributeLastBaseline,
    
#endif
    
#if (__IPHONE_OS_VERSION_MIN_REQUIRED >= 80000) || (__TV_OS_VERSION_MIN_REQUIRED >= 9000)
    
    MASAttributeLeftMargin = 1 << NSLayoutAttributeLeftMargin,
    MASAttributeRightMargin = 1 << NSLayoutAttributeRightMargin,
    MASAttributeTopMargin = 1 << NSLayoutAttributeTopMargin,
    MASAttributeBottomMargin = 1 << NSLayoutAttributeBottomMargin,
    MASAttributeLeadingMargin = 1 << NSLayoutAttributeLeadingMargin,
    MASAttributeTrailingMargin = 1 << NSLayoutAttributeTrailingMargin,
    MASAttributeCenterXWithinMargins = 1 << NSLayoutAttributeCenterXWithinMargins,
    MASAttributeCenterYWithinMargins = 1 << NSLayoutAttributeCenterYWithinMargins,

#endif
    
};

 
@interface SAConstraintMaker : NSObject

 
@property (nonatomic, strong, readonly) SAConstraint *left;
@property (nonatomic, strong, readonly) SAConstraint *top;
@property (nonatomic, strong, readonly) SAConstraint *right;
@property (nonatomic, strong, readonly) SAConstraint *bottom;
@property (nonatomic, strong, readonly) SAConstraint *leading;
@property (nonatomic, strong, readonly) SAConstraint *trailing;
@property (nonatomic, strong, readonly) SAConstraint *width;
@property (nonatomic, strong, readonly) SAConstraint *height;
@property (nonatomic, strong, readonly) SAConstraint *centerX;
@property (nonatomic, strong, readonly) SAConstraint *centerY;
@property (nonatomic, strong, readonly) SAConstraint *baseline;

#if (__IPHONE_OS_VERSION_MIN_REQUIRED >= 80000) || (__TV_OS_VERSION_MIN_REQUIRED >= 9000) || (__MAC_OS_X_VERSION_MIN_REQUIRED >= 101100)

@property (nonatomic, strong, readonly) SAConstraint *firstBaseline;
@property (nonatomic, strong, readonly) SAConstraint *lastBaseline;

#endif

#if (__IPHONE_OS_VERSION_MIN_REQUIRED >= 80000) || (__TV_OS_VERSION_MIN_REQUIRED >= 9000)

@property (nonatomic, strong, readonly) SAConstraint *leftMargin;
@property (nonatomic, strong, readonly) SAConstraint *rightMargin;
@property (nonatomic, strong, readonly) SAConstraint *topMargin;
@property (nonatomic, strong, readonly) SAConstraint *bottomMargin;
@property (nonatomic, strong, readonly) SAConstraint *leadingMargin;
@property (nonatomic, strong, readonly) SAConstraint *trailingMargin;
@property (nonatomic, strong, readonly) SAConstraint *centerXWithinMargins;
@property (nonatomic, strong, readonly) SAConstraint *centerYWithinMargins;

#endif

 
@property (nonatomic, strong, readonly) SAConstraint *(^attributes)(MASAttribute attrs);

 
@property (nonatomic, strong, readonly) SAConstraint *edges;

 
@property (nonatomic, strong, readonly) SAConstraint *size;

 
@property (nonatomic, strong, readonly) SAConstraint *center;

 
@property (nonatomic, assign) BOOL updateExisting;

 
@property (nonatomic, assign) BOOL removeExisting;

 
- (id)initWithView:(MAS_VIEW *)view;

 
- (NSArray *)install;

- (SAConstraint * (^)(dispatch_block_t))group;

@end
