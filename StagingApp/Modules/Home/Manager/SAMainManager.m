
#import "SAMainManager.h"

#import "SAUserManager.h"

#import "SASaveGPSApiManager.h"
#import "SALocationManager.h"
#import "SAWifiManager.h"
#import "SAContactsManager.h"
#import "SADeviceInfoSaveApiManager.h"

#import "SASaveDeviceChannelApiManager.h"
#import "SATabBarViewController.h"
#import "SADictionary+QLNetworkingMethods.h"
#import "SAOrderConfigApiManager.h"
#import "SAUploadInfoManager.h"
#import "SAArray+QLNetworkingMethods.h"
#import "SAData+Compress.h"

 

static NSString * const Route_SureProduct = @"URL/JS/SDZZ_INDEX";

static NSString * const Route_NeedVerify = @"APP/CLViewControllerUserDataList";

static NSString * const Route_BindBank = @"APP/Product/PayFail/BindBankCard";

static NSString * const Route_Repayment = @"APP/CLViewRepay";

static NSString * const Route_LoanRenewal = @"APP/CLViewLoanRenewal";


@interface SAMainManager ()<QLAPIManagerParamSource, QLAPIManagerCallBackDelegate>

@property (nonatomic, strong) SAOrderConfigApiManager *configManager; 

@property (nonatomic, strong) SASaveGPSApiManager *onlyGpsManager; 
@property (nonatomic, strong) SASaveGPSApiManager *saveGPSApiMgr; 
@property (nonatomic, strong) NSDictionary *gpsDict;


@property (nonatomic, strong) SAUploadInfoManager *wifiApiMgr;    
@property (nonatomic, strong) NSDictionary *wifiDict;

@property (nonatomic, strong) SADeviceInfoSaveApiManager *deviceInfoApiMgr; 

@property (nonatomic, strong) SAUploadInfoManager *uploadConManager; 

@property (nonatomic, strong)SALocationManager *locationMgr;
@property (nonatomic, strong)SAContactsManager *contactMgr;
@property (nonatomic, strong)SAWifiManager *wifiMgr;


@property (nonatomic, strong)NSDictionary *dict;
@property (nonatomic, copy)BasicParamsUploadSuccess uploadSuccess;

@property (nonatomic, strong)SASaveDeviceChannelApiManager *saveChannelApiMgr;

@property (nonatomic, strong) NSArray *contactsArray;


@property (nonatomic, strong) NSString *contactsUrl;


@property (nonatomic, assign) BOOL disableContact;

@property (nonatomic, assign) BOOL gpsSucc;
@property (nonatomic, assign) BOOL contactSucc;



@end

@implementation SAMainManager


- (void)uploadBorrowBasicInfo:(BasicParamsUploadSuccess)uploadBlock
{
    self.uploadSuccess = uploadBlock;
    
    [SATool showWindowHUD:nil];
    [self.configManager loadData];
}

- (void)startUploadInfo
{
    
    dispatch_queue_t globalQueue = dispatch_get_global_queue(0, 0);
    
    dispatch_group_t group = dispatch_group_create();
          
    dispatch_group_async(group, globalQueue, ^{
        self.wifiDict = [self.wifiMgr fetchUploadWifiParameter];
        [self.wifiApiMgr loadData];
    });
    dispatch_group_async(group, globalQueue, ^{
        [self.deviceInfoApiMgr loadData];
    });
      
    dispatch_group_notify(group, dispatch_get_main_queue(), ^{
        NSLog(@"全部请求执行完毕!");
        
        if(!self.disableContact)
        {
            __weak typeof(self) weakSelf = self;
            [self.locationMgr startCheckTipLocationResult:^(SALocationManager * _Nonnull mgr) {
                
                weakSelf.gpsDict = @{@"lat":mgr.latitude, @"lng":mgr.longitude};
                [weakSelf.saveGPSApiMgr loadData];
                
                [weakSelf.contactMgr fetchUploadContactsParams:^(NSArray *arr) {
                    if(arr.count == 0){
                        [SATool hideWindowHUD];
                        return;
                    }
                    
                    weakSelf.contactsArray = arr;
                    [weakSelf uploadContactOSS];
                }];
                
                
            } andFail:^(NSString * _Nonnull error) {
                                
                [SATool hideWindowHUD];
                [SATool textStateWindowHUD:error];
            }];
            
            
        }
        else
        {
            if (self.uploadSuccess) {
                [SATool hideWindowHUD];
                self.uploadSuccess();
            }
        }
    });
}

- (void)initMainVcOtherNetwork {
    
    if (![SAUserManager isLogin]) {
        [self.saveChannelApiMgr loadData];
    }
}

- (void)uploadGps
{
}

#pragma mark - 通讯录上传oss

- (void)uploadContactOSS
{
    NSString *jsonStr = [self.contactsArray QL_jsonString];
    NSData *conData = [jsonStr dataUsingEncoding:NSUTF8StringEncoding];
    
    
    [[SANLUploadManager sharedInstance] uploadObjectAsync:conData
                                               fileType:NPFileType_Txt
                                                   success:^(NSString *path) {
        if (path) {
            NSString  *dataUrl = path;
            self.contactsUrl = dataUrl;
            [self.uploadConManager loadData];
        }
    }
    failed:^(NSError * _Nonnull error) {
        [SATool hideWindowHUD];
    }];
}

#pragma mark - QLAPIManagerCallBackDelegate
- (void)managerCallAPIDidSuccess:(SABaseManager *)manager
{
    if (manager == self.configManager) {
        
        NSDictionary *dataDict = [manager fetchDataWithReformer:self.configManager];
        self.disableContact = [dataDict[@"disableC"] boolValue];
        [self startUploadInfo];
    }
    
    if (manager == self.onlyGpsManager) {
        self.onlyGpsManager = nil;
    }
    
    if (manager == self.uploadConManager) {
        self.uploadConManager = nil;
        
        self.contactSucc = true;
        if(self.contactSucc && self.gpsSucc){
            if (self.uploadSuccess) {
                [SATool hideWindowHUD];
                self.uploadSuccess();
            }
        }
        
    }
    else if (manager == self.saveGPSApiMgr) {
        self.saveGPSApiMgr = nil;
        
        self.gpsSucc = true;
        if(self.contactSucc && self.gpsSucc){
            if (self.uploadSuccess) {
                [SATool hideWindowHUD];
                self.uploadSuccess();
            }
        }
    }
    else if (manager == self.wifiApiMgr) {
        self.wifiApiMgr = nil;
    }
    else if (manager == self.deviceInfoApiMgr) {
        self.deviceInfoApiMgr = nil;
    }
    else if (manager == self.saveChannelApiMgr) {
        self.saveChannelApiMgr = nil;
    }
}

- (void)managerCallAPIDidFailed:(SABaseManager *)manager
{
    [SATool hideWindowHUD];
    
   if (manager == self.saveChannelApiMgr) {
        self.saveChannelApiMgr = nil;
    }
    
    NSString *failMsg  = [SAResponseHandle netWorkErrorMsg:[manager fetchDataWithReformer:nil] andErrorType:manager.errorType];
    [SATool textStateWindowHUD:failMsg];
}

#pragma - mark QLAPIManagerParamSource
- (NSDictionary *)paramsForApi:(SABaseManager *)manager {
    if (manager == self.deviceInfoApiMgr) {
        return @{
                 @"deviceBrand": [SADeviceTool deviceModelName],
                 @"hasRoot": [NSNumber numberWithBool:NO],
                 @"phoneVersion": [SADeviceTool deviceSystemVersion],
                 @"googleAdId": [SADeviceTool deviceUUID],
                 @"simState": @([SADeviceTool getSIMState]),
                 @"guestId": [SADeviceTool deviceGuestID]
            };
    }  else if (manager == self.saveChannelApiMgr) {
        return @{
                 @"deviceBrand": [SADeviceTool deviceModelName],
                 @"hasRoot": [NSNumber numberWithBool:NO],
                 @"phoneVersion": [SADeviceTool deviceSystemVersion],
                 @"googleAdId": [SADeviceTool deviceUUID],
                 @"simState": @([SADeviceTool getSIMState]),
                 @"guestId": [SADeviceTool deviceGuestID]
            };
    } else if (manager == self.uploadConManager){
        return @{
            @"dataUrl": self.contactsUrl,
            @"sourceType": @3,
            @"type": @3,
            @"dataType": @2 
        };
    } else if (manager == self.wifiApiMgr){
        return @{
            @"data": [self.wifiDict QL_jsonString],
            @"sourceType": @3,
            @"type": @2,
        };
    }else if (manager == self.onlyGpsManager){
        return self.gpsDict;
    }else if (manager == self.saveGPSApiMgr){
        return self.gpsDict;
    }
    else {
        return self.dict;
    }
    return [NSDictionary dictionary];
}

#pragma mark - getters

- (SASaveGPSApiManager *)onlyGpsManager {
    if (_onlyGpsManager == nil) {
        _onlyGpsManager = [[SASaveGPSApiManager alloc] init];
        _onlyGpsManager.delegate = self;
        _onlyGpsManager.paramSource = self;
    }
    return _onlyGpsManager;
}

- (SASaveGPSApiManager *)saveGPSApiMgr {
    if (_saveGPSApiMgr == nil) {
        _saveGPSApiMgr = [[SASaveGPSApiManager alloc] init];
        _saveGPSApiMgr.delegate = self;
        _saveGPSApiMgr.paramSource = self;
    }
    return _saveGPSApiMgr;
}

- (SAUploadInfoManager *)uploadConManager {
    if (_uploadConManager == nil) {
        _uploadConManager = [[SAUploadInfoManager alloc] init];
        _uploadConManager.delegate = self;
        _uploadConManager.paramSource = self;
    }
    return _uploadConManager;
}

- (SAUploadInfoManager *)wifiApiMgr {
    if (_wifiApiMgr == nil) {
        _wifiApiMgr = [[SAUploadInfoManager alloc] init];
        _wifiApiMgr.delegate = self;
        _wifiApiMgr.paramSource = self;
    }
    return _wifiApiMgr;
}

- (SADeviceInfoSaveApiManager *)deviceInfoApiMgr {
    if (_deviceInfoApiMgr == nil) {
        _deviceInfoApiMgr = [[SADeviceInfoSaveApiManager alloc] init];
        _deviceInfoApiMgr.delegate = self;
        _deviceInfoApiMgr.paramSource = self;
    }
    return _deviceInfoApiMgr;
}

- (SALocationManager *)locationMgr {
    if (_locationMgr == nil) {
        _locationMgr = [[SALocationManager alloc] init];
    }
    return _locationMgr;
}

- (SAContactsManager *)contactMgr {
    if (_contactMgr == nil) {
        _contactMgr = [[SAContactsManager alloc] init];
    }
    return _contactMgr;
}

- (SAWifiManager *)wifiMgr {
    if (_wifiMgr == nil) {
        _wifiMgr = [[SAWifiManager alloc] init];
    }
    return _wifiMgr;
}


- (SASaveDeviceChannelApiManager *)saveChannelApiMgr {
    if (_saveChannelApiMgr == nil) {
        _saveChannelApiMgr = [[SASaveDeviceChannelApiManager alloc] init];
        _saveChannelApiMgr.delegate = self;
        _saveChannelApiMgr.paramSource = self;
    }
    return _saveChannelApiMgr;
}


- (NSArray *)contactsArray{
    if (_contactsArray == nil) {
        _contactsArray = [NSArray array];
    }
    return _contactsArray;
}

- (NSDictionary *)wifiDict{
    if (_wifiDict == nil) {
        _wifiDict = [NSDictionary dictionary];
    }
    return _wifiDict;
}

- (NSDictionary *)gpsDict{
    if (_gpsDict == nil) {
        _gpsDict = [NSDictionary dictionary];
    }
    return _gpsDict;
}

- (SAOrderConfigApiManager *)configManager{
    if (_configManager == nil) {
        _configManager = [[SAOrderConfigApiManager alloc] init];
        _configManager.delegate = self;
        _configManager.paramSource = self;
    }
    return _configManager;
}

@end
