
#ifndef QLNetworkEnum_h
#define QLNetworkEnum_h

#if DEBUG
#define QLDebugLog(...)            printf("%s", [[NSString stringWithFormat:__VA_ARGS__] UTF8String]);
#else
#define QLDebugLog(...)           
#endif


 
typedef NS_ENUM (NSUInteger, QLAPIManagerErrorType){
    
    QLAPIManagerErrorTypeDefault,        
    QLAPIManagerErrorTypeSuccess,        
    QLAPIManagerErrorTypeNoContent,      
    QLAPIManagerErrorTypeParamsError,    
    QLAPIManagerErrorTypeTimeout,        
    QLAPIManagerErrorTypeNoNetWork       
};


typedef NS_ENUM (NSUInteger, QLAPIManagerRequestType){
    
    QLAPIManagerRequestTypeGet,
    QLAPIManagerRequestTypePost,
    QLAPIManagerRequestTypePut,
    QLAPIManagerRequestTypeDelete,
    QLAPIManagerRequestTypeUpload,
    QLAPIManagerRequestTypeDownload
};


typedef NS_ENUM(NSUInteger, NPURLResponseStatus){
    
    NPURLResponseStatusSuccess,        
    NPURLResponseStatusErrorTimeout,
    NPURLResponseStatusErrorNoNetwork  
};

#endif  
