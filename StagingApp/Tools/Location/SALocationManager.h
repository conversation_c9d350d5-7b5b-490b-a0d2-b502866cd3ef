

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@class SALocationManager;

typedef void(^PositionSuccess)(SALocationManager *mgr);
typedef void(^PositionFail)(NSString *error);

@interface SALocationManager : NSObject

@property (nonatomic, copy)NSString *latitude;            
@property (nonatomic, copy)NSString *longitude;           

@property (nonatomic, copy)NSString *province;        
@property (nonatomic, copy)NSString *city;            
@property (nonatomic, copy)NSString *county;         


 
- (void)startCheckTipLocationResult:(PositionSuccess _Nullable)successBlock andFail:(PositionFail _Nullable)failBlock;


 
- (void)startDefaultLocationResult:(PositionSuccess _Nullable)successBlock andFail:(PositionFail _Nullable)failBlock;

 
 
+ (BOOL)locationIsEnable;

@end

NS_ASSUME_NONNULL_END
