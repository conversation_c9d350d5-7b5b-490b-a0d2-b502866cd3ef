
#import "NPTradeAlertViewController.h"
#import "SAGradientView.h"

#define kPayBtnHeight  42
#define kAlertHeight   180

@interface NPTradeAlertViewController ()


@property (nonatomic, strong)UIView *maskView;
@property (nonatomic, strong)SAGradientView *alertView;

@property (nonatomic, strong)UILabel *msgLab;
@property (nonatomic, strong)SAGradientButton *sureBtn;
@property (nonatomic, strong)SAGradientButton *cancelBtn;

@property (nonatomic, copy) SureBlock sureBlock;

@end

static UIWindow *alertWindow;

@implementation NPTradeAlertViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
}
#pragma mark - Views Network
- (void)createViews
{
    [self.view addSubview:self.maskView];
    [self.view addSubview:self.alertView];
    
    [self.alertView addSubview:self.msgLab];
    
    
    [self.alertView addSubview:self.sureBtn];
    [self.alertView addSubview:self.cancelBtn];
}

- (void)renderView
{
    
    [self createViews];
    
    self.maskView.frame = CGRectMake(0, 0, UIScreenWidth, UIScreenHeight);
    
    self.alertView.frame = CGRectMake(0, UIScreenHeight-kAlertHeight, UIScreenWidth, kAlertHeight);
    
    UIBezierPath *maskPath = [UIBezierPath
        bezierPathWithRoundedRect:self.alertView.bounds
        byRoundingCorners:(UIRectCornerTopLeft | UIRectCornerTopRight)
        cornerRadii:CGSizeMake(12, 12)
    ];
    CAShapeLayer *maskLayer = [CAShapeLayer layer];
    maskLayer.frame = self.alertView.bounds;
    maskLayer.path = maskPath.CGPath;
    self.alertView.layer.mask = maskLayer;

    [self.msgLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.equalTo(self.alertView).offset(20);
        make.top.equalTo(self.alertView).offset(34);
        make.right.equalTo(self.alertView).offset(-20);
    }];
    
    [self.sureBtn mas_makeConstraints:^(SAConstraintMaker *make) {
        make.right.equalTo(self.msgLab);
        make.top.equalTo(self.msgLab.mas_bottom).offset(26);
        make.height.mas_equalTo(kPayBtnHeight);
        make.left.equalTo(self.alertView.mas_centerX).offset(-30);
    }];
    self.sureBtn.layer.cornerRadius = 7;
    self.sureBtn.layer.masksToBounds = YES;
    
    [self.cancelBtn mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.equalTo(self.msgLab);
        make.height.top.equalTo(self.sureBtn);
        make.right.equalTo(self.sureBtn.mas_left).offset(-12);
    }];
    self.cancelBtn.layer.cornerRadius = 7;
    self.cancelBtn.layer.masksToBounds = YES;
    self.cancelBtn.layer.borderColor = [UIColor colorWithHex:0x888888].CGColor;
    self.cancelBtn.layer.borderWidth = 1;
    [self.cancelBtn setTitleColor:[UIColor colorWithHex:0x888888] forState:UIControlStateNormal];
        
    _msgLab.text = @"完成借款合同签署立马放款到账，真的要放弃吗?";
}

#pragma mark - show、present、dismiss
+ (instancetype)showFaceAlertSureAction:(SureBlock)sure
{
    NPTradeAlertViewController *alertVc = [[NPTradeAlertViewController alloc] init];
    alertVc.sureBlock = sure;
    
    [alertVc createViews];
    
    [alertVc renderView];
    
    [alertVc presentFromView];
    
    return alertVc;
}

- (void)presentFromView {
    
    if (alertWindow == nil) {
        alertWindow = [[UIWindow alloc] initWithFrame:[UIScreen mainScreen].bounds];
        alertWindow.backgroundColor = [UIColor clearColor];
        alertWindow.windowLevel = UIWindowLevelAlert - 1;
        
    }
    
    alertWindow.rootViewController = self;
    
    
    self.alertView.transform = CGAffineTransformMakeScale(1.1, 1.1);
    self.maskView.alpha = 0.0;
    [UIView animateWithDuration:0.3 animations:^{
        self.alertView.transform = CGAffineTransformMakeScale(1.0, 1.0);
        self.maskView.alpha = 1.0;
    }];
    
    [alertWindow makeKeyAndVisible];
}

 
- (void)dismissUpdateAlert {
    
    [SATool hideWindowHUD];
    [UIView animateWithDuration:0.3 animations:^{
        self.maskView.alpha = 0.0;
        [self.maskView removeFromSuperview];
        alertWindow.hidden = YES;
        alertWindow = nil;
    }];
}

- (void)clickSureButton:(UIButton *)btn {
    
    [self dismissUpdateAlert];
   
}

- (void)clickCancelButton
{
    [self dismissUpdateAlert];
    
    if(self.sureBlock){
        self.sureBlock();
    }
}

#pragma mark - getters
- (UIView *)maskView {
    if (_maskView == nil) {
        
        _maskView = [[UIView alloc] init];
        _maskView.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.6];
        _maskView.alpha = 0.0;
        
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(dismissUpdateAlert)];
        _maskView.userInteractionEnabled = YES;
        [_maskView addGestureRecognizer:tap];
    }
    return _maskView;
}

- (SAGradientView *)alertView {
    if (_alertView == nil) {
        _alertView = [[SAGradientView alloc] init];
        [_alertView gradientBackgroundColors:@[kMinorColor, [UIColor whiteColor]] locations:nil startPoint:CGPointMake(0.5, 0) endPoint:CGPointMake(0.5, 0.6)];
    }
    return _alertView;
}

- (UILabel *)msgLab {
    if (_msgLab == nil) {
        _msgLab = [[UILabel alloc] init];
        _msgLab.font = BoldFont_XX6(15);
        _msgLab.textColor = [UIColor blackColor];
        _msgLab.numberOfLines = 0;
    }
    return _msgLab;
}


- (SAGradientButton *)sureBtn {
    if (_sureBtn == nil) {
        _sureBtn = [SAGradientButton buttonWithType:UIButtonTypeCustom];
        [_sureBtn gradientBackgroundColors:@[kButtonStartColor, kButtonEndColor] locations:nil startPoint:CGPointMake(0, 0.5) endPoint:CGPointMake(1, 0.5)];
        [_sureBtn setTitle:@"继续申请" forState:UIControlStateNormal];
        [_sureBtn setTitleColor:kHomeTextColor forState:UIControlStateNormal];
        _sureBtn.titleLabel.font = [UIFont fontSizeOfXX_6:15];
        _sureBtn.titleLabel.textAlignment = NSTextAlignmentCenter;
        [_sureBtn addTarget:self action:@selector(clickSureButton:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _sureBtn;
}

- (SAGradientButton *)cancelBtn {
    if (_cancelBtn == nil) {
        _cancelBtn = [SAGradientButton buttonWithType:UIButtonTypeCustom];
        [_cancelBtn setTitle:@"放弃额度" forState:UIControlStateNormal];
        _cancelBtn.titleLabel.font = [UIFont fontSizeOfXX_6:14];
        _cancelBtn.titleLabel.textAlignment = NSTextAlignmentCenter;
        [_cancelBtn addTarget:self action:@selector(clickCancelButton) forControlEvents:UIControlEventTouchUpInside];
    }
    return _cancelBtn;
}

- (void)dealloc {
}

@end
