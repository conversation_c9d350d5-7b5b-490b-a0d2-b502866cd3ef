
#import "SABankItemView.h"

#import "SABindCardSendCodeApi.h"
#import "SABindCardResendSmsApi.h"


@interface SABankItemView ()<QLAPIManagerCallBackDelegate, QLAPIManagerParamSource, UITextFieldDelegate>

@property (nonatomic, strong) UILabel *titleLab;
@property (nonatomic, strong) UITextField *itemField;
@property (nonatomic, strong) UIImageView *arrowImg;
@property (nonatomic, strong) UIButton *codeBtn;
@property (nonatomic, strong) UIView *lineV;

@property (nonatomic, strong) NSTimer *timer;
@property (nonatomic, assign) int times;
@property (nonatomic, strong) NSNumber *effectiveTime;

@property (nonatomic, strong) SABindCardSendCodeApi  *codeApi;
@property (nonatomic, strong) SABindCardResendSmsApi *resendManager;


@end

@implementation SABankItemView

#pragma mark - Life cycle

- (instancetype)initWithFrame:(CGRect)frame
{
    if (self = [super initWithFrame:frame])
    {
        self.digitsNum = 100;
        [self createUI];
        [self setConstraints];
    }
    
    return self;
}

- (void)createUI
{
    [self addSubview:self.titleLab];
    [self addSubview:self.itemField];
    [self addSubview:self.arrowImg];
    [self addSubview:self.lineV];
    
    self.itemField.delegate = self;
    [self.itemField addTarget:self action:@selector(textFieldDidChange:) forControlEvents:UIControlEventEditingChanged];
}

- (void)setConstraints
{
    [self.titleLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.equalTo(self).offset(20);
        make.centerY.equalTo(self);
        make.width.mas_equalTo(70);
    }];
    
    [self.itemField mas_makeConstraints:^(SAConstraintMaker *make) {
        make.centerY.equalTo(self);
        make.right.equalTo(self.arrowImg.mas_left).offset(-10);
        make.left.equalTo(self.titleLab.mas_right);
    }];
    
    [self.arrowImg mas_makeConstraints:^(SAConstraintMaker *make) {
        make.centerY.equalTo(self);
        make.right.equalTo(self).offset(-12);
        make.width.height.mas_equalTo(20);
    }];
    
    [self.lineV mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.equalTo(self.itemField);
        make.right.equalTo(self).offset(-12);
        make.bottom.equalTo(self).offset(-1);
        make.height.mas_equalTo(0.7);
    }];
}

#pragma mark - Action

- (void)sendCode:(id)sender
{
    if(![NSString judgeStringExist: self.bankNo]){
        [SATool textStateWindowHUD:@"请先输入银行卡号!"];
        return;
    }
    if(![NSString judgeStringExist: self.bankCode]){
        [SATool textStateWindowHUD:@"请先选择所属银行!"];
        return;
    }
    if(![NSString judgeStringExist: self.mobile]){
        [SATool textStateWindowHUD:@"请先输入手机号码!"];
        return;
    }
    
    [SATool showActivityHUD:nil];
    [self.codeApi loadData];
    
}

#pragma mark - UITextFieldDelegate

- (void)textFieldDidChange:(UITextField *)textField
{
    if(self.delegate && [self.delegate respondsToSelector:@selector(bankItemEndEditing:)]){
        [self.delegate bankItemEndEditing: self];
    }
}


- (BOOL)textField:(UITextField *)textField shouldChangeCharactersInRange:(NSRange)range replacementString:(NSString *)string
{
    if (textField == self.itemField) {
        
        if (range.length == 1 && string.length == 0) {
          return YES;
        }
        
        else if (self.itemField.text.length >= self.digitsNum) {
          self.itemField.text = [textField.text substringToIndex: self.digitsNum];
          return NO;
        }
    }
    
    return YES;
}

#pragma mark QLAPIManagerCallBackDelegate
- (void)managerCallAPIDidSuccess:(SABaseManager *)manager
{
    [SATool hideHUDView];

    if (manager == self.codeApi)
    {
        
        NSDictionary *dataDict = [manager fetchDataWithReformer:self.codeApi];
        if(dataDict && [dataDict isKindOfClass:[NSDictionary class]]){
            NSString *errorMsg = dataDict[@"errorMsg"];
            if ([NSString judgeStringExist:errorMsg]) {
                [SATool textStateWindowHUD:errorMsg];
            }else{
                [self.itemField becomeFirstResponder];
                self.flowId = dataDict[@"flowId"];
                self.effectiveTime = @(60);
                NSString *str = [NSString stringWithFormat:@"%02lds", [self.effectiveTime integerValue]];
                [self.codeBtn setTitle:str forState:UIControlStateNormal];
                
                [self addTimer];
            }
        }
    }
    if(manager == self.resendManager)
    {
        
        NSDictionary *dataDict = [manager fetchDataWithReformer:self.resendManager];
        
        if(dataDict && [dataDict isKindOfClass:[NSDictionary class]]){
            NSString *errorMsg = dataDict[@"errorMsg"];
            if ([NSString judgeStringExist:errorMsg]) {
                [SATool textStateWindowHUD:errorMsg];
            }else{
                [self.itemField becomeFirstResponder];
                self.flowId = dataDict[@"flowId"];
                self.effectiveTime = @(60);
                NSString *str = [NSString stringWithFormat:@"%02lds", [self.effectiveTime integerValue]];
                [self.codeBtn setTitle:str forState:UIControlStateNormal];
                
                [self addTimer];
            }
        }
    }
}

- (void)managerCallAPIDidFailed:(SABaseManager *)manager 
{
    [SATool hideHUDView];
    NSString *failMsg  = [SAResponseHandle netWorkErrorMsg:[manager fetchDataWithReformer:nil] andErrorType:manager.errorType];
    [SATool textStateHUD:failMsg];
}

#pragma mark QLAPIManagerParamSource
- (NSDictionary *)paramsForApi:(SABaseManager *)manager {
    if (manager == self.codeApi) {
        return @{@"mobile": self.mobile, @"cardNo": self.bankNo, @"orderType": @1, @"bankName": self.bankName, @"bankCode": self.bankCode};
    } else if (manager == self.resendManager){
        return @{@"flowId": self.flowId, @"orderType": @1};
    }
   
    return [NSDictionary dictionary];
}

#pragma mark - timer

- (void)addTimer 
{
    [self removeTimer];
    
    self.times = self.effectiveTime.intValue;
    NSTimer *timer = [NSTimer timerWithTimeInterval:1.0 target:self selector:@selector(beginUpdateUI) userInfo:nil repeats:YES];
    self.timer = timer;
    [[NSRunLoop mainRunLoop] addTimer:timer forMode:NSRunLoopCommonModes];
}

- (void)removeTimer {
    [_timer invalidate];
    self.timer = nil;
}

- (void)beginUpdateUI {
    
    if (self.times > 0) {
        self.times--;
        NSString *str = [NSString stringWithFormat:@"%02ds", self.times];
        [self.codeBtn setTitle:str forState:UIControlStateNormal];
        self.codeBtn.enabled = NO;
        [self.codeBtn setTitleColor:[UIColor colorWithHex:0x666666] forState:UIControlStateNormal];
    } else {
        [self.codeBtn setTitle:@"重新发送" forState:UIControlStateNormal];
        self.codeBtn.enabled = YES;
        [self.codeBtn setTitleColor:kButtonEndColor forState:UIControlStateNormal];
    }
}

#pragma mark - setter

- (void)setIsArrow:(BOOL)isArrow
{
    _isArrow = isArrow;
    if (isArrow) {
        self.itemField.userInteractionEnabled = NO;
        [self.arrowImg mas_updateConstraints:^(SAConstraintMaker *make) {
            make.width.height.mas_equalTo(20);
        }];
    }else{
        self.itemField.userInteractionEnabled = YES;
        [self.arrowImg mas_updateConstraints:^(SAConstraintMaker *make) {
            make.width.height.mas_equalTo(0);
        }];
    }
}

- (void)setIsVCode:(BOOL)isVCode
{
    _isVCode = isVCode;
    if(isVCode){
        [self addSubview:self.codeBtn];
        [self.codeBtn mas_makeConstraints:^(SAConstraintMaker *make) {
            make.right.equalTo(self).offset(-12);
            make.width.mas_equalTo(102);
            make.height.mas_equalTo(36);
            make.centerY.equalTo(self);
        }];
        self.codeBtn.layer.masksToBounds = YES;
        self.codeBtn.layer.cornerRadius = 18;
        
        [self.arrowImg mas_updateConstraints:^(SAConstraintMaker *make) {
            make.width.height.mas_equalTo(0);
        }];
    }
}

- (void)setShowLine:(BOOL)showLine
{
    self.lineV.hidden = !showLine;
}

- (void)setIsNumber:(BOOL)isNumber
{
    if(isNumber){
        self.itemField.keyboardType = UIKeyboardTypeNumberPad;
    }
}

- (void)setTitleText:(NSString *)titleText
{
    self.titleLab.text = titleText;
}

- (void)setFieldHolder:(NSString *)fieldHolder
{
    self.itemField.placeholder = fieldHolder;
}

- (void)setItemValue:(NSString *)itemValue
{
    if([NSString judgeStringExist:itemValue]){
        self.itemField.text = itemValue;
    }
}

#pragma mark - getter

- (NSString *)itemValue{
    return  self.itemField.text;
}

- (UILabel *)titleLab{
    FF_Fetch_UILable(_titleLab, [UIColor blackColor], Font_XX6(13))
}

- (UITextField *)itemField{
    FF_Fetch_UITextField(_itemField, [UIColor colorWithHex: 0x666666], Font_XX6(13))
}

- (UIImageView *)arrowImg{
    FF_Fetch_UIImageViewWithImage(_arrowImg, [UIImage imageNamed:@"verify_list_arrow"])
}

- (UIView *)lineV{
    FF_Fetch_UIView(_lineV, [UIColor colorWithHex:0xdddddd])
}

- (UIButton *)codeBtn{
    if(_codeBtn == nil){
        _codeBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_codeBtn addTarget:self action:@selector(sendCode:) forControlEvents:UIControlEventTouchUpInside];
        [_codeBtn setTitleColor:kButtonEndColor forState:UIControlStateNormal];
        _codeBtn.titleLabel.font = Font_XX6(13);
        [_codeBtn setTitle:@"发送验证码" forState:UIControlStateNormal];
    }
    return _codeBtn;
}

- (SABindCardSendCodeApi *)codeApi {
    if (_codeApi == nil) {
        _codeApi = [[SABindCardSendCodeApi alloc] init];
        _codeApi.delegate = self;
        _codeApi.paramSource = self;
    }
    return _codeApi;
}

- (SABindCardResendSmsApi *)resendManager{
    if (_resendManager == nil) {
        _resendManager = [[SABindCardResendSmsApi alloc] init];
        _resendManager.delegate = self;
        _resendManager.paramSource = self;
    }
    return _resendManager;
}

@end
