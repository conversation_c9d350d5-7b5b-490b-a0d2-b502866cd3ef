

#import "SACenterYTextView.h"

@implementation SACenterYTextView

- (instancetype)initWithFrame:(CGRect)frame {
    
    if (self = [super initWithFrame:frame]) {
        self.textAlignment = NSTextAlignmentCenter;
        [self addObserver:self forKeyPath:@"contentSize" options:  (NSKeyValueObservingOptionNew) context:NULL];
    }
    return self;
}


- (instancetype)initWithCoder:(NSCoder *)aDecoder {
    
    if (self = [super initWithCoder:aDecoder]) {
        self.textAlignment = NSTextAlignmentCenter;
        [self addObserver:self forKeyPath:@"contentSize" options:  (NSKeyValueObservingOptionNew) context:NULL];
    }
    return self;
}

- (void)observeValueForKeyPath:(NSString *)keyPath ofObject:(id)object change:(NSDictionary *)change context:(void *)context {
    
    if ([keyPath isEqualToString:@"contentSize"]) {
        
        UITextView *textView = object;
        if (self == textView) {
            
            CGFloat realH = textView.contentSize.height;
            CGFloat sizeH = textView.bounds.size.height;
            
            
            if (realH != sizeH) {
                self.realHeight = realH;
            }
            
        }
    }
}


- (void)layoutSubviews {
    [super layoutSubviews];
    
    CGFloat realH = self.contentSize.height;
    CGFloat sizeH = self.size.height;
    
    CGFloat spaceH =  sizeH - realH;
    
    
    CGFloat inset = 0;
    if (spaceH > 0) {
        inset = MAX(0, spaceH/2.0);
    }
    
    
    self.contentInset = UIEdgeInsetsMake(inset, self.contentInset.left, inset, self.contentInset.right);
}

- (BOOL)canPerformAction:(SEL)action withSender:(id)sender {
    if (action == @selector(paste:))
        return self.isPicker;
    if (action == @selector(select:))
        return self.isPicker;
    if (action == @selector(selectAll:))
        return self.isPicker;
    return [super canPerformAction:action withSender:sender];
}

- (void)dealloc {
    [self removeObserver:self forKeyPath:@"contentSize"];
}

@end
