//
//  EsignFaceVerifyResult.h
//  EsignSDKIOS
//
//  Created by 叶鹏飞 on 2024/8/8.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/**
 人脸服务返回结果对象
 */
@interface EsignFaceVerifyResult : NSObject

/**
 核身对应的faceAuthCode
 */
@property (nonatomic, assign, readonly) NSString *faceAuthCode;

/**
 核身结果是否通过
 */
@property (nonatomic, assign, readonly) BOOL isSuccess;


/*
 核身结果的对应错误码
 */
@property (nonatomic, copy, readonly) NSString *faceCode;


/*
 核身结果的对应错误描述
 */
@property (nonatomic, copy, readonly) NSString *faceMsg;

+ (instancetype)resultWithParams:(NSDictionary *)params;

@end

NS_ASSUME_NONNULL_END
