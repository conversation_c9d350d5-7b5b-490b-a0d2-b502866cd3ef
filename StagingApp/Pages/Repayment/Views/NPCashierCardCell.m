//
//  NPCashierCardCell.m
//  StagingApp
//
//  Created by <PERSON>een on 2024/10/25.
//  Copyright © 2024 Facebook. All rights reserved.
//

#import "NPCashierCardCell.h"

@interface NPCashierCardCell ()

@property (nonatomic, strong) UIImageView *logoImg;
@property (nonatomic, strong) UILabel *nameLab;
@property (nonatomic, strong) UILabel *noLab;
@property (nonatomic, strong) UIImageView *selectedImg;

@end

@implementation NPCashierCardCell

#pragma mark - Life cycle

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier])
    {
        self.contentView.layer.masksToBounds  = YES;
        self.contentView.layer.cornerRadius = 6;
        self.contentView.backgroundColor = [UIColor whiteColor];
        
        [self createUI];
        [self setConstraints];
    }
    
    return self;
}

- (void)createUI
{
    [self.contentView addSubview:self.logoImg];
    [self.contentView addSubview:self.nameLab];
    [self.contentView addSubview:self.noLab];
    [self.contentView addSubview:self.selectedImg];
}

- (void)setConstraints
{
    [self.logoImg mas_makeConstraints:^(SAConstraintMaker *make) {
        make.centerY.equalTo(self.contentView);
        make.left.equalTo(self.contentView).offset(12);
        make.width.height.mas_equalTo(40);
    }];
    self.logoImg.layer.masksToBounds  = YES;
    self.logoImg.layer.cornerRadius = 20;
    self.logoImg.layer.borderWidth = 0.8;
    self.logoImg.layer.borderColor = [UIColor colorWithHex:0xf6f6f6].CGColor;

    [self.nameLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.centerY.equalTo(self.logoImg);
        make.left.equalTo(self.logoImg.mas_right).offset(8);
    }];
    [self.noLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.centerY.equalTo(self.logoImg);
        make.left.equalTo(self.nameLab.mas_right).offset(3);
    }];
    
    [self.selectedImg mas_makeConstraints:^(SAConstraintMaker *make) {
        make.centerY.equalTo(self.contentView);
        make.right.equalTo(self.contentView).offset(-12);
        make.width.height.mas_equalTo(20);
    }];
}

#pragma mark - setter

- (void)setCardModel:(SACashierCardModel *)cardModel
{
    _cardModel = cardModel;
    
    [self.logoImg sd_setImageWithURL:[NSURL URLWithString:cardModel.bankLogo]];
    
    self.nameLab.text = cardModel.bankName;
    self.noLab.text = [NSString stringWithFormat:@"(%@)", cardModel.cardNo];
    
}

- (void)setChoosed:(BOOL)choosed
{
    _choosed = choosed;
    
    self.selectedImg.image = [UIImage customImageNamed:choosed ? @"verify_check_sel" : @"verify_check_nor"];
}

- (void)setFrame:(CGRect)frame
{
    frame.origin.x += 12;
    frame.size.width -= 24;
    
    frame.origin.y += 5;
    frame.size.height -= 10;
    
    [super setFrame: frame];
}

#pragma mark - getter
- (UIImageView *)logoImg{
    FF_Fetch_UIImageViewWithColor(_logoImg, [UIColor clearColor])
}

- (UILabel *)nameLab
{
    FF_Fetch_UILable(_nameLab, [UIColor blackColor], Font_XX6(15))
}

-  (UILabel *)noLab{
    FF_Fetch_UILable(_noLab, [UIColor blackColor], Font_XX6(15))
}

- (UIImageView *)selectedImg{
    FF_Fetch_UIImageViewWithColor(_selectedImg, [UIColor clearColor])
}

@end
