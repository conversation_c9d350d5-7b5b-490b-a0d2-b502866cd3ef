
#import "SAConfirmSmsAlertView.h"

#import "SAGradientView.h"
#import "SABindCardSendCodeApi.h"
#import "SABindCardResendSmsApi.h"
#import "SASaveBindCardInfoApi.h"
#import <CRBoxInputView/CRBoxInputView.h>

@interface SAConfirmSmsAlertView ()<QLAPIManagerCallBackDelegate, QLAPIManagerParamSource, UITextFieldDelegate>

@property (nonatomic, strong) UIView *maskView;          
@property (nonatomic, strong) UIView *alertView;         

@property (nonatomic, strong) UILabel *titleLab;
@property (nonatomic, strong) UIButton *cancelBtn;
@property (nonatomic, strong) CRBoxInputView *smsField;

@property (nonatomic, strong) UIButton *codeBtn;
@property (nonatomic, strong) SAGradientButton *submitBtn;         

@property (nonatomic, copy) SmsSubmitBlock completeBlock;


@property (nonatomic, strong) NSString *tradeNo;
@property (nonatomic, strong) NSTimer *timer;
@property (nonatomic, assign) int times;
@property (nonatomic, strong) NSNumber *effectiveTime;

@property (nonatomic, strong) SABindCardSendCodeApi  *codeApi;
@property (nonatomic, strong) SABindCardResendSmsApi *resendManager;
@property (nonatomic, strong) SASaveBindCardInfoApi  *bindApi;

@property (nonatomic, strong) NSString *flowId;

@end

static UIWindow *alertWindow;

@implementation SAConfirmSmsAlertView

- (void)viewDidLoad 
{
    [super viewDidLoad];
}

#pragma mark - Views Network

- (void)createViews
{
    [self.view addSubview:self.maskView];
    [self.view addSubview:self.alertView];
    
    [self.alertView addSubview:self.titleLab];
    [self.alertView addSubview:self.cancelBtn];
    
    
    [self.alertView addSubview:self.codeBtn];
    [self.alertView addSubview:self.submitBtn];
}

- (void)renderView
{
    [self createViews];
    
    self.maskView.frame = CGRectMake(0, 0, UIScreenWidth, UIScreenHeight);
        
    CGFloat alertW = UIScreenWidth-40;
    self.alertView.frame = CGRectMake(20, 200, alertW, 270);
    self.alertView.center = CGPointMake(self.view.center.x, self.view.center.y-45);
    self.alertView.layer.masksToBounds = YES;
    self.alertView.layer.cornerRadius = 10;

    self.titleLab.frame = CGRectMake(40, 10, alertW-40*2, XX_6(40));
    self.cancelBtn.frame = CGRectMake(self.titleLab.maxX, 0, 40, 40);
    
    CRBoxInputCellProperty *cellProperty = [CRBoxInputCellProperty new];
    cellProperty.cellBgColorNormal = [UIColor whiteColor];
    cellProperty.cellBgColorSelected = [UIColor whiteColor];
    cellProperty.cellCursorColor = kPrimaryColor;
    cellProperty.cellCursorWidth = 2;
    cellProperty.cellCursorHeight = 30;
    cellProperty.cornerRadius = 4;
    cellProperty.borderWidth = 1;
    cellProperty.cellBorderColorFilled = kPrimaryColor;
    cellProperty.cellFont = [UIFont boldSystemFontOfSize:24];
    cellProperty.cellTextColor = [UIColor blackColor];
    cellProperty.configCellShadowBlock = ^(CALayer * _Nonnull layer) {
        layer.shadowColor = [kPrimaryColor colorWithAlphaComponent:0.2].CGColor;
        layer.shadowOpacity = 1;
        layer.shadowOffset = CGSizeMake(0, 2);
        layer.shadowRadius = 4;
    };
    
    CRBoxInputView *boxInputView = [[CRBoxInputView alloc] initWithFrame:CGRectMake(15, self.titleLab.maxY + 20, alertW-15*2, 50)];
    boxInputView.customCellProperty = cellProperty;
    boxInputView.keyBoardType = UIKeyboardTypeNumberPad;
    [boxInputView loadAndPrepareViewWithBeginEdit:YES]; 
    self.smsField = boxInputView;
    [self.alertView addSubview:self.smsField];
    
    self.codeBtn.frame = CGRectMake(40, self.smsField.maxY + XX_6(10), alertW - 80, 44);

    self.submitBtn.frame = CGRectMake(40, self.codeBtn.maxY + XX_6(15), alertW - 80, 44);
    self.submitBtn.layer.cornerRadius = XX_6(22);
    self.submitBtn.layer.masksToBounds = YES;

    self.titleLab.text = @"确认担保费用收取";
    
    
    @weakify(self)
    [self.smsField resetCodeLength:6 beginEdit:YES];
    self.smsField.textDidChangeblock = ^(NSString * _Nullable text, BOOL isFinished) {
        if(isFinished){
            @strongify(self)
            
            [SATool showWindowHUD:nil];
            [self.bindApi loadData];
        }
    };
}

#pragma mark - show、present、dismiss
+ (instancetype)showSMSAlert:(NSString *)tradeNo submitAction:(SmsSubmitBlock)submitBlock
{
    SAConfirmSmsAlertView *alertVc = [[SAConfirmSmsAlertView alloc] init];
    alertVc.completeBlock = submitBlock;
    alertVc.tradeNo = tradeNo;
    [alertVc renderView];
    [alertVc presentFromView];
    return alertVc;
}

- (void)presentFromView
{
    if (alertWindow == nil) {
        alertWindow = [[UIWindow alloc] initWithFrame:[UIScreen mainScreen].bounds];
        alertWindow.backgroundColor = [UIColor clearColor];
        alertWindow.windowLevel = UIWindowLevelAlert - 1;
        
    }
    
    alertWindow.rootViewController = self;
    
    self.alertView.transform = CGAffineTransformMakeScale(1.1, 1.1);
    self.maskView.alpha = 0.0;
    [UIView animateWithDuration:0.25 animations:^{
        self.alertView.transform = CGAffineTransformMakeScale(1.0, 1.0);
        self.maskView.alpha = 1.0;
    }];
    
    [alertWindow makeKeyAndVisible];
    [self sendCode:nil];
    
    [self.smsField becomeFirstResponder];
}

 
- (void)dismissAlert {
    
    self.maskView.alpha = 0.0;
    [self.maskView removeFromSuperview];
    
    alertWindow.hidden = YES;
    
    alertWindow = nil;
}

#pragma mark QLAPIManagerCallBackDelegate
- (void)managerCallAPIDidSuccess:(SABaseManager *)manager
{
    [SATool hideWindowHUD];
    if (manager == self.codeApi)
    {
        
        NSDictionary *dataDict = [manager fetchDataWithReformer:self.codeApi];
        
        NSString *errorMsg = dataDict[@"errorMsg"];
        if ([NSString judgeStringExist:errorMsg]) {
            [SATool textStateWindowHUD:errorMsg];
            [self.smsField clearAllWithBeginEdit:YES];
        }else{
            self.flowId = dataDict[@"flowId"];
            self.effectiveTime = @(60);
            NSString *str = [NSString stringWithFormat:@"%02lds后重新获取验证码", [self.effectiveTime integerValue]];
            [self.codeBtn setTitle:str forState:UIControlStateNormal];
            
            [self addTimer];
        }
    }
    if(manager == self.resendManager)
    {
        
        NSDictionary *dataDict = [manager fetchDataWithReformer:self.resendManager];
        
        NSString *errorMsg = dataDict[@"errorMsg"];
        if ([NSString judgeStringExist:errorMsg]) {
            [SATool textStateWindowHUD:errorMsg];
            [self.smsField clearAllWithBeginEdit:YES];
        }else{
            self.flowId = dataDict[@"flowId"];
            self.effectiveTime = @(60);
            NSString *str = [NSString stringWithFormat:@"%02lds后重新获取验证码", [self.effectiveTime integerValue]];
            [self.codeBtn setTitle:str forState:UIControlStateNormal];
            
            [self addTimer];
        }
    }
    
    if(manager == self.bindApi)
    {
        NSDictionary *dataDict = [manager fetchDataWithReformer:self.bindApi];
        
        NSString *errorMsg = dataDict[@"errorMsg"];
        if ([NSString judgeStringExist:errorMsg]) {
            [SATool textStateWindowHUD:errorMsg];
            [self.smsField clearAllWithBeginEdit:YES];
        }else{
            [self dismissAlert];
            if (self.completeBlock) {
                self.completeBlock();
            }
        }
    }
}

- (void)managerCallAPIDidFailed:(SABaseManager *)manager 
{
    [SATool hideWindowHUD];
    NSString *failMsg  = [SAResponseHandle netWorkErrorMsg:[manager fetchDataWithReformer:nil] andErrorType:manager.errorType];
    [SATool textStateWindowHUD:failMsg];
    
    if (manager == self.bindApi){
        [self.smsField clearAllWithBeginEdit:YES];
    }
}

#pragma mark QLAPIManagerParamSource
- (NSDictionary *)paramsForApi:(SABaseManager *)manager {
    if (manager == self.codeApi) {
        return @{@"orderType": @"2"};
    } else if (manager == self.resendManager){
        return @{@"orderType": @"2", @"flowId": self.flowId};
    } else if (manager == self.bindApi){
        return @{@"orderType": @"2", @"flowId": self.flowId, @"smsCode": self.smsField.textValue};
    }
   
    return [NSDictionary dictionary];
}

#pragma mark - Action

- (void)sendCode:(id)sender
{
    [SATool showWindowHUD:nil];
    if([NSString judgeStringExist:self.flowId]){
        [self.resendManager loadData];
    }else{
        [self.codeApi loadData];
    }
}

- (void)clickSureButton:(UIButton *)btn 
{
    if(![NSString judgeStringExist:self.flowId]){
        [SATool textStateWindowHUD:@"请先发送短信验证码"];
        return;
    }
    if(![NSString judgeStringExist:self.smsField.textValue]){
        [SATool textStateWindowHUD:@"请输入短信验证码"];
        return;
    }
    [SATool showWindowHUD:nil];
    [self.bindApi loadData];
}

- (void)cancelAction
{
    [self dismissAlert];
}

#pragma mark - timer

- (void)addTimer
{
    [self removeTimer];
    
    self.times = self.effectiveTime.intValue;
    NSTimer *timer = [NSTimer timerWithTimeInterval:1.0 target:self selector:@selector(beginUpdateUI) userInfo:nil repeats:YES];
    self.timer = timer;
    [[NSRunLoop mainRunLoop] addTimer:timer forMode:NSRunLoopCommonModes];
}

- (void)removeTimer 
{
    [_timer invalidate];
    self.timer = nil;
}

- (void)beginUpdateUI 
{
    if (self.times > 0) {
        self.times--;
        NSString *str = [NSString stringWithFormat:@"%02ds后重新获取验证码", self.times];
        [self.codeBtn setTitle:str forState:UIControlStateNormal];
        self.codeBtn.enabled = NO;
        [self.codeBtn setTitleColor:[UIColor colorWithHex:0x777777] forState:UIControlStateNormal];
    } else {
        [self.codeBtn setTitle:@"重新发送验证码" forState:UIControlStateNormal];
        self.codeBtn.enabled = YES;
        [self.codeBtn setTitleColor:kPrimaryColor forState:UIControlStateNormal];
    }
}

#pragma mark - getters

- (UIView *)maskView {
    if (_maskView == nil) {
        
        _maskView = [[UIView alloc] init];
        _maskView.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.7];
        _maskView.alpha = 0.0;
    }
    return _maskView;
}

- (UIView *)alertView {
    if (_alertView == nil) {
        _alertView = [[UIView alloc] init];
        _alertView.backgroundColor = [UIColor whiteColor];
    }
    return _alertView;
}

- (UILabel *)titleLab {
    if (_titleLab == nil) {
        _titleLab = [[UILabel alloc] init];
        _titleLab.font = BoldFont_XX6(14);
        _titleLab.textAlignment = NSTextAlignmentCenter;
        _titleLab.textColor = [UIColor blackColor];
    }
    return _titleLab;
}


- (UIButton *)cancelBtn{
    if(_cancelBtn == nil){
        _cancelBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_cancelBtn addTarget:self action:@selector(cancelAction) forControlEvents:UIControlEventTouchUpInside];
        [_cancelBtn setImage:[UIImage imageNamed:@"close"] forState:UIControlStateNormal];
    }
    return _cancelBtn;
}

- (UIButton *)codeBtn{
    if(_codeBtn == nil){
        _codeBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_codeBtn addTarget:self action:@selector(sendCode:) forControlEvents:UIControlEventTouchUpInside];
        [_codeBtn setTitleColor:kPrimaryColor forState:UIControlStateNormal];
        _codeBtn.titleLabel.font = Font_XX6(14);
        [_codeBtn setTitle:@"发送验证码" forState:UIControlStateNormal];
    }
    return _codeBtn;
}

- (SAGradientButton *)submitBtn
{
    if (_submitBtn == nil) {
        _submitBtn = [SAGradientButton buttonWithType:UIButtonTypeCustom];
        [_submitBtn setTitle:@"确认" forState:UIControlStateNormal];
        [_submitBtn setTitleColor:[UIColor colorWithHex:0xffffff] forState:UIControlStateNormal];
        _submitBtn.titleLabel.font = [UIFont fontSizeOfXX_6:16];
        _submitBtn.titleLabel.textAlignment = NSTextAlignmentCenter;
        [_submitBtn addTarget:self action:@selector(clickSureButton:) forControlEvents:UIControlEventTouchUpInside];
        _submitBtn.backgroundColor = kPrimaryColor;
    }
    return _submitBtn;
}

- (void)dealloc {
    
}

- (SABindCardSendCodeApi *)codeApi {
    if (_codeApi == nil) {
        _codeApi = [[SABindCardSendCodeApi alloc] init];
        _codeApi.delegate = self;
        _codeApi.paramSource = self;
    }
    return _codeApi;
}

- (SABindCardResendSmsApi *)resendManager{
    if (_resendManager == nil) {
        _resendManager = [[SABindCardResendSmsApi alloc] init];
        _resendManager.delegate = self;
        _resendManager.paramSource = self;
    }
    return _resendManager;
}

- (SASaveBindCardInfoApi *)bindApi{
    if (_bindApi == nil) {
        _bindApi = [[SASaveBindCardInfoApi alloc] init];
        _bindApi.delegate = self;
        _bindApi.paramSource = self;
    }
    return _bindApi;
}

@end
