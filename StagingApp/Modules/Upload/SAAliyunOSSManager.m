
#import "SAAliyunOSSManager.h"
#import "SAScope.h"

#import <AliyunOSSiOS/OSSService.h>
#import "SATokenApiManager.h"
#import "SANLUploadManager.h"

@interface SAAliyunOSSManager ()<QLAPIManagerParamSource, QLAPIManagerCallBackDelegate>

@property (nonatomic, strong) OSSClient                     *client;
@property (nonatomic, strong) OSSPutObjectRequest           *put;


@property (nonatomic, strong) NSString                       *relativePath;
@property (nonatomic, strong) NSString                       *fullPath;
@property (nonatomic, strong) NSNumber                       *tagNumber;

@property (nonatomic, assign) NSInteger                     retryCount; 

@property (nonatomic, copy)   YJBUploadSuccess              successBlock;
@property (nonatomic, copy)   YJBUploadFailed               failedBlock;

@property (nonatomic, strong) SATokenApiManager         *ossTokenAPIManager;

@property (nonatomic, strong) SABaseManager              *apiManager;
@property (nonatomic, strong) NSMutableDictionary           *uploadParams;
@property (nonatomic, strong) NSDictionary                    *apiParams;


@end

@implementation SAAliyunOSSManager


+(instancetype) sharedInstance
{
    static dispatch_once_t once;
    static id instance;
    dispatch_once(&once, ^{
        instance = [self new];
    });
    return instance;
}


- (void)setupEnvironment
{
#ifdef DEBUG
    [OSSLog enableLog];
#endif
}

- (void)launchUpdateToken
{
    [self.ossTokenAPIManager loadData];
}

#pragma mark - Upload Data

 
- (void)uploadObjectAsync:(NSData *)fileData
               apiManager:(NSString *)apiManagerName
                apiParams:(NSDictionary *)params
                      tag:(NSNumber *)tagNumber
                  success:(YJBUploadSuccess)successBlock
                   failed:(YJBUploadFailed)failBlock
{
    if (fileData == nil)
    {
        NSAssert(NO, @"fileData 参数不能为空");
        return;
    }

    self.put                   = nil;
    self.put.uploadingData     = fileData;
    
    NSArray  *splitList = [self.client.endpoint componentsSeparatedByString:@"//"];
    
    NSString *relativePath = self.put.objectKey;
    NSString *fullPath  = [NSString stringWithFormat:@"%@//%@.%@/%@", splitList[0], self.put.bucketName, splitList[1], relativePath];
    
    self.relativePath        = relativePath;
    self.fullPath            = fullPath;
    self.tagNumber           = tagNumber;
    self.successBlock        = successBlock;
    self.failedBlock         = failBlock;
    
    [self handleApiManager:apiManagerName apiParams:params];
    [self uploadOSSFileWithTag:self.tagNumber fullPath:fullPath relativePath: relativePath];
}

- (void)uploadObjectAsync:(NSData *)fileData
               apiManager:(NSString *)apiManagerName
                apiParams:(NSDictionary *)params
                  success:(YJBUploadSuccess)successBlock
                   failed:(YJBUploadFailed)failBlock
{
    [self uploadObjectAsync:fileData apiManager:apiManagerName apiParams:params tag:nil success:successBlock failed:failBlock];
}

- (void)uploadObjectAsync:(NSData *)fileData
                  success:(YJBUploadSuccess)successBlock
                   failed:(YJBUploadFailed)failBlock
{
    [self uploadObjectAsync:fileData apiManager:nil apiParams:nil tag:nil success:successBlock failed:failBlock];
}

- (void)uploadObjectAsync:(NSData *)fileData
                      tag:(NSNumber *)tagNumber
                  success:(YJBUploadSuccess)successBlock
                   failed:(YJBUploadFailed)failBlock;
{
    [self uploadObjectAsync:fileData apiManager:nil apiParams:nil tag:tagNumber success:successBlock failed:failBlock];
}

#pragma mark - YJBAPIManagerCallBackDelegate

- (void)managerCallAPIDidSuccess:(SABaseManager *)manager
{
    if (manager == self.ossTokenAPIManager)
    {
        NSDictionary *retDict = [manager fetchDataWithReformer:self.ossTokenAPIManager];
        if ([retDict isKindOfClass:[NSDictionary class]] && retDict)
        {
            if (self.put && self.put.uploadingData)
            {
                [self uploadOSSFileWithTag:self.tagNumber fullPath:self.fullPath relativePath:self.relativePath];
            }
            else
            {
                self.client = nil;
            }
        }
    }
    else
    {
        if (self.successBlock)
        {
            self.successBlock([manager fetchDataWithReformer:nil]);
            self.apiManager = nil;
        }
    }
}

- (void)managerCallAPIDidFailed:(SABaseManager *)manager
{
    if (manager == self.ossTokenAPIManager)
    {
        NSString *errorMsg = [SAResponseHandle netWorkErrorMsg:[manager fetchDataWithReformer:nil] andErrorType:manager.errorType];
        [SATool textStateHUD:errorMsg];
    }
    else
    {
        if (self.failedBlock)
        {
            NSString *errorMsg = [SAResponseHandle netWorkErrorMsg:[manager fetchDataWithReformer:nil] andErrorType:manager.errorType];
            self.failedBlock([NSError errorWithDomain:@"OSS-Upload-File" code:manager.errorType userInfo:@{@"msg":errorMsg}]);
            self.apiManager = nil;
        }
    }
}

#pragma mark - YJBAPIManagerParamSource

- (NSDictionary *)paramsForApi:(SABaseManager *)manager
{
    if (manager == self.apiManager)
    {
        return self.uploadParams;
    }
    return [NSDictionary new];
}

#pragma mark - Private

- (void)uploadOSSFileWithTag:(NSNumber *)tag fullPath:(NSString *)fullPath relativePath: (NSString *)relativePath
{
    OSSTask * putTask = [self.client putObject:self.put];
    @weakify(self)
    [putTask continueWithBlock:^id(OSSTask *task){
        
        @strongify(self)
        if (!task.error)
        {
            self.put = nil;
            self.retryCount = 0;
            if (self.apiManager == nil)
            {
                if (self.successBlock)
                {
                    NSMutableDictionary *params = [NSMutableDictionary new];
                    params[kFileURL] = fullPath;
                    params[kRelativeUrl] = relativePath;
                    if (tag)
                    {
                        params[kTag] = tag;
                    }
                    self.successBlock(params);
                }
            }
            else
            {
                [self.apiParams.allKeys enumerateObjectsUsingBlock:^(NSString *key, NSUInteger idx, BOOL * _Nonnull stop) {
                    
                    if ([key isEqualToString:kFileURL])
                    {
                        [self.uploadParams setObject:fullPath forKey:self.apiParams[key]];
                    }
                    else
                    {
                        [self.uploadParams setObject:self.apiParams[key] forKey:key];
                    }
                }];
                [self.apiManager loadData];
            }
        }
        else
        {
                if (self.failedBlock)
                {
                    NSError *error = [NSError errorWithDomain:@"OSS-Upload-File" code:task.error.code userInfo:@{@"msg":task.error.description}];
                    self.failedBlock(error);
                }
        }
        return nil;
    }];
}

- (void)handleApiManager:(NSString *)apiManagerName
               apiParams:(NSDictionary *)params
{
    if (apiManagerName == nil || params == nil) return;
    if (![params objectForKey:kFileURL]) return;
    
    self.apiParams = params;
    Class apiManagerClass         = NSClassFromString(apiManagerName);
    self.apiManager               = [[apiManagerClass alloc] init];
    self.apiManager.delegate     = self;
    self.apiManager.paramSource = self;
    self.uploadParams             = [NSMutableDictionary dictionary];
}

#pragma mark - getters & setters

- (SATokenApiManager *)ossTokenAPIManager
{
    if (!_ossTokenAPIManager)
    {
        _ossTokenAPIManager = [[SATokenApiManager alloc] init];
        _ossTokenAPIManager.delegate    = self;
        _ossTokenAPIManager.paramSource = self;
    }
    return _ossTokenAPIManager;
}

- (OSSPutObjectRequest *)put
{
    if (!_put)
    {
        _put = [OSSPutObjectRequest new];
        _put.bucketName = [SANLUploadManager sharedInstance].upModel.bucketName; 
        NSString *uuid = [[NSUUID UUID] UUIDString];
        _put.objectKey  = [NSString stringWithFormat:@"%@/%@", [SANLUploadManager sharedInstance].upModel.objectName, uuid];
    }
    return _put;
}

- (OSSClient *)client
{
    if (!_client)
    {
        id<OSSCredentialProvider> credential = [[OSSFederationCredentialProvider alloc] initWithFederationTokenGetter:^OSSFederationToken *{
            
            OSSFederationToken *token       = [OSSFederationToken new];
            token.tAccessKey                = [SANLUploadManager sharedInstance].upModel.accessKeyId;
            token.tSecretKey                = [SANLUploadManager sharedInstance].upModel.accessKeySecret;
            token.tToken                    = [SANLUploadManager sharedInstance].upModel.securityToken;
            token.expirationTimeInGMTFormat = [SANLUploadManager sharedInstance].upModel.expiration;
            return token;
            
        }];
        
        OSSClientConfiguration * conf   = [OSSClientConfiguration new];
        conf.maxRetryCount              = 3;
        conf.timeoutIntervalForRequest  = 30;
        conf.timeoutIntervalForResource = 24 * 60 * 60;
        
        NSString *endPointString        = [SANLUploadManager sharedInstance].upModel.baseUrl; 
        _client                         = [[OSSClient alloc] initWithEndpoint:endPointString credentialProvider:credential clientConfiguration:conf];
    }
    return _client;
}

@end
