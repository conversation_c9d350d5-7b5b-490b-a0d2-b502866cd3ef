
#import "SATextPickerView.h"

@interface SATextPickerView ()<UIPickerViewDelegate, UIPickerViewDataSource>
 
@property (nonatomic, strong) UIPickerView *pickerView;
 
@property (nonatomic, copy) NSArray *dataList;

@property(nonatomic, assign) NSInteger rollingComponent;
@property(nonatomic, assign) NSInteger rollingRow;

@end

@implementation SATextPickerView

#pragma mark - 1.显示【单列】选择器
+ (void)showPickerWithTitle:(NSString *)title
              dataSourceArr:(NSArray *)dataSourceArr
                selectIndex:(NSInteger)selectIndex
                resultBlock:(BRSingleResultBlock)resultBlock {
    
    SATextPickerView *strPickerView = [[SATextPickerView alloc]init];
    strPickerView.pickerMode = BRTextPickerComponentSingle;
    strPickerView.title = title;
    strPickerView.dataSourceArr = dataSourceArr;
    strPickerView.selectIndex = selectIndex;
    strPickerView.singleResultBlock = resultBlock;
    
    
    [strPickerView show];
}

#pragma mark - 2.显示【多列】选择器
+ (void)showMultiPickerWithTitle:(NSString *)title
                   dataSourceArr:(NSArray *)dataSourceArr
                    selectIndexs:(NSArray <NSNumber *>*)selectIndexs
                     resultBlock:(BRMultiResultBlock)resultBlock {
    
    SATextPickerView *strPickerView = [[SATextPickerView alloc]init];
    strPickerView.pickerMode = BRTextPickerComponentMulti;
    strPickerView.title = title;
    strPickerView.dataSourceArr = dataSourceArr;
    strPickerView.selectIndexs = selectIndexs;
    strPickerView.multiResultBlock = resultBlock;
    
    
    [strPickerView show];
}

#pragma mark - 3.显示【联动】选择器
+ (void)showCascadePickerWithTitle:(nullable NSString *)title
                     dataSourceArr:(nullable NSArray *)dataSourceArr
                      selectIndexs:(nullable NSArray <NSNumber *> *)selectIndexs
                       resultBlock:(nullable BRMultiResultBlock)resultBlock {
    
    SATextPickerView *strPickerView = [[SATextPickerView alloc]init];
    strPickerView.pickerMode = BRTextPickerComponentCascade;
    strPickerView.title = title;
    strPickerView.dataSourceArr = dataSourceArr;
    strPickerView.selectIndexs = selectIndexs;
    strPickerView.multiResultBlock = resultBlock;
    
    
    [strPickerView show];
}

#pragma mark - 初始化自定义选择器
- (instancetype)initWithPickerMode:(BRTextPickerMode)pickerMode {
    if (self = [super init]) {
        self.pickerMode = pickerMode;
    }
    return self;
}

#pragma mark - 处理选择器数据 和 默认选择状态
- (void)handlerPickerData {
    
    BOOL dataSourceError = NO;
    if (self.dataSourceArr.count == 0) {
        dataSourceError = YES;
    }
    id item = [self.dataSourceArr firstObject];
    if (self.pickerMode == BRTextPickerComponentSingle) {
        dataSourceError = !([item isKindOfClass:[NSString class]] || [item isKindOfClass:[SATextModel class]]);
    } else if (self.pickerMode == BRTextPickerComponentMulti) {
        dataSourceError = ![item isKindOfClass:[NSArray class]];
    } else if (self.pickerMode == BRTextPickerComponentCascade) {
        dataSourceError = ![item isKindOfClass:[SATextModel class]];
    }
    if (dataSourceError) {
        BRErrorLog(@"数据源异常！请检查选择器数据源的格式");
        return;
    }
    
    
    if (self.pickerMode == BRTextPickerComponentSingle) {
        self.dataList = self.dataSourceArr;
        if (self.selectIndex < 0 || self.selectIndex >= self.dataList.count) {
            self.selectIndex = 0;
        }
    } else if (self.pickerMode == BRTextPickerComponentMulti) {
        self.dataList = self.dataSourceArr;
        NSMutableArray *selectIndexs = [[NSMutableArray alloc]init];
        for (NSInteger component = 0; component < self.dataList.count; component++) {
            NSArray *itemArr = self.dataList[component];
            NSInteger row = 0;
            if (self.selectIndexs.count > 0 && component < self.selectIndexs.count) {
                NSInteger index = [self.selectIndexs[component] integerValue];
                row = ((index > 0 && index < itemArr.count) ? index : 0);
            }
            [selectIndexs addObject:@(row)];
        }
        self.selectIndexs = [selectIndexs copy];
    } else if (self.pickerMode == BRTextPickerComponentCascade) {
        NSMutableArray *dataList = [[NSMutableArray alloc]init];
        [dataList addObject:self.dataSourceArr];
        NSMutableArray *selectIndexs = [[NSMutableArray alloc]init];
        
        BOOL hasNext = self.dataSourceArr.count > 0;
        NSInteger i = 0;
        NSInteger selectIndex = self.selectIndexs.count > 0 && i < self.selectIndexs.count ? [self.selectIndexs[i] integerValue] : 0;
        [selectIndexs addObject:@(selectIndex)];
        SATextModel *selectModel = self.dataSourceArr[selectIndex];
        while (hasNext) {
            NSArray *nextArr = selectModel.children;
            if (!nextArr || nextArr.count == 0) {
                hasNext = NO;
                break;
            }
            [dataList addObject:nextArr];
            
            i++;
            selectIndex = self.selectIndexs.count > 0 && i < self.selectIndexs.count ? [self.selectIndexs[i] integerValue] : 0;
            [selectIndexs addObject:@(selectIndex)];
            selectModel = nextArr[selectIndex];
        }
        
        
        if (self.showColumnNum > 0) {
            NSInteger dataListCount = dataList.count;
            if (self.showColumnNum < dataListCount) {
                
                dataList = [[dataList subarrayWithRange:NSMakeRange(0, self.showColumnNum)] mutableCopy];
                selectIndexs = [[selectIndexs subarrayWithRange:NSMakeRange(0, self.showColumnNum)] mutableCopy];
            } else {
                
                for (NSInteger i = 0; i < self.showColumnNum - dataListCount; i++) {
                    
                    SATextModel *placeholderModel = [[SATextModel alloc]init];
                    NSArray *placeholderArr = @[placeholderModel];
                    [dataList addObject:placeholderArr];
                    [selectIndexs addObject:@(0)];
                }
            }
        }
        self.dataList = [dataList copy];
        self.selectIndexs = [selectIndexs copy];
    }
}

#pragma mark - 选择器
- (UIPickerView *)pickerView {
    if (!_pickerView) {
        CGFloat pickerHeaderViewHeight = self.pickerHeaderView ? self.pickerHeaderView.bounds.size.height : 0;
        _pickerView = [[UIPickerView alloc]initWithFrame:CGRectMake(0, self.pickerStyle.titleBarHeight + pickerHeaderViewHeight, self.keyView.bounds.size.width, self.pickerStyle.pickerHeight)];
        _pickerView.backgroundColor = self.pickerStyle.pickerColor;
        _pickerView.autoresizingMask = UIViewAutoresizingFlexibleBottomMargin | UIViewAutoresizingFlexibleRightMargin | UIViewAutoresizingFlexibleWidth;
        _pickerView.dataSource = self;
        _pickerView.delegate = self;
    }
    return _pickerView;
}

#pragma mark - UIPickerViewDataSource
- (NSInteger)numberOfComponentsInPickerView:(UIPickerView *)pickerView {
    switch (self.pickerMode) {
        case BRTextPickerComponentSingle:
            return 1;
        case BRTextPickerComponentMulti:
        case BRTextPickerComponentCascade:
        {
            if (self.pickerStyle.columnSpacing > 0) {
                return self.dataList.count * 2 - 1;
            }
            return self.dataList.count;
        }
            
        default:
            break;
    }
}

- (NSInteger)pickerView:(UIPickerView *)pickerView numberOfRowsInComponent:(NSInteger)component {
    switch (self.pickerMode) {
        case BRTextPickerComponentSingle:
            return self.dataList.count;
        case BRTextPickerComponentMulti:
        case BRTextPickerComponentCascade:
        {
            if (self.pickerStyle.columnSpacing > 0) {
                if (component % 2 == 1) {
                    return 1;
                } else {
                    component = component / 2;
                }
            }
            NSArray *itemArr = self.dataList[component];
            return itemArr.count;
        }
            break;
            
        default:
            break;
    }
}

#pragma mark - UIPickerViewDelegate
- (UIView *)pickerView:(UIPickerView *)pickerView viewForRow:(NSInteger)row forComponent:(NSInteger)component reusingView:(nullable UIView *)view {
    
    UILabel *label = (UILabel *)view;
    if (!label) {
        label = [[UILabel alloc]init];
        label.backgroundColor = [UIColor clearColor];
        label.textAlignment = NSTextAlignmentCenter;
        label.font = self.pickerStyle.pickerTextFont;
        label.textColor = self.pickerStyle.pickerTextColor;
        label.numberOfLines = self.pickerStyle.maxTextLines;
        
        label.adjustsFontSizeToFitWidth = YES;
        
        label.minimumScaleFactor = 0.5f;
    }
    
    
    [self.pickerStyle setupPickerSelectRowStyle:pickerView titleForRow:row forComponent:component];
    
    
    
    NSInteger selectRow = [pickerView selectedRowInComponent:component];
    if (selectRow >= 0) {
        self.rollingComponent = component;
        self.rollingRow = selectRow;
    }

    
    if (self.pickerMode == BRTextPickerComponentSingle) {
        id item = self.dataList[row];
        if ([item isKindOfClass:[SATextModel class]]) {
            SATextModel *model = (SATextModel *)item;
            label.text = model.text;
        } else {
            label.text = item;
        }
    } else if (self.pickerMode == BRTextPickerComponentMulti || self.pickerMode == BRTextPickerComponentCascade) {
        
        if (self.pickerStyle.columnSpacing > 0) {
            if (component % 2 == 1) {
                label.text = @"";
                return label;
            } else {
                component = component / 2;
            }
        }
        
        NSArray *itemArr = self.dataList[component];
        id item = [itemArr objectAtIndex:row];
        if ([item isKindOfClass:[SATextModel class]]) {
            SATextModel *model = (SATextModel *)item;
            label.text = model.text;
        } else {
            label.text = item;
        }
    }
    
    return label;
}

- (BOOL)getRollingStatus:(UIView *)view {
    if ([view isKindOfClass:[UIScrollView class]]) {
        UIScrollView *scrollView = (UIScrollView *)view;
        if (scrollView.dragging || scrollView.decelerating) {
            
            return YES;
        }
    }
    
    for (UIView *subView in view.subviews) {
        if ([self getRollingStatus:subView]) {
            return YES;
        }
    }
    
    return NO;
}

- (BOOL)isRolling {
    return [self getRollingStatus:self.pickerView];
}

- (void)pickerView:(UIPickerView *)pickerView didSelectRow:(NSInteger)row inComponent:(NSInteger)component {
    switch (self.pickerMode) {
        case BRTextPickerComponentSingle:
        {
            self.selectIndex = row;
            
            self.singleChangeBlock ? self.singleChangeBlock([self getSingleSelectModel], self.selectIndex): nil;
        }
            break;
        case BRTextPickerComponentMulti:
        {
            
            if (self.pickerStyle.columnSpacing > 0) {
                if (component % 2 == 1) {
                    return;
                } else {
                    component = component / 2;
                }
            }
            
            if (component < self.selectIndexs.count) {
                NSMutableArray *mutableArr = [self.selectIndexs mutableCopy];
                [mutableArr replaceObjectAtIndex:component withObject:@(row)];
                self.selectIndexs = [mutableArr copy];
            }
            
            
            self.multiChangeBlock ? self.multiChangeBlock([self getMultiSelectModels], self.selectIndexs): nil;
        }
            break;
        case BRTextPickerComponentCascade:
        {
            
            if (self.pickerStyle.columnSpacing > 0) {
                if (component % 2 == 1) {
                    return;
                } else {
                    component = component / 2;
                }
            }
            
            if (component < self.selectIndexs.count) {
                NSMutableArray *selectIndexs = [[NSMutableArray alloc]init];
                for (NSInteger i = 0; i < self.selectIndexs.count; i++) {
                    if (i < component) {
                        [selectIndexs addObject:self.selectIndexs[i]];
                    } else if (i == component) {
                        [selectIndexs addObject:@(row)];
                    } else {
                        [selectIndexs addObject:@(0)];
                    }
                }
                self.selectIndexs = [selectIndexs copy];
            }
            
            
            [self reloadData];
            
            
            self.multiChangeBlock ? self.multiChangeBlock([self getMultiSelectModels], self.selectIndexs): nil;
        }
            break;
            
        default:
            break;
    }
}

#pragma mark - 获取【单列】选择器选择的值
- (SATextModel *)getSingleSelectModel {
    id item = self.selectIndex < self.dataList.count ? self.dataList[self.selectIndex] : nil;
    if ([item isKindOfClass:[SATextModel class]]) {
        SATextModel *model = (SATextModel *)item;
        model.index = self.selectIndex;
        return model;
    } else {
        SATextModel *model = [[SATextModel alloc]init];
        model.index = self.selectIndex;
        model.text = item;
        return model;
    }
}

#pragma mark - 获取【多列】选择器选择的值
- (NSArray *)getMultiSelectModels {
    NSMutableArray *modelArr = [[NSMutableArray alloc]init];
    for (NSInteger i = 0; i < self.dataList.count; i++) {
        NSInteger index = [self.selectIndexs[i] integerValue];
        NSArray *dataArr = self.dataList[i];
        
        id item = index < dataArr.count ? dataArr[index] : nil;
        if ([item isKindOfClass:[SATextModel class]]) {
            SATextModel *model = (SATextModel *)item;
            model.index = index;
            [modelArr addObject:model];
        } else {
            SATextModel *model = [[SATextModel alloc]init];
            model.index = index;
            model.text = item;
            [modelArr addObject:model];
        }
    }
    return [modelArr copy];
}

- (CGFloat)pickerView:(UIPickerView *)pickerView rowHeightForComponent:(NSInteger)component {
    return self.pickerStyle.rowHeight;
}

- (CGFloat)pickerView:(UIPickerView *)pickerView widthForComponent:(NSInteger)component {
    if (self.pickerStyle.columnSpacing > 0 && component % 2 == 1) {
        return self.pickerStyle.columnSpacing;
    }
    NSInteger columnCount = [self numberOfComponentsInPickerView:pickerView];
    CGFloat columnWidth = self.pickerView.bounds.size.width / columnCount;
    if (self.pickerStyle.columnWidth > 0 && self.pickerStyle.columnWidth <= columnWidth) {
        return self.pickerStyle.columnWidth;
    }
    return columnWidth;
}

#pragma mark - 重写父类方法
- (void)reloadData {
    
    [self handlerPickerData];
    
    [self.pickerView reloadAllComponents];
    
    if (self.pickerMode == BRTextPickerComponentSingle) {
        [self.pickerView selectRow:self.selectIndex inComponent:0 animated:self.selectRowAnimated];
    } else if (self.pickerMode == BRTextPickerComponentMulti || self.pickerMode == BRTextPickerComponentCascade) {
        for (NSInteger i = 0; i < self.selectIndexs.count; i++) {
            NSNumber *row = [self.selectIndexs objectAtIndex:i];
            NSInteger component = i;
            if (self.pickerStyle.columnSpacing > 0) {
                component = i * 2;
            }
            [self.pickerView selectRow:[row integerValue] inComponent:component animated:self.selectRowAnimated];
        }
    }
}

- (void)addPickerToView:(UIView *)view {
    
    if (view) {
        
        [view setNeedsLayout];
        [view layoutIfNeeded];
        
        self.frame = view.bounds;
        CGFloat pickerHeaderViewHeight = self.pickerHeaderView ? self.pickerHeaderView.bounds.size.height : 0;
        CGFloat pickerFooterViewHeight = self.pickerFooterView ? self.pickerFooterView.bounds.size.height : 0;
        self.pickerView.frame = CGRectMake(0, pickerHeaderViewHeight, view.bounds.size.width, view.bounds.size.height - pickerHeaderViewHeight - pickerFooterViewHeight);
        [self addSubview:self.pickerView];
    } else {
        
        CGFloat pickerHeaderViewHeight = self.pickerHeaderView ? self.pickerHeaderView.bounds.size.height : 0;
        self.pickerView.frame = CGRectMake(0, self.pickerStyle.titleBarHeight + pickerHeaderViewHeight, self.keyView.bounds.size.width, self.pickerStyle.pickerHeight);
        
        [self.alertView addSubview:self.pickerView];
    }
    
    
    if (self.pickerStyle.clearPickerNewStyle) {
        [self.pickerStyle addSeparatorLineView:self.pickerView];
    }
    
    
    [self reloadData];
    
    __weak typeof(self) weakSelf = self;
    
    self.doneBlock = ^{
        if (weakSelf.isRolling) {
            NSLog(@"选择器滚动还未结束");
            
            
            [weakSelf pickerView:weakSelf.pickerView didSelectRow:weakSelf.rollingRow inComponent:weakSelf.rollingComponent];
        }
    
        
        if (weakSelf.pickerMode == BRTextPickerComponentSingle) {
            weakSelf.singleResultBlock ? weakSelf.singleResultBlock([weakSelf getSingleSelectModel], weakSelf.selectIndex): nil;
        } else if (weakSelf.pickerMode == BRTextPickerComponentMulti || weakSelf.pickerMode == BRTextPickerComponentCascade) {
            weakSelf.multiResultBlock ? weakSelf.multiResultBlock([weakSelf getMultiSelectModels], weakSelf.selectIndexs): nil;
        }
    };
    
    [super addPickerToView:view];
}

#pragma mark - 重写父类方法
- (void)addSubViewToPicker:(UIView *)customView {
    [self.pickerView addSubview:customView];
}

#pragma mark - 弹出选择器视图
- (void)show {
    [self addPickerToView:nil];
}

#pragma mark - 关闭选择器视图
- (void)dismiss {
    [self removePickerFromView:nil];
}

#pragma mark - setter 方法
- (void)setFileName:(NSString *)fileName {
    NSString *filePath = [[NSBundle mainBundle] pathForResource:fileName ofType:nil];
    if (filePath && filePath.length > 0) {
        if ([fileName hasSuffix:@".plist"]) {
            
            NSArray *dataArr = [[NSArray alloc] initWithContentsOfFile:filePath];
            if (dataArr && dataArr.count > 0) {
                self.dataSourceArr = dataArr;
            }
        } else if ([fileName hasSuffix:@".json"]) {
            
            NSData *jsonData = [NSData dataWithContentsOfFile:filePath];
            NSArray *dataArr = [NSJSONSerialization JSONObjectWithData:jsonData options:NSJSONReadingMutableContainers error:nil];
            if (dataArr && dataArr.count > 0) {
                self.dataSourceArr = [NSArray br_modelArrayWithJson:dataArr mapper:nil];
            }
        }
    }
}

@end
