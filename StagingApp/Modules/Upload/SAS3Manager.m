
#import "SAS3Manager.h"

#import <AWSCore.h>
#import <AWSS3.h>

@interface SAS3Manager ()

@property (nonatomic, strong) AWSBasicSessionCredentialsProvider *credentialsProvider;

@property (nonatomic, strong) NSString                           *fullPath;

@end

@implementation SAS3Manager

+(instancetype) sharedInstance
{
    static dispatch_once_t once;
    static id instance;
    dispatch_once(&once, ^{
        instance = [self new];
    });
    return instance;
}

- (void)setup
{
    NSLog(@"初始化aws===");
    self.credentialsProvider = nil;
    self.credentialsProvider = [[AWSBasicSessionCredentialsProvider alloc] initWithAccessKey:[SANLUploadManager sharedInstance].upModel.accessKeyId
                                                                                   secretKey:[SANLUploadManager sharedInstance].upModel.accessKeySecret
                                                                                sessionToken:[SANLUploadManager sharedInstance].upModel.securityToken];

    AWSRegionType regionType = [[SANLUploadManager sharedInstance].upModel.region aws_regionTypeValue];
    AWSServiceConfiguration *configuration = [[AWSServiceConfiguration alloc] initWithRegion:regionType
                                                                        credentialsProvider:self.credentialsProvider];
    
    AWSS3TransferUtility *transferUtility = [AWSS3TransferUtility S3TransferUtilityForKey:[SANLUploadManager sharedInstance].upModel.accessKeyId];
    if(transferUtility == nil){
        [AWSS3TransferUtility registerS3TransferUtilityWithConfiguration:configuration forKey:[SANLUploadManager sharedInstance].upModel.accessKeyId];
    }
}

- (void)uploadObjectAsync:(NSData *)fileData
                  success:(NPUploadSuccess)successBlock
                   failed:(NPUploadFailed)failBlock
{
    NSString *uuid = [[NSUUID UUID] UUIDString];
    NSString *relativePath  = [NSString stringWithFormat:@"%@/%@", [SANLUploadManager sharedInstance].upModel.objectName, uuid];

    NSString *fullPath  = [NSString stringWithFormat:@"%@/%@", [SANLUploadManager sharedInstance].upModel.urlPrefix, relativePath];
    NSLog(@"fullpath---%@---%@", [SANLUploadManager sharedInstance].upModel.urlPrefix, fullPath);

    
    AWSS3TransferUtility *transferUtility = [AWSS3TransferUtility S3TransferUtilityForKey:[SANLUploadManager sharedInstance].upModel.accessKeyId];
    [[transferUtility uploadData:fileData
                          bucket:[SANLUploadManager sharedInstance].upModel.bucketName
                             key:relativePath
                     contentType:@"image/jpeg"
                      expression:nil
               completionHandler:^(AWSS3TransferUtilityUploadTask * _Nonnull task, NSError * _Nullable error) {
        
        NSLog(@"complete----");
        if(!error){
            successBlock(fullPath);
        }else{
            failBlock(error);
        }
        
        }] continueWithBlock:^id(AWSTask *task) {
            
            if (task.error) {
                NSLog(@"AWSTask.error: %@", task.error);
                failBlock(task.error);
            }
            
            if (task.result) {
                NSLog(@"task result----%@", task.result);
            }
            return nil;
        }];
}

#pragma mark - getter


@end
