
#import "SADeviceTool.h"
#import <sys/utsname.h>
#import "SABCTool.h"
#import <Reachability/Reachability.h>
#import <CoreTelephony/CTTelephonyNetworkInfo.h>
#import <CoreTelephony/CTCarrier.h>

@implementation SADeviceTool

+ (NSString *)deviceModelName {
    static NSString *deviceModelName;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        deviceModelName = [self getDeviceModelName];
    });
    return deviceModelName;
}

+ (NSString *)deviceSystemVersion {
    UIDevice *device = [UIDevice currentDevice];
    return device.systemVersion;
}

+ (NSString *)buildVersion {
    static NSString *buildVersion;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        NSDictionary *infoDic = [[NSBundle mainBundle] infoDictionary];
        buildVersion = [infoDic objectForKey:@"CFBundleVersion"];
    });
    return buildVersion;
}

+ (NSString *)appVersion {
    static NSString *appVersion;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        NSDictionary *infoDic = [[NSBundle mainBundle] infoDictionary];
        appVersion = [infoDic objectForKey:@"CFBundleShortVersionString"];
    });
    return appVersion;
}

+ (NSString *)appName {
    static NSString *appName;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        NSDictionary *infoDic = [[NSBundle mainBundle] infoDictionary];
        appName = [infoDic objectForKey:@"CFBundleDisplayName"];
    });
    return appName;
}

+ (NSString *)getDeviceModelName {
    struct utsname systemInfo;
    uname(&systemInfo);
    NSString *deviceString = [NSString stringWithCString:systemInfo.machine encoding:NSUTF8StringEncoding];
    
    
    if ([deviceString isEqualToString:@"iPhone3,1"])    return @"iPhone 4";
    if ([deviceString isEqualToString:@"iPhone3,2"])    return @"iPhone 4";
    if ([deviceString isEqualToString:@"iPhone3,3"])    return @"iPhone 4";
    if ([deviceString isEqualToString:@"iPhone4,1"])    return @"iPhone 4S";
    if ([deviceString isEqualToString:@"iPhone5,1"])    return @"iPhone 5";
    if ([deviceString isEqualToString:@"iPhone5,2"])    return @"iPhone 5 (GSM+CDMA)";
    if ([deviceString isEqualToString:@"iPhone5,3"])    return @"iPhone 5c (GSM)";
    if ([deviceString isEqualToString:@"iPhone5,4"])    return @"iPhone 5c (GSM+CDMA)";
    if ([deviceString isEqualToString:@"iPhone6,1"])    return @"iPhone 5s (GSM)";
    if ([deviceString isEqualToString:@"iPhone6,2"])    return @"iPhone 5s (GSM+CDMA)";
    if ([deviceString isEqualToString:@"iPhone7,1"])    return @"iPhone 6 Plus";
    if ([deviceString isEqualToString:@"iPhone7,2"])    return @"iPhone 6";
    if ([deviceString isEqualToString:@"iPhone8,1"])    return @"iPhone 6s";
    if ([deviceString isEqualToString:@"iPhone8,2"])    return @"iPhone 6s Plus";
    if ([deviceString isEqualToString:@"iPhone8,4"])    return @"iPhone SE";
    
    if ([deviceString isEqualToString:@"iPhone9,1"])    return @"CH JP HK iPhone 7";
    if ([deviceString isEqualToString:@"iPhone9,2"])    return @"HK CH iPhone 7 Plus";
    if ([deviceString isEqualToString:@"iPhone9,3"])    return @"US TW iPhone 7";
    if ([deviceString isEqualToString:@"iPhone9,4"])    return @"US TW iPhone 7 Plus";
    if ([deviceString isEqualToString:@"iPhone10,1"])   return @"CH(A1863) JP(A1906)iPhone 8";
    if ([deviceString isEqualToString:@"iPhone10,4"])   return @"US(Global/A1905)iPhone 8";
    if ([deviceString isEqualToString:@"iPhone10,2"])   return @"CH(A1864) JP(A1898)iPhone 8 Plus";
    if ([deviceString isEqualToString:@"iPhone10,5"])   return @"US(Global/A1897)iPhone 8 Plus";
    if ([deviceString isEqualToString:@"iPhone10,3"])   return @"CH(A1865) JP(A1902)iPhone X";
    if ([deviceString isEqualToString:@"iPhone10,6"])   return @"US(Global/A1901)iPhone X";
    if ([deviceString isEqualToString:@"iPhone11,2"])   return @"iPhone XS";
    if ([deviceString isEqualToString:@"iPhone11,4"])   return @"iPhone XS Max";
    if ([deviceString isEqualToString:@"iPhone11,6"])   return @"iPhone XS Max";
    if ([deviceString isEqualToString:@"iPhone11,8"])   return @"iPhone XR";
    
    if ([deviceString isEqualToString:@"iPhone12,1"])   return @"iPhone 11";
    if ([deviceString isEqualToString:@"iPhone12,3"])   return @"iPhone 11 Pro";
    if ([deviceString isEqualToString:@"iPhone12,5"])   return @"iPhone 11 Pro Max";
    if ([deviceString isEqualToString:@"iPhone12,8"])   return @"iPhone SE 2";
    if ([deviceString isEqualToString:@"iPhone13,1"])   return @"iPhone 12 mini";
    if ([deviceString isEqualToString:@"iPhone13,2"])   return @"iPhone 12";
    if ([deviceString isEqualToString:@"iPhone13,3"])   return @"iPhone 12 Pro";
    if ([deviceString isEqualToString:@"iPhone13,4"])   return @"iPhone 12 Pro Max";

    if([deviceString isEqualToString:@"iPhone14,4"])    return @"iPhone 13 mini";
    if([deviceString isEqualToString:@"iPhone14,5"])    return @"iPhone 13";
    if([deviceString isEqualToString:@"iPhone14,2"])    return @"iPhone 13 Pro";
    if([deviceString isEqualToString:@"iPhone14,3"])    return @"iPhone 13 Pro Max";
    if([deviceString isEqualToString:@"iPhone14,6"])    return @"iPhone SE (3rd generation)";
    
    if([deviceString isEqualToString:@"iPhone14,7"])    return @"iPhone 14";
    if([deviceString isEqualToString:@"iPhone14,8"])    return @"iPhone 14 Plus";
    if([deviceString isEqualToString:@"iPhone15,2"])    return @"iPhone 14 Pro";
    if([deviceString isEqualToString:@"iPhone15,3"])    return @"iPhone 14 Pro Max";

    if([deviceString isEqualToString:@"iPhone15,4"])    return @"iPhone 15";
    if([deviceString isEqualToString:@"iPhone15,5"])    return @"iPhone 15 Plus";
    if([deviceString isEqualToString:@"iPhone16,1"])    return @"iPhone 15 Pro";
    if([deviceString isEqualToString:@"iPhone16,2"])    return @"iPhone 15 Pro Max";

    
    
    
    if ([deviceString containsString:@"iPod"]) {
        return @"iPod series";
    } else if ([deviceString containsString:@"iPad"]) {
        return @"iPad series";
    } else {
        return [NSString stringWithFormat:@"other %@",deviceString];
    }
}

+ (NSString *)bundleId {
    NSString *identifier = [[NSBundle mainBundle] bundleIdentifier];
    return identifier;
}

 
+ (NSString *)deviceUUID {
    static NSString *deviceUUIDStr;
        deviceUUIDStr = [SABCTool getDeviceID];
    return deviceUUIDStr;
}

+ (NSString *)deviceGuestID
{
    static NSString *deviceUUIDStr;
        deviceUUIDStr = [SABCTool getGuestID];
    return deviceUUIDStr;
}



+ (BOOL)isEnableWIFI {
    return ([[Reachability reachabilityForLocalWiFi] isReachableViaWiFi]);
}

+ (BOOL)isEnable3G {
    return ([[Reachability reachabilityForInternetConnection] isReachableViaWWAN]);
}

+ (BOOL)isLink {
    if ([self isEnable3G] || [self isEnableWIFI]) {
        return YES;
    }
    return NO;
}

+ (NSString *)languages {
        
    NSArray *languages = [[NSUserDefaults standardUserDefaults] valueForKey:@"AppleLanguages"];
    NSString *currentLanguage = languages.firstObject;

    if ([currentLanguage hasPrefix:@"en"]) {
        currentLanguage = @"en";
    } else if ([currentLanguage hasPrefix:@"th"]) {
        currentLanguage = @"th";
    }else {
        currentLanguage = @"zh";
    }
    
    return @"zh";
}

 
+ (BOOL)isIndonesian {
    return [[self languages] isEqualToString:@"in"] ? YES : NO;
}

+ (NSInteger)getSIMState
{
     CTTelephonyNetworkInfo *networkInfo = [[CTTelephonyNetworkInfo alloc] init];
     if (@available(iOS 12.0, *)) {
          NSDictionary *ctDict = networkInfo.serviceSubscriberCellularProviders;
          if ([ctDict allKeys].count > 1) {
               NSArray *keys = [ctDict allKeys];
               CTCarrier *carrier1 = [ctDict objectForKey:[keys firstObject]];
               CTCarrier *carrier2 = [ctDict objectForKey:[keys lastObject]];
               if (carrier1.mobileCountryCode.length && carrier2.mobileCountryCode.length) {
                    return 2;
               }else if (!carrier1.mobileCountryCode.length && !carrier2.mobileCountryCode.length) {
                    return 0;
               }else {
                    return 1;
               }
          }else if ([ctDict allKeys].count == 1) {
               NSArray *keys = [ctDict allKeys];
               CTCarrier *carrier1 = [ctDict objectForKey:[keys firstObject]];
               if (carrier1.mobileCountryCode.length) {
                    return 1;
               }else {
                    return 0;
               }
          }else {
               return 0;
          }
     }else {
          CTCarrier *carrier = [networkInfo subscriberCellularProvider];
          NSString *carrier_name = carrier.mobileCountryCode;
          if (carrier_name.length) {
               return 1;
          }else {
               return 0;
          }
     }
}
@end
