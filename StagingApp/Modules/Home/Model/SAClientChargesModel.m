
#import "SAClientChargesModel.h"
#import <MJExtension.h>

@implementation SAClientChargesModel

+ (void)load {
    
    [SAClientChargesModel mj_setupObjectClassInArray:^NSDictionary *{
        return @{
                 @"protocolUrls" : @"SAHomeProtocolModel"
                 };
    }];
}

+ (instancetype)objectWithDict:(NSDictionary *)dict {
    return [self mj_objectWithKeyValues:dict];
}

- (CGFloat)repaymentMiddleHeight {
    if (_repaymentMiddleHeight) {
        return _repaymentMiddleHeight;
    }
    
    
    CGFloat maxW = UIScreenWidth - XX_6(80);
    CGSize maxSize = CGSizeMake(maxW, MAXFLOAT);
    
    CGSize tipsSize = [self.tip boundingRectWithSize:maxSize options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:[UIFont fontSizeOfXX_6:14]} context:nil].size;
    
    CGSize codeSize = [self.repaymentVo.repaymentCode boundingRectWithSize:maxSize options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:[UIFont fontSizeOfXX_6:23]} context:nil].size;
    
    NSString *nameStr = [NSString stringWithFormat:@"%@%@", NSLocalizedString(@"repayment_bank_name_title", nil), self.repaymentVo.name];
    CGSize nameSize = [nameStr boundingRectWithSize:maxSize options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:[UIFont fontSizeOfXX_6:14]} context:nil].size;
    
    _repaymentMiddleHeight = XX_6(110) + tipsSize.height + codeSize.height + nameSize.height;
    
    return _repaymentMiddleHeight;
}

- (CGFloat)repaymentFootHeight {
    if (_repaymentFootHeight) {
        return _repaymentFootHeight;
    }
    
    _repaymentFootHeight = XX_6(40)*self.protocolUrls.count + XX_6(80);
    
    return _repaymentFootHeight;
}

@end
