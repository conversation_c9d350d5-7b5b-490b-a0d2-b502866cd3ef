
#import "SAPersonalInfoModel.h"
#import <MJExtension.h>

@implementation SAPersonalInfoModel

+ (void)load {
    
    [SAPersonalInfoModel mj_setupObjectClassInArray:^NSDictionary *{
        return @{
                 @"selectVo" : @"SAPersonalInfoCheckModel"
                 };
    }];
}

+ (NSMutableArray *)arrayTransformToModels:(NSArray *)array{
    if (array.count == 0) {
        return @[];
    }
    NSMutableArray *modelsArr = [NSMutableArray array];
    for (int i=0; i<array.count; i++) {
        SAPersonalInfoModel *mod = [SAPersonalInfoModel mj_objectWithKeyValues:array[i]];
        [modelsArr addObject:mod];
    }
    
    return modelsArr;
}

- (CGFloat)titleW {
    if (_titleW) {
        return _titleW;
    }
    
    
    CGFloat maxWid = XX_6(300);
    CGSize maxSize = CGSizeMake(maxWid, MAXFLOAT);
    CGSize realSize = [self.paramName boundingRectWithSize:maxSize options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:[UIFont fontSizeOfXX_6:16]} context:nil].size;
    return realSize.width + 10;
}

- (void)setInputStr:(NSString *)inputStr {
    _inputStr = inputStr;
    self.inputValue = inputStr;
}

- (CGFloat)cellHeight {
    
    if (_cellHeight) {
        return _cellHeight;
    }
    if (self.paramType == 1) {      
        
        NSString *realStr = self.inputValue.length == 0 ? self.inputStr : self.inputValue;
        if (realStr.length == 0) {
            _cellHeight = XX_6(60);
            return XX_6(60);
        } else {
            
            CGFloat maxW = XX_6(335) - XX_6(30) - self.titleW;
            CGSize maxSize = CGSizeMake(maxW, MAXFLOAT);
            
            CGSize realSize = [realStr boundingRectWithSize:maxSize options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:[UIFont fontSizeOfXX_6:14]} context:nil].size;
            if (realSize.height < 61) {
                _cellHeight = XX_6(60);
                return XX_6(60);
            }
            _cellHeight = realSize.height + XX_6(25);
            return realSize.height + XX_6(25);
        }
        
    } else if (self.paramType == 2) {       
        _cellHeight = XX_6(60);
        return XX_6(60);
    
    } else if (self.paramType == 5) {       
    
        
        CGFloat maxW = XX_6(335) - XX_6(30) - self.titleW;
        
        NSString *realStr = self.inputValue.length == 0 ? self.inputDesc : self.inputValue;
        
        UILabel *lab = [[UILabel alloc] init];
        lab.width = maxW;
        lab.textAlignment = NSTextAlignmentRight;
        lab.text = realStr;
        lab.textColor= [UIColor blackColor];
        lab.numberOfLines = 0;
        [lab sizeToFit];
        
        if (lab.height < 61) {
            _cellHeight = XX_6(60);
            return _cellHeight;
        }
        
        _cellHeight = lab.height + XX_6(5);
        
        return lab.height;
        
    } else {        
        _cellHeight = XX_6(60);
        return XX_6(60);
    }
}

@end
