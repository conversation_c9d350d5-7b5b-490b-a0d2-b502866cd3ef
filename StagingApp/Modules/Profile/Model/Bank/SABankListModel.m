
#import "SABankListModel.h"

@implementation SABankListModel

- (CGFloat)heigt {
    if (_heigt) {
        return _heigt;
    }
    
    
    CGFloat maxWid = UIScreenWidth - XX_6(40);
    CGSize maxSize = CGSizeMake(maxWid, MAXFLOAT);
    CGSize realSize = [self.bankDesc boundingRectWithSize:maxSize options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:[UIFont fontSizeOfXX_6:18]} context:nil].size;
    _heigt = realSize.height;
    if (_heigt < XX_6(40)) {
        _heigt = XX_6(40);
    }
    return _heigt;
}

@end
