//
//  EsignSDKIOS.h
//  EsignSDKIOS
//
//  Created by 叶鹏飞 on 2024/8/1.
//

#import <Foundation/Foundation.h>

//! Project version number for EsignSDKIOS.
FOUNDATION_EXPORT double EsignSDKIOSVersionNumber;

//! Project version string for EsignSDKIOS.
FOUNDATION_EXPORT const unsigned char EsignSDKIOSVersionString[];

// In this header, you should import all the public headers of your framework using statements like #import <EsignSDKIOS/PublicHeader.h>

#import <EsignSDK/EsignFaceManager.h>
