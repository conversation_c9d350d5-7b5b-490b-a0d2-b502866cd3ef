
#import <Foundation/Foundation.h>
#import <MBProgressHUD.h>


NS_ASSUME_NONNULL_BEGIN

@interface SATool : NSObject<MBProgressHUDDelegate>

#pragma mark - VC

 
+ (void)showActivityHUD:(nullable NSString *)text;


 
+ (void)hideHUDView;


 
+ (void)textStateHUD:(nullable NSString *)text;


#pragma mark - Window

+ (void)showWindowHUD:(nullable NSString *)text;
+ (void)hideWindowHUD;

+ (void)textStateWindowHUD:(nullable NSString *)text;
+ (void)textStateWindowHUD:(nullable NSString *)text finishBlock:(void (^)(void))finishBlock;

@end

NS_ASSUME_NONNULL_END
