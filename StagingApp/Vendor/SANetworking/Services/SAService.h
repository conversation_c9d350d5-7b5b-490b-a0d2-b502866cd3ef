
 

#import <Foundation/Foundation.h>
#import "SAResponse.h"

@protocol QLServiceProtocol <NSObject>

@property (nonatomic, readonly) BOOL isOnline;

@property (nonatomic, readonly) NSString *offlineApiBaseUrl;
@property (nonatomic, readonly) NSString *onlineApiBaseUrl;

@property (nonatomic, readonly) NSString *offlineApiVersion;
@property (nonatomic, readonly) NSString *onlineApiVersion;

@property (nonatomic, readonly) NSString *onlinePublicKey;
@property (nonatomic, readonly) NSString *offlinePublicKey;

@property (nonatomic, readonly) NSString *onlinePrivateKey;
@property (nonatomic, readonly) NSString *offlinePrivateKey;

@optional

- (NSDictionary *)extraParmas;

- (NSDictionary *)extraHttpHeadParmasWithMethodName:(NSString *)method requestParams:(NSDictionary *)requestParams;

- (NSString *)urlGeneratingRuleByMethodName:(NSString *)method;

- (BOOL)shouldCallBackByFailedOnCallingAPI:(SAResponse *)response;

- (NSDictionary *)handleResponseData:(NSDictionary *)data;

@end

@interface SAService : NSObject

@property (nonatomic, strong, readonly) NSString *apiBaseUrl;
@property (nonatomic, strong, readonly) NSString *apiVersion;
@property (nonatomic, strong, readonly) NSString *publicKey;
@property (nonatomic, strong, readonly) NSString *privateKey;

@property (nonatomic, weak, readonly) id<QLServiceProtocol> child;

 
- (NSString *)urlGeneratingRuleByMethodName:(NSString *)method;

@end
