
#import "SASettingViewController.h"

#import "SAUserCenterApiManager.h"
#import "SAUserManager.h"
#import "SAUserCenterModel.h"
#import "SALogoutApiManager.h"
#import "SACommonWebViewController.h"

#import "SAGetCustomerUrlApiManager.h"
#import "SAGetProtocolApiManager.h"
#import <SDWebImage/SDImageCache.h>

#define kCenterCellHeight   60
#define kItemBaseTag        198

#define kSettingCellHeight       60

@interface SASettingViewController ()<UITableViewDelegate, UITableViewDataSource, QLAPIManagerCallBackDelegate, QLAPIManagerParamSource, NPRequestMethodProtocol>

@property (nonatomic, strong) SALogoutApiManager *loginOutApiMgr;

@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) SAGradientButton *signoutBtn;
@property (nonatomic, strong) UILabel *versionLab;

@property (nonatomic, strong) SAGetProtocolApiManager *protocolManager;
@property (nonatomic, strong) NSString *protocolKey;

@end

@implementation SASettingViewController

- (void)viewDidAppear:(BOOL)animated
{
    [super viewDidAppear:animated];
}

- (void)viewDidDisappear:(BOOL)animated
{
    [super viewDidDisappear:animated];
}

- (void)viewWillAppear:(BOOL)animated
{
    [super viewWillAppear:animated];
    [UIApplication sharedApplication].statusBarStyle = UIStatusBarStyleLightContent;
}

- (void)viewDidLoad
{
    [super viewDidLoad];
    self.view.backgroundColor = [UIColor colorWithHex:0xf6f6f6];
    self.navigationItem.title = @"设置";
    
    [self addCustomViews];
    [self createFrames];
    

}

- (void)addCustomViews
{
    [self.view addSubview:self.tableView];
    self.tableView.layer.masksToBounds = YES;
    self.tableView.layer.cornerRadius = 10;
    self.tableView.scrollEnabled = NO;
    
    [self.view addSubview:self.signoutBtn];
    [self.view addSubview:self.versionLab];
}

- (void)createFrames
{
    [self.tableView mas_makeConstraints:^(SAConstraintMaker *make) {
        make.top.equalTo(self.view).offset(12);
        make.left.equalTo(self.view).offset(12);
        make.centerX.equalTo(self.view);
        make.height.mas_equalTo(5 * kSettingCellHeight);
    }];
    
    [self.versionLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.centerX.equalTo(self.view);
        make.bottom.equalTo(self.view).offset(-60);
    }];
    
    [self.signoutBtn mas_makeConstraints:^(SAConstraintMaker *make) {
        make.bottom.equalTo(self.versionLab.mas_top).offset(-25);
        make.centerX.equalTo(self.view);
        make.height.mas_equalTo(44);
        make.left.equalTo(self.view).offset(44);
    }];
    _signoutBtn.layer.cornerRadius = 7;
    _signoutBtn.layer.masksToBounds = YES;
}

#pragma mark - 协议
#pragma mark UITableViewDataSource

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return 5;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    static NSString * identify = @"typesCellId";
    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:identify];
    if (cell == nil) {
        cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleValue1 reuseIdentifier:identify];
    }
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    cell.accessoryType = UITableViewCellAccessoryDisclosureIndicator;
    
    if(indexPath.row == 0){
        cell.textLabel.text = @"用户注册协议";
        cell.detailTextLabel.text = @"";
    }
    if(indexPath.row == 1){
        cell.textLabel.text = @"隐私保护政策";
        cell.detailTextLabel.text = @"";
    }
    if(indexPath.row == 2){
        cell.textLabel.text = [NSString stringWithFormat:@"关于%@", [[NSBundle mainBundle] objectForInfoDictionaryKey:@"CFBundleDisplayName"]];
    }
   
    if(indexPath.row == 3){
        cell.textLabel.text = [NSString stringWithFormat:@"App版本号 %@", [[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleShortVersionString"]];
    }
    if(indexPath.row == 4){
        cell.textLabel.text = @"清除缓存";
        cell.detailTextLabel.text = [NSString stringWithFormat:@"%.2fkb", [self getCachesSize]];
    }
    
    if(indexPath.row == 3){
        cell.accessoryType = UITableViewCellAccessoryNone;
    }else{
        cell.accessoryType = UITableViewCellAccessoryDisclosureIndicator;
    }
    
    return cell;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    return kSettingCellHeight;
}

#pragma mark UITableViewDelegate

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    [self didSelectCell:indexPath.row];
}

- (void)didSelectCell:(NSInteger)index
{
    if (index == 0)
    {
        self.protocolKey = PRIVACY_POLICY_ZCXY;
        [self showActivityHUD:nil];
        [self.protocolManager loadData];
    }
    
    if (index == 1) 
    {
        self.protocolKey = PRIVACY_POLICY_YSXY;
        [self showActivityHUD:nil];
        [self.protocolManager loadData];
    }
    
    if (index == 2)
    {
        if([NSString judgeStringExist:self.aboutUrl]){
            SACommonWebViewController *webVC = [[SACommonWebViewController alloc] init];
            webVC.url = self.aboutUrl;
            webVC.navTitle = @"关于我们";
            webVC.hidesBottomBarWhenPushed = YES;
            [[SACommonTool currentViewController].navigationController pushViewController:webVC animated:NO];
        }
    }
    
    if(index == 4)
    {
        [self showActivityHUD:nil];
        [[SDImageCache sharedImageCache] clearDiskOnCompletion:^{
            dispatch_async(dispatch_get_global_queue(0, 0), ^{
                [[SDImageCache sharedImageCache] clearMemory];
                [[NSFileManager defaultManager] removeItemAtPath:[NSSearchPathForDirectoriesInDomains(NSCachesDirectory,NSUserDomainMask, YES)lastObject]error:nil];
                
                dispatch_async(dispatch_get_main_queue(), ^{
                   
                    [self hideHUDView];
                    [self textStateHUD:@"清除成功"];
                    
                    [self.tableView reloadData];
                });

            });
        }];
    }
}

#pragma mark - 获取缓存

- (double)getCachesSize
{
    SDImageCache *imageCache = [SDImageCache sharedImageCache];
    
    NSUInteger fileSize = [imageCache totalDiskSize];
    NSString *myCachePath = [NSSearchPathForDirectoriesInDomains(NSCachesDirectory, NSUserDomainMask, YES) lastObject];
    NSFileManager *fileM = [NSFileManager defaultManager];
    NSDictionary *fileInfo = [fileM attributesOfItemAtPath:myCachePath error:nil];
    fileSize +=fileInfo.fileSize;
    return fileSize/1024.0;
}


#pragma mark - 登出

- (void)loginOut
{
    UIAlertController *alertVc = [UIAlertController alertControllerWithTitle:@""
                                                                     message:@"您确定退出登录吗？"
                                                              preferredStyle:UIAlertControllerStyleAlert];
    UIAlertAction *cancel = [UIAlertAction actionWithTitle:NSLocalizedString(@"slideFoot", nil) style:UIAlertActionStyleCancel handler:nil];
    
    UIAlertAction *sure = [UIAlertAction actionWithTitle:NSLocalizedString(@"ploadFactory", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        [self showActivityHUD:nil];
        [self.loginOutApiMgr loadData];
    }];
    [alertVc addAction:cancel];
    [alertVc addAction:sure];
    
    [self presentViewController:alertVc animated:YES completion:nil];
}

#pragma mark QLAPIManagerCallBackDelegate
- (void)managerCallAPIDidSuccess:(SABaseManager *)manager 
{
    [self hideHUDView];
    
    if (manager == self.loginOutApiMgr) 
    {
        [SAUserManager loginOutClearInfo];
        [self.navigationController popToRootViewControllerAnimated:NO];
        [[SATabBarViewController sharedInstance] setTabIndex:0];
    }
    
    if(manager == self.protocolManager)
    {
        NSDictionary *dataDict = [manager fetchDataWithReformer:self.protocolManager];
        SAProtocolViewController *vc = [SAProtocolViewController new];
        vc.htmlStr = dataDict[@"text"];
        vc.navTitle = dataDict[@"title"];
        vc.companyName = dataDict[@"companyName"];
        vc.appName = dataDict[@"appName"];
        [self presentViewController:[[UINavigationController alloc] initWithRootViewController:vc] animated:YES completion:nil];
    }
}

- (void)managerCallAPIDidFailed:(SABaseManager *)manager
{
    [self hideHUDView];
    NSString *failMsg  = [SAResponseHandle netWorkErrorMsg:[manager fetchDataWithReformer:nil] andErrorType:manager.errorType];
    [self textStateHUD:failMsg];
}

#pragma mark QLAPIManagerParamSource

- (NSDictionary *)paramsForApi:(SABaseManager *)manager {
    return [NSDictionary dictionary];
}

- (NSString *)methodForApi:(SABaseManager *)manager
{
    if (manager == self.protocolManager) {
        return [NSString stringWithFormat:@"1a99fb9ad8534512af0ede2979dfd68f/%@", self.protocolKey];
    }
    return @"";
}

#pragma mark - getters

- (UITableView *)tableView {
    if (_tableView == nil) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
        _tableView.backgroundColor = [UIColor whiteColor];
        _tableView.dataSource      = self;
        _tableView.delegate        = self;
        _tableView.tableFooterView = [[UIView alloc] init];
        if (@available(iOS 11.0, *)) {
            _tableView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
        }
        _tableView.separatorStyle = UITableViewCellSeparatorStyleSingleLine;
        _tableView.separatorColor = [UIColor colorWithHex:0xdddddd];
        _tableView.separatorInset = UIEdgeInsetsMake(0, XX_6(25), 0, 0);
        _tableView.estimatedRowHeight = 0;
        _tableView.estimatedSectionFooterHeight = 0;
        _tableView.estimatedSectionHeaderHeight = 0;
    }
    return _tableView;
}

- (SALogoutApiManager *)loginOutApiMgr {
    if (_loginOutApiMgr == nil) {
        _loginOutApiMgr = [[SALogoutApiManager alloc] init];
        _loginOutApiMgr.delegate = self;
        _loginOutApiMgr.paramSource = self;
    }
    return _loginOutApiMgr;
}

- (SAGradientButton *)signoutBtn
{
    if (_signoutBtn == nil) {
        _signoutBtn = [SAGradientButton buttonWithType:UIButtonTypeCustom];
        [_signoutBtn gradientBackgroundColors:@[kButtonStartColor, kButtonEndColor] locations:nil startPoint:CGPointMake(0, 0.5) endPoint:CGPointMake(1, 0.5)];
        [_signoutBtn setTitle:@"安全退出" forState:UIControlStateNormal];
        _signoutBtn.backgroundColor = [UIColor whiteColor];
        [_signoutBtn setTitleColor:[UIColor blackColor] forState:UIControlStateNormal];
        _signoutBtn.titleLabel.font = Font_XX6(15);
        _signoutBtn.layer.masksToBounds = YES;
        [_signoutBtn addTarget:self action:@selector(loginOut) forControlEvents:UIControlEventTouchUpInside];
    }
    return _signoutBtn;
}

- (SAGetProtocolApiManager *)protocolManager {
    if (_protocolManager == nil) {
        _protocolManager = [[SAGetProtocolApiManager alloc] init];
        _protocolManager.delegate = self;
        _protocolManager.paramSource = self;
        _protocolManager.methodSource = self;
    }
    return _protocolManager;
}

- (UILabel *)versionLab{
    FF_Fetch_UILable_CenterX(_versionLab, [UIColor colorWithHex:0x555555], Font_XX6(13))
}

@end
