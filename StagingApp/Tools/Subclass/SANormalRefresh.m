
#import "SANormalRefresh.h"
#import "SAColor+Extension.h"

@implementation SAJGRefreshNormalHeader

 
- (instancetype)initWithFrame:(CGRect)frame{
    if(self = [super initWithFrame:frame]){
        
        self.automaticallyChangeAlpha = YES;
        
        self.lastUpdatedTimeLabel.hidden = YES;
        
        
        self.stateLabel.textColor = [UIColor colorWithHex:0x666666];
    }
    return self;
}

@end

@implementation SARefreshNormalFooter

 
- (instancetype)initWithFrame:(CGRect)frame{
    if(self = [super initWithFrame:frame]){
        
        self.stateLabel.font = [UIFont systemFontOfSize:14];
        
        self.stateLabel.textColor = [UIColor colorWithHex:0xc9c9c9];
        
        [self setTitle:nil forState:MJRefreshStateIdle];
        [self setTitle:nil forState:MJRefreshStateRefreshing];
        [self setTitle:nil forState:MJRefreshStatePulling];
        [self setTitle:@"到底啦~" forState:MJRefreshStateNoMoreData];
        
    }
    return self;
}

@end

