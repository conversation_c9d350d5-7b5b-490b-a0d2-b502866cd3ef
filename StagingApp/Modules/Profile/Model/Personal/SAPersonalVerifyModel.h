
#import <Foundation/Foundation.h>

#import "SAPersonalInfoModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface SAPersonalVerifyModel : NSObject

@property (nonatomic, copy)NSString *name;
@property (nonatomic, assign)BOOL enabled;
@property (nonatomic, strong)NSMutableArray <SAPersonalInfoModel *>*inputParams;

@property (nonatomic, strong)NSArray <NSNumber *>*showIndexArray;        

@property (nonatomic, assign)CGFloat personalBaseInfoTableH;        

@property (nonatomic, assign)CGFloat personalOtherInfoTableH;        

+ (instancetype)objectWithDict:(NSDictionary *)dict;
+ (id)arrayTransfromToModels:(NSArray *)array;

@end

NS_ASSUME_NONNULL_END
