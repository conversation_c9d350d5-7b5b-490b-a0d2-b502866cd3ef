
#import "SAGetOcrConfigApiManager.h"

@implementation SAGetOcrConfigApiManager

#pragma mark - life cycle
- (instancetype)init
{
    if (self = [super init])
    {
        self.validator = self;
    }
    
    return self;
}

#pragma mark - CTAPIManager
- (NSString *)methodName
{
    return @"8871db1fe492492f9a465b9da8e2621d";
}

- (NSString *)serviceType
{
    return kNPBasicServiceKey;
}

- (QLAPIManagerRequestType)requestType
{
    return QLAPIManagerRequestTypeGet;
}

- (BOOL)shouldCache
{
    return NO;
}

#pragma mark - YJBAPIManagerDataReformer

- (id)manager:(SABaseManager *)manager reformData:(NSDictionary *)data
{
    if ([manager isKindOfClass:[self class]])
    {
        NSDictionary *dataDic  = [data objectForKey:@"data"];
        return dataDic;
    }
    return nil;
}

#pragma mark - YJBAPIManagerValidator
- (BOOL)manager:(SABaseManager *)manager isCorrectWithParamsData:(NSDictionary *)data
{
    return YES;
}

- (BOOL)manager:(SABaseManager *)manager isCorrectWithCallBackData:(id)data
{
    return [SAResponseHandle processRespDictCodeZero:data];
}
@end
