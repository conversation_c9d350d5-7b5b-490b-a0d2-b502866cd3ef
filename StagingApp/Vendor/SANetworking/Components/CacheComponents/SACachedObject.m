
#import "SACachedObject.h"
#import "SANetworkingConfigurationManager.h"

@interface SACachedObject ()

@property (nonatomic, copy, readwrite) NSData *content;
@property (nonatomic, copy, readwrite) NSDate *lastUpdateTime;

@end

@implementation SACachedObject

#pragma mark - Life cycle

- (instancetype)initWithContent:(NSData *)content
{
    if (self = [super init])
    {
        self.content = content;
    }
    return self;
}

#pragma mark - Public method

- (void)updateContent:(NSData *)content
{
    self.content = content;
}

#pragma mark - getters

- (BOOL)isEmpty
{
    return self.content == nil;
}

- (BOOL)isOutdated
{
    NSTimeInterval timeInterval = [[NSDate date] timeIntervalSinceDate:self.lastUpdateTime];
    return timeInterval > [SANetworkingConfigurationManager sharedInstance].cacheOutdateTimeSeconds ;
}

- (void)setContent:(NSData *)content
{
    _content = [content copy];
    self.lastUpdateTime = [NSDate dateWithTimeIntervalSinceNow:0];
}

@end
