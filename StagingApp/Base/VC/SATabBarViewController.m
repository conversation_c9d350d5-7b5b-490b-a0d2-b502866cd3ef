
#import "SATabBarViewController.h"

#import "SANavigationViewController.h"
#import "SAHomePageViewController.h"
#import "SAMineViewController.h"
#import "SALoginViewController.h"
#import "SAMainHomeApiManager.h"
#import "SAHomeModel.h"
#import "SARepaymentViewController.h"
#import "SARepayListVC.h"

@interface SATabBarViewController ()<UITabBarControllerDelegate>

@property (nonatomic, strong) NSMutableArray                *navControllersArray;
@property (nonatomic, strong) SAHomePageViewController *mainVc;
@property (nonatomic, strong) SAMineViewController *meVc;

@end

@implementation SATabBarViewController

#pragma mark - Orientation

- (BOOL)shouldAutorotate
{
    return [self.selectedViewController shouldAutorotate];
}

- (UIInterfaceOrientationMask)supportedInterfaceOrientations
{
    return [self.selectedViewController supportedInterfaceOrientations];
}

- (UIInterfaceOrientation)preferredInterfaceOrientationForPresentation
{
    return [self.selectedViewController preferredInterfaceOrientationForPresentation];
}

#pragma mark - Life cycle

+ (void)load
{    
    NSMutableDictionary *norDict = [NSMutableDictionary dictionary];
    norDict[NSFontAttributeName] = [UIFont systemFontOfSize:10];
    norDict[NSForegroundColorAttributeName] = [UIColor colorWithHex:0x8a8a8a];
    [[UITabBarItem appearance] setTitleTextAttributes:norDict forState:UIControlStateNormal];
    
    
    NSMutableDictionary *selDict = [NSMutableDictionary dictionary];
    selDict[NSFontAttributeName] = [UIFont systemFontOfSize:10];
    selDict[NSForegroundColorAttributeName] = [UIColor blackColor];
    [[UITabBarItem appearance] setTitleTextAttributes:selDict forState:UIControlStateSelected];
    
    [[UITabBar appearance] setUnselectedItemTintColor:[UIColor colorWithHex:0x8a8a8a]];
    [[UITabBar appearance] setTintColor:[UIColor blackColor]];
    
}

+ (instancetype)sharedInstance {
    static dispatch_once_t once;
    static id instance;
    dispatch_once(&once, ^{
        instance = [[self alloc] init];
    });
    return instance;
}

- (void)viewDidLoad 
{
    [super viewDidLoad];
    
    self.delegate = self;
    [self setupTabbarItems];
}

- (void)viewWillAppear:(BOOL)animated
{
    [super viewWillAppear:animated];
}

- (void)restTabBar
{
    [[SACommonTool currentViewController].navigationController dismissViewControllerAnimated:YES completion:nil];
    for (UINavigationController *navCtr in self.navControllersArray)
    {
        [navCtr popToRootViewControllerAnimated:YES];
    }
    
    [self setTabIndex:0];
}

- (void)setTabIndex:(NSInteger)index{
    [self setSelectedIndex:index];
}

- (BOOL)tabBarController:(UITabBarController *)tabBarController shouldSelectViewController:(UIViewController *)viewController{
    
    NSLog(@"---当前status---%ld", [SAUserManager getBorrowStatus]);
    if ([viewController.tabBarItem.title isEqualToString:@"我的"] && ![SAUserManager isLogin]) {
        UINavigationController *navVC = [[UINavigationController alloc] initWithRootViewController:[[SALoginViewController alloc] init]];
        navVC.modalPresentationStyle = UIModalPresentationFullScreen;
        [[SACommonTool currentViewController] presentViewController:navVC animated:YES completion:nil];

        return NO;
    }
    
    if ([viewController.tabBarItem.title isEqualToString:@"待还款"] && ![SAUserManager isLogin]) {
        UINavigationController *navVC = [[UINavigationController alloc] initWithRootViewController:[[SALoginViewController alloc] init]];
        navVC.modalPresentationStyle = UIModalPresentationFullScreen;
        [[SACommonTool currentViewController] presentViewController:navVC animated:YES completion:nil];

        return NO;
    }

    return YES;
}

#pragma mark - Private

- (void)setupTabbarItems {
    
    [self.navControllersArray removeAllObjects];
    
    SAHomePageViewController *mainVc = [[SAHomePageViewController alloc] init];
    mainVc.tabBarItem.title = @"首页";
    mainVc.tabBarItem.image = [[UIImage imageNamed:@"tabBar_home_nor"] imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal];
    mainVc.tabBarItem.selectedImage = [[UIImage customImageNamed:@"tabBar_home_sel"] imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal];

    
    if (UIScreenHeight < 811) {
        mainVc.tabBarItem.titlePositionAdjustment = UIOffsetMake(0, -3);
    }
    SANavigationViewController *mainNav = [[SANavigationViewController alloc] initWithRootViewController:mainVc];
    [self.navControllersArray addObject:mainNav];
    
    SARepayListVC *repayVC = [[SARepayListVC alloc] init];
    repayVC.tabBarItem.title = @"待还款";
    repayVC.tabBarItem.image = [[UIImage imageNamed:@"tabBar_repay_nor"] imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal];
    repayVC.tabBarItem.selectedImage = [[UIImage customImageNamed:@"tabBar_repay_sel"] imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal];
    
    if (UIScreenHeight < 811) {
        repayVC.tabBarItem.titlePositionAdjustment = UIOffsetMake(0, -3);
    }
    SANavigationViewController *repayNav = [[SANavigationViewController alloc] initWithRootViewController:repayVC];
    [self.navControllersArray addObject:repayNav];

    
    SAMineViewController *meVc = [[SAMineViewController alloc] init];
    meVc.tabBarItem.title = @"我的";
    meVc.tabBarItem.image = [[UIImage imageNamed:@"tabBar_me_nor"] imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal];
    meVc.tabBarItem.selectedImage = [[UIImage customImageNamed:@"tabBar_me_sel"] imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal];

    
    if (UIScreenHeight < 811) {
        meVc.tabBarItem.titlePositionAdjustment = UIOffsetMake(0, -3);
    }
    SANavigationViewController *meNav = [[SANavigationViewController alloc] initWithRootViewController:meVc];
    [self.navControllersArray addObject:meNav];
    
    [self setViewControllers:self.navControllersArray animated:YES];
}

#pragma mark - getters

- (NSMutableArray *)navControllersArray {
    if (_navControllersArray == nil) {
        _navControllersArray = [NSMutableArray array];
    }
    return _navControllersArray;
}

@end
