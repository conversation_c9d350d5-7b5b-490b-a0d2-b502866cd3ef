
#import "SAGradientView.h"

@implementation SAGradientView

 
+ (Class)layerClass {
    return [CAGradientLayer class];
}

- (void)setColors:(NSArray *)colors {
    _colors = colors;
    NSMutableArray *mutArr = [NSMutableArray array];
    for (UIColor *clr in colors) {
        [mutArr addObject:(__bridge id)clr.CGColor];
    }
    if ([self.layer isKindOfClass:[CAGradientLayer class]]) {
        [((CAGradientLayer *)self.layer) setColors:mutArr];
    }
}

- (void)setLocations:(NSArray<NSNumber *> *)locations {
    _locations = locations;
    if ([self.layer isKindOfClass:[CAGradientLayer class]]) {
        [((CAGradientLayer *)self.layer) setLocations:locations];
    }
}

- (void)setStartPoint:(CGPoint)startPoint {
    _startPoint = startPoint;
    ((CAGradientLayer *)self.layer).startPoint = startPoint;
}

- (void)setEndPoint:(CGPoint)endPoint {
    _endPoint = endPoint;
    ((CAGradientLayer *)self.layer).endPoint = endPoint;
}

- (void)gradientBackgroundColors:(NSArray<UIColor *> *_Nullable)colors locations:(NSArray<NSNumber *> *_Nullable)locations startPoint:(CGPoint)startPoint endPoint:(CGPoint)endPoint {
    self.colors = colors;
    self.locations = locations;
    self.startPoint = startPoint;
    self.endPoint = endPoint;
}

- (void)clearGradient {
    self.colors = nil;
    self.locations = nil;
    self.startPoint = CGPointMake(0.5, 0);
    self.endPoint = CGPointMake(0.5, 1);
}
@end

@implementation SAGradientLabel

+ (Class)layerClass {
    return [CAGradientLayer class];
}

- (void)setColors:(NSArray *)colors {
    _colors = colors;
    NSMutableArray *mutArr = [NSMutableArray array];
    for (UIColor *clr in colors) {
        [mutArr addObject:(__bridge id)clr.CGColor];
    }
    if ([self.layer isKindOfClass:[CAGradientLayer class]]) {
        [((CAGradientLayer *)self.layer) setColors:mutArr];
    }
}

- (void)setLocations:(NSArray<NSNumber *> *)locations {
    _locations = locations;
    if ([self.layer isKindOfClass:[CAGradientLayer class]]) {
        [((CAGradientLayer *)self.layer) setLocations:locations];
    }
}

- (void)setStartPoint:(CGPoint)startPoint {
    _startPoint = startPoint;
    ((CAGradientLayer *)self.layer).startPoint = startPoint;
}

- (void)setEndPoint:(CGPoint)endPoint {
    _endPoint = endPoint;
    ((CAGradientLayer *)self.layer).endPoint = endPoint;
}

- (void)gradientBackgroundColors:(NSArray<UIColor *> *_Nullable)colors locations:(NSArray<NSNumber *> *_Nullable)locations startPoint:(CGPoint)startPoint endPoint:(CGPoint)endPoint {
    self.colors = colors;
    self.locations = locations;
    self.startPoint = startPoint;
    self.endPoint = endPoint;
}

- (void)clearGradient {
    self.colors = nil;
    self.locations = nil;
    self.startPoint = CGPointMake(0.5, 0);
    self.endPoint = CGPointMake(0.5, 1);
}

@end

@implementation SAGradientButton

+ (Class)layerClass {
    return [CAGradientLayer class];
}

- (void)setColors:(NSArray *)colors {
    _colors = colors;
    NSMutableArray *mutArr = [NSMutableArray array];
    for (UIColor *clr in colors) {
        [mutArr addObject:(__bridge id)clr.CGColor];
    }
    if ([self.layer isKindOfClass:[CAGradientLayer class]]) {
        [((CAGradientLayer *)self.layer) setColors:mutArr];
    }
}

- (void)setLocations:(NSArray<NSNumber *> *)locations {
    _locations = locations;
    if ([self.layer isKindOfClass:[CAGradientLayer class]]) {
        [((CAGradientLayer *)self.layer) setLocations:locations];
    }
}

- (void)setStartPoint:(CGPoint)startPoint {
    _startPoint = startPoint;
    ((CAGradientLayer *)self.layer).startPoint = startPoint;
}

- (void)setEndPoint:(CGPoint)endPoint {
    _endPoint = endPoint;
    ((CAGradientLayer *)self.layer).endPoint = endPoint;
}

- (void)gradientBackgroundColors:(NSArray<UIColor *> *_Nullable)colors locations:(NSArray<NSNumber *> *_Nullable)locations startPoint:(CGPoint)startPoint endPoint:(CGPoint)endPoint {
    self.colors = colors;
    self.locations = locations;
    self.startPoint = startPoint;
    self.endPoint = endPoint;
}

- (void)clearGradient {
    self.colors = nil;
    self.locations = nil;
    self.startPoint = CGPointMake(0.5, 0);
    self.endPoint = CGPointMake(0.5, 1);
}

@end
