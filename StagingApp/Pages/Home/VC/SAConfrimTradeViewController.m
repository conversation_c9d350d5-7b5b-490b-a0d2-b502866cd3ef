
#import "SAConfrimTradeViewController.h"
#import "SAGetTradeDetailApi.h"
#import "SATradeDetailCell.h"
#import "BRPickerView.h"
#import "SASubmitTradeApi.h"
#import "SAGradientView.h"
#import "SAConfirmSmsAlertView.h"
#import "SAGetEsignUrlApi.h"
#import "SACommonWebViewController.h"
#import "NPTradeAlertViewController.h"


@interface SAConfrimTradeViewController ()<QLAPIManagerCallBackDelegate, QLAPIManagerParamSource, UITableViewDelegate, UITableViewDataSource, UIGestureRecognizerDelegate>

@property (nonatomic, strong) UITableView *tableV;

@property (nonatomic, strong) UIView *footerView;
@property (nonatomic, strong) UIView *stepBoxV;
@property (nonatomic, strong) SAGradientButton *nextBtn;


@property (nonatomic, strong) SAGetTradeDetailApi *detailApi;
@property (nonatomic, strong) SASubmitTradeApi *submitApi;
@property (nonatomic, strong) SAGetEsignUrlApi *getUrlApi;


@property (nonatomic, strong) NSArray *topTradeFieldDetails;
@property (nonatomic, strong) NSArray *middleTradeFieldDetails;
@property (nonatomic, strong) NSArray *bottomTradeFieldDetails;
@property (nonatomic, strong) NSArray *borrowPurposeList;

@property (nonatomic, strong) NSArray *orderFlowVOList;


@property (nonatomic, strong) NSDictionary *pledgeDetailInfo;

@property (nonatomic, strong) NSString *purposeType;


@end

@implementation SAConfrimTradeViewController

#pragma mark - Life cycle

- (void)viewDidLoad
{
    [super viewDidLoad];
    self.navigationItem.title = @"借款信息确认";
    self.view.backgroundColor= [UIColor colorWithHex:0xf6f6f6];
    self.purposeType = @"";
    
    self.navigationController.interactivePopGestureRecognizer.delegate = self;

    [self createUI];
}

- (void)viewWillAppear:(BOOL)animated
{
    [super viewWillAppear:animated];
    
    [self showActivityHUD:nil];
    [self.detailApi loadData];
}

- (void)createUI
{
    [self.view addSubview:self.tableV];
    [self.view addSubview:self.footerView];
    
    [self.tableV mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.top.right.equalTo(self.view);
        make.bottom.equalTo(self.footerView.mas_top).offset(-2);
    }];
    [self.footerView mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.right.bottom.equalTo(self.view);
        make.height.mas_equalTo(210);
    }];
    [self.nextBtn mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.equalTo(self.footerView).offset(30);
        make.centerX.equalTo(self.footerView);
        make.bottom.equalTo(self.footerView).offset(-30);
        make.height.mas_equalTo(44);
    }];
    self.nextBtn.layer.masksToBounds = YES;
    self.nextBtn.layer.cornerRadius = 7;
    [self.footerView bringSubviewToFront:self.nextBtn];
}

#pragma mark - UIGestureRecognizerDelegate

- (BOOL)gestureRecognizerShouldBegin:(UIGestureRecognizer *)gestureRecognizer
{
    if(gestureRecognizer == self.navigationController.interactivePopGestureRecognizer){
        
        [NPTradeAlertViewController showFaceAlertSureAction:^{
            [self popSelf];
        }];
        return NO;
    }
    return YES;
}

#pragma mark - 点击左上角返回

- (void)backAction:(id)sender
{
    [NPTradeAlertViewController showFaceAlertSureAction:^{
        [self popSelf];
    }];
}

#pragma mark - Action

- (void)nextAction
{
    [self showActivityHUD:nil];
    [self.submitApi loadData];
}

- (void)signAction
{
    [self showActivityHUD:nil];
    [self.getUrlApi loadData];
}

#pragma mark - UITableViewDataSource

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return 3;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    if(section == 0){
        return self.topTradeFieldDetails.count;
    }else if (section == 1){
        return self.middleTradeFieldDetails.count;
    }else if (section == 2){
        return self.bottomTradeFieldDetails.count;
    }
    return 0;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    static NSString *ConfirmTradeID = @"ConfirmTrade-Cell";
    SATradeDetailCell *cell = [tableView dequeueReusableCellWithIdentifier:ConfirmTradeID];
    if (cell == nil) {
        cell = [[SATradeDetailCell alloc] initWithStyle:UITableViewCellStyleValue1 reuseIdentifier:ConfirmTradeID];
    }
    cell.backgroundColor = [UIColor whiteColor];
    
    NSDictionary *dict = nil;
    if(indexPath.section == 0){
        dict = self.topTradeFieldDetails[indexPath.row];
        cell.showLine = indexPath.row == (self.topTradeFieldDetails.count - 1);

    }else if (indexPath.section == 1){
        dict = self.middleTradeFieldDetails[indexPath.row];
        cell.showLine = indexPath.row == (self.middleTradeFieldDetails.count - 1);

    }else if (indexPath.section == 2){
        dict = self.bottomTradeFieldDetails[indexPath.row];
        cell.showLine = indexPath.row == (self.bottomTradeFieldDetails.count - 1);

    }
    [cell renderData:dict];
    
    return  cell;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath
{
    return 45;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    
    __block NSMutableDictionary *dict = nil;
    if(indexPath.section == 0){
        dict = self.topTradeFieldDetails[indexPath.row];
    }else if (indexPath.section == 1){
        dict = self.middleTradeFieldDetails[indexPath.row];
    }else if (indexPath.section == 2){
        dict = self.bottomTradeFieldDetails[indexPath.row];
    }
    
    if ([dict[@"id"] isEqualToString:@"borrowPurpose"]) {
        SATextPickerView *textPickerView = [[SATextPickerView alloc]initWithPickerMode:BRTextPickerComponentSingle];
        NSDictionary *mapper = @{ @"code": @"type", @"text": @"desc" };
        
        NSArray<SATextModel *> *modelArr = [NSArray br_modelArrayWithJson:self.borrowPurposeList mapper:mapper];
        textPickerView.dataSourceArr = modelArr;
        textPickerView.singleResultBlock = ^(SATextModel *model, NSInteger index) {
            self.purposeType = model.code;
            dict[@"value"] = model.text;
            [self.tableV reloadData];
        };
        [textPickerView show];
    }
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section
{
    return CGFLOAT_MIN;
}
//
//- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section
//{
//    return [UIView new];
//}
//
- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section
{
    return 3;
}
//
- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section
{
    UIView *view = [UIView new];
    view.backgroundColor = [UIColor colorWithHex:0xf6f6f6];
    return view;
}

#pragma mark - QLAPIManagerCallBackDelegate

- (void)managerCallAPIDidSuccess:(SABaseManager *)manager
{
    [self hideHUDView];
    if (manager == self.detailApi)
    {
        NSDictionary *dataDict = [manager fetchDataWithReformer:self.detailApi];
        
        self.topTradeFieldDetails = dataDict[@"topTradeFieldDetails"];
        self.middleTradeFieldDetails = dataDict[@"middleTradeFieldDetails"];
        self.bottomTradeFieldDetails = dataDict[@"bottomTradeFieldDetails"];
        self.pledgeDetailInfo = dataDict[@"pledgeDetailVO"];
        self.borrowPurposeList = dataDict[@"borrowPurposeList"];
        
        self.orderFlowVOList = dataDict[@"orderFlowVOList"];
        
        [self.tableV reloadData];
        if(self.orderFlowVOList.count > 0){
            [self.footerView addSubview:self.stepBoxV];
            [self.stepBoxV mas_makeConstraints:^(SAConstraintMaker *make) {
                make.left.right.top.equalTo(self.footerView);
                make.height.mas_equalTo(150);
            }];
        }
    }
    
    if(manager == self.submitApi)
    {
        NSDictionary *dataDict = [manager fetchDataWithReformer:self.submitApi];
        NSString *jumpUrl = dataDict[@"jumpUrl"];
        
        if([jumpUrl isEqualToString:@"APP/Product/ConfirmTradeSms"]){
            [SAConfirmSmsAlertView showSMSAlert:self.tradeNo submitAction:^{
                [self signAction];
            }];
        }
        if([jumpUrl isEqualToString:@"APP/Product/SignContract"]){
            [self signAction];
        }
    }
    
    if (manager == self.getUrlApi)
    {
        NSDictionary *dataDict = [manager fetchDataWithReformer:self.getUrlApi];
        NSLog(@"url---%@", dataDict);
        
        NSString *url = dataDict[@"shortUrl"];
        SACommonWebViewController *webVC = [[SACommonWebViewController alloc] init];
        webVC.url = url;
        webVC.fromCarrier = YES;
        webVC.navTitle = @"借款合同";
        webVC.hidesBottomBarWhenPushed = YES;
        [[SACommonTool currentViewController].navigationController pushViewController:webVC animated:NO];
    }
}

- (void)managerCallAPIDidFailed:(SABaseManager *)manager
{
    [self hideHUDView];
    
    NSString *failMsg  = [SAResponseHandle netWorkErrorMsg:[manager fetchDataWithReformer:nil] andErrorType:manager.errorType];
    [self textStateHUD:failMsg];
}

#pragma mark - QLAPIManagerParamSource

- (NSDictionary *)paramsForApi:(SABaseManager *)manager
{
    if (manager == self.detailApi)
    {
        return @{@"tradeNo": self.tradeNo};
    }
    if(manager == self.submitApi)
    {
        return @{@"tradeNo": self.tradeNo, @"borrowPurpose": self.purposeType };
    }
    if (manager == self.getUrlApi)
    {
        return @{@"tradeNo": self.tradeNo};
    }
    return [NSDictionary dictionary];
}

#pragma mark - getter

- (UITableView *)tableV {
    if (_tableV == nil) {
        _tableV = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
        _tableV.dataSource      = self;
        _tableV.delegate        = self;
        if (@available(iOS 11.0, *)) {
            _tableV.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
        }
        _tableV.backgroundColor = [UIColor clearColor];
        _tableV.separatorStyle = UITableViewCellSeparatorStyleNone;
    }
    return _tableV;
}

- (SAGetTradeDetailApi *)detailApi{
    if(_detailApi == nil){
        _detailApi = [SAGetTradeDetailApi new];
        _detailApi.delegate = self;
        _detailApi.paramSource = self;
    }
    return _detailApi;
}

- (SASubmitTradeApi *)submitApi{
    if(_submitApi == nil){
        _submitApi = [SASubmitTradeApi new];
        _submitApi.delegate = self;
        _submitApi.paramSource = self;
    }
    return _submitApi;
}

- (SAGradientButton *)nextBtn{
    if (_nextBtn == nil) {
        _nextBtn = [SAGradientButton buttonWithType:UIButtonTypeCustom];
        [_nextBtn gradientBackgroundColors:@[kButtonStartColor, kButtonEndColor] locations:nil startPoint:CGPointMake(0, 0.5) endPoint:CGPointMake(1, 0.5)];
        [_nextBtn addTarget:self action:@selector(nextAction) forControlEvents:UIControlEventTouchUpInside];
        [_nextBtn setTitle:@"已完成，下一步" forState:UIControlStateNormal];
        [_nextBtn setTitleColor:kHomeTextColor forState:UIControlStateNormal];
    }
    
    return _nextBtn;
}


- (SAGetEsignUrlApi *)getUrlApi{
    if(_getUrlApi == nil){
        _getUrlApi = [SAGetEsignUrlApi new];
        _getUrlApi.delegate = self;
        _getUrlApi.paramSource = self;
    }
    return _getUrlApi;
}

- (UIView *)footerView{
    if (_footerView == nil) {
        _footerView = [UIView new];
        
        [_footerView addSubview:self.nextBtn];
    }
    
    return _footerView;
}

- (UIView *)stepBoxV{
    if (_stepBoxV == nil) {
        _stepBoxV = [UIView new];
        
        UIView *view = [UIView new];
        
//        UILabel *titleLab = [UILabel new];
//        titleLab.textColor = [UIColor blackColor];
//        titleLab.font = BoldFont_XX6(14);
//        [view addSubview:titleLab];
        
        UILabel *tipsLab = [UILabel new];
        tipsLab.textColor = [UIColor colorWithHex:0x666666];
        tipsLab.font = Font_XX6(11);
        [view addSubview:tipsLab];
        
//        [titleLab mas_makeConstraints:^(SAConstraintMaker *make) {
//            make.left.equalTo(view).offset(12);
//            make.top.equalTo(view).offset(3);
//        }];
        [tipsLab mas_makeConstraints:^(SAConstraintMaker *make) {
            make.left.equalTo(view).offset(12);
            make.top.equalTo(view).offset(3);
        }];
        
//        titleLab.text = @"确认借款并签署借款合同";
        tipsLab.text = @"完成以下步骤立即放款";
        
        CGFloat singleH = 48;
        UIView *contentBox = [UIView new];
        contentBox.backgroundColor = [UIColor whiteColor];
        [view addSubview:contentBox];
        [contentBox mas_makeConstraints:^(SAConstraintMaker *make) {
            make.left.equalTo(tipsLab);
            make.centerX.equalTo(view);
            make.top.equalTo(tipsLab.mas_bottom).offset(10);
            make.height.mas_equalTo(singleH *self.orderFlowVOList.count);
        }];
        contentBox.layer.masksToBounds = YES;
        contentBox.layer.cornerRadius = 6;
        
        [self.orderFlowVOList enumerateObjectsUsingBlock:^(NSDictionary *item, NSUInteger idx, BOOL * _Nonnull stop) {
            UIView *itemBox = [UIView new];
            [contentBox addSubview:itemBox];
            
            [itemBox mas_makeConstraints:^(SAConstraintMaker *make) {
                make.left.right.equalTo(contentBox);
                make.height.mas_equalTo(singleH);
                make.top.equalTo(contentBox).offset(singleH*idx);
            }];
            
            UIImageView *icon = [UIImageView new];
            [icon sd_setImageWithURL:[NSURL URLWithString:item[@"flowLogo"]]];
            [itemBox addSubview:icon];
            
            UILabel *lab = [UILabel new];
            lab.textColor = [UIColor colorWithHex:0x333333];
            lab.font = Font_XX6(13);
            lab.text = item[@"flowName"];
            [itemBox addSubview:lab];
            
            UIButton *btn = [UIButton buttonWithType:UIButtonTypeCustom];
            [btn setTitle:item[@"buttonName"] forState:UIControlStateNormal];
            [btn addTarget:self action:@selector(nextAction) forControlEvents:UIControlEventTouchUpInside];
            btn.titleLabel.font = Font_XX6(13);
            [itemBox addSubview:btn];
            
            [icon mas_makeConstraints:^(SAConstraintMaker *make) {
                make.width.height.mas_equalTo(22);
                make.centerY.equalTo(itemBox);
                make.left.equalTo(itemBox).offset(12);
            }];
            
            [lab mas_makeConstraints:^(SAConstraintMaker *make) {
                make.centerY.equalTo(itemBox);
                make.left.equalTo(icon.mas_right).offset(6);
            }];
            [btn mas_makeConstraints:^(SAConstraintMaker *make) {
                make.centerY.equalTo(lab);
                make.right.equalTo(itemBox).offset(-12);
                make.height.mas_equalTo(28);
                make.width.mas_equalTo(90);
            }];
            btn.layer.cornerRadius = 14;
            btn.layer.masksToBounds = YES;
            
            BOOL buttonStatus = [item[@"buttonStatus"] boolValue];
            if(buttonStatus){
                btn.backgroundColor = [UIColor clearColor];
                [btn setTitleColor:[UIColor colorWithHex:0x444444] forState:UIControlStateNormal];
            }else{
                btn.backgroundColor = kMinorColor;
                [btn setTitleColor:kButtonEndColor forState:UIControlStateNormal];
            }
        }];
        
        _stepBoxV = view;
    }
    return _stepBoxV;
}

@end
