 

#import <Foundation/Foundation.h>

@class SABaseManager;
@protocol QLAPIManagerInterceptor <NSObject>

@optional
- (BOOL)manager:(SABaseManager *)manager beforePerformSuccessWithResponse:(SAResponse *)response;
- (void)manager:(SABaseManager *)manager afterPerformSuccessWithResponse:(SAResponse *)response;

- (BOOL)manager:(SABaseManager *)manager beforePerformFailWithResponse:(SAResponse *)response;
- (void)manager:(SABaseManager *)manager afterPerformFailWithResponse:(SAResponse *)response;

- (BOOL)manager:(SABaseManager *)manager shouldCallAPIWithParams:(NSDictionary *)params;
- (void)manager:(SABaseManager *)manager afterCallingAPIWithParams:(NSDictionary *)params;

@end
