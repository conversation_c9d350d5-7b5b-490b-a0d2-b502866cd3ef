
#import "SARepaymentCell.h"
#import "SAPayClientChargesApiManager.h"
#import "SACommonWebViewController.h"
#import "SADictionary+QLNetworkingMethods.h"
#import "SAOfflineRepayViewController.h"
#import "SARepayH5ViewController.h"
#import "NPOnlineRepayViewController.h"

#define kStatusLabWidth     80
#define kStatusLabHeight    26

#define kRepayBtnHeight     30

@interface SARepaymentCell ()<QLAPIManagerParamSource, QLAPIManagerCallBackDelegate, NPRequestMethodProtocol>


@property (nonatomic, strong) UIView *jieBgV;
@property (nonatomic, strong) UIImageView *jieIcon;
@property (nonatomic, strong) UILabel *jieTitleLab;
@property (nonatomic, strong) UILabel *jieStatusLab;
@property (nonatomic, strong) UILabel *jieSerialLab;
@property (nonatomic, strong) UILabel *jiePeriodLab;
@property (nonatomic, strong) UILabel *jieRepayLab;
@property (nonatomic, strong) UIButton *payBtn;
@property (nonatomic, strong) UILabel *jieDateLab;
@property (nonatomic, strong) UILabel *jieDaysLab;


@property (nonatomic, strong) UIView *baoBgV;
@property (nonatomic, strong) UIImageView *baoIcon;
@property (nonatomic, strong) UILabel *baoTitleLab;
@property (nonatomic, strong) UILabel *baoStatusLab;
@property (nonatomic, strong) UILabel *baoSerialLab;
@property (nonatomic, strong) UILabel *baoPeriodLab;
@property (nonatomic, strong) UILabel *baoRepayLab;
@property (nonatomic, strong) UIButton *baoPayBtn;
@property (nonatomic, strong) UILabel *baoDateLab;
@property (nonatomic, strong) UILabel *baoDaysLab;

@property (nonatomic, strong) SAPayClientChargesApiManager *payChargeManager;
@property (nonatomic, strong) NSString *currentPayType;


@property (nonatomic, strong) UIView *dotV1;
@property (nonatomic, strong) UIView *dotV2;
@property (nonatomic, strong) UIView *dotV3;
@property (nonatomic, strong) UIView *dotV4;
@property (nonatomic, strong) UIView *dotV5;
@property (nonatomic, strong) UIView *dotV6;

@property (nonatomic, strong) UIView *dotVJieRepay;
@property (nonatomic, strong) UIView *dotVBaoRepay;


@end

@implementation SARepaymentCell

#pragma mark - Life cycle

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier])
    {
        [self createUI];
        [self setConstraints];
        
    }
    
    return self;
}

- (void)createUI
{
    [self.contentView addSubview:self.jieBgV];
    [self.jieBgV addSubview:self.jieIcon];
    [self.jieBgV addSubview:self.jieTitleLab];
    [self.jieBgV addSubview:self.jieStatusLab];
    [self.jieBgV addSubview:self.jieSerialLab];
    [self.jieBgV addSubview:self.jiePeriodLab];
    [self.jieBgV addSubview:self.jieRepayLab];
    [self.jieBgV addSubview:self.payBtn];
    [self.jieBgV addSubview:self.jieDateLab];
    [self.jieBgV addSubview:self.dotVJieRepay];
    [self.jieBgV addSubview:self.jieDaysLab];

    [self.jieBgV addSubview:self.dotV1];
    [self.jieBgV addSubview:self.dotV2];
    [self.jieBgV addSubview:self.dotV3];

    [self.contentView addSubview:self.baoBgV];
    [self.baoBgV addSubview:self.baoIcon];
    [self.baoBgV addSubview:self.baoTitleLab];
    [self.baoBgV addSubview:self.baoStatusLab];
    [self.baoBgV addSubview:self.baoSerialLab];
    [self.baoBgV addSubview:self.baoPeriodLab];
    [self.baoBgV addSubview:self.baoRepayLab];
    [self.baoBgV addSubview:self.baoPayBtn];
    [self.baoBgV addSubview:self.baoDateLab];
    [self.baoBgV addSubview:self.dotVBaoRepay];
    [self.baoBgV addSubview:self.baoDaysLab];

    [self.baoBgV addSubview:self.dotV4];
    [self.baoBgV addSubview:self.dotV5];
    [self.baoBgV addSubview:self.dotV6];

    
    self.jieBgV.layer.borderWidth = 0.7;
    self.jieBgV.layer.borderColor = [UIColor colorWithHex:0xe3e3e3].CGColor;
    
    self.baoBgV.layer.borderWidth = 0.7;
    self.baoBgV.layer.borderColor = [UIColor colorWithHex:0xe3e3e3].CGColor;

}

- (void)setConstraints
{
    CGFloat hGap = 12;
    CGFloat vGap = 10;
    
    CGFloat dotSize = 6;
    
    [self.jieBgV mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.top.right.equalTo(self.contentView);
    }];
    
    [self.jieIcon mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.equalTo(self.jieBgV).offset(hGap);
        make.top.equalTo(self.jieBgV).offset(vGap);
        make.width.height.mas_equalTo(20);
    }];
    [self.jieTitleLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.centerY.equalTo(self.jieIcon);
        make.left.equalTo(self.jieIcon.mas_right).offset(hGap);
    }];
    [self.jieStatusLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.right.top.equalTo(self.jieBgV);
        make.width.mas_equalTo(kStatusLabWidth);
        make.height.mas_equalTo(kStatusLabHeight);
    }];
    
    [self.dotV1 mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.equalTo(self.jieTitleLab);
        make.width.height.mas_equalTo(dotSize);
        make.top.equalTo(self.jieTitleLab.mas_bottom).offset(vGap+10);
    }];
    self.dotV1.layer.masksToBounds = YES;
    self.dotV1.layer.cornerRadius = dotSize/2;
    
    [self.jieSerialLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.equalTo(self.dotV1.mas_right).offset(7);
        make.centerY.equalTo(self.dotV1);
    }];
    
    [self.dotV2 mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.equalTo(self.dotV1);
        make.width.height.equalTo(self.dotV1);
        make.top.equalTo(self.jieSerialLab.mas_bottom).offset(vGap+5);
    }];
    self.dotV2.layer.masksToBounds = YES;
    self.dotV2.layer.cornerRadius = dotSize/2;
    [self.jiePeriodLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.equalTo(self.jieSerialLab);
        make.centerY.equalTo(self.dotV2);
    }];
    
    [self.dotV3 mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.equalTo(self.dotV1);
        make.width.height.equalTo(self.dotV1);
        make.top.equalTo(self.jiePeriodLab.mas_bottom).offset(vGap+5);
    }];
    self.dotV3.layer.masksToBounds = YES;
    self.dotV3.layer.cornerRadius = dotSize/2;
    [self.jieRepayLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.equalTo(self.jieSerialLab);
        make.centerY.equalTo(self.dotV3);
    }];
    
    [self.dotVJieRepay mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.equalTo(self.dotV1);
        make.width.height.equalTo(self.dotV1);
        make.top.equalTo(self.jieRepayLab.mas_bottom).offset(vGap+5);
    }];
    self.dotVJieRepay.layer.masksToBounds = YES;
    self.dotVJieRepay.layer.cornerRadius = dotSize/2;

    [self.jieDaysLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.equalTo(self.jieSerialLab);
        make.centerY.equalTo(self.dotVJieRepay);
    }];

   
    [self.payBtn  mas_makeConstraints:^(SAConstraintMaker *make) {
        make.right.equalTo(self.jieBgV).offset(-hGap);
        make.top.equalTo(self.jieRepayLab.mas_bottom).offset(vGap);
        make.width.mas_equalTo(kStatusLabWidth + 24);
        make.height.mas_equalTo(kRepayBtnHeight);
        make.bottom.equalTo(self.jieBgV).offset(-vGap);
    }];
    
    [self.jieDateLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.right.equalTo(self.jieStatusLab);
        make.centerY.equalTo(self.jieSerialLab);
    }];
   
    
    [self.baoBgV mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.right.equalTo(self.jieBgV);
        make.top.equalTo(self.jieBgV.mas_bottom).offset(vGap);
    }];
    [self.baoIcon mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.equalTo(self.jieIcon);
        make.top.equalTo(self.baoBgV).offset(vGap);
        make.width.height.equalTo(self.jieIcon);
    }];
    [self.baoTitleLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.centerY.equalTo(self.baoIcon);
        make.left.equalTo(self.baoIcon.mas_right).offset(hGap);
    }];
    [self.baoStatusLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.right.top.equalTo(self.baoBgV);
        make.width.height.equalTo(self.jieStatusLab);
    }];
    
    [self.dotV4 mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.equalTo(self.dotV1);
        make.width.height.equalTo(self.dotV1);
        make.top.equalTo(self.baoTitleLab.mas_bottom).offset(vGap+10);
    }];
    self.dotV4.layer.masksToBounds = YES;
    self.dotV4.layer.cornerRadius = dotSize/2;
    [self.baoSerialLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.equalTo(self.jieSerialLab);
        make.centerY.equalTo(self.dotV4);
    }];
    
    [self.dotV5 mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.equalTo(self.dotV1);
        make.width.height.equalTo(self.dotV1);
        make.top.equalTo(self.baoSerialLab.mas_bottom).offset(vGap+5);
    }];
    self.dotV5.layer.masksToBounds = YES;
    self.dotV5.layer.cornerRadius = dotSize/2;

    [self.baoPeriodLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.equalTo(self.baoSerialLab);
        make.centerY.equalTo(self.dotV5);
    }];
    
    [self.dotV6 mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.equalTo(self.dotV1);
        make.width.height.equalTo(self.dotV1);
        make.top.equalTo(self.baoPeriodLab.mas_bottom).offset(vGap+5);
    }];
    self.dotV6.layer.masksToBounds = YES;
    self.dotV6.layer.cornerRadius = dotSize/2;
    [self.baoRepayLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.equalTo(self.baoSerialLab);
        make.centerY.equalTo(self.dotV6);
    }];
    
    [self.dotVBaoRepay mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.equalTo(self.dotV1);
        make.width.height.equalTo(self.dotV1);
        make.top.equalTo(self.baoRepayLab.mas_bottom).offset(vGap+5);
    }];
    self.dotVBaoRepay.layer.masksToBounds = YES;
    self.dotVBaoRepay.layer.cornerRadius = dotSize/2;

    [self.baoDaysLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.equalTo(self.baoSerialLab);
        make.centerY.equalTo(self.dotVBaoRepay);
    }];

    
    [self.baoPayBtn  mas_makeConstraints:^(SAConstraintMaker *make) {
        make.right.width.height.equalTo(self.payBtn);
        make.top.equalTo(self.baoRepayLab.mas_bottom).offset(vGap);
        make.bottom.equalTo(self.baoBgV).offset(-vGap);
    }];
    
    [self.baoDateLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.right.equalTo(self.baoStatusLab);
        make.centerY.equalTo(self.baoSerialLab);
    }];
    
    self.jieBgV.layer.masksToBounds = YES;
    self.jieBgV.layer.cornerRadius = 8;

    self.baoBgV.layer.masksToBounds = YES;
    self.baoBgV.layer.cornerRadius = 8;

    self.payBtn.layer.cornerRadius = kRepayBtnHeight/2;
    self.baoPayBtn.layer.cornerRadius = kRepayBtnHeight/2;
}

- (void)layoutSubviews
{
    [super layoutSubviews];
    
    CGRect bounds = CGRectMake(0, 0, kStatusLabWidth, kStatusLabHeight);
    UIBezierPath *maskPath = [UIBezierPath
        bezierPathWithRoundedRect:bounds
        byRoundingCorners:(UIRectCornerBottomLeft)
        cornerRadii:CGSizeMake(14, 14)
    ];
    CAShapeLayer *maskLayer = [CAShapeLayer layer];
    maskLayer.frame = bounds;
    maskLayer.path = maskPath.CGPath;
    self.jieStatusLab.layer.mask = maskLayer;
    
    UIBezierPath *maskPath2 = [UIBezierPath
        bezierPathWithRoundedRect:bounds
        byRoundingCorners:(UIRectCornerBottomLeft)
        cornerRadii:CGSizeMake(14, 14)
    ];
    CAShapeLayer *maskLayer2 = [CAShapeLayer layer];
    maskLayer2.frame = bounds;
    maskLayer2.path = maskPath2.CGPath;
    self.baoStatusLab.layer.mask = maskLayer2;
}

#pragma mark - Action

- (void)payAction
{
    if(self.model.paidStatus == NPPaidStatus_Paid) return;
    
    self.currentPayType = @"10";
    [SATool showWindowHUD:nil];
    [self.payChargeManager loadData];
}

- (void)payBaoAction
{
    if(self.model.pledgePaidStatus == NPPaidStatus_Paid) return;

    self.currentPayType = @"11";
    [SATool showWindowHUD:nil];
    [self.payChargeManager loadData];
}

- (void)copyOrderNoAction
{
    UIPasteboard *pasteboard = [UIPasteboard generalPasteboard];
    pasteboard.string = self.model.tradeNo;
    [SATool textStateHUD:NSLocalizedString(@"onfirmmAvigation", nil)];
}

#pragma mark QLAPIManagerCallBackDelegate

- (void)managerCallAPIDidSuccess:(SABaseManager *)manager 
{
    [SATool hideWindowHUD];
    if (manager == self.payChargeManager){
        
        NSDictionary *dataDict = [manager fetchDataWithReformer:self.payChargeManager];
        NSInteger repayType = [dataDict[@"payClientType"] integerValue];
        NSString *paymentStr = dataDict[@"repaymentJson"];
        NSDictionary *paymentDict = [NSDictionary QL_stringToDict:paymentStr];

        if(repayType == 6)
        {
            
            SAOfflineRepayViewController *vc = [SAOfflineRepayViewController new];
            vc.hidesBottomBarWhenPushed = YES;
            [[SACommonTool currentViewController].navigationController pushViewController:vc animated:YES];
        }
        else if (repayType == 5){
            
            SARepayH5ViewController *webVc = [[SARepayH5ViewController alloc] init];
            webVc.url = paymentDict[@"payUrl"];
            webVc.navTitle = @"收银台";
            webVc.hidesBottomBarWhenPushed = YES;
            [[SACommonTool currentViewController].navigationController pushViewController:webVc animated:YES];
        }
        else if (repayType == 7){
            
            NPOnlineRepayViewController *vc = [[NPOnlineRepayViewController alloc] init];
            vc.key = paymentDict[@"payKey"];
            vc.hidesBottomBarWhenPushed = YES;
            [[SACommonTool currentViewController].navigationController pushViewController:vc animated:YES];
        }
    }
}

- (void)managerCallAPIDidFailed:(SABaseManager *)manager 
{
    [SATool hideWindowHUD];
    NSString *failMsg  = [SAResponseHandle netWorkErrorMsg:[manager fetchDataWithReformer:nil] andErrorType:manager.errorType];
    [SATool textStateWindowHUD:failMsg];
}

#pragma mark QLAPIManagerParamSource

- (NSDictionary *)paramsForApi:(SABaseManager *)manager
{
   if (manager == self.payChargeManager){
        return @{
            @"payType": self.currentPayType,
            @"billNo": self.model.billNo
        };
    }
    return [NSDictionary dictionary];
}

#pragma mark - NPRequestMethodProtocol

- (NSString *)methodForApi:(SABaseManager *)manager
{
    if (manager == self.payChargeManager)
    {
//        return [NSString stringWithFormat:@"pay/client/charges"];
        return [NSString stringWithFormat:@"b244b865e6ab42809744261f2d8ca11a"];
    }
    return @"";
}

#pragma mark - setter

- (void)setModel:(SARepayListModel *)model
{
    _model = model;
            
    self.jieStatusLab.text = model.paidOrderStatusDesc;
    self.jieTitleLab.text = [NSString stringWithFormat:@"本息账单剩余待还  %@元", model.needPayCapital];
    self.jieSerialLab.text = [NSString stringWithFormat:@"账单期数  %@", model.currentPeriodDesc];
    self.jiePeriodLab.text = [NSString stringWithFormat:@"账单周期  %@", model.cycleDate];
    self.jieRepayLab.text = [NSString stringWithFormat:@"账单应还本息  %@元", model.repaymentCapital];
    self.jieDateLab.text = model.repaymentDate;

    self.baoStatusLab.text = model.pledgeOrderStatusDesc;
    self.baoTitleLab.text = [NSString stringWithFormat:@"担保费剩余待付  %@元", model.pledgeNeedPayCapital];
    self.baoSerialLab.text = [NSString stringWithFormat:@"账单期数  %@", model.currentPeriodDesc];
    self.baoPeriodLab.text = [NSString stringWithFormat:@"账单周期  %@", model.cycleDate];
    self.baoRepayLab.text = [NSString stringWithFormat:@"担保费应还  %@元", model.pledgeRepaymentCapital];
    self.baoDateLab.text = model.pledgeRepaymentDate;

    [self.payBtn setTitle:model.paidStatusDesc forState:UIControlStateNormal];
    [self.baoPayBtn setTitle:model.pledgePaidStatusDesc forState:UIControlStateNormal];

    
    if(model.paidStatus == NPPaidStatus_Paid)
    {
        self.jieStatusLab.backgroundColor = [UIColor colorWithHex:0xc2c5cc];
        self.payBtn.backgroundColor = [UIColor colorWithHex:0xc2c5cc];
        
        self.dotVJieRepay.hidden = YES;
        self.jieDaysLab.hidden = YES;
    }
    else
    {
        if(self.model.overdueDays > 0){
            self.jieStatusLab.backgroundColor = [UIColor redColor];
            self.payBtn.backgroundColor = [UIColor redColor];

        }else{
            self.jieStatusLab.backgroundColor = [UIColor colorWithHex:0x598CF3];
            self.payBtn.backgroundColor = [UIColor colorWithHex:0x598CF3];
        }

        self.dotVJieRepay.hidden = NO;
        self.jieDaysLab.hidden = NO;
    }
    
    if(model.pledgePaidStatus == NPPaidStatus_Paid)
    {
        self.baoStatusLab.backgroundColor = [UIColor colorWithHex:0xc2c5cc];
        self.baoPayBtn.backgroundColor = [UIColor colorWithHex:0xc2c5cc];

        self.dotVBaoRepay.hidden = YES;
        self.baoDaysLab.hidden = YES;

    }
    else
    {
        if(self.model.overdueDays > 0){
            self.baoStatusLab.backgroundColor = [UIColor redColor];
            self.baoPayBtn.backgroundColor = [UIColor redColor];
        }else{
            self.baoStatusLab.backgroundColor = [UIColor colorWithHex:0x598CF3];
            self.baoPayBtn.backgroundColor = [UIColor colorWithHex:0x598CF3];
        }

        self.dotVBaoRepay.hidden = NO;
        self.baoDaysLab.hidden = NO;
    }
    
    NSMutableAttributedString *jieDaysStr;
    if(model.overdueDays > 0){
        jieDaysStr  = [[NSMutableAttributedString alloc] initWithString: [NSString stringWithFormat:@"已逾期 %ld 天", model.overdueDays]];
        [jieDaysStr setAttributes:@{NSForegroundColorAttributeName: [UIColor colorWithHex:0xFF4500], NSFontAttributeName: BoldFont_XX6(12)} range: [[NSString stringWithFormat:@"已逾期 %ld 天", model.overdueDays] rangeOfString:[NSString stringWithFormat:@"%ld", model.overdueDays] ]];
    }else{
        jieDaysStr  = [[NSMutableAttributedString alloc] initWithString: [NSString stringWithFormat:@"距离到期日 %ld 天", model.repaymentDays]];
        [jieDaysStr setAttributes:@{NSForegroundColorAttributeName: [UIColor colorWithHex:0xFF4500], NSFontAttributeName: BoldFont_XX6(12)} range: [[NSString stringWithFormat:@"距离到期日 %ld 天", model.repaymentDays] rangeOfString:[NSString stringWithFormat:@"%ld", model.repaymentDays] ]];
    }
    self.jieDaysLab.attributedText = jieDaysStr;
    
    
    NSMutableAttributedString *baoDaysStr;
    if(model.pledgeOverdueDays > 0){
        baoDaysStr  = [[NSMutableAttributedString alloc] initWithString: [NSString stringWithFormat:@"已逾期 %ld 天", model.pledgeOverdueDays]];
        [baoDaysStr setAttributes:@{NSForegroundColorAttributeName: [UIColor colorWithHex:0xFF4500], NSFontAttributeName: BoldFont_XX6(12)} range: [[NSString stringWithFormat:@"已逾期 %ld 天", model.pledgeOverdueDays] rangeOfString:[NSString stringWithFormat:@"%ld", model.pledgeOverdueDays] ]];
    }else{
        baoDaysStr  = [[NSMutableAttributedString alloc] initWithString: [NSString stringWithFormat:@"距离到期日 %ld 天", model.pledgeRepaymentDays]];
        [baoDaysStr setAttributes:@{NSForegroundColorAttributeName: [UIColor colorWithHex:0xFF4500], NSFontAttributeName: BoldFont_XX6(12)} range: [[NSString stringWithFormat:@"距离到期日 %ld 天", model.pledgeRepaymentDays] rangeOfString:[NSString stringWithFormat:@"%ld", model.pledgeRepaymentDays] ]];
    }
    self.baoDaysLab.attributedText = baoDaysStr;
}

- (void)setFrame:(CGRect)frame
{
    frame.origin.x  = 12;
    frame.size.width -= 24;
    
    frame.origin.y += 6;
    frame.size.height -= 12;
    
    [super setFrame:frame];
}

#pragma mark - getter


- (UILabel *)jieStatusLab{
    FF_Fetch_UILable_CenterX(_jieStatusLab,[UIColor whiteColor], Font_XX6(12))
}

- (UILabel *)baoStatusLab{
    FF_Fetch_UILable_CenterX(_baoStatusLab,[UIColor whiteColor], Font_XX6(12))
}


- (UIImageView *)jieIcon{
    FF_Fetch_UIImageViewWithImage(_jieIcon, [UIImage imageNamed:@"repay_bill"])
}

- (UILabel *)jieTitleLab{
    FF_Fetch_UILable(_jieTitleLab, [UIColor blackColor], BoldFont_XX6(14))
}

- (UILabel *)jieRepayLab{
    FF_Fetch_UILable(_jieRepayLab, [UIColor colorWithHex:0x999999], Font_XX6(12))
}

- (UILabel *)jieSerialLab{
    FF_Fetch_UILable(_jieSerialLab, [UIColor colorWithHex:0x999999], Font_XX6(12))
}
- (UILabel *)jiePeriodLab{
    FF_Fetch_UILable(_jiePeriodLab, [UIColor colorWithHex:0x999999], Font_XX6(12))
}

- (UIButton *)payBtn{
    if (_payBtn == nil) {
        _payBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_payBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        [_payBtn addTarget:self action:@selector(payAction) forControlEvents:UIControlEventTouchUpInside];
        _payBtn.layer.masksToBounds = YES;
        _payBtn.titleLabel.font = Font_XX6(12);
    }
    return _payBtn;
}


- (UIImageView *)baoIcon{
    FF_Fetch_UIImageViewWithImage(_baoIcon, [UIImage imageNamed:@"repay_db"])
}

- (UILabel *)baoTitleLab{
    FF_Fetch_UILable(_baoTitleLab, [UIColor blackColor], BoldFont_XX6(14))
}

- (UILabel *)baoRepayLab{
    FF_Fetch_UILable(_baoRepayLab, [UIColor colorWithHex:0x999999], Font_XX6(12))
}

- (UILabel *)baoSerialLab{
    FF_Fetch_UILable(_baoSerialLab, [UIColor colorWithHex:0x999999], Font_XX6(12))
}

- (UILabel *)baoPeriodLab{
    FF_Fetch_UILable(_baoPeriodLab, [UIColor colorWithHex:0x999999], Font_XX6(12))
}

- (UIButton *)baoPayBtn{
    if (_baoPayBtn == nil) {
        _baoPayBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_baoPayBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        [_baoPayBtn addTarget:self action:@selector(payBaoAction) forControlEvents:UIControlEventTouchUpInside];
        _baoPayBtn.layer.masksToBounds = YES;
        _baoPayBtn.titleLabel.font = Font_XX6(12);

    }
    return _baoPayBtn;
}

- (SAPayClientChargesApiManager *)payChargeManager{
    if(_payChargeManager == nil){
        _payChargeManager = [[SAPayClientChargesApiManager alloc] init];
        _payChargeManager.delegate = self;
        _payChargeManager.paramSource = self;
        _payChargeManager.methodSource = self;
    }
    return _payChargeManager;
}

- (UIView *)jieBgV{
    FF_Fetch_UIView(_jieBgV, [UIColor whiteColor])
}

- (UIView *)baoBgV{
    FF_Fetch_UIView(_baoBgV, [UIColor whiteColor])
}

- (UIView *)dotV1{
    FF_Fetch_UIView(_dotV1, [UIColor colorWithHex: 0x6BB0DB])
}

- (UIView *)dotV2{
    FF_Fetch_UIView(_dotV2, [UIColor colorWithHex: 0x7D89E9])
}

- (UIView *)dotV3{
    FF_Fetch_UIView(_dotV3, [UIColor orangeColor])
}

- (UIView *)dotV4{
    FF_Fetch_UIView(_dotV4, [UIColor colorWithHex: 0x6BB0DB])
}

- (UIView *)dotV5{
    FF_Fetch_UIView(_dotV5, [UIColor colorWithHex: 0x7D89E9])
}

- (UIView *)dotV6{
    FF_Fetch_UIView(_dotV6, [UIColor orangeColor])
}

- (UILabel *)jieDateLab{
    FF_Fetch_UILable_RightX(_jieDateLab, [UIColor blackColor], BoldFont_XX6(12))
}

- (UILabel *)baoDateLab{
    FF_Fetch_UILable_RightX(_baoDateLab, [UIColor blackColor], BoldFont_XX6(12))
}

- (UILabel *)jieDaysLab{
    FF_Fetch_UILable(_jieDaysLab, [UIColor colorWithHex:0x999999], Font_XX6(12))
}

- (UILabel *)baoDaysLab{
    FF_Fetch_UILable(_baoDaysLab, [UIColor colorWithHex:0x999999], Font_XX6(12))
}

- (UIView *)dotVJieRepay{
    FF_Fetch_UIView(_dotVJieRepay, [UIColor colorWithHex:0xFF4500])
}

- (UIView *)dotVBaoRepay{
    FF_Fetch_UIView(_dotVBaoRepay, [UIColor colorWithHex:0xFF4500])
}

@end
