
#import "SARepaymentViewController.h"
#import "SAGetRepayBillListApi.h"
#import "SAGetPaidBillListApi.h"
#import "SATabedSlideView.h"
#import "SARepayListVC.h"
#import "SAPaidListVC.h"
#import "SAServiceView.h"

#define kSliderTabHeight     48

@interface SARepaymentViewController ()<DLTabedSlideViewDelegate>


@property (nonatomic, strong) SAGetRepayBillListApi  *repayApi;
@property (nonatomic, strong) SAGetPaidBillListApi *paidApi;

@property (strong, nonatomic) SATabedSlideView *tabedSlideView;

@property (nonatomic, strong) SAServiceView *serviceView;

@end

@implementation SARepaymentViewController

#pragma mark - Life cycle

- (instancetype)init
{
    if (self = [super init])
    {
        self.backImgName = @"";
    }

    return self;
}

- (void)viewDidAppear:(BOOL)animated
{
    [super viewDidAppear:animated];
    
    self.navigationController.interactivePopGestureRecognizer.enabled = NO;
}

- (void)viewDidDisappear:(BOOL)animated
{
    [super viewDidDisappear:animated];
    
    self.navigationController.interactivePopGestureRecognizer.enabled = YES;
}

- (void)viewDidLoad
{
    [super viewDidLoad];
    
    self.navigationItem.title = NSLocalizedString(@"commonObjectAttribute", nil);
    [self createUI];
    
    [self.serviceView showActivityService];
}

- (void)createUI
{
    self.tabedSlideView =  [[SATabedSlideView alloc] initWithFrame:CGRectMake(0, 0, UIScreenWidth, UIScreenHeight-kTabBarHeight-kTopHeight)];
    [self.view addSubview:self.tabedSlideView];
    self.tabedSlideView.baseViewController = self;
    self.tabedSlideView.delegate = self;
    self.tabedSlideView.tabItemNormalColor = [UIColor blackColor];
    self.tabedSlideView.tabItemSelectedColor = kPrimaryColor;
    self.tabedSlideView.tabbarTrackColor = kPrimaryColor;
    self.tabedSlideView.tabbarBottomSpacing = 3.0;
    
    SATabedbarItem *item1 = [SATabedbarItem itemWithTitle:@"待还款" image:nil selectedImage:nil];
    SATabedbarItem *item2 = [SATabedbarItem itemWithTitle:@"已结清" image:nil selectedImage:nil];
    self.tabedSlideView.tabbarItems = @[item1, item2];
    [self.tabedSlideView buildTabbar];
    
    self.tabedSlideView.selectedIndex = 0;
}

#pragma mark - DLTabedSlideViewDelegate

- (NSInteger)numberOfTabsInDLTabedSlideView:(SATabedSlideView *)sender
{
    return 2;
}

- (UIViewController *)SATabedSlideView:(SATabedSlideView *)sender controllerAt:(NSInteger)index
{
    switch (index) {
        case 0:
        {
            SARepayListVC *ctrl = [[SARepayListVC alloc] init];
            return ctrl;
        }
        case 1:
        {
            SAPaidListVC *ctrl = [[SAPaidListVC alloc] init];
            return ctrl;
        }

        default:
            return nil;
    }
}

#pragma mark - getter

- (SAServiceView *)serviceView{
    if(_serviceView == nil){
        _serviceView = [[SAServiceView alloc] init];
    }
    return _serviceView;
}

@end
