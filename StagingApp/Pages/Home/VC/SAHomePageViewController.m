
#import "SAHomePageViewController.h"

#import "SANormalRefresh.h"

#import "SAMainHomeApiManager.h"
#import "SAHomeModel.h"
#import "SAMainManager.h"

#import "SAHomeRejectCell.h"
#import "SAHomeNormalCell.h"
#import "SAHomeReviewingCell.h"
#import "SAUserManager.h"
#import "SACommonWebViewController.h"
#import "SAHomeConfirmCell.h"
#import "SALoginViewController.h"
#import "NPInviteAlertViewController.h"
#import "SAHomeLoaningCell.h"
#import "SAGetCustomerUrlApiManager.h"
#import "SAServiceView.h"

@interface SAHomePageViewController ()<QLAPIManagerParamSource, QLAPIManagerCallBackDelegate, UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, strong) SAMainHomeApiManager *homeApiMgr;
@property (nonatomic, strong) SAHomeModel *homeModel;
@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) SAServiceView *serviceView;

@property (nonatomic, strong) SAMainManager *mainMgr;

@property (nonatomic, assign) BOOL showedAlert;

@property (nonatomic, strong) SAGetCustomerUrlApiManager *customerApiMgr;

@end

@implementation SAHomePageViewController

static NSString * const HomeWaiBorrowID = @"HomeWaiBorrow";
static NSString * const HomeAuditProgressID = @"HomeAuditProgress";
static NSString * const HomeWaitRepaymentID = @"HomeWaitRepayment";
static NSString * const HomeRejectBorrowID = @"HomeRejectBorrow";
static NSString * const HomeOverdueProgressID = @"HomeOverdueProgress";
static NSString * const HomeWaitingID = @"HomeWaitingID";
static NSString * const HomeLoanFailID = @"HomeLoanFailID";
static NSString * const HomeLoanSignId = @"HomeLoanSignId";
static NSString * const HomeLoanConfirmId = @"HomeLoanConfirmId";

#pragma mark - Life cycle

- (void)viewDidAppear:(BOOL)animated
{
    [super viewDidAppear:animated];
    
    self.navigationController.interactivePopGestureRecognizer.enabled = NO;
}

- (void)viewDidDisappear:(BOOL)animated
{
    [super viewDidDisappear:animated];
    
    self.navigationController.interactivePopGestureRecognizer.enabled = YES;
}

- (void)viewWillAppear:(BOOL)animated 
{
    [super viewWillAppear:animated];
    
    [self.navigationController setNavigationBarHidden:YES animated:YES];
        
    [self reloadHome];
    [self.customerApiMgr loadData];
}

- (void)viewDidLoad 
{
    [super viewDidLoad];
    
    self.view.backgroundColor = [UIColor colorWithHex:0xf6f6f6];
    [self addCustomViews];
    [self setupConstraint];
    
    [self.mainMgr uploadGps];
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(reloadHome) name:@"RefreshHomeData" object:nil];
}

- (void)addCustomViews 
{
    [self.view addSubview:self.tableView];
}

- (void)setupConstraint 
{
    [self.tableView mas_makeConstraints:^(SAConstraintMaker *make) {
        make.top.left.right.bottom.equalTo(self.view);
    }];
    
    [self.serviceView showActivityService];
}

#pragma mark - shuaxin

- (void)reloadHome
{
    [self showActivityHUD:nil];
    [self.homeApiMgr loadData];
}

#pragma mark - Protocol协议
#pragma mark UITableViewDataSource
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.homeModel != nil ? 1 : 0;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    
    SAHomeBasicCell *cell = nil;
    if (self.homeModel == nil) {
        cell = [[SAHomeBasicCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:nil];
        return cell;
    } else {
        NSString *statusCode = self.homeModel.templateCode;
        if ([statusCode isEqualToString:State_WaitBorrow] || [statusCode isEqualToString:State_CanSubmit]) {
            cell = [tableView dequeueReusableCellWithIdentifier:HomeWaiBorrowID];
        }
        else if ([statusCode isEqualToString:State_AuditProgress]) {
            cell = [tableView dequeueReusableCellWithIdentifier:HomeAuditProgressID];
        }
        else if ([statusCode isEqualToString:State_LoanFail]){
            cell = [tableView dequeueReusableCellWithIdentifier:HomeLoanFailID];
        }
        else if ( [statusCode isEqualToString:State_WaitMoney]) {
            cell = [tableView dequeueReusableCellWithIdentifier:HomeWaitingID];
        }
        else if ([statusCode isEqualToString:State_WaitRepayment] || [statusCode isEqualToString:State_OverduePay]) {
            cell = [tableView dequeueReusableCellWithIdentifier:HomeWaitRepaymentID];
        }
        else if ([statusCode isEqualToString:State_AuditFail]) {
            cell = [tableView dequeueReusableCellWithIdentifier:HomeRejectBorrowID];
        }
        else if ([statusCode isEqualToString:State_ConfirmTrade]) {
            cell = [tableView dequeueReusableCellWithIdentifier:HomeLoanConfirmId];
        }
        else {
            cell = [[SAHomeBasicCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:nil];
        }
        cell.selectionStyle = UITableViewCellSelectionStyleNone;
        cell.backgroundColor = [UIColor clearColor];
        
        cell.model = self.homeModel;
        [cell renderCoreBg];
        return cell;
    }
}

#pragma mark UITableViewDelegate
- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return self.homeModel.cellHeight;
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    return 0.000001f;
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    return 0.000001f;
}

#pragma mark QLAPIManagerCallBackDelegate
- (void)managerCallAPIDidSuccess:(SABaseManager *)manager {
    [self hideHUDView];
    [self endLoading];
    if (manager == self.homeApiMgr)
    {
        NSDictionary *dataDict = [manager fetchDataWithReformer:self.homeApiMgr];
        
        self.homeModel = [SAHomeModel objectWithDict:dataDict];
        
        [SAUserManager setBorrowStatus:self.homeModel.borrowStatus];
        [[NSUserDefaults standardUserDefaults] setObject:self.homeModel.productLikeUrl forKey:@"likeurl"];
        [self.tableView reloadData];
        
        if(!self.showedAlert && [self.homeModel.buttonJumpUrl isEqualToString:@"APP/CLViewControllerUserDataList"]){
            self.showedAlert = YES;
            [NPInviteAlertViewController showAmount:self.homeModel.maxAmount sureAction:^{
                if ([SAUserManager isLogin]){
                    [SAVerifyListManager sharedInstance].popVCName = NSStringFromClass([[SACommonTool currentViewController] class]);
                    [[SAVerifyListManager sharedInstance] handleVerifyJump];
                }else{
                    UINavigationController *navVC = [[UINavigationController alloc] initWithRootViewController:[[SALoginViewController alloc] init]];
                    navVC.modalPresentationStyle = UIModalPresentationFullScreen;
                    [[SACommonTool currentViewController].navigationController presentViewController:navVC animated:YES completion:nil];
                }
            }];
        }
    }
    else if (manager == self.customerApiMgr)
    {
        NSDictionary *dict = [manager fetchDataWithReformer:self.customerApiMgr];
        NSLog(@"ssss---%@", dict);
        [SAAdvanceManager sharedInstance].customerServiceUrl = [NSString judgeStringExist:dict[@"customerServiceUrl"]] ? dict[@"customerServiceUrl"] : @"";
    }
}

- (void)managerCallAPIDidFailed:(SABaseManager *)manager {
    [self hideHUDView];
    [self endLoading];
    
    NSString *failMsg  = [SAResponseHandle netWorkErrorMsg:[manager fetchDataWithReformer:nil] andErrorType:manager.errorType];
    [SATool textStateHUD:failMsg];
}

- (void)endLoading {
    
    if (self.tableView.mj_header.isRefreshing) {
        [self.tableView.mj_header endRefreshing];
    }
}

#pragma mark QLAPIManagerParamSource
- (NSDictionary *)paramsForApi:(SABaseManager *)manager {
    return [NSDictionary dictionary];
}

#pragma mark - Actions


#pragma mark - getter
- (SAMainHomeApiManager *)homeApiMgr {
    if (_homeApiMgr == nil) {
        _homeApiMgr = [[SAMainHomeApiManager alloc] init];
        _homeApiMgr.delegate = self;
        _homeApiMgr.paramSource = self;
    }
    return _homeApiMgr;
}

- (SAMainManager *)mainMgr {
    if (_mainMgr == nil) {
        _mainMgr = [[SAMainManager alloc] init];
    }
    return _mainMgr;
}

- (UITableView *)tableView {
    if (_tableView == nil) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
        _tableView.backgroundColor = [UIColor clearColor];
        _tableView.dataSource      = self;
        _tableView.delegate        = self;
        _tableView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
        _tableView.contentSize = CGSizeMake(UIScreenWidth, UIScreenHeight-kTabBarHeight);

        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.estimatedRowHeight = 0;
        _tableView.estimatedSectionFooterHeight = 0;
        _tableView.estimatedSectionHeaderHeight = 0;
        _tableView.tableHeaderView  = [UIView new];
        [_tableView registerClass:[SAHomeNormalCell class] forCellReuseIdentifier:HomeWaiBorrowID];
        [_tableView registerClass:[SAHomeReviewingCell class] forCellReuseIdentifier:HomeAuditProgressID];
        [_tableView registerClass:[SAHomeRejectCell class] forCellReuseIdentifier:HomeRejectBorrowID];
        [_tableView registerClass:[SAHomeConfirmCell class] forCellReuseIdentifier:HomeLoanConfirmId];
        [_tableView registerClass:[SAHomeLoaningCell class] forCellReuseIdentifier:HomeWaitingID];
        
        __weak typeof(self) weakSelf = self;
        _tableView.mj_header = [SAJGRefreshNormalHeader headerWithRefreshingBlock:^{
            [weakSelf.homeApiMgr loadData];
        }];
    }
    return _tableView;
}

- (SAGetCustomerUrlApiManager *)customerApiMgr
{
    if(_customerApiMgr == nil){
        _customerApiMgr = [[SAGetCustomerUrlApiManager alloc] init];
        _customerApiMgr.delegate = self;
        _customerApiMgr.paramSource = self;
        
    }
    return _customerApiMgr;
}

- (SAServiceView *)serviceView{
    if(_serviceView == nil){
        _serviceView = [[SAServiceView alloc] init];
    }
    return _serviceView;
}

@end

