
#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef void(^SelectContactsCompleteBlock)(NSString *nameStr, NSString *phoneStr);
typedef void(^CancelSelectContactsBlock)(void);

typedef void(^GainAllContactsBlock)(NSArray *arr);

@interface SAContactsManager : NSObject

 
- (void)startSelectContacts:(SelectContactsCompleteBlock)completeBlock withCancel:(CancelSelectContactsBlock)cancelBlock;

 

- (void)fetchUploadContactsParams:(GainAllContactsBlock)gainBlock;

 
+ (BOOL)contactsIsEnable;

@end

NS_ASSUME_NONNULL_END
