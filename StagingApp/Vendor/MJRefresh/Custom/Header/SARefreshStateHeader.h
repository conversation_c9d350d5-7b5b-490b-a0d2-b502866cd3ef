
#if __has_include(<MJRefresh/SARefreshHeader.h>)
#import <MJRefresh/SARefreshHeader.h>
#else
#import "SARefreshHeader.h"
#endif

NS_ASSUME_NONNULL_BEGIN

@interface SARefreshStateHeader : SARefreshHeader
#pragma mark - 刷新时间相关
 
@property (copy, nonatomic, nullable) NSString *(^lastUpdatedTimeText)(NSDate * _Nullable lastUpdatedTime);
 
@property (weak, nonatomic, readonly) UILabel *lastUpdatedTimeLabel;

#pragma mark - 状态相关
 
@property (assign, nonatomic) CGFloat labelLeftInset;
 
@property (weak, nonatomic, readonly) UILabel *stateLabel;
 
- (instancetype)setTitle:(NSString *)title forState:(MJRefreshState)state;
@end

@interface SARefreshStateHeader (ChainingGrammar)

- (instancetype)modifyLastUpdatedTimeText:(NSString * (^)(NSDate * _Nullable lastUpdatedTime))handler;

@end

NS_ASSUME_NONNULL_END
