
#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface SAProvinceModel : NSObject
 
@property (nullable, nonatomic, copy) NSString *code;
 
@property (nullable, nonatomic, copy) NSString *name;
 
@property (nullable, nonatomic, copy) NSArray *citylist;
 
@property (nonatomic, assign) NSInteger index;

@end

@interface SACityModel : NSObject
 
@property (nullable, nonatomic, copy) NSString *code;
 
@property (nullable, nonatomic, copy) NSString *name;
 
@property (nullable, nonatomic, copy) NSArray *arealist;
 
@property (nonatomic, assign) NSInteger index;

@end

@interface SAAreaModel : NSObject
 
@property (nullable, nonatomic, copy) NSString *code;
 
@property (nullable, nonatomic, copy) NSString *name;
 
@property (nonatomic, assign) NSInteger index;

@end

NS_ASSUME_NONNULL_END
