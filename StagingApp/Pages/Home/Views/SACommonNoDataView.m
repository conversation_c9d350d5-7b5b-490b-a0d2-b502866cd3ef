
#import "SACommonNoDataView.h"

@interface SACommonNoDataView ()

@property (nonatomic, strong) UIImageView *imgV;
@property (nonatomic, strong) UILabel *titleL;

@end

@implementation SACommonNoDataView

#pragma mark - Life cycle

- (instancetype)initWithImg:(NSString *)imgName title:(NSString *)title
{
    if (self = [super init])
    {
        [self addCustomViews];
        [self setConstraints];
        
        self.imgV.image = [UIImage imageNamed:imgName];
        self.titleL.text = title;
    }
    
    return self;
}

- (void)addCustomViews
{
    [self addSubview:self.imgV];
    [self addSubview:self.titleL];
}

- (void)setConstraints
{
    [self.imgV mas_makeConstraints:^(SAConstraintMaker *make) {
        make.centerX.equalTo(self);
        make.centerY.equalTo(self).offset(-15);
        make.size.mas_equalTo(CGSizeMake(100, 100));
    }];
    self.imgV.contentMode = UIViewContentModeScaleAspectFit;
    
    [self.titleL mas_makeConstraints:^(SAConstraintMaker *make) {
        make.centerX.equalTo(self.imgV);
        make.top.equalTo(self.imgV.mas_bottom).offset(5);
    }];
}

#pragma mark - setter

#pragma mark - getter

- (UIImageView *)imgV{
    if (_imgV == nil) {
        _imgV = [[UIImageView alloc] init];
    }
    return _imgV;
}

- (UILabel *)titleL{
    FF_Fetch_UILable_CenterX(_titleL, [UIColor colorWithHex:0x666666], Font_XX6(13))
}

@end
