
#import "SARequestGenerator.h"
#import "SAServiceFactory.h"
#import "SADictionary+QLNetworkingMethods.h"
#import "SAString+QLNetworkingMethods.h"
#import "SAObject+QLNetworkingMethods.h"
#import <AFNetworking/AFNetworking.h>
#import "SAService.h"
#import "SALogger.h"
#import "SARequest+QLNetworkingMethods.h"
#import "SANetworkingConfigurationManager.h"

@interface SARequestGenerator ()

@property (nonatomic, strong) AFHTTPRequestSerializer *httpRequestSerializer;

@end

@implementation SARequestGenerator

#pragma mark - Life cycle

+ (instancetype)sharedInstance
{
    static dispatch_once_t onceToken;
    static SARequestGenerator *sharedInstance = nil;
    dispatch_once(&onceToken, ^{
       
        sharedInstance = [[SARequestGenerator alloc] init];
    });
    return sharedInstance;
}

- (instancetype)init
{
    if (self = [super init])
    {
        [self initialRequestGenerator];
    }
    return self;
}

- (void)initialRequestGenerator
{
    _httpRequestSerializer = [AFHTTPRequestSerializer serializer];
    _httpRequestSerializer.timeoutInterval = [SANetworkingConfigurationManager sharedInstance].apiNetworkingTimeoutSeconds;
    _httpRequestSerializer.cachePolicy = NSURLRequestUseProtocolCachePolicy;
}

#pragma mark - Public methods

- (NSURLRequest *)generateGETRequestWithServiceIdentifier:(NSString *)serviceIdentifier requestParams:(NSDictionary *)requestParams methodName:(NSString *)methodName
{
    return [self generateRequestWithServiceIdentifier:serviceIdentifier requestParams:requestParams methodName:methodName requestWithMethod:@"GET"];
}

- (NSURLRequest *)generatePOSTRequestWithServiceIdentifier:(NSString *)serviceIdentifier requestParams:(NSDictionary *)requestParams methodName:(NSString *)methodName
{
    return [self generateRequestWithServiceIdentifier:serviceIdentifier requestParams:requestParams methodName:methodName requestWithMethod:@"POST"];
}

- (NSURLRequest *)generatePutRequestWithServiceIdentifier:(NSString *)serviceIdentifier requestParams:(NSDictionary *)requestParams methodName:(NSString *)methodName
{
    return [self generateRequestWithServiceIdentifier:serviceIdentifier requestParams:requestParams methodName:methodName requestWithMethod:@"PUT"];
}

- (NSURLRequest *)generateDeleteRequestWithServiceIdentifier:(NSString *)serviceIdentifier requestParams:(NSDictionary *)requestParams methodName:(NSString *)methodName
{
    return [self generateRequestWithServiceIdentifier:serviceIdentifier requestParams:requestParams methodName:methodName requestWithMethod:@"DELETE"];
}

- (NSURLRequest *)generateUploadRequestWithServiceIdentifier:(NSString *)serviceIdentifier
                                               requestParams:(NSDictionary *)requestParams
                                                  methodName:(NSString *)methodName
                                                        data:(id)data
                                                    mimeType:(id)mimeType
                                                    fileName:(id)fileName {
    
    
    self.httpRequestSerializer.timeoutInterval = [SANetworkingConfigurationManager sharedInstance].apiNetworkingTimeoutSeconds;
    [[SANetworkingConfigurationManager sharedInstance] resetTimeOutToDefault];
    
    SAService *service  = [[SAServiceFactory sharedInstance] serviceWithIdentifier:serviceIdentifier];
    NSString *urlString = [service urlGeneratingRuleByMethodName:methodName];
    
    NSDictionary *totalRequestParams = [self totalRequestParamsByService:service requestParams:requestParams];
    
    NSMutableURLRequest *request = [self.httpRequestSerializer multipartFormRequestWithMethod:@"POST"
                                                                                    URLString:urlString
                                                                                   parameters:totalRequestParams
                                                                    constructingBodyWithBlock:^(id<AFMultipartFormData>  _Nonnull formData) {
																		
																		if ([data isKindOfClass:[NSData class]])
																		{
                                                                            
                                                                            NSString *nameStr = fileName;
                                                                            if ([nameStr containsString:@".jpg"]) {
                                                                                nameStr = [nameStr stringByReplacingOccurrencesOfString:@".jpg" withString:@""];
                                                                            }
																			[formData appendPartWithFileData:data
																										name:nameStr
																									fileName:fileName
																									mimeType:mimeType];
																		}
																		else if ([data isKindOfClass:[NSArray class]])
																		{
																			[data enumerateObjectsUsingBlock:^(NSData *singleData, NSUInteger index, BOOL * _Nonnull stop) {
																				
																				[formData appendPartWithFileData:singleData
																											name:fileName[index]
																										fileName:fileName[index]
																										mimeType:mimeType[index]];
																			}];
																		}
																		
                                                                    } error:nil];
    
    if ([service.child respondsToSelector:@selector(extraHttpHeadParmasWithMethodName: requestParams:)])
    {
        NSDictionary *dict = [service.child extraHttpHeadParmasWithMethodName:methodName requestParams:requestParams];
        if (dict)
        {
            [dict enumerateKeysAndObjectsUsingBlock:^(id  _Nonnull key, id  _Nonnull obj, BOOL * _Nonnull stop) {
                
                if (![key isEqualToString:@"Content-Type"]) {
                    [request setValue:obj forHTTPHeaderField:key];
                }
            }];
        }
    }
    request.requestParams = totalRequestParams;
    return request;
}

#pragma mark - Private method

- (NSURLRequest *)generateRequestWithServiceIdentifier:(NSString *)serviceIdentifier requestParams:(NSDictionary *)requestParams methodName:(NSString *)methodName requestWithMethod:(NSString *)method
{
    
    self.httpRequestSerializer.timeoutInterval = [SANetworkingConfigurationManager sharedInstance].apiNetworkingTimeoutSeconds;
    [[SANetworkingConfigurationManager sharedInstance] resetTimeOutToDefault];
    
    SAService *service  = [[SAServiceFactory sharedInstance] serviceWithIdentifier:serviceIdentifier];
    NSString *urlString = [service urlGeneratingRuleByMethodName:methodName];
    
    NSDictionary *totalRequestParams = [self totalRequestParamsByService:service requestParams:requestParams];
    
    NSMutableURLRequest *request = [self.httpRequestSerializer requestWithMethod:method URLString:urlString parameters:totalRequestParams error:NULL];
    if (![method isEqualToString:@"GET"] && [SANetworkingConfigurationManager sharedInstance].shouldSetParamsInHTTPBodyButGET)
    {
        NSString *requestString = [NSString QL_dictToString:requestParams deleteSpace:NO];
        if ([NSString judgeStringValid:requestString])
        {
            request.HTTPBody = [requestString dataUsingEncoding:NSUTF8StringEncoding];
        }
    }
    
    
    
    if ([service.child respondsToSelector:@selector(extraHttpHeadParmasWithMethodName: requestParams:)])
    {
        NSDictionary *tempDict = requestParams;
        if ([method isEqualToString:@"GET"]){
            tempDict = @{};
        }
        NSDictionary *dict = [service.child extraHttpHeadParmasWithMethodName:methodName requestParams:tempDict];
        if (dict)
        {
            [dict enumerateKeysAndObjectsUsingBlock:^(id  _Nonnull key, id  _Nonnull obj, BOOL * _Nonnull stop) {
                [request setValue:obj forHTTPHeaderField:key];
            }];
        }
    }
    
    request.requestParams = totalRequestParams;
    return request;
}

- (NSDictionary *)totalRequestParamsByService:(SAService *)service requestParams:(NSDictionary *)requestParams
{
    NSMutableDictionary *totalRequestParams = [NSMutableDictionary dictionaryWithDictionary:requestParams];
    
    if ([service.child respondsToSelector:@selector(extraParmas)])
    {
        if ([service.child extraParmas])
        {
            [[service.child extraParmas] enumerateKeysAndObjectsUsingBlock:^(id  _Nonnull key, id  _Nonnull obj, BOOL * _Nonnull stop) {
                [totalRequestParams setObject:obj forKey:key];
            }];
        }
    }
    
    return [totalRequestParams copy];
}

@end
