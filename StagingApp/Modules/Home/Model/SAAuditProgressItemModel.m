
#import "SAAuditProgressItemModel.h"

@interface SAAuditProgressItemModel ()

@property (nonatomic, assign)CGFloat titleH;
@property (nonatomic, assign)CGFloat valueH;

@end

@implementation SAAuditProgressItemModel

- (CGFloat)titleW {
    if (_titleW) {
        return _titleW;
    }
    
    
    CGFloat maxW = (UIScreenWidth - XX_6(80)) * 0.5 - XX_6(5);
    CGSize maxSize = CGSizeMake(maxW, MAXFLOAT);
    
    CGSize realSize = [self.title boundingRectWithSize:maxSize options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:[UIFont fontSizeOfXX_6:14]} context:nil].size;
    
    self.titleH = realSize.height + XX_6(10);
    
    _titleW = realSize.width;
    
    return _titleW;
}

- (CGFloat)cellHeight {
    if (_cellHeight ) {
        return _cellHeight;
    }
    
    [self titleW];
    CGFloat heig = self.titleH > self.valueH ? self.titleH : self.valueH;
    
    if (heig < XX_6(34)) {
        heig = XX_6(34);
    }
    
    _cellHeight = heig;
    return _cellHeight;
}

- (CGFloat)valueH {
    if (_valueH) {
        return _valueH;
    }
    
    
    CGFloat maxW = (UIScreenWidth - XX_6(80)) * 0.5;
    CGSize maxSize = CGSizeMake(maxW, MAXFLOAT);
    
    CGSize realSize = [self.value boundingRectWithSize:maxSize options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:[UIFont fontSizeOfXX_6:12]} context:nil].size;
    _valueH = realSize.height + XX_6(10);
    
    return _valueH;
}

@end
