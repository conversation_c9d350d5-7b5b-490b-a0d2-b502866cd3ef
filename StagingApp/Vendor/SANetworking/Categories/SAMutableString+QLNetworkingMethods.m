
#import "SAMutableString+QLNetworkingMethods.h"
#import "SAObject+QLNetworkingMethods.h"

@implementation NSMutableString (SANetworkingMethods)

- (void)QL_appendURLRequest:(NSURLRequest *)request
{
    [self appendFormat:@"\n\nHTTP URL:\n\t%@", request.URL];
    [self appendFormat:@"\n\nHTTP Header:\n%@", request.allHTTPHeaderFields ? request.allHTTPHeaderFields : @"\t\t\t\t\tN/A"];
    [self appendFormat:@"\n\nHTTP Body:\n%@", [[[NSString alloc] initWithData:request.HTTPBody encoding:NSUTF8StringEncoding] QL_defaultValue:@"\t\t\t\tN/A"]];
}

@end
