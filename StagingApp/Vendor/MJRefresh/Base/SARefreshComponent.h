
#import <UIKit/UIKit.h>
#if __has_include(<MJRefresh/SARefreshConst.h>)
#import <MJRefresh/SARefreshConst.h>
#else
#import "SARefreshConst.h"
#endif

NS_ASSUME_NONNULL_BEGIN

 
typedef NS_ENUM(NSInteger, MJRefreshState) {
     
    MJRefreshStateIdle = 1,
     
    MJRefreshStatePulling,
     
    MJRefreshStateRefreshing,
     
    MJRefreshStateWillRefresh,
     
    MJRefreshStateNoMoreData
};

 
typedef void (^MJRefreshComponentRefreshingBlock)(void) MJRefreshDeprecated("first deprecated in 3.3.0 - Use `MJRefreshComponentAction` instead");
 
typedef void (^MJRefreshComponentBeginRefreshingCompletionBlock)(void) MJRefreshDeprecated("first deprecated in 3.3.0 - Use `MJRefreshComponentAction` instead");
 
typedef void (^MJRefreshComponentEndRefreshingCompletionBlock)(void) MJRefreshDeprecated("first deprecated in 3.3.0 - Use `MJRefreshComponentAction` instead");

 
typedef void (^MJRefreshComponentAction)(void);

 
@interface SARefreshComponent : UIView
{
     
    UIEdgeInsets _scrollViewOriginalInset;
     
    __weak UIScrollView *_scrollView;
}

#pragma mark - 刷新动画时间控制
 
@property (nonatomic) NSTimeInterval fastAnimationDuration;
 
@property (nonatomic) NSTimeInterval slowAnimationDuration;
 
- (instancetype)setAnimationDisabled;

#pragma mark - 刷新回调
 
@property (copy, nonatomic, nullable) MJRefreshComponentAction refreshingBlock;
 
- (void)setRefreshingTarget:(id)target refreshingAction:(SEL)action;

 
@property (weak, nonatomic) id refreshingTarget;
 
@property (assign, nonatomic) SEL refreshingAction;
 
- (void)executeRefreshingCallback;

#pragma mark - 刷新状态控制
 
- (void)beginRefreshing;
- (void)beginRefreshingWithCompletionBlock:(void (^)(void))completionBlock;
 
@property (copy, nonatomic, nullable) MJRefreshComponentAction beginRefreshingCompletionBlock;
 
@property (copy, nonatomic, nullable) MJRefreshComponentAction endRefreshingAnimateCompletionBlock MJRefreshDeprecated("first deprecated in 3.3.0 - Use `endRefreshingAnimationBeginAction` instead");
@property (copy, nonatomic, nullable) MJRefreshComponentAction endRefreshingAnimationBeginAction;
 
@property (copy, nonatomic, nullable) MJRefreshComponentAction endRefreshingCompletionBlock;
 
- (void)endRefreshing;
- (void)endRefreshingWithCompletionBlock:(void (^)(void))completionBlock;
 
@property (assign, nonatomic, readonly, getter=isRefreshing) BOOL refreshing;

 
@property (assign, nonatomic) MJRefreshState state;

#pragma mark - 交给子类去访问
 
@property (assign, nonatomic, readonly) UIEdgeInsets scrollViewOriginalInset;
 
@property (weak, nonatomic, readonly) UIScrollView *scrollView;

#pragma mark - 交给子类们去实现
 
- (void)prepare NS_REQUIRES_SUPER;
 
- (void)placeSubviews NS_REQUIRES_SUPER;
 
- (void)scrollViewContentOffsetDidChange:(nullable NSDictionary *)change NS_REQUIRES_SUPER;
 
- (void)scrollViewContentSizeDidChange:(nullable NSDictionary *)change NS_REQUIRES_SUPER;
 
- (void)scrollViewPanStateDidChange:(nullable NSDictionary *)change NS_REQUIRES_SUPER;

 
- (void)i18nDidChange NS_REQUIRES_SUPER;

#pragma mark - 其他
 
@property (assign, nonatomic) CGFloat pullingPercent;
 
@property (assign, nonatomic, getter=isAutoChangeAlpha) BOOL autoChangeAlpha MJRefreshDeprecated("请使用automaticallyChangeAlpha属性");
 
@property (assign, nonatomic, getter=isAutomaticallyChangeAlpha) BOOL automaticallyChangeAlpha;
@end

@interface UILabel(MJRefresh)
+ (instancetype)mj_label;
- (CGFloat)mj_textWidth;
@end

@interface SARefreshComponent (ChainingGrammar)

#pragma mark - <<< 为 Swift 扩展链式语法 >>> -
- (instancetype)autoChangeTransparency:(BOOL)isAutoChange;
- (instancetype)afterBeginningAction:(MJRefreshComponentAction)action;
- (instancetype)endingAnimationBeginningAction:(MJRefreshComponentAction)action;
- (instancetype)afterEndingAction:(MJRefreshComponentAction)action;


- (instancetype)linkTo:(UIScrollView *)scrollView;

@end

NS_ASSUME_NONNULL_END
