
#import <UIKit/UIKit.h>

@class SASlideView;

@protocol DLSlideViewDataSource <NSObject>
- (NSInteger)numberOfControllersInDLSlideView:(SASlideView *)sender;
- (UIViewController *)SASlideView:(SASlideView *)sender controllerAt:(NSInteger)index;
@end

@protocol DLSlideViewDelegate <NSObject>
@optional
- (void)SASlideView:(SASlideView *)slide switchingFrom:(NSInteger)oldIndex to:(NSInteger)toIndex percent:(float)percent;
- (void)SASlideView:(SASlideView *)slide didSwitchTo:(NSInteger)index;
- (void)SASlideView:(SASlideView *)slide switchCanceled:(NSInteger)oldIndex;
@end

@interface SASlideView : UIView
@property(nonatomic, assign) NSInteger selectedIndex;
@property(nonatomic, weak) UIViewController *baseViewController;
@property(nonatomic, weak) id<DLSlideViewDelegate>delegate;
@property(nonatomic, weak) id<DLSlideViewDataSource>dataSource;
- (void)switchTo:(NSInteger)index;

@end
