
#import "SACommonWebViewController.h"
#import <WebKit/WebKit.h>
#import <WebViewJavascriptBridge/WKWebViewJavascriptBridge.h>
#import "SAScope.h"

@interface SACommonWebViewController ()<WKNavigationDelegate, WKUIDelegate>

@property (nonatomic, strong) WKWebView *webVBox;
@property (nonatomic, strong) WKWebViewJavascriptBridge *bridge;

@property (nonatomic,strong) CALayer *progresslayer;

@end

@implementation SACommonWebViewController

- (void)viewWillAppear:(BOOL)animated
{
    [super viewWillAppear:animated];
    
    [UIApplication sharedApplication].statusBarStyle = UIStatusBarStyleDefault;
    [self.navigationController setNavigationBarHidden:NO animated:YES];
}

- (void)viewDidLoad
{
    [super viewDidLoad];
    
    self.navigationItem.title = self.navTitle;
    [self.view addSubview:self.webVBox];
    
    self.progresslayer = [[CALayer alloc]init];
    self.progresslayer.frame = CGRectMake(0,0,UIScreenWidth*0.1, 2);
    self.progresslayer.backgroundColor = kPrimaryColor.CGColor;
    [self.view.layer addSublayer:self.progresslayer];

    [self addBackButton];
    
    [self setupBridge];
    
    
    NSURLRequest *request = [NSURLRequest requestWithURL:[NSURL URLWithString:self.url]];
    [self.webVBox loadRequest:request];
}

- (void)addBackButton
{
    UIButton *backButton = [[UIButton alloc] initWithFrame:CGRectMake(0, 0, 40, 40)];
    [backButton setBackgroundColor:[UIColor clearColor]];
    [backButton setImage:[[UIImage imageNamed:@"nav_back_gray"] imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal] forState:UIControlStateNormal];
    [backButton setImage:[[UIImage imageNamed:@"nav_back_gray"] imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal] forState:UIControlStateHighlighted];
    [backButton addTarget:self action:@selector(backAction) forControlEvents:UIControlEventTouchUpInside];
    backButton.contentHorizontalAlignment = UIControlContentHorizontalAlignmentLeft;
    self.navigationItem.leftBarButtonItem = [[UIBarButtonItem alloc] initWithCustomView:backButton];
    self.navigationItem.leftBarButtonItem.style = UIBarButtonItemStylePlain;
}

- (void)viewWillLayoutSubviews
{
    [super viewWillLayoutSubviews];
    self.webVBox.frame = self.view.bounds;
}

- (void)setupBridge
{
    _bridge = [WKWebViewJavascriptBridge bridgeForWebView:self.webVBox];
    [_bridge setWebViewDelegate:self];
    
    @weakify(self)
    [_bridge registerHandler:@"jsJumpAppHome" handler:^(id data, WVJBResponseCallback responseCallback) {
        @strongify(self)
        [self.navigationController popToRootViewControllerAnimated:YES];
        responseCallback(nil);
    }];
    
    [_bridge registerHandler:@"jsGoBack" handler:^(id data, WVJBResponseCallback responseCallback) {
        @strongify(self)
        [self.navigationController popViewControllerAnimated:YES];
        responseCallback(nil);
    }];
    
    [_bridge registerHandler:@"jsOpenBrowser" handler:^(id data, WVJBResponseCallback responseCallback) {
        [[UIApplication sharedApplication] openURL:[NSURL URLWithString:data] options:@{} completionHandler:nil];
    }];
    
    [_bridge registerHandler:@"jsIdentityBack" handler:^(id data, WVJBResponseCallback responseCallback) {
        @strongify(self)
        [self.navigationController popViewControllerAnimated:YES];
        NSLog(@"succ-----%@", data);
        BOOL succ = data;
        if(succ){
            [[SAVerifyListManager sharedInstance] handleVerifyJump];
        }
        responseCallback(nil);
    }];
}

- (void)backAction
{
    if (self.webVBox.canGoBack==YES)
    {
        [self.webVBox goBack];
    }
    else
    {
        
        [self.navigationController popViewControllerAnimated:YES];
    }
}

#pragma mark - WKNavigationDelegate
- (void)webView:(WKWebView *)webView didStartProvisionalNavigation:(WKNavigation *)navigation
{
    [SATool showActivityHUD:nil];
}
- (void)webView:(WKWebView *)webView didCommitNavigation:(WKNavigation *)navigation
{
}

- (void)webView:(WKWebView *)webView didFinishNavigation:(WKNavigation *)navigation
{
    
    
}
- (void)webView:(WKWebView *)webView didFailNavigation:(WKNavigation *)navigation withError:(NSError *)error
{
    [SATool hideHUDView];
}
- (void)webView:(WKWebView *)webView didReceiveServerRedirectForProvisionalNavigation:(WKNavigation *)navigation

{
    
}
- (void)webView:(WKWebView *)webView decidePolicyForNavigationResponse:(WKNavigationResponse *)navigationResponse decisionHandler:(void (^)(WKNavigationResponsePolicy))decisionHandler
{

    
    decisionHandler(WKNavigationResponsePolicyAllow);
}

- (void)webView:(WKWebView *)webView decidePolicyForNavigationAction:(WKNavigationAction *)navigationAction decisionHandler:(void (^)(WKNavigationActionPolicy))decisionHandler
{

    
    decisionHandler(WKNavigationActionPolicyAllow);
}

#pragma mark - getters
- (WKWebView *)webVBox
{
    if (_webVBox == nil) {
        WKWebViewConfiguration *config = [[WKWebViewConfiguration alloc] init];
        config.allowsInlineMediaPlayback = YES;

        _webVBox = [[WKWebView alloc] initWithFrame:self.view.bounds configuration:config];
        _webVBox.navigationDelegate = self;
        _webVBox.UIDelegate = self;
        _webVBox.opaque = NO;
        [_webVBox addObserver:self
                       forKeyPath:@"estimatedProgress"
                          options:NSKeyValueObservingOptionNew context:nil];
    }
    return _webVBox;
}

-(void)observeValueForKeyPath:(NSString *)keyPath
                     ofObject:(id)object
                       change:(NSDictionary<NSKeyValueChangeKey,id> *)change
                      context:(void *)context
{
     if ([keyPath isEqualToString:NSStringFromSelector(@selector(estimatedProgress))] && object == self.webVBox)
     {
         self.progresslayer.opacity = 1;
         float floatNum = [[change objectForKey:@"new"] floatValue];
         __weak __typeof(self)weakSelf = self;
         if (floatNum == 1) {
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [SATool hideHUDView];
                weakSelf.progresslayer.opacity = 0;
            });
         } else {
             dispatch_async(dispatch_get_main_queue(), ^{
                 weakSelf.progresslayer.opacity = 1;
                 self.progresslayer.frame = CGRectMake(0, 0,UIScreenWidth*floatNum, 2);
             });
         }
     }
}

@end

