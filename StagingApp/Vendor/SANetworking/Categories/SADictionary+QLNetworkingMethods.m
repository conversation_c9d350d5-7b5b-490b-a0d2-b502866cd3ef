
#import "SADictionary+QLNetworkingMethods.h"
#import "SAArray+QLNetworkingMethods.h"

@implementation NSDictionary (SANetworkingMethods)

 
- (NSString *)QL_urlParamsStringSignature:(BOOL)isForSignature
{
    NSArray *sortedArray = [self QL_transformedUrlParamsArraySignature:isForSignature];
    return [sortedArray QL_paramsString];
}

 
- (NSString *)QL_jsonString
{
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:self options:NSJSONWritingPrettyPrinted error:NULL];
    return [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
}

 
- (NSArray *)QL_transformedUrlParamsArraySignature:(BOOL)isForSignature
{
    NSMutableArray *result = [[NSMutableArray alloc] init];
    [self enumerateKeysAndObjectsUsingBlock:^(id key, id obj, BOOL *stop) {
        
        if (![obj isKindOfClass:[NSString class]])
        {
            obj = [NSString stringWithFormat:@"%@", obj];
        }
        if (!isForSignature)
        {
            obj = (NSString *)CFBridgingRelease(CFURLCreateStringByAddingPercentEscapes(NULL,  (CFStringRef)obj,  NULL,  (CFStringRef)@"!*'();:@&;=+$,/?%#[]",  kCFStringEncodingUTF8));
        }
        if ([obj length] > 0)
        {
            [result addObject:[NSString stringWithFormat:@"%@=%@", key, obj]];
        }
    }];
    
    NSArray *sortedResult = [result sortedArrayUsingSelector:@selector(compare:)];
    return sortedResult;
}

+ (NSDictionary *)QL_stringToDict:(NSString *)jsonString
{
    if (jsonString == nil) return nil;

    NSData *data = [jsonString dataUsingEncoding:NSUTF8StringEncoding];
    NSError *error;
    NSDictionary *dict = [NSJSONSerialization JSONObjectWithData:data options:NSJSONReadingMutableContainers error:&error];
    if (error)
    {
        return nil;
    }
    return dict;
}

@end
