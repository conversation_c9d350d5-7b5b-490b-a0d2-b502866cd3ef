
#import "SANetworkingConfigurationManager.h"
#import <AFNetworking/AFNetworking.h>

@implementation SANetworkingConfigurationManager

+ (instancetype)sharedInstance
{
    static SANetworkingConfigurationManager *sharedInstance;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        
        sharedInstance = [[SANetworkingConfigurationManager alloc] init];
        sharedInstance.shouldCache = YES;
        sharedInstance.serviceIsOnline = NO;
        sharedInstance.apiNetworkingTimeoutSeconds = 30.0f;
        sharedInstance.cacheOutdateTimeSeconds = 300;
        sharedInstance.cacheCountLimit = 1000;
        sharedInstance.shouldSetParamsInHTTPBodyButGET = YES;
        [[AFNetworkReachabilityManager sharedManager] startMonitoring];
    });
    return sharedInstance;
}

- (BOOL)isReachable
{
    if ([AFNetworkReachabilityManager sharedManager].networkReachabilityStatus == AFNetworkReachabilityStatusUnknown)
    {
        return YES;
    }
    else
    {
        return [[AFNetworkReachabilityManager sharedManager] isReachable];
    }
}

- (void)resetTimeOutToDefault {
    self.apiNetworkingTimeoutSeconds = 30.0f;
}

@end
