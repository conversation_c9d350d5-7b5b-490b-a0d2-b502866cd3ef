
#import "SATradeDetailCell.h"

@interface SATradeDetailCell ()

@property (nonatomic, strong) UILabel *titLab;
@property (nonatomic, strong) UILabel *valLab;
@property (nonatomic, strong) UIImageView *arrowImg;
@property (nonatomic, strong) UIView *lineV;

@end

@implementation SATradeDetailCell

#pragma mark - Life cycle

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier])
    {
        [self createUI];
        [self setConstraints];
    }
    
    return self;
}

- (void)createUI
{
    [self.contentView addSubview:self.titLab];
    [self.contentView addSubview:self.valLab];
    [self.contentView addSubview:self.arrowImg];
    [self.contentView addSubview:self.lineV];
}

- (void)setConstraints
{
    [self.titLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.centerY.equalTo(self.contentView);
        make.left.equalTo(self.contentView).offset(12);
    }];
    
    [self.valLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.centerY.equalTo(self.titLab);
        make.right.equalTo(self.arrowImg.mas_left).offset(-5);
    }];
    
    [self.arrowImg mas_makeConstraints:^(SAConstraintMaker *make) {
        make.right.equalTo(self.contentView).offset(-10);
        make.centerY.equalTo(self.titLab);
        make.width.height.mas_equalTo(15);
    }];
    
    [self.lineV mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.equalTo(self.titLab);
        make.bottom.equalTo(self.contentView).offset(-1);
        make.right.equalTo(self.contentView).offset(-10);
        make.height.mas_equalTo(0.7);
    }];
}

#pragma mark - setter

- (void)renderData:(NSDictionary *)dict
{
    self.titLab.text = dict[@"title"];
    self.valLab.text = dict[@"value"];
    
    if([dict[@"id"] isEqualToString:@"borrowPurpose"]){
        self.arrowImg.hidden = NO;
    }else{
        self.arrowImg.hidden = YES;
    }
}

-  (void)setShowLine:(BOOL)showLine
{
    self.lineV.hidden = showLine;
}

//-  (void)setFrame:(CGRect)frame
//{
//    frame.origin.x +=  12;
//    frame.size.width -= 24;
//    
//    [super setFrame:frame];
//}

#pragma mark - getter

- (UILabel *)titLab{
    FF_Fetch_UILable(_titLab, [UIColor colorWithHex:0x666666] , Font_XX6(13))
}

- (UILabel *)valLab{
    FF_Fetch_UILable_RightX(_valLab, [UIColor blackColor], BoldFont_XX6(13))
}

- (UIImageView *)arrowImg{
    FF_Fetch_UIImageViewWithImage(_arrowImg, [UIImage imageNamed:@"verify_list_arrow"])
}

- (UIView *)lineV{
    FF_Fetch_UIView(_lineV, [UIColor colorWithHex:0xf6f6f6])
}

@end
