
#import "SAViewController+MASAdditions.h"

#ifdef MAS_VIEW_CONTROLLER

@implementation MAS_VIEW_CONTROLLER (MASAdditions)

- (SAViewAttribute *)mas_topLayoutGuide {
    return [[SAViewAttribute alloc] initWithView:self.view item:self.topLayoutGuide layoutAttribute:NSLayoutAttributeBottom];
}
- (SAViewAttribute *)mas_topLayoutGuideTop {
    return [[SAViewAttribute alloc] initWithView:self.view item:self.topLayoutGuide layoutAttribute:NSLayoutAttributeTop];
}
- (SAViewAttribute *)mas_topLayoutGuideBottom {
    return [[SAViewAttribute alloc] initWithView:self.view item:self.topLayoutGuide layoutAttribute:NSLayoutAttributeBottom];
}

- (SAViewAttribute *)mas_bottomLayoutGuide {
    return [[SAViewAttribute alloc] initWithView:self.view item:self.bottomLayoutGuide layoutAttribute:NSLayoutAttributeTop];
}
- (SAViewAttribute *)mas_bottomLayoutGuideTop {
    return [[SAViewAttribute alloc] initWithView:self.view item:self.bottomLayoutGuide layoutAttribute:NSLayoutAttributeTop];
}
- (SAViewAttribute *)mas_bottomLayoutGuideBottom {
    return [[SAViewAttribute alloc] initWithView:self.view item:self.bottomLayoutGuide layoutAttribute:NSLayoutAttributeBottom];
}



@end

#endif
