
#import "SAVerifyBankCardViewController.h"
 
#import "SAGetSupportBankListApi.h"
#import "SASaveBindCardInfoApi.h"

#import "SAPersonalInfoModel.h"
#import "SAVerifyInfoCell.h"

#import "SAGetProtocolApiManager.h"
#import "SABankItemView.h"
#import "SAGetSupportBankListApi.h"
#import "SASaveBindCardInfoApi.h"
#import "SABindCardInfoModel.h"
#import "SABankBinListViewController.h"
#import <MJExtension.h>
#import "SAQueryCardBinApi.h"
#import "SAOrderConfirmApiManager.h"
#import "SAFaceBackAlert.h"

#define kBankItemHeight         54

@interface SAVerifyBankCardViewController ()<QLAPIManagerCallBackDelegate, QLAPIManagerParamSource, NPRequestMethodProtocol, UITextViewDelegate, NPRequestMethodProtocol, NPBankItemViewDelegate, UIGestureRecognizerDelegate>

@property (nonatomic, strong) UIView *footerView;
@property (nonatomic, strong) SAGradientButton *submitBtn;

@property (nonatomic, assign) BOOL finished;

@property (nonatomic, strong) SAGetSupportBankListApi  *supportBankApi;

@property (nonatomic, strong) SABindCardInfoModel *detailModel;
@property (nonatomic, assign) BOOL needHolderName;
@property (nonatomic, strong) NSArray *nameList;


@property (nonatomic, strong) SASaveBindCardInfoApi *saveBankApiMgr;
@property (nonatomic, strong) NSMutableDictionary *saveDict;

@property (nonatomic, assign) BOOL isAgree;
@property (nonatomic, strong) UIView *bottomView;
@property (nonatomic, strong) UIImageView *agreeImg;
@property (nonatomic, strong) SAGetProtocolApiManager *protocolManager;
@property (nonatomic, strong) NSString *protocolKey;

@property (nonatomic, strong) UIView *bgView;
@property (nonatomic, strong) UILabel *titLab;
@property (nonatomic, strong) UILabel *tipsLab;
@property (nonatomic, strong) SABankItemView *bankNoView;
@property (nonatomic, strong) SABankItemView *bankNameView;
@property (nonatomic, strong) SABankItemView *holderNameView;
@property (nonatomic, strong) SABankItemView *mobileView;
@property (nonatomic, strong) SABankItemView *codeView;

@property (nonatomic, strong) SAQueryCardBinApi *cardBinApi;
@property (nonatomic, strong) SAOrderConfirmApiManager *orderConfirmApiMgr;

@property (nonatomic, strong) UIView *topTipsV;

@end

@implementation SAVerifyBankCardViewController

- (void)viewWillAppear:(BOOL)animated 
{
    [super viewWillAppear:animated];
}

- (void)viewDidAppear:(BOOL)animated
{
    [super viewWillAppear:animated];
    
    if(self.isFirstBind){
    }
}

- (void)viewWillDisappear:(BOOL)animated
{
    [super viewWillDisappear:animated];
    
    self.navigationController.interactivePopGestureRecognizer.enabled = YES;
}

- (void)viewDidLoad 
{
    [super viewDidLoad];
    
    self.navigationItem.title = self.isFirstBind ? @"添加银行卡" : @"绑定银行卡";
    [self.submitBtn setTitle:@"已完成，下一步" forState:UIControlStateNormal];
    
    self.navigationController.interactivePopGestureRecognizer.delegate = self;

    self.view.backgroundColor = [UIColor colorWithHex:0xf6f6f6];
    self.isAgree = YES;
    
    
    [self showActivityHUD:nil];
    [self.supportBankApi loadData];
}

- (void)creatUI 
{
    [self.view addSubview:self.topTipsV];
    [self.topTipsV mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.right.top.equalTo(self.view);
        make.height.mas_equalTo(54);
    }];

    [self.view addSubview:self.bgView];
    
    [self.bgView addSubview:self.titLab];
    [self.bgView addSubview:self.tipsLab];
    [self.bgView addSubview:self.bankNoView];
    [self.bgView addSubview:self.bankNameView];
    [self.bgView addSubview:self.holderNameView];
    [self.bgView addSubview:self.mobileView];
    [self.bgView addSubview:self.codeView];

    [self.view addSubview:self.footerView];
    [self.footerView mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.centerX.equalTo(self.view);
        make.top.equalTo(self.bgView.mas_bottom).offset(20);
        make.height.mas_equalTo(90);
    }];
}

- (void)createFrames
{
    [self.bgView mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.equalTo(self.view).offset(10);
        make.centerX.equalTo(self.view);
        make.top.equalTo(self.topTipsV.mas_bottom).offset(12);
        make.height.mas_equalTo((self.needHolderName ? 6 : 5) * kBankItemHeight + 15);
    }];
    self.bgView.layer.cornerRadius = 10;
    self.bgView.layer.masksToBounds = YES;
    
    [self.titLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.top.equalTo(self.bgView).offset(10);
        make.left.equalTo(self.bgView).offset(20);
    }];
    [self.tipsLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.equalTo(self.titLab);
        make.top.equalTo(self.titLab.mas_bottom).offset(5);
    }];
    self.titLab.text = @"输入卡号添加";
    self.tipsLab.text = @"绑定本人一类借记卡";
    
    [self.bankNoView mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.right.equalTo(self.bgView);
        make.height.mas_equalTo(kBankItemHeight);
        make.top.equalTo(self.tipsLab.mas_bottom).offset(5);
    }];
    self.bankNoView.titleText = @"卡号";
    self.bankNoView.fieldHolder = @"请输入本人银行卡号";
    self.bankNoView.isArrow = NO;
    self.bankNoView.isNumber = YES;

    
    [self.bankNameView mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.right.height.equalTo(self.bankNoView);
        make.top.equalTo(self.bankNoView.mas_bottom);
    }];
    self.bankNameView.titleText = @"银行";
    self.bankNameView.fieldHolder = @"请选择银行";
    self.bankNameView.isArrow = YES;
    UITapGestureRecognizer *tapName = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(selectBankName)];
    self.bankNameView.userInteractionEnabled = YES;
    [self.bankNameView addGestureRecognizer:tapName];
    
    if(self.needHolderName){
        [self.holderNameView mas_makeConstraints:^(SAConstraintMaker *make) {
            make.left.right.height.equalTo(self.bankNoView);
            make.top.equalTo(self.bankNameView.mas_bottom);
        }];
        self.holderNameView.titleText = @"姓名";
        self.holderNameView.fieldHolder = @"请输入姓名";
        self.holderNameView.isArrow = NO;

    }
    
    [self.mobileView mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.right.height.equalTo(self.bankNoView);
        make.top.equalTo((self.needHolderName ? self.holderNameView : self.bankNameView).mas_bottom);
    }];
    self.mobileView.titleText = @"手机号";
    self.mobileView.fieldHolder = @"请输入预留手机号";
    self.mobileView.isArrow = NO;
    self.mobileView.isNumber = YES;
    self.mobileView.digitsNum = 11;

    
    [self.codeView mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.right.height.equalTo(self.bankNoView);
        make.top.equalTo(self.mobileView.mas_bottom);
    }];
    self.codeView.titleText = @"验证码";
    self.codeView.fieldHolder = @"请输入验证码";
    self.codeView.isVCode = YES;
    self.codeView.isNumber = YES;
    self.codeView.digitsNum = 6;

    self.bankNoView.delegate = self;
    self.holderNameView.delegate = self;
    self.mobileView.delegate = self;
    self.codeView.delegate = self;

}

#pragma mark - UIGestureRecognizerDelegate

- (BOOL)gestureRecognizerShouldBegin:(UIGestureRecognizer *)gestureRecognizer
{
    if(gestureRecognizer == self.navigationController.interactivePopGestureRecognizer && self.isFirstBind){
        
        [SAFaceBackAlert showFaceAlertSureAction:^{
            [self popSelf];
        }];
        return NO;
    }
    return YES;
}

#pragma mark - 选择所属银行

- (void)selectBankName
{
    SABankBinListViewController *vc = [SABankBinListViewController new];
    vc.nameList = self.nameList;
    vc.popToVcClassName = @"SAVerifyBankCardViewController";
    @weakify(self)
    vc.selectBlock = ^(SABankNameModel *nameModel) {
        @strongify(self)
        self.bankNameView.itemValue = nameModel.bankName;
        self.bankNameView.itemKey = nameModel.bankCode;
        
        self.codeView.bankName = nameModel.bankName;
        self.codeView.bankCode = nameModel.bankCode;
    };
    [self.navigationController pushViewController:vc animated:YES];
}

#pragma mark - NPBankItemViewDelegate

- (void)bankItemEndEditing:(SABankItemView *)itemView
{
    if (itemView == self.bankNoView) {
        
        self.codeView.bankNo = itemView.itemValue;
    }
    if (itemView == self.mobileView) {
        
        self.codeView.mobile = itemView.itemValue;
    }
    
    if(itemView == self.bankNoView)
    {
        if([NSString judgeStringExist:self.bankNoView.itemValue]){
            [self.cardBinApi loadData];
        }
    }
}

#pragma mark - TextView

-(BOOL)textView:(UITextView *)textView shouldInteractWithURL:(nonnull NSURL *)URL inRange:(NSRange)characterRange interaction:(UITextItemInteraction)interaction{

    if ([URL.scheme isEqualToString:@"wtkk"])
    {
        NSLog(@"点击了委托扣款");
        self.protocolKey = PRIVACY_POLICY_ZHWTKKSQS;
        [self showActivityHUD:nil];
        [self.protocolManager loadData];
    }

    return YES;
}

- (void)changeAgree
{
    self.isAgree = !self.isAgree;
    if(!self.isAgree){
        self.agreeImg.image = [UIImage imageNamed:@"verify_check_nor"];
    } else {
        self.agreeImg.image = [UIImage customImageNamed:@"verify_check_sel"];
    }
}

#pragma mark - 点击左上角返回

- (void)backAction:(id)sender
{
    if (!self.isFirstBind) {
        [self popSelf];
        return;
    }
    
    if(self.finished){
        [self popSelf];
        return;
    }
    
    [SAFaceBackAlert showFaceAlertSureAction:^{
        [self popSelf];
    }];
}

#pragma mark QLAPIManagerCallBackDelegate

- (void)managerCallAPIDidSuccess:(SABaseManager *)manager {
    [self hideHUDView];
    if (manager == self.supportBankApi) 
    {
        NSDictionary *dataDict =[manager fetchDataWithReformer:self.supportBankApi];
        self.nameList = [SABankNameModel mj_objectArrayWithKeyValuesArray: dataDict[@"bankRoList"]];
        
        [self creatUI];
        [self createFrames];
        
    }
    else if (manager == self.saveBankApiMgr) 
    {
        NSDictionary *dataDict = [manager fetchDataWithReformer:self.saveBankApiMgr];
        
        if(dataDict && [dataDict isKindOfClass:[NSDictionary class]]){
            
            NSString *errorMsg = dataDict[@"errorMsg"];
            if ([NSString judgeStringExist:errorMsg]) {
                [self textStateHUD:errorMsg];
            }else{
                [self textStateHUD:dataDict[@"msg"]];
                if(self.isFirstBind){
                    [self showActivityHUD:nil];
                    [self.orderConfirmApiMgr loadData];
                }else{
                    [self popSelf];
                }
            }
        }
    }
    
    if(manager == self.protocolManager)
    {
        NSDictionary *dataDict = [manager fetchDataWithReformer:self.protocolManager];
        SAProtocolViewController *vc = [SAProtocolViewController new];
        vc.htmlStr = dataDict[@"text"];
        vc.navTitle = dataDict[@"title"];
        vc.companyName = dataDict[@"companyName"];
        vc.appName = dataDict[@"appName"];
        [self presentViewController:[[UINavigationController alloc] initWithRootViewController:vc] animated:YES completion:nil];
    }
    
    if(manager == self.cardBinApi)
    {
        NSDictionary *dataDict = [manager fetchDataWithReformer:self.cardBinApi];
        
        if(dataDict && [dataDict isKindOfClass:[NSDictionary class]]){
            self.bankNameView.itemValue = [NSString judgeStringExist:dataDict[@"bankName"]] ? dataDict[@"bankName"] : @"";
            self.bankNameView.itemKey = [NSString judgeStringExist:dataDict[@"bankCode"]] ? dataDict[@"bankCode"] : @"";
            
            self.codeView.bankName = [NSString judgeStringExist:dataDict[@"bankName"]] ? dataDict[@"bankName"] : @"";
            self.codeView.bankCode = [NSString judgeStringExist:dataDict[@"bankCode"]] ? dataDict[@"bankCode"] : @"";
        }
    }
    if(manager ==  self.orderConfirmApiMgr)
    {
        [self popSelf];
    }
}

- (void)managerCallAPIDidFailed:(SABaseManager *)manager {
    [self hideHUDView];
    NSString *failMsg  = [SAResponseHandle netWorkErrorMsg:[manager fetchDataWithReformer:nil] andErrorType:manager.errorType];
    [self textStateHUD:failMsg];
}

#pragma mark QLAPIManagerParamSource

- (NSDictionary *)paramsForApi:(SABaseManager *)manager 
{
    if (manager == self.cardBinApi) {
        return @{@"cardNo": self.bankNoView.itemValue};
    }
    if (manager == self.saveBankApiMgr) {
        return self.saveDict;
    }
    if (manager == self.orderConfirmApiMgr){
        return @{@"productCode": @""};
    }
    return [NSDictionary dictionary];
}

- (NSString *)methodForApi:(SABaseManager *)manager
{
    if (manager == self.protocolManager) {
        return [NSString stringWithFormat:@"1a99fb9ad8534512af0ede2979dfd68f/%@", self.protocolKey];
    }
    return @"";
}

#pragma mark - Actions

- (void)submitAction
{
    if(!self.isAgree){
        [self textStateHUD:@"阅读并同意协议"];
        return;
    }
    if(![NSString judgeStringExist:self.bankNoView.itemValue]){
        [self textStateHUD:@"请先输入您的银行卡号"];
        return;
    }
    if(![NSString judgeStringExist:self.bankNameView.itemValue]){
        [self textStateHUD:@"请先选择所属银行"];
        return;
    }
    if(self.needHolderName && ![NSString judgeStringExist:self.holderNameView.itemValue]){
        [self textStateHUD:@"请先输入您的姓名"];
        return;
    }
    if(![NSString judgeStringExist:self.mobileView.itemValue]){
        [self textStateHUD:@"请先输入预留手机号"];
        return;
    }
    if(![NSString judgeStringExist:self.codeView.itemValue]){
        [self textStateHUD:@"请先输入验证码"];
        return;
    }
    
    self.saveDict = [@{
                        @"code": self.bankNameView.itemKey,
                        @"bankName": self.bankNameView.itemValue,
                        @"cardNo": self.bankNoView.itemValue,
                        @"mobile": self.mobileView.itemValue,
                        @"smsCode": self.codeView.itemValue,
                        @"flowId": self.codeView.flowId,
                        @"orderType": self.orderType ? self.orderType : @1
                    } mutableCopy];
    if(self.needHolderName){
        [self.saveDict setObject:self.holderNameView.itemValue forKey:@"name"];
    }
    
    [self showActivityHUD:nil];
    [self.saveBankApiMgr loadData];
}

#pragma mark - getters

- (UIView *)bgView{
    FF_Fetch_UIView(_bgView, [UIColor whiteColor])
}

- (SABankItemView *)bankNoView{
    if(_bankNoView == nil){
        _bankNoView = [SABankItemView new];
    }
    return _bankNoView;
}

- (SABankItemView *)bankNameView{
    if(_bankNameView == nil){
        _bankNameView = [SABankItemView new];
    }
    return _bankNameView;
}

- (SABankItemView *)holderNameView{
    if(_holderNameView == nil){
        _holderNameView = [SABankItemView new];
    }
    return _holderNameView;
}

- (SABankItemView *)mobileView{
    if(_mobileView == nil){
        _mobileView = [SABankItemView new];
    }
    return _mobileView;
}

- (SABankItemView *)codeView{
    if(_codeView == nil){
        _codeView = [SABankItemView new];
    }
    return _codeView;
}

- (UIView *)footerView {
    if (_footerView == nil) {
        _footerView = [[UIView alloc] init];
                
        [_footerView addSubview:self.submitBtn];
        [self.submitBtn mas_makeConstraints:^(SAConstraintMaker *make) {
            make.centerX.equalTo(_footerView);
            make.left.equalTo(_footerView).offset(25);
            make.height.mas_equalTo(44);
            make.top.equalTo(_footerView).offset(10);
        }];
        
    }
    return _footerView;
}

- (SAGradientButton *)submitBtn{
    if (_submitBtn == nil) {
        _submitBtn = [SAGradientButton buttonWithType:UIButtonTypeCustom];
        [_submitBtn gradientBackgroundColors:@[kButtonStartColor, kButtonEndColor] locations:nil startPoint:CGPointMake(0, 0.5) endPoint:CGPointMake(1, 0.5)];
        _submitBtn.backgroundColor = kPrimaryColor;
        [_submitBtn setTitleColor:kHomeTextColor forState:UIControlStateNormal];
        _submitBtn.layer.cornerRadius  = 7;
        _submitBtn.layer.masksToBounds = YES;
        [_submitBtn addTarget:self action:@selector(submitAction) forControlEvents:UIControlEventTouchUpInside];
    }
    return _submitBtn;
}

- (SAGetSupportBankListApi *)supportBankApi {
    if (_supportBankApi == nil) {
        _supportBankApi = [[SAGetSupportBankListApi alloc] init];
        _supportBankApi.delegate = self;
        _supportBankApi.paramSource = self;
    }
    return _supportBankApi;
}

- (SASaveBindCardInfoApi *)saveBankApiMgr {
    if (_saveBankApiMgr == nil) {
        _saveBankApiMgr = [[SASaveBindCardInfoApi alloc] init];
        _saveBankApiMgr.delegate = self;
        _saveBankApiMgr.paramSource = self;
    }
    return _saveBankApiMgr;
}

- (NSMutableDictionary *)saveDict {
    if (_saveDict == nil) {
        _saveDict = [NSMutableDictionary dictionary];
    }
    return _saveDict;
}

- (SAGetProtocolApiManager *)protocolManager {
    if (_protocolManager == nil) {
        _protocolManager = [[SAGetProtocolApiManager alloc] init];
        _protocolManager.delegate = self;
        _protocolManager.paramSource = self;
        _protocolManager.methodSource = self;
    }
    return _protocolManager;
}


- (SAQueryCardBinApi *)cardBinApi {
    if (_cardBinApi == nil) {
        _cardBinApi = [[SAQueryCardBinApi alloc] init];
        _cardBinApi.delegate = self;
        _cardBinApi.paramSource = self;
    }
    return _cardBinApi;
}

- (SAOrderConfirmApiManager *)orderConfirmApiMgr {
    if (_orderConfirmApiMgr == nil) {
        _orderConfirmApiMgr = [[SAOrderConfirmApiManager alloc] init];
        _orderConfirmApiMgr.delegate = self;
        _orderConfirmApiMgr.paramSource = self;
    }
    return _orderConfirmApiMgr;
}

- (UILabel *)titLab{
    FF_Fetch_UILable(_titLab, [UIColor blackColor], Font_XX6(16))
}

- (UILabel *)tipsLab{
    FF_Fetch_UILable(_tipsLab, [UIColor colorWithHex:0x999999], Font_XX6(12))
}

- (UIView *)topTipsV{
    if (_topTipsV == nil) {
        _topTipsV = [UIView new];
        _topTipsV.backgroundColor = [UIColor colorWithHex:0xFFF6F1];
        
        UIImageView *icon = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"warning"]];
        [_topTipsV addSubview:icon];
        
        UILabel *lab = [UILabel new];
        lab.textColor = [UIColor colorWithHex:0xEA9947];
        lab.text = @"仅用于身份验证、额度评估、信贷相关业务审核使用，您的信息将严格保密";
        lab.font = Font_XX6(11);
        lab.numberOfLines = 0;
        [_topTipsV addSubview:lab];
        
        [icon mas_makeConstraints:^(SAConstraintMaker *make) {
            make.top.left.equalTo(_topTipsV).offset(12);
            make.width.height.mas_equalTo(16);
        }];
        [lab mas_makeConstraints:^(SAConstraintMaker *make) {
            make.left.equalTo(icon.mas_right).offset(4);
            make.top.equalTo(_topTipsV).offset(12);
            make.right.equalTo(_topTipsV).offset(-12);
        }];
    }
    return _topTipsV;
}
@end
