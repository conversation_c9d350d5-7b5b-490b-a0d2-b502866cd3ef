
#import "SAFileUploadApiManager.h"

@implementation SAFileUploadApiManager

#pragma mark - life cycle

- (instancetype)init
{
    if (self = [super init])
    {
        self.validator = self;
    }
    
    return self;
}

#pragma mark - QLAPIManager

- (NSString *)methodName
{
    return @"2dc611eae1de4628a0f91200e0dc64b2";
}

- (NSString *)serviceType
{
    return kNPBasicServiceKey;
}

- (QLAPIManagerRequestType)requestType
{
    return QLAPIManagerRequestTypeUpload;
}

- (BOOL)shouldCache
{
    return NO;
}

- (NSString *)requestMimeType
{
    return @"image/jpeg";
}

- (NSString *)fileName
{
    return @"name";
}

#pragma mark - QLAPIManagerDataReformer

- (id)manager:(SABaseManager *)manager reformData:(NSDictionary *)data
{
    if ([manager isKindOfClass:[self class]])
    {
        return [data objectForKey:@"data"];;
    }
    return nil;
}

#pragma mark - QLAPIManagerValidator

- (BOOL)manager:(SABaseManager *)manager isCorrectWithParamsData:(NSDictionary *)data
{
    return YES;
}

- (BOOL)manager:(SABaseManager *)manager isCorrectWithCallBackData:(id)data
{
    return [SAResponseHandle processRespDictCodeZero:data];
}

@end
