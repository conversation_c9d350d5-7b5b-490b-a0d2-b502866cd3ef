
#import "SAHomeReviewingCell.h"
#import "SATabBarViewController.h"
#import "SAVerifyListManager.h"

@interface SAHomeReviewingCell()

@property (nonatomic, strong) UILabel *titleLab;
@property (nonatomic, strong) UILabel *amountLab;
@property (nonatomic, strong) UILabel *statusLab;
@property (nonatomic, strong) UILabel *tipsLab;
@property (nonatomic, strong) UILabel *stepTitLab;
@property (nonatomic, strong) UIView *stepBoxV;

@end

@implementation SAHomeReviewingCell

#pragma mark - Life cycle

- (void)renderCoreBg
{
    [self addCoreSubviews];
    [self setCoreConstraints];
    
    [self.coreBg mas_updateConstraints:^(SAConstraintMaker *make) {
        make.height.mas_equalTo(380);
    }];
    
    self.titleLab.text = @"授信额度(元)";
    self.amountLab.text = self.model.maxAmount;
    self.statusLab.text = self.model.tips;
    self.tipsLab.text = self.model.bigTips;
    self.stepTitLab.text = @"贷款步骤";
    
    
}

- (void)addCoreSubviews
{
    [self.coreBg addSubview:self.titleLab];
    [self.coreBg addSubview:self.amountLab];
    [self.coreBg addSubview:self.statusLab];
    [self.coreBg addSubview:self.tipsLab];
    [self.coreBg addSubview:self.stepTitLab];
    [self.coreBg addSubview:self.stepBoxV];
}

- (void)setCoreConstraints
{
    CGFloat verGap = 15;
    
    [self.titleLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.top.equalTo(self.coreBg).offset(20);
        make.centerX.equalTo(self.coreBg);
    }];
    [self.amountLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.centerX.equalTo(self.coreBg);
        make.top.equalTo(self.titleLab.mas_bottom).offset(2);
    }];
    
    [self.statusLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.centerX.equalTo(self.coreBg);
        make.top.equalTo(self.amountLab.mas_bottom).offset(30);
    }];
    
    [self.tipsLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.centerX.equalTo(self.coreBg);
        make.top.equalTo(self.statusLab.mas_bottom).offset(verGap);
        make.left.equalTo(self.coreBg).offset(30);
    }];
    
    [self.stepTitLab mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.equalTo(self.coreBg).offset(20);
        make.top.equalTo(self.tipsLab.mas_bottom).offset(20);
    }];
    
    [self.stepBoxV mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.equalTo(self.stepTitLab);
        make.top.equalTo(self.stepTitLab.mas_bottom).offset(5);
    }];
}

#pragma mark - action

- (void)onClickAction
{
    
}

#pragma mark - setter


#pragma mark - getter

- (UILabel *)titleLab{
    FF_Fetch_UILable_CenterX(_titleLab, [UIColor blackColor], Font_XX6(15))
}

- (UILabel *)amountLab{
    FF_Fetch_UILable_CenterX(_amountLab, kHomeTextColor, BoldFont_XX6(40))
}

- (UILabel *)statusLab{
    FF_Fetch_UILable_CenterX(_statusLab, kHomeTextColor, BoldFont_XX6(18))
}

- (UILabel *)tipsLab{
    FF_Fetch_UILable_NumZERO_CenterX(_tipsLab, kHomeTextColor, Font_XX6(13), 0)
}

- (UILabel *)stepTitLab{
    FF_Fetch_UILable(_stepTitLab, [UIColor colorWithHex:0x333333], Font_XX6(15))
}

- (UIView *)stepBoxV{
    if(_stepBoxV == nil){
        _stepBoxV = [UIView new];
        
        __block UIView *lastV = nil;
        [self.model.borrowStepVOList enumerateObjectsUsingBlock:^(NPHomeStepModel *item, NSUInteger idx, BOOL * _Nonnull stop) {
            
            UIView *view = [UIView new];
            [_stepBoxV addSubview:view];
            
            UIImageView *icon = [[UIImageView alloc] init];
            icon.image = item.finished ? [UIImage customImageNamed:@"verify_check_sel"] : [UIImage imageNamed:@"verify_check_nor"];
            [view addSubview:icon];
            
            UILabel *textLab = [UILabel new];
            textLab.textColor = kHomeTextColor;
            textLab.font = Font_XX6(11);
            textLab.text = [NSString stringWithFormat:@"%ld.%@", (idx+1), item.content];
            [view addSubview:textLab];
            
            [view mas_makeConstraints:^(SAConstraintMaker *make) {
                make.left.equalTo(_stepBoxV);
                make.height.mas_equalTo(30);
                make.top.equalTo(_stepBoxV).offset(idx*40);
            }];
            [icon mas_makeConstraints:^(SAConstraintMaker *make) {
                make.width.height.mas_equalTo(15);
                make.left.equalTo(view);
                make.centerY.equalTo(view);
            }];
            [textLab mas_makeConstraints:^(SAConstraintMaker *make) {
                make.centerY.equalTo(view);
                make.left.equalTo(icon.mas_right).offset(20);
            }];
            
            
            if(lastV){
                UIView *line = [UIView new];
                line.backgroundColor = [UIColor colorWithHex:0xcdcdcd];
                [_stepBoxV addSubview:line];
                [line mas_makeConstraints:^(SAConstraintMaker *make) {
                    make.width.mas_equalTo(1);
                    make.centerX.equalTo(icon);
                    make.top.equalTo(lastV.mas_bottom).offset(-2);
                    make.height.mas_equalTo(16);
                }];
            }
            
            lastV = view;
        }];
    }
    
    return _stepBoxV;
}

@end
