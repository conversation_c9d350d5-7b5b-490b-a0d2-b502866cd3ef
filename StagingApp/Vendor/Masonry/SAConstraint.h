
#import "MASUtilities.h"

 
@interface SAConstraint : NSObject


 
- (SAConstraint * (^)(MASEdgeInsets insets))insets;

 
- (SAConstraint * (^)(CGFloat inset))inset;

 
- (SAConstraint * (^)(CGSize offset))sizeOffset;

 
- (SAConstraint * (^)(CGPoint offset))centerOffset;

 
- (SAConstraint * (^)(CGFloat offset))offset;

 
- (SAConstraint * (^)(NSValue *value))valueOffset;

 
- (SAConstraint * (^)(CGFloat multiplier))multipliedBy;

 
- (SAConstraint * (^)(CGFloat divider))dividedBy;

 
- (SAConstraint * (^)(MASLayoutPriority priority))priority;

 
- (SAConstraint * (^)(void))priorityLow;

 
- (SAConstraint * (^)(void))priorityMedium;

 
- (SAConstraint * (^)(void))priorityHigh;

 
- (SAConstraint * (^)(id attr))equalTo;

 
- (SAConstraint * (^)(id attr))greaterThanOrEqualTo;

 
- (SAConstraint * (^)(id attr))lessThanOrEqualTo;

 
- (SAConstraint *)with;

 
- (SAConstraint *)and;

 
- (SAConstraint *)left;
- (SAConstraint *)top;
- (SAConstraint *)right;
- (SAConstraint *)bottom;
- (SAConstraint *)leading;
- (SAConstraint *)trailing;
- (SAConstraint *)width;
- (SAConstraint *)height;
- (SAConstraint *)centerX;
- (SAConstraint *)centerY;
- (SAConstraint *)baseline;

#if (__IPHONE_OS_VERSION_MIN_REQUIRED >= 80000) || (__TV_OS_VERSION_MIN_REQUIRED >= 9000) || (__MAC_OS_X_VERSION_MIN_REQUIRED >= 101100)

- (SAConstraint *)firstBaseline;
- (SAConstraint *)lastBaseline;

#endif

#if (__IPHONE_OS_VERSION_MIN_REQUIRED >= 80000) || (__TV_OS_VERSION_MIN_REQUIRED >= 9000)

- (SAConstraint *)leftMargin;
- (SAConstraint *)rightMargin;
- (SAConstraint *)topMargin;
- (SAConstraint *)bottomMargin;
- (SAConstraint *)leadingMargin;
- (SAConstraint *)trailingMargin;
- (SAConstraint *)centerXWithinMargins;
- (SAConstraint *)centerYWithinMargins;

#endif


 
- (SAConstraint * (^)(id key))key;


 
- (void)setInsets:(MASEdgeInsets)insets;

 
- (void)setInset:(CGFloat)inset;

 
- (void)setSizeOffset:(CGSize)sizeOffset;

 
- (void)setCenterOffset:(CGPoint)centerOffset;

 
- (void)setOffset:(CGFloat)offset;



#if TARGET_OS_MAC && !(TARGET_OS_IPHONE || TARGET_OS_TV)
 
@property (nonatomic, copy, readonly) SAConstraint *animator;
#endif

 
- (void)activate;

 
- (void)deactivate;

 
- (void)install;

 
- (void)uninstall;

@end


 
#define mas_equalTo(...)                 equalTo(MASBoxValue((__VA_ARGS__)))
#define mas_greaterThanOrEqualTo(...)    greaterThanOrEqualTo(MASBoxValue((__VA_ARGS__)))
#define mas_lessThanOrEqualTo(...)       lessThanOrEqualTo(MASBoxValue((__VA_ARGS__)))

#define mas_offset(...)                  valueOffset(MASBoxValue((__VA_ARGS__)))


#ifdef MAS_SHORTHAND_GLOBALS

#define equalTo(...)                     mas_equalTo(__VA_ARGS__)
#define greaterThanOrEqualTo(...)        mas_greaterThanOrEqualTo(__VA_ARGS__)
#define lessThanOrEqualTo(...)           mas_lessThanOrEqualTo(__VA_ARGS__)

#define offset(...)                      mas_offset(__VA_ARGS__)

#endif


@interface SAConstraint (AutoboxingSupport)

 
- (SAConstraint * (^)(id attr))mas_equalTo;
- (SAConstraint * (^)(id attr))mas_greaterThanOrEqualTo;
- (SAConstraint * (^)(id attr))mas_lessThanOrEqualTo;

 
- (SAConstraint * (^)(id offset))mas_offset;

@end
