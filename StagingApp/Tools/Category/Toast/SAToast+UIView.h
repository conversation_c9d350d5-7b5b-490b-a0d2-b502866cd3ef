 

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

#define kToastViewTag 35426

@interface UIView (Toast)

- (void)makeToast:(NSString *)message;
- (void)makeToast:(NSString *)message duration:(CGFloat)interval position:(id)position;
- (void)makeToast:(NSString *)message duration:(CGFloat)interval position:(id)position title:(NSString *)title;
- (void)makeToast:(NSString *)message duration:(CGFloat)interval position:(id)position title:(NSString *)title image:(UIImage *)image;
- (void)makeToast:(NSString *)message duration:(CGFloat)interval position:(id)position image:(UIImage *)image;
- (void)makeToast:(NSString *)message duration:(CGFloat)interval position:(id)position tag:(int)tag;
- (void)makeToast:(NSString *)message duration:(CGFloat)interval position:(id)position title:(NSString *)title tag:(int)tag;

- (void)makeToastActivity;
- (void)makeToastActivity:(id)position;
- (void)hideToastActivity;

- (void)showToast:(UIView *)toast;
- (void)showToast:(UIView *)toast duration:(CGFloat)interval position:(id)point;
- (void)showToast:(UIView *)toast duration:(CGFloat)interval position:(id)point tag:(int)tag;

@end
