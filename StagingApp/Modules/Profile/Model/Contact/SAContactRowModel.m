
#import "SAContactRowModel.h"
#import <MJExtension.h>


@interface SAContactRowModel ()

@property (nonatomic, assign)CGFloat titleHeig;

@end

@implementation SAContactRowModel

+ (void)load {
    
    [SAContactRowModel mj_setupObjectClassInArray:^NSDictionary *{
        return @{
                 @"selectVo" : @"SAContactSelectModel"
                 };
    }];
}

- (CGFloat)titleWidt {
    if (_titleWidt) {
        return _titleWidt;
    }
    
    
    CGFloat maxWid = XX_6(145);
    CGSize maxSize = CGSizeMake(maxWid, MAXFLOAT);
    CGSize realSize = [self.paramName boundingRectWithSize:maxSize options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:[UIFont fontSizeOfXX_6:16]} context:nil].size;
    
    self.titleHeig = realSize.height;
    _titleWidt = realSize.width;
    
    return _titleWidt + 10;
}

- (CGFloat)cellHeight {
    if (_cellHeight) {
        return _cellHeight;
    }
    
    
    CGFloat maxWid = 0;
    
    if (self.paramType == 2) {      
        maxWid = UIScreenWidth - XX_6(80) - XX_6(30) - XX_6(5) - self.titleWidt;
    } else if (self.paramType == 3) {       
        maxWid = UIScreenWidth - XX_6(80) - XX_6(5) - self.titleWidt;
    }
    
    CGSize maxSize = CGSizeMake(maxWid, MAXFLOAT);
    CGSize realSize = [self.inputValue boundingRectWithSize:maxSize options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:[UIFont fontSizeOfXX_6:14]} context:nil].size;
    CGFloat heigt = self.titleHeig > realSize.height ? self.titleHeig : realSize.height;
    _cellHeight = heigt;
    if (heigt < XX_6(50)) {
        _cellHeight = XX_6(50);
    }
    return _cellHeight;
}


@end
