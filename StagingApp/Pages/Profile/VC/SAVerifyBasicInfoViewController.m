
#import "SAVerifyBasicInfoViewController.h"
 

#import "SAGetPersonalInfoApiManager.h"
#import "SAPersonalVerifyModel.h"

#import "SAVerifyInfoCell.h"

#import "SAManager.h"
#import "SAFaceBackAlert.h"
#import "SASavePersonalInfoApiManager.h"
#import "SAGetCitiesListApiManager.h"
#import "SAGetProtocolApiManager.h"

@interface SAVerifyBasicInfoViewController ()<QLAPIManagerCallBackDelegate, QLAPIManagerParamSource, UITableViewDelegate, UITableViewDataSource, UITextViewDelegate, NPRequestMethodProtocol, UIGestureRecognizerDelegate>

@property (nonatomic, strong) UIView *headerView;
@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) UIView *footerView;

@property (nonatomic, strong)SAGetPersonalInfoApiManager *getInfoApiMgr;
@property (nonatomic, strong) NSMutableArray <SAPersonalVerifyModel *>*allInfoList;

@property (nonatomic, strong)SASavePersonalInfoApiManager *saveInfoApiMgr;
@property (nonatomic, strong)NSMutableDictionary *saveParams;

@property (nonatomic, assign)BOOL isShow;

@property (nonatomic, strong) SAGetCitiesListApiManager *cityManager;

@property (nonatomic, assign) BOOL finished;

@property (nonatomic, assign) BOOL isAgree;
@property (nonatomic, strong) UIView *bottomView;
@property (nonatomic, strong) UIImageView *agreeImg;
@property (nonatomic, strong) SAGetProtocolApiManager *protocolManager;
@property (nonatomic, strong) NSString *protocolKey;

@property (nonatomic, strong) UIView *topTipsV;

@end

@implementation SAVerifyBasicInfoViewController

- (void)viewWillAppear:(BOOL)animated
{
    [super viewWillAppear:animated];
}

- (void)viewDidAppear:(BOOL)animated
{
    [super viewWillAppear:animated];
    
}

- (void)viewWillDisappear:(BOOL)animated
{
    [super viewWillDisappear:animated];
    
//    self.navigationController.interactivePopGestureRecognizer.enabled = YES;
}

- (void)viewDidLoad
{
    [super viewDidLoad];
    self.isAgree = YES;
    
    self.navigationController.interactivePopGestureRecognizer.delegate = self;

    self.navigationItem.title = @"基本信息";
    self.view.backgroundColor = [UIColor colorWithHex:0xf6f6f6];
    self.isShow = YES;
    [self addCustomViews];
    [self createFrames];
    
    [self showActivityHUD:nil];
    [self.getInfoApiMgr loadData];
}

- (void)addCustomViews
{
    [self.view addSubview:self.topTipsV];
    [self.topTipsV mas_makeConstraints:^(SAConstraintMaker *make) {
        make.left.right.top.equalTo(self.view);
        make.height.mas_equalTo(54);
    }];

    [self.view addSubview:self.headerView];
    [self.view addSubview:self.tableView];
    [self.view addSubview:self.footerView];
}

- (void)createFrames
{
    [self.tableView mas_makeConstraints:^(SAConstraintMaker *make) {
        make.bottom.mas_equalTo(self.footerView.mas_top);
        make.left.right.equalTo(self.view);
        make.top.equalTo(self.topTipsV.mas_bottom).offset(10);
    }];
}

#pragma mark - UIGestureRecognizerDelegate

- (BOOL)gestureRecognizerShouldBegin:(UIGestureRecognizer *)gestureRecognizer
{
    if(gestureRecognizer == self.navigationController.interactivePopGestureRecognizer){
        
        [SAFaceBackAlert showFaceAlertSureAction:^{
            [self popSelf];
        }];
        return NO;
    }
    return YES;
}

#pragma mark - TextView

-(BOOL)textView:(UITextView *)textView shouldInteractWithURL:(nonnull NSURL *)URL inRange:(NSRange)characterRange interaction:(UITextItemInteraction)interaction{

    if ([URL.scheme isEqualToString:@"yhxx"])
    {
        NSLog(@"点击了信息授权");
        self.protocolKey = PRIVACY_POLICY_GRXXSQS;
        [self showActivityHUD:nil];
        [self.protocolManager loadData];

    }
    else if ([URL.scheme isEqualToString:@"sjcx"])
    {
        NSLog(@"点击了数据查询");
        self.protocolKey = PRIVACY_POLICY_SJCXSQSMS;
        [self showActivityHUD:nil];
        [self.protocolManager loadData];
    }
    else if ([URL.scheme isEqualToString:@"jkxz"])
    {
        NSLog(@"点击了借款须知");
        self.protocolKey = PRIVACY_POLICY_JKXZJFXTS;
        [self showActivityHUD:nil];
        [self.protocolManager loadData];
    }
    else if ([URL.scheme isEqualToString:@"mgxx"])
    {
        NSLog(@"点击了敏感信息");
        self.protocolKey = PRIVACY_POLICY_MGGRXXSQS;
        [self showActivityHUD:nil];
        [self.protocolManager loadData];
    }
    else if ([URL.scheme isEqualToString:@"zxcx"])
    {
        NSLog(@"点击了征信查询");
        self.protocolKey = PRIVACY_POLICY_SJSYFZXCXSQTK;
        [self showActivityHUD:nil];
        [self.protocolManager loadData];
    }

    return YES;
}

- (void)changeAgree
{
    self.isAgree = !self.isAgree;
    if(!self.isAgree){
        self.agreeImg.image = [UIImage imageNamed:@"verify_check_nor"];
    } else {
        self.agreeImg.image = [UIImage customImageNamed:@"verify_check_sel"];
    }
}

#pragma mark - 点击左上角返回

- (void)backAction:(id)sender
{
    if(self.finished){
        [self popSelf];
        return;
    }
    [SAFaceBackAlert showFaceAlertSureAction:^{
        [self popSelf];
    }];
}

#pragma mark - 协议
#pragma mark UITableViewDataSource

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView{
    return self.allInfoList.count;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    if(self.allInfoList.count > 0){
        return self.allInfoList[section].inputParams.count;
    }
    return 0;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    static NSString * identify = @"typesCellId";
    SAVerifyInfoCell *cell = [tableView dequeueReusableCellWithIdentifier:identify];
    if (cell == nil) {
        cell = [[SAVerifyInfoCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:identify];
    }
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    
    SAPersonalInfoModel *model = self.allInfoList[indexPath.section].inputParams[indexPath.row];
    cell.personalmodel = model;
    return cell;
}

#pragma mark UITableViewDelegate

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    SAPersonalInfoModel *model = self.allInfoList[indexPath.section].inputParams[indexPath.row];
    if(model.paramType == 8){
        SAVerifyInfoCell *cell = [tableView cellForRowAtIndexPath:indexPath];
        [cell handleSelectAction];
    }
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    if(self.allInfoList.count == 0) return CGFLOAT_MIN;
    return XX_6(60);
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section{
    return XX_6(20);
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section{
    if(self.allInfoList.count == 0) return nil;
    UIView *view = [UIView new];
    return  view;
}

#pragma mark QLAPIManagerCallBackDelegate
- (void)managerCallAPIDidSuccess:(SABaseManager *)manager {
    [self hideHUDView];
    if (manager == self.getInfoApiMgr) {
        NSDictionary *dataDict = [manager fetchDataWithReformer:self.getInfoApiMgr];
        self.finished = [dataDict[@"finished"] boolValue];
        self.allInfoList = (NSMutableArray *)[SAPersonalVerifyModel arrayTransfromToModels:dataDict[@"userInputInfoVOList"]];
        [self.tableView reloadData];
    }
    else if (manager == self.saveInfoApiMgr) {
        NSDictionary *dataDict = [manager fetchDataWithReformer:self.saveInfoApiMgr];
        
        [self textStateHUD:@"保存成功"];
        [[SAVerifyListManager sharedInstance] handleVerifyJump];
    }
    
    if(manager == self.cityManager){
        NSDictionary *dataDict = [manager fetchDataWithReformer:self.cityManager];
        if(dataDict[@"locationInfoVOList"] && [dataDict[@"locationInfoVOList"] isKindOfClass:[NSArray class]]){
            [SAAdvanceManager sharedInstance].citiesArr = [NSArray arrayWithObject:dataDict[@"locationInfoVOList"]];
        }
    }
    
    if(manager == self.protocolManager)
    {
        NSDictionary *dataDict = [manager fetchDataWithReformer:self.protocolManager];
        SAProtocolViewController *vc = [SAProtocolViewController new];
        vc.htmlStr = dataDict[@"text"];
        vc.navTitle = dataDict[@"title"];
        vc.companyName = dataDict[@"companyName"];
        vc.appName = dataDict[@"appName"];
        [self presentViewController:[[UINavigationController alloc] initWithRootViewController:vc] animated:YES completion:nil];
    }
}

- (void)managerCallAPIDidFailed:(SABaseManager *)manager {
    [self hideHUDView];
    NSString *failMsg  = [SAResponseHandle netWorkErrorMsg:[manager fetchDataWithReformer:nil] andErrorType:manager.errorType];
    [SATool textStateHUD:failMsg];
}

#pragma mark QLAPIManagerParamSource

- (NSDictionary *)paramsForApi:(SABaseManager *)manager {
    if(manager == self.saveInfoApiMgr){
        return self.saveParams;
    }
    return [NSDictionary dictionary];
}

- (NSString *)methodForApi:(SABaseManager *)manager
{
    if (manager == self.protocolManager) {
        return [NSString stringWithFormat:@"1a99fb9ad8534512af0ede2979dfd68f/%@", self.protocolKey];
    }
    return @"";
}

#pragma mark - Actions

- (void)submitAction
{
    if(!self.isAgree){
        [self textStateHUD:@"阅读并同意协议"];
        return;
    }
    
    NSMutableDictionary *paramsDict = [NSMutableDictionary dictionary];
    for(int i=0; i<self.allInfoList.count;i++){
        SAPersonalVerifyModel *model = self.allInfoList[i];
        for (int inputIdx=0; inputIdx < model.inputParams.count; inputIdx++) {
            SAPersonalInfoModel *info = model.inputParams[inputIdx];
            NSLog(@"输入值 为-------%@", info.inputValue);
            if (info.inputValue == nil || info.inputValue.length == 0) {
                [self textStateHUD:[NSString stringWithFormat:@"%@ %@", info.inputDesc, info.paramName]];
                return;
            }
            [paramsDict setValue:info.inputValue forKey:info.param];
            self.saveParams = paramsDict;
        }
    }
    
    [self showActivityHUD:nil];
    [self.saveInfoApiMgr loadData];

    NSLog(@"个人信息 ------ %@", paramsDict);
}

#pragma mark - getters

- (UITableView *)tableView {
    if (_tableView == nil) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
        _tableView.contentSize = CGSizeMake(UIScreenWidth, UIScreenHeight-XX_6(95));
        _tableView.backgroundColor = [UIColor clearColor];
        _tableView.dataSource      = self;
        _tableView.delegate        = self;
        if (@available(iOS 11.0, *)) {
            _tableView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
        }
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.estimatedRowHeight = 0;
        _tableView.estimatedSectionFooterHeight = 0;
        _tableView.estimatedSectionHeaderHeight = 0;
    }
    return _tableView;
}

- (UIView *)headerView{
    FF_Fetch_UIView(_headerView, [UIColor colorWithHex:0xfcaca3]);
}

- (UIView *)footerView {
    if (_footerView == nil) {
        _footerView = [[UIView alloc] init];
        _footerView.frame = CGRectMake(0, UIScreenHeight-XX_6(90)-kTopHeight, UIScreenWidth, XX_6(90));
        SAGradientButton *nextBtn = [SAGradientButton buttonWithType:UIButtonTypeCustom];
        [nextBtn gradientBackgroundColors:@[kButtonStartColor, kButtonEndColor] locations:nil startPoint:CGPointMake(0, 0.5) endPoint:CGPointMake(1, 0.5)];
        [nextBtn setTitle:@"已完成，下一步" forState:UIControlStateNormal];
        [nextBtn setTitleColor:kHomeTextColor forState:UIControlStateNormal];
        nextBtn.layer.cornerRadius  = 7;
        nextBtn.layer.masksToBounds = YES;
        [nextBtn addTarget:self action:@selector(submitAction) forControlEvents:UIControlEventTouchUpInside];
        [_footerView addSubview:nextBtn];
        [nextBtn mas_makeConstraints:^(SAConstraintMaker *make) {
            make.centerX.centerY.equalTo(_footerView);
            make.left.equalTo(_footerView).offset(25);
            make.height.mas_equalTo(44);
        }];
        
    }
    return _footerView;
}

- (SAGetPersonalInfoApiManager *)getInfoApiMgr {
    if (_getInfoApiMgr == nil) {
        _getInfoApiMgr = [[SAGetPersonalInfoApiManager alloc] init];
        _getInfoApiMgr.delegate = self;
        _getInfoApiMgr.paramSource = self;
    }
    return _getInfoApiMgr;
}

- (SASavePersonalInfoApiManager *)saveInfoApiMgr {
    if (_saveInfoApiMgr == nil) {
        _saveInfoApiMgr = [[SASavePersonalInfoApiManager alloc] init];
        _saveInfoApiMgr.delegate = self;
        _saveInfoApiMgr.paramSource = self;
    }
    return _saveInfoApiMgr;
}

- (NSMutableArray *)allInfoList{
    if(_allInfoList == nil){
        _allInfoList = [NSMutableArray array];
    }
    return _allInfoList;
}

- (BOOL)prefersHomeIndicatorAutoHidden{
    return YES;
}

- (SAGetCitiesListApiManager *)cityManager{
    if(_cityManager == nil){
        _cityManager = [SAGetCitiesListApiManager new];
        _cityManager.delegate = self;
        _cityManager.paramSource = self;
    }
    return _cityManager;
}

- (SAGetProtocolApiManager *)protocolManager {
    if (_protocolManager == nil) {
        _protocolManager = [[SAGetProtocolApiManager alloc] init];
        _protocolManager.delegate = self;
        _protocolManager.paramSource = self;
        _protocolManager.methodSource = self;
    }
    return _protocolManager;
}

- (UIView *)topTipsV{
    if (_topTipsV == nil) {
        _topTipsV = [UIView new];
        _topTipsV.backgroundColor = [UIColor colorWithHex:0xFFF6F1];
        
        UIImageView *icon = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"warning"]];
        [_topTipsV addSubview:icon];
        
        UILabel *lab = [UILabel new];
        lab.textColor = [UIColor colorWithHex:0xEA9947];
        lab.text = @"仅用于身份验证、额度评估、信贷相关业务审核使用，您的信息将严格保密";
        lab.font = Font_XX6(11);
        lab.numberOfLines = 0;
        [_topTipsV addSubview:lab];
        
        [icon mas_makeConstraints:^(SAConstraintMaker *make) {
            make.top.left.equalTo(_topTipsV).offset(12);
            make.width.height.mas_equalTo(16);
        }];
        [lab mas_makeConstraints:^(SAConstraintMaker *make) {
            make.left.equalTo(icon.mas_right).offset(4);
            make.top.equalTo(_topTipsV).offset(12);
            make.right.equalTo(_topTipsV).offset(-12);
        }];
    }
    return _topTipsV;
}

@end
